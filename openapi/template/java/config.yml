configVersion: 2.0.0
generation:
  sdkClassName: SDK
  maintainOpenAPIOrder: true
  usageSnippets:
    optionalPropertyRendering: withExample
  useClassNamesForArrayFields: true
  fixes:
    nameResolutionDec2023: true
    nameResolutionFeb2025: true
    parameterOrderingFeb2024: true
    requestResponseComponentNamesFeb2024: true
    securityFeb2025: true
  auth:
    oAuth2ClientCredentialsEnabled: true
    oAuth2PasswordEnabled: true
java:
  version: 0.0.1
  additionalDependencies: []
  additionalPlugins: []
  artifactID: openapi
  clientServerStatusCodesAsErrors: true
  companyEmail: <EMAIL>
  companyName: My Company
  companyURL: www.mycompany.com
  defaultErrorName: APIException
  flattenGlobalSecurity: true
  githubURL: github.com/owner/repo
  groupID: org.openapis
  imports:
    option: openapi
    paths:
      callbacks: models/callbacks
      errors: models/errors
      operations: models/operations
      shared: models/components
      webhooks: models/webhooks
  inputModelSuffix: input
  license:
    name: The MIT License (MIT)
    shortName: MIT
    url: https://mit-license.org/
  maxMethodParams: 4
  outputModelSuffix: output
  projectName: openapi
  templateVersion: v2
