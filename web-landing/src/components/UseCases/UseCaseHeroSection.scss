@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

@use '../../app/landing-mixin.scss' as *;

$content-max-width-sm: 600px;
$content-max-width-md: 380px;
$content-max-width-lg: 466px;

.use_case_hero_section_container {
    @include landing-secondary-background-gradient(bottom, 0.1);

    width: 100vw;
    overflow: hidden;
}

.use_case_hero_section {
    max-width: $landing-max-content-width;
    overflow: hidden;
    margin: 0 auto;
    padding-top: $spacer-48;
    @include flex-column-start;

    @include breakpoint(md) {
        display: grid;
        grid-template-columns: $content-max-width-md 1fr;
        padding-top: 0;
        overflow: visible;

        .use_case_hero_section__img {
            margin-left: -$spacer-32;
        }
    }

    @include breakpoint(lg) {
        grid-template-columns: $content-max-width-lg 1fr;
    }

    & > section {
        flex: 1;
    }

    .use_case_hero_section__content {
        @include flex-column-center;

        gap: $spacer-4;
        text-align: center;
        max-width: $content-max-width-sm;
        margin: 0 auto;
        padding: 0 $spacer-24;

        @include breakpoint(md) {
            @include flex-column-start;

            gap: $spacer-16;
            text-align: left;
            min-width: $content-max-width-md;
            max-width: $content-max-width-md;
            padding-left: $spacer-40;
            margin-top: 150px;
        }

        @include breakpoint(lg) {
            min-width: $content-max-width-lg;
            max-width: $content-max-width-lg;
            padding-left: $size-48;
        }

        @include breakpoint(xl) {
            padding-left: $size-100;
        }
    }

    .use_case_hero_section__buttons {
        display: flex;
        flex-direction: row;
        gap: $spacer-16;
        justify-content: center;
        margin-top: $spacer-16;

        @include breakpoint(md) {
            justify-content: flex-start;
        }
    }

    .use_case_hero_section__header {
        color: themed($text);
        // @include hero-header;
    }

    .use_case_hero_section__description {
        color: themed($text-secondary);
        // font-weight: $font-weight-light;
        // @include hero-description;
    }

    .use_case_hero_section__button {
        @include hero-button-styling;

        min-width: 160px;

        @include breakpoint(md) {
            align-self: start;
            min-width: 200px;
            min-height: $size-56;
            border-radius: 6px;
        }
    }

    .use_case_hero_section__img_mobile {
        width: 100%;
        height: auto;

        @include breakpoint(sm) {
            margin-top: -$spacer-24;
        }
    }

    .use_case_hero_section__img_desktop {
        width: auto;
        height: 645px;

        @include breakpoint(lg) {
            height: 743px;
        }
    }
}
