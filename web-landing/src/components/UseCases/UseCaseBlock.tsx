import classNames from 'classnames';

import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';

import './UseCaseBlock.scss';

export interface UseCaseBlock {
    icon: IconSrc;
    header: React.ReactNode;
    description: React.ReactNode;
}

interface Props {
    block: UseCaseBlock;
    className?: string;
}

export const UseCaseBlock = ({ block, className }: Props) => {
    const { icon, header, description } = block;
    return (
        <div className={classNames('use_case_block', className ?? '')}>
            <div className="use_case_block__icon">
                <Icon icon={icon} />
            </div>
            <h4 className="use_case_block__header">{header}</h4>
            <div className="use_case_block__description">{description}</div>
        </div>
    );
};
