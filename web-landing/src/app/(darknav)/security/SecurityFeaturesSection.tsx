import { FeatureBlock } from '@landing/components/FeatureBlock/FeatureBlock';

import { SecurityFeatureGroup } from './SecurityFeatures';

import './SecurityFeaturesSection.scss';

interface Props {
    securityFeatureGroup: SecurityFeatureGroup;
}
export const SecurityFeaturesSection = ({ securityFeatureGroup }: Props) => {
    return (
        <div className="security_features_section">
            <div className="security_features_section__title">
                <h2>{securityFeatureGroup.groupTitle}</h2>
                <h2>Security</h2>
            </div>

            <div className="security_features_section__list">
                {securityFeatureGroup.features.map(({ icon, title, description }, idx) => (
                    <FeatureBlock
                        block={{
                            icon,
                            header: title,
                            description,
                        }}
                        key={idx}
                        variant="secondary"
                    />
                ))}
            </div>
        </div>
    );
};
