@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use '../../landing-mixin.scss' as *;

.security {
    .security__header {
        position: relative;
        overflow: hidden;

        &::after {
            content: '';
            position: absolute;
            width: 550px;
            height: 615px;
            right: -20%;
            bottom: -150px;
            background: url('~@clientAssets/landing/integrations-background.png') transparent 50% / cover no-repeat;
            opacity: 0.02;

            @include breakpoint(md) {
                right: -10%;
                bottom: -400px;
                width: 898px;
                height: 1000px;
            }
        }

        .home_section__content {
            display: grid;
            grid-template:
                'title'
                'description'
                'subtitle'
                / auto;

            h1 {
                grid-area: title;
                color: $white-80;
            }
            .security__header__description {
                grid-area: description;
                color: rgba(218 214 253 / 80%);
            }

            .security__header__disclaimer {
                grid-area: subtitle;
                color: #eceafe;
                font-weight: 500;
            }

            .icon {
                grid-area: icon;
                display: none;
                @include icon-shadow;
            }

            @include breakpoint(md) {
                grid:
                    'spacer icon' 1fr
                    'title icon' auto
                    'description icon' auto
                    'subtitle icon' auto
                    'spacer2 icon' 1fr
                    / auto 1fr;

                .icon {
                    height: 356px;
                    width: 335px;
                    display: block;
                }
                gap: 8px;
            }

            @include breakpoint(lg) {
                .icon {
                    height: 428px;
                    width: 403px;
                    display: block;
                }
            }
        }
    }

    .security__header .home_section__content,
    .security_certificates_section .home_section__content,
    .security_features_section {
        max-width: 1258px;
        margin: 0 auto;
    }

    .security_certificates_section,
    .security_features_section,
    .security__trust {
        position: relative;

        &::after {
            content: ' ';
            display: block;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent 0%, rgb(222 229 237 / 100%) 50%, transparent 100%);
            border: none;
            margin: 0;
            height: 1px;
        }
    }

    .security__trust {
        padding: 40px 25px;
        @include flex-column-center;

        text-align: center;
        background: radial-gradient(
                94.93% 59.5% at 26.56% 86.78%,
                rgba(62, 45, 218, 0.26) 0%,
                rgba(62, 45, 218, 0.03) 100%
            ),
            radial-gradient(126.99% 80.6% at 71.9% 43.51%, rgba(173, 0, 255, 0.2) 0%, rgba(173, 0, 255, 0.02) 100%),
            radial-gradient(124.02% 87.38% at 50% 100%, rgba(0, 133, 255, 0.04) 0%, rgba(0, 133, 255, 0) 100%);

        .security__trust__icon {
            height: 80px;
            width: auto;
            margin-bottom: 25px;

            @include breakpoint(md) {
                height: 100px;
                margin-bottom: 30px;
            }
        }

        .security__trust__description {
            @include landing-description;

            color: themed($text);
        }
    }
}
