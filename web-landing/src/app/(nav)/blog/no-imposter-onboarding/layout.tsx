import { ReactNode } from 'react';

import { CreateMetadata } from '@landing/utils/Metadata';

export const metadata = CreateMetadata({
    title: 'Your new developer feels like a fraud. It’s your onboarding process.',
    description:
        'New engineers face a tough learning curve, navigating a new role and a complex codebase. It’s no wonder they feel like imposters. We need to reimagine onboarding to better support them.',
    imageUrl: 'https://getunblocked.com/public/blog-article-009-preview.jpg',
    canonical: 'https://getunblocked.com/blog/no-imposter-onboarding',
});

export default function Fn({ children }: { children: ReactNode }) {
    return children;
}
