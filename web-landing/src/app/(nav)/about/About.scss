@use 'layout' as *;
@use 'colors' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;

.about {
    @include flex-column;

    .aboutHero {
        @include flex-column-center;

        display: block;
        padding: 40px 0;
        position: relative;

        // background: #d3cce3; /* fallback for old browsers */
        // background: linear-gradient(to bottom right, #f7f6ff 36%, #f0dfff 100%);
        // border-bottom: 1px solid $light-purple-10;

        h1 {
            background: linear-gradient(356deg, #362783, #7b6dc2);
            /* stylelint-disable-next-line property-no-vendor-prefix */
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        p {
            font-size: 18px;
            line-height: 24px;
            color: themed($text-secondary);
            letter-spacing: -0.015em;

            @include breakpoint(md) {
                font-size: 20px;
                line-height: 27px;
            }

            @include breakpoint(lg) {
                font-size: 22px;
                line-height: 30px;
            }
        }

        .section__text {
            max-width: 800px;
            text-align: center;
            margin: 0 auto;
            padding: 0 24px;
        }

        // @include breakpoint(sm) {
        //     padding: 60px 0 80px;
        // }

        .about_investor_divider {
            margin-bottom: 16px;
        }
    }

    .ourTeam {
        padding: 60px 0;

        // max-width: 1080px;
        margin: 0 auto;
        text-align: center;

        .teamGrid {
            display: grid;
            gap: 32px 16px;
            padding: 24px 0;
            grid-template-columns: 1fr 1fr;

            @include breakpoint(sm) {
                grid-template-columns: 1fr 1fr;
                gap: 48px 40px;
            }

            @include breakpoint(md) {
                column-gap: 100px;
            }

            @include breakpoint(lg) {
                grid-template-columns: 1fr 1fr 1fr;
                column-gap: 112px;
            }

            @include breakpoint(xl) {
                grid-template-columns: 1fr 1fr 1fr 1fr;
                column-gap: 100px;
            }
        }
    }

    .hiring {
        padding: 60px 24px;
        background: linear-gradient(to bottom right, #f6f0fe 36%, #f0dcfe 100%);

        .section__text {
            max-width: 1024px;
            text-align: center;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        h2 {
            margin-bottom: 16px;
        }

        button {
            margin: 12px auto;

            a {
                color: $white-100;
                text-decoration: none;
            }

            @include breakpoint(md) {
                // padding: 16px 32px;
            }
        }
    }

    .ourInvestors {
        padding: 60px 24px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;

        h3 {
            margin-bottom: 48px;
        }

        .ventureGrid {
            display: grid;
            width: 280px;
            gap: 24px 40px;
            margin: 48px 0;

            @include breakpoint(md) {
                grid-template-columns: 1fr 1fr 1fr;
                width: 720px;
            }

            @include breakpoint(lg) {
                width: 926px;
            }

            @include breakpoint(xl) {
                width: 1080px;
            }
        }

        .angelGrid {
            // width: 1080px;
            display: grid;

            // grid-template-columns: 1fr 1fr 1fr;
            gap: 40px 40px;
            margin: 48px 0;

            @include breakpoint(sm) {
                grid-template-columns: 1fr 1fr 1fr;
                width: 480px;
            }

            @include breakpoint(md) {
                grid-template-columns: 1fr 1fr 1fr;
                width: 720px;
            }

            @include breakpoint(lg) {
                width: 926px;
            }

            @include breakpoint(xl) {
                width: 1080px;
            }
        }
    }
}
