import { spawn } from 'child_process';
import { app } from 'electron';
import fsPromises from 'fs/promises';
import path from 'path';

import { logger } from '@shared/webUtils/log';

import { UnpackPkgFile } from './FileUnpackager';

// The set of bundle IDs
// The apps for all of these bundle IDs are shut down when updating
// The app for the *first* bundle ID is re-launched after updating
const BUNDLE_IDS = ['com.nextchaptersoftware.UnblockedHub', 'com.nextchaptersoftware.UnblockedHubBeta'];

const APP_NAME = 'Unblocked.app';

const log = logger('MacSelfUpdater');

export async function UpdateMac(downloadedPkgPath: string) {
    const expandedPkgPath = await UnpackPkgFile(downloadedPkgPath, 'unblocked-desktop-unpacked');

    // This is the current app's "Unblocked.app" path
    const appPath = path.resolve(app.getAppPath(), '..', '..', '..');

    // This is the source .app content to copy over the current app
    const sourceAppPath = path.join(expandedPkgPath, 'desktop.pkg', 'payload', APP_NAME);

    try {
        await fsPromises.rm(downloadedPkgPath);
    } catch (e) {
        log.warn("Couldn't delete upgrade package", e);
    }

    const updaterPath = path.join(process.resourcesPath, 'MacUpdater');

    // Note: we use spawn here instead of Runner, becuase we want this to be run detached from this parent process,
    // ignoring its stdio channels
    spawn(updaterPath, [appPath, sourceAppPath, BUNDLE_IDS.join(',')], {
        stdio: 'ignore',
        detached: true,
    });

    // Done -- exit
    app.exit();
}
