import classNames from 'classnames';
import { ReactNode, useEffect, useMemo } from 'react';

import { IDEPluginInstallerStreamTraits } from '@desktop/components/ClientWorkspace/IDEPluginInstallerStreamTraits';
import { IndeterminateProgressBar } from '@desktop/components/IndeterminateProcessBar/IndeterminateProgressBar';
import { RenderWebview } from '@desktop/components/RenderWebview';
import { useStream } from '@shared/stores/DataCacheStream';
import { Button } from '@shared/webComponents/Button/Button';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { StringsHelper } from '@shared/webUtils';
import { BrandIcons } from '@shared/webUtils/BrandIcons';

import { faCircleCheck } from '@fortawesome/pro-light-svg-icons/faCircleCheck';
import { faCircleExclamation } from '@fortawesome/pro-light-svg-icons/faCircleExclamation';
import { faCircleXmark } from '@fortawesome/pro-light-svg-icons/faCircleXmark';

import '@desktop/styleOverrides';
import './InstallIDEPlugins.scss';

interface Props {
    icon: IconSrc;
    iconClass?: string;
    title?: string;
    children: ReactNode;
    actions?: ReactNode;
}

function InstallPluginState({ icon, iconClass, title, children, actions }: Props) {
    return (
        <div className="install_ide_plugins">
            <Icon className={classNames('install_ide_plugins__icon', iconClass)} icon={icon} size={64} />
            {title && <h1 className="install_ide_plugins__title">{title}</h1>}
            <div className="install_ide_plugins__content">{children}</div>
            {actions && <div className="install_ide_plugins__actions">{actions}</div>}
        </div>
    );
}

function InstalledIDELabel(ideNames: string[]): string {
    switch (ideNames.length) {
        case 0:
            return ''; // shouldn't happen

        case 1:
            return ideNames[0];

        case 2:
            return `${ideNames[0]} and ${ideNames[1]}`;

        default:
            const extraItemCount = ideNames.length - 2;
            return `${ideNames[0]}, ${ideNames[1]}, and ${extraItemCount} other ${StringsHelper.pluralize({ singular: 'IDE' })}`;
    }
}

function InstallIDEPlugins() {
    const stream = useMemo(
        () =>
            ClientWorkspace.instance().getControlledStream(IDEPluginInstallerStreamTraits, {
                $case: 'idePluginInstaller',
            }),
        []
    );

    const installState = useStream(() => stream.stream, [stream]);

    // Begin installation when this view is opened
    useEffect(() => stream.sendCommand({ $case: 'startInstall' }), [stream]);

    if (!installState || (installState.$case === 'idle' && !installState.lastResult)) {
        return <Loading />;
    }

    if (installState.$case === 'running') {
        return (
            <InstallPluginState icon={BrandIcons.unblocked}>
                <>
                    <IndeterminateProgressBar className="install_ide_plugins__progress" />
                    <div>Downloading and installing Unblocked plugins...</div>
                </>
            </InstallPluginState>
        );
    }

    if (!installState.lastResult) {
        return <Loading />;
    }

    switch (installState.lastResult.$case) {
        case 'success':
            return (
                <InstallPluginState
                    icon={faCircleCheck}
                    iconClass="install_ide_plugins__icon__success"
                    title="Unblocked plugins successfully installed."
                    actions={<Button onClick={() => stream.sendCommand({ $case: 'close' })}>Close</Button>}
                >
                    The Unblocked IDE Plugin was installed in{' '}
                    {InstalledIDELabel(installState.lastResult.installedIdeNames)}
                </InstallPluginState>
            );

        case 'error':
            return (
                <InstallPluginState
                    icon={faCircleXmark}
                    iconClass="install_ide_plugins__icon__error"
                    title="The Unblocked plugin could not be installed."
                    actions={<Button onClick={() => stream.sendCommand({ $case: 'close' })}>Close</Button>}
                >
                    An error occurred during installation. Please reach out to the Unblocked team for assistance.
                </InstallPluginState>
            );

        case 'noIdes':
            return (
                <InstallPluginState
                    icon={faCircleExclamation}
                    iconClass="install_ide_plugins__icon__error"
                    title="Unblocked could not detect an IDE on your Mac."
                    actions={<Button onClick={() => stream.sendCommand({ $case: 'close' })}>Close</Button>}
                >
                    Download Visual Studio Code, or a JetBrains IDE before trying again.
                </InstallPluginState>
            );
    }
}

RenderWebview(InstallIDEPlugins);
