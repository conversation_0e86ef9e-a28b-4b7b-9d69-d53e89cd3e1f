//
//  InstallViewModel.swift
//  UnblockedInstaller
//
//  Created by <PERSON> on 2024-05-24.
//


import SwiftUI

class InstallViewModel: ObservableObject {
    @Published var progress: Double = 0
    @Published var description: String = ""
    @Published var hasError: Bool = false
    
    func install() async {
        do {
            let installationManager = InstallationManager()
            await MainActor.run {
                description = "Downloading Unblocked"
            }
            guard let downloadUrl = try await installationManager.getDownloadUrl() else {
                print("Failed to get download url")
                return
            }
            await MainActor.run {
                progress = 10
            }
            guard let installedUrl = try await installationManager.downloadInstaller(url: downloadUrl) else {
                print("Failed to get install url")
                return
            }
            await MainActor.run {
                progress = 60
            description = "Installing Unblocked"

            }
                        guard let unpackagedTmpUrl = installationManager.unpackage(url: installedUrl) else {
                print("Failed to unpackage")
                return
            }
            await MainActor.run {
                progress = 90
            }
            let didInstall = await installationManager.moveToApplications(url: unpackagedTmpUrl)
            await MainActor.run {
                progress = 100
            }
            installationManager.cleanup()
            print("Did install: \(didInstall)")
        } catch {
            print("error \(error)")
            await MainActor.run {
                hasError = true
            }
        }
    }
}
