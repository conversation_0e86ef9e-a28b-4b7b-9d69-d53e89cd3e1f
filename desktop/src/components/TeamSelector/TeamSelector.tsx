import { forwardRef, Ref, useMemo } from 'react';

import { Icon } from '@shared/webComponents/Icon/Icon';
import { UserTeamIcon } from '@shared/webComponents/UserIcon/UserTeamIcon';

import { faChevronDown } from '@fortawesome/pro-regular-svg-icons/faChevronDown';

import { Props as TeamSelectorDropdownProps, TeamSelectorDropdown } from './TeamSelectorDropdown';

type Props = TeamSelectorDropdownProps;
import classNames from 'classnames';

import { useStream } from '@shared/stores/DataCacheStream';
import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { AllMyUnreadThreadsStreamTraits } from '@shared/webComponents/ClientWorkspace/ClientWorkspaceStreamTraits';
import { Dropdown } from '@shared/webComponents/Dropdown/Dropdown';

import './TeamSelector.scss';
const TeamSelectorButtonInternal = ({ currentTeam, currentTeamMember }: Props, ref: Ref<HTMLDivElement>) => {
    const stream = useStream(
        () =>
            ClientWorkspace.instance().getStream(AllMyUnreadThreadsStreamTraits, { $case: 'allMyUnreadThreadsStream' }),
        []
    );

    const hasUnreads = useMemo(() => {
        // Unreads for every other team.
        return (
            stream?.$case === 'ready' &&
            stream.value.threads.filter((v) => v.thread.teamId !== currentTeam.id).length > 0
        );
    }, [stream, currentTeam]);
    const teamMemberDisplayName = currentTeamMember?.identity.username ?? currentTeamMember?.identity.displayName;

    const className = classNames({
        team_selector: true,
        'team_selector--has_unreads': hasUnreads,
    });

    return (
        <div ref={ref} className={className}>
            <UserTeamIcon
                className="team_selector__icon"
                currentTeam={currentTeam}
                currentTeamMember={currentTeamMember}
                iconSize="large"
                userIconSize="xSmall"
            />
            <div className="team_selector__profile_team">{currentTeam.displayName}</div>
            {teamMemberDisplayName && <div className="team_selector__profile_user">{teamMemberDisplayName}</div>}
            <Icon className="home_navigator__profile__chevron" icon={faChevronDown} size="xSmall" />
        </div>
    );
};
const TeamSelectorButton = forwardRef(TeamSelectorButtonInternal);

export const TeamSelector = (props: Props) => {
    return (
        <Dropdown header={<TeamSelectorButton {...props} />} withCaret={false}>
            <TeamSelectorDropdown {...props} />
        </Dropdown>
    );
};
