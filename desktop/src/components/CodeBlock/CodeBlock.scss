@use 'fonts' as *;
@use 'flex' as *;
@use 'misc' as *;
@use 'layout' as *;
@use 'theme' as *;

.code_block {
    font-size: $font-size-13;

    .code_block__code {
        border-color: themed($code-block-border);
    }

    .code_block__code pre {
        display: table;
        width: 100%;
        margin: 0;
        margin-top: $spacer-1;
        font-family: $source;
        padding: $spacer-8 0;
        overflow-x: auto;
        counter-reset: step;
        counter-increment: step calc(var(--start, 1) - 1);
        margin-bottom: 0;

        .line {
            box-sizing: border-box;
            width: 100%;
            position: relative;

            &::before {
                content: counter(step);
                counter-increment: step;
                padding-right: $spacer-5;
                margin-right: $spacer-5;
                display: inline-block;
                text-align: right;
                color: themed($code-block-lines);
                line-height: 1.5;
                font-size: inherit;
                font-family: inherit;
                user-select: none;
            }

            &.highlighted::after {
                content: '';
                position: absolute;
                inset: 0;
                opacity: 0.05;
                background-color: themed($code-highlight);
            }
        }
    }
}
