@use 'layout' as *;
@use 'theme' as *;
@use 'misc' as *;
@use 'fonts' as *;

@use '~shared/webComponents/Banner/Banner.scss' as *;

.banner {
    box-sizing: border-box;
    min-height: $size-22;
    padding: $spacer-4 $spacer-20;
    font-size: $font-size-13;
    line-height: $line-height-17;

    &.banner__with_header {
        padding: $spacer-4 $spacer-24;
    }

    &.banner__with_header.banner__with_icon {
        padding: $spacer-12;
    }

    &.banner__floating {
        padding: $spacer-8 $spacer-12 $spacer-12 $spacer-12;
    }
    &.banner__info {
        background-color: themed($banner-info-bg);
        color: themed($banner-info-fg);
    }

    &.banner__warning {
        background-color: themed($banner-warning-bg);
        color: themed($banner-warning-fg);
        border-top: $border-width $border-style themed($banner-warning-border);
        border-bottom: $border-width $border-style themed($banner-warning-border);

        &.banner__with_header {
            .banner__content {
                color: themed($banner-warning-fg-secondary);
            }
        }

        &.banner__floating {
            border: $border-width $border-style themed($banner-warning-border-secondary);
        }

        &.banner__with_tail {
            @include banner-tail-color(themed($banner-warning-border-secondary), themed($banner-warning-bg));
        }
    }

    &.banner__alert {
        background-color: themed($banner-alert-bg);
        color: themed($banner-alert-fg);
        border-bottom: $border-width $border-style themed($banner-alert-border);

        &.banner__with_header {
            .banner__content {
                color: themed($banner-alert-fg-secondary);
            }
        }

        &.banner__floating {
            border: $border-width $border-style themed($banner-alert-border-secondary);
        }

        &.banner__with_tail {
            @include banner-tail-color(themed($banner-alert-border-secondary), themed($banner-alert-bg));
        }
    }

    &.banner__success {
        background-color: themed($banner-success-bg);
        color: themed($banner-success-fg);
        border-bottom: $border-width $border-style themed($banner-success-border);

        &.banner__with_header {
            .banner__content {
                color: themed($banner-success-fg-secondary);
            }
        }

        &.banner__floating {
            border: $border-width $border-style themed($banner-success-border-secondary);
        }

        &.banner__with_tail {
            @include banner-tail-color(themed($banner-success-border-secondary), themed($banner-success-bg));
        }
    }

    &.banner__private {
        background-color: themed($banner-private-bg);
        color: themed($banner-private-fg);

        .banner__icon {
            color: themed($banner-private-icon);
        }
    }

    &.banner__unblocked {
        background: themed($banner-unblocked-bg-base);
        background: themed($banner-unblocked-bg-left), themed($banner-unblocked-bg-right), themed($banner-unblocked-bg);
        color: themed($banner-unblocked-fg);
        border-bottom: $border-width $border-style themed($banner-unblocked-border);

        &.banner__with_header {
            .banner__content {
                color: themed($banner-unblocked-fg-secondary);
            }
        }

        &.banner__floating {
            border: $border-width $border-style themed($banner-unblocked-border-secondary);
        }

        &.banner__with_tail {
            @include banner-tail-color(themed($banner-unblocked-border-secondary), themed($banner-unblocked-bg-base));
        }
    }
}
