import { ElectronTokenProvider } from '@desktop/auth/ElectronTokenProvider';
import { AuthStore } from '@shared/stores/AuthStore';

import { LoginExtensionProps } from './LoginViewTypes';

export const handleLoginEvent = async (event: LoginExtensionProps) => {
    console.log('handle login event', event);
    switch (event.command) {
        case 'token':
            ElectronTokenProvider.setRefreshToken(event.token);
            AuthStore.get().refreshAuth(true);
            break;
    }
};
