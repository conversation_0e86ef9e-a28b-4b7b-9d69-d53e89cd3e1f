@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'button-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'theme' as *;

.modal_dialog.delete_reference_dialog {
    color: themed($text);
    font-size: $font-size-13;
    line-height: $line-height-17;

    .modal_dialog__header {
        margin-bottom: 0;
    }

    .modal_dialog__body {
        .modal_dialog__content {
            margin: $spacer-8 0 $spacer-12;
        }
    }

    .delete_reference_dialog__reasons_container {
        margin-top: $spacer-12;

        .delete_reference_dialog__reasons_checkbox_label {
            font-weight: $font-weight-bold;
        }

        .delete_reference_dialog__reasons {
            margin: $spacer-4 0 $spacer-6 $spacer-16;

            .delete_reference_dialog__reasons_rows {
                @include flex-column-start;

                gap: $spacer-4;
            }

            .text_area {
                margin-top: $spacer-8;
            }
        }
    }
    .modal_dialog__buttons,
    .modal_dialog__new_buttons {
        .button__primary {
            @include destructive-secondary-button;
        }
    }
}
