@use 'layout' as *;
@use 'theme' as *;
@use 'flex' as *;
@use 'misc' as *;
@use 'fonts' as *;

.toggle_thread_privacy_dropdown {
    .dropdown__group__header {
        border-color: themed($border);
        background-color: themed($row-section-bg);
        padding: $spacer-4 $spacer-16;
    }
    .toggle_thread_privacy_dropdown__header {
        span {
            color: themed($text);
        }
        .copy_link {
            color: themed($link-copy);
            span {
                color: themed($link-copy);
            }
        }
    }

    .dropdown__items {
        .dropdown__item {
            margin: 0;
            padding: $spacer-12 $spacer-16;
            border-radius: 0;

            &.dropdown__item__active,
            &:hover {
                background-color: themed($toggle-thread-privacy-dropdown-hover-bg);
                color: themed($toggle-thread-privacy-dropdown-hover-fg);
            }
        }
    }
}

.toggle_thread_privacy_row {
    .toggle_thread_row__title {
        font-size: $font-size-13;
        line-height: $line-height-17;
    }
    .toggle_thread_row__description {
        color: themed($text-tertiary);
        font-size: $font-size-12;
        line-height: $line-height-16;
    }

    .toggle_thread_row__check {
        color: themed($success);
    }
}
