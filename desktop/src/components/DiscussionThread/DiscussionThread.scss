@use 'layout' as *;
@use 'misc' as *;
@use 'flex' as *;
@use 'fonts' as *;

.discussion_thread {
    .discussion_thread__footer {
        max-width: none;
        padding: $spacer-2 $spacer-30 $spacer-6 $spacer-30;
        grid-template-rows: auto $spacer-20;
        gap: $spacer-4;

        .loop_in_experts_list {
            font-size: $font-size-13;
            line-height: $line-height-17;

            .loop_in_experts_list__description {
                margin-top: $spacer-1;
            }

            .expert_button {
                font-size: $font-size-13;
                line-height: $line-height-17;
            }
        }

        @media (min-width: $messageLayoutBreakpoint) {
            .loop_in_experts_list {
                margin-left: 0;
            }
        }
    }

    .discussion_thread__reply_section {
        .team_member_row .user_icon {
            margin: 0;
        }

        @media (min-width: $messageLayoutBreakpoint) {
            grid-template-columns: 1fr;
        }
    }

    .thread_view {
        max-width: none;

        @media (min-width: $messageLayoutBreakpoint) {
            padding: 0 84px $spacer-20 $spacer-36;
        }
    }
}
