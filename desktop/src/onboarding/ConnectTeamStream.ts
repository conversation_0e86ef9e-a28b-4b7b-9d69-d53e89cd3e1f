import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { API } from '@shared/api';

import { AuthStore } from '@shared/stores/AuthStore';
import { TeamStore } from '@shared/stores/TeamStore';
import { WindowFocusStream } from '@shared/stores/WindowFocusStream';
import { LazyValue } from '@shared/webUtils/LazyValue';

import { ConnectTeamState } from './ConnectTeamTypes';

export class ConnectTeamStream {
    static readonly instance = LazyValue(() => new ConnectTeamStream());

    private refreshStream = Stream.merge(WindowFocusStream.gainedFocusStream, Stream.periodic(30000))
        .mapAsync(() => this.refreshAuthAndTeams())
        .mapTo(true)
        .startWith(true);

    readonly stream: Stream<ConnectTeamState> = Stream.combine(
        AuthStore.get().isAuthenticatedStream,
        this.refreshStream
    )
        .map(([isAuthenticated]) => isAuthenticated)
        .compose(dropRepeats())
        .mapAsync((isAuthenticated) => this.getConnectState(isAuthenticated));

    private async refreshAuthAndTeams() {
        try {
            await AuthStore.get().refreshAuth(true);
            await TeamStore.refreshTeams();
        } catch {}
    }

    private async getConnectState(isAuthenticated: boolean): Promise<ConnectTeamState> {
        if (!isAuthenticated) {
            return { $case: 'loading' };
        }

        const installations = await API.scmInstall.listInstallations();

        return {
            $case: 'loaded',
            provider: installations.provider,
            installUrl: installations.installUrl,
        };
    }
}
