package com.nextchaptersoftware.models

import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.RepoId
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class UserLocalFileContextTest {

    private val content = """
    @Serializable
    data class UserLocalFileContext(
        @Contextual
        val repoId: RepoId,

        val filePath: String,

        val partialContent: String? = null,

        val isVisible: <PERSON><PERSON>an,

        val isActive: <PERSON><PERSON>an,

        @Contextual
        val lastViewedAt: Instant? = null,

        val lineRange: Range? = null,

        val selectedLineRange: Range? = null,

        val selectedColumnRange: Range? = null,
    ) {
        var selectedContent: String? = partialContent?.let { fileContent ->
            lineRange?.let { fileLineRange ->
                selectedLineRange?.let {
                    val lines = fileContent.split("\n")
                    val selectedLines = lines.subList(
                        max(0, selectedLineRange.start - fileLineRange.start),
                        min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                    )
                    when (selectedLines.size) {
                        0 -> null
                        1 -> selectedLines.first().let { firstLine ->
                            selectedColumnRange?.let {
                                firstLine.substring(
                                    max(0, selectedColumnRange.start - 1),
                                    min(selectedColumnRange.end, firstLine.length),
                                )
                            } ?: firstLine
                        }

                        else -> selectedLines.joinToString("\n")
                    }
                }
            }
        }
    }
    """.trimIndent()

    @Suppress("LongMethod")
    @Test
    fun `sub line selection is annotated with selected section`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(17, 17),
            selectedColumnRange = UserLocalFileRange(9, 23),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

                val [[[partialContent]]]: String? = null,

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
                var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }
            """.trimIndent(),
        )
    }

    @Suppress("LongMethod")
    @Test
    fun `full line selection is annotated with selected section`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(17, 17),
            selectedColumnRange = UserLocalFileRange(1, 40),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

            [[[    val partialContent: String? = null,]]]

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
                var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }
            """.trimIndent(),
        )
    }

    @Suppress("LongMethod")
    @Test
    fun `multi line selection is annotated with selected section`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(32, 56),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

                val partialContent: String? = null,

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
            [[[    var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }]]]
            """.trimIndent(),
        )
    }

    @Suppress("LongMethod")
    @Test
    fun `single line selection is annotated with selected section`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(17, 17),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

            [[[    val partialContent: String? = null,]]]

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
                var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }
            """.trimIndent(),
        )
    }

    @Test
    fun `no selected lines means no annotations`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo(content)
    }

    @Test
    fun `edge case, empty content`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = "",
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(1, 1),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo("")
    }

    @Test
    fun `edge case, null content`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = null,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(1, 1),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isNull()
    }

    @Test
    fun `edge case, all content selected`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(10, 56),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo("""[[[$content]]]""")
    }

    @Test
    fun `line ranges are clamped`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(0, 57),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo("""[[[$content]]]""")
    }

    @Test
    fun basics() {
        val list = listOf(1, 2, 3)
        val sublist = list.subList(0, 0)
        assertThat(sublist).isEmpty()
    }

    @Suppress("LongMethod")
    @Test
    fun `column ranges are clamped`() {
        val localFileContext = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(17, 17),
            selectedColumnRange = UserLocalFileRange(-10, 800),
        )

        assertThat(localFileContext.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

            [[[    val partialContent: String? = null,]]]

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
                var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }
            """.trimIndent(),
        )
    }

    @Suppress("LongMethod")
    @Test
    fun `column ranges are out of bounds`() {
        val localFileContextLessThanBounds = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(0, 8),
            selectedColumnRange = UserLocalFileRange(0, 80),
        )

        assertThat(localFileContextLessThanBounds.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

                val partialContent: String? = null,

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
                var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }
            """.trimIndent(),
        )

        val localFileContextGreaterThanBounds = UserLocalFileContext(
            installationId = InstallationId.random(),
            repoId = RepoId.random(),
            filePath = "/path/to/file",
            partialContent = content,
            isVisible = true,
            isActive = true,
            lastViewedAt = null,
            lineRange = UserLocalFileRange(10, 56),
            selectedLineRange = UserLocalFileRange(58, 64),
            selectedColumnRange = UserLocalFileRange(0, 80),
        )

        assertThat(localFileContextGreaterThanBounds.contentWithAnnotatedSelection).isEqualTo(
            """
            @Serializable
            data class UserLocalFileContext(
                @Contextual
                val repoId: RepoId,

                val filePath: String,

                val partialContent: String? = null,

                val isVisible: Boolean,

                val isActive: Boolean,

                @Contextual
                val lastViewedAt: Instant? = null,

                val lineRange: Range? = null,

                val selectedLineRange: Range? = null,

                val selectedColumnRange: Range? = null,
            ) {
                var selectedContent: String? = partialContent?.let { fileContent ->
                    lineRange?.let { fileLineRange ->
                        selectedLineRange?.let {
                            val lines = fileContent.split("\n")
                            val selectedLines = lines.subList(
                                max(0, selectedLineRange.start - fileLineRange.start),
                                min(selectedLineRange.end + 1 - fileLineRange.start, lines.size),
                            )
                            when (selectedLines.size) {
                                0 -> null
                                1 -> selectedLines.first().let { firstLine ->
                                    selectedColumnRange?.let {
                                        firstLine.substring(
                                            max(0, selectedColumnRange.start - 1),
                                            min(selectedColumnRange.end, firstLine.length),
                                        )
                                    } ?: firstLine
                                }

                                else -> selectedLines.joinToString("\n")
                            }
                        }
                    }
                }
            }
            """.trimIndent(),
        )
    }
}
