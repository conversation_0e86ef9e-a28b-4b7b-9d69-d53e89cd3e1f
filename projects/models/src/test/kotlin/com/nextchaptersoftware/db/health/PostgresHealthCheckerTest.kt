package com.nextchaptersoftware.db.health

import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PostgresHealthCheckerTest : DatabaseTestsBase() {

    private val healthChecker = PostgresHealthChecker()

    @Test
    fun check() = suspendingDatabaseTest {
        healthChecker.check().also {
            assertThat(it).isTrue
        }
    }
}
