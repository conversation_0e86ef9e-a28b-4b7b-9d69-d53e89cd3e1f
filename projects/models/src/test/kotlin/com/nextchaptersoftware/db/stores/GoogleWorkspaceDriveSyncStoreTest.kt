package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GoogleWorkspaceDriveSyncStoreTest : DatabaseTestsBase() {
    private val store = Stores.googleWorkspaceDriveSyncStore

    @Test
    fun `upsert and setLastSynced`() = suspendingDatabaseTest {
        val installation = makeInstallation()
        val identity = makeIdentity()

        val result = store.upsert(
            installationId = installation.idValue,
            identityId = identity.idValue,
        )

        assertThat(result.lastSynced).isNull()

        val now = Instant.nowWithMicrosecondPrecision()

        store.setLastSynced(
            installationId = installation.idValue,
            identityId = identity.idValue,
            lastSynced = now,
        )

        val updated = store.upsert(
            installationId = installation.idValue,
            identityId = identity.idValue,
        )

        assertThat(updated.id).isEqualTo(result.id)
        assertThat(updated.installationId).isEqualTo(result.installationId)
        assertThat(updated.identityId).isEqualTo(result.identityId)
        assertThat(updated.lastSynced).isEqualTo(now)
    }
}
