package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeOrgBilling
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgBillingEmailDAO
import com.nextchaptersoftware.db.models.OrgBillingEmailModel
import com.nextchaptersoftware.db.models.OrgBillingEmailState
import com.nextchaptersoftware.db.models.OrgBillingEmailType
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class OrgBillingEmailStoreTest : DatabaseTestsBase() {
    private val store = OrgBillingEmailStore()

    @Test
    fun `create, setState, cancelAllPending, and getNextPendingBatch`() = suspendingDatabaseTest {
        val orgBillingA = makeOrgBilling().asDataModel()
        val orgBillingB = makeOrgBilling().asDataModel()

        val now = Instant.nowWithMicrosecondPrecision()

        // Sanity check
        assertThat(store.getNextPendingBatch()).isEmpty()
        assertThat(suspendedTransaction { OrgBillingEmailDAO.all().count() }).isEqualTo(0)

        store.create(
            orgBillingId = orgBillingA.id,
            type = OrgBillingEmailType.TrialPitch,
            send = now.minus(1.hours),
            expire = now.plus(2.hours),
        )
        store.create(
            orgBillingId = orgBillingA.id,
            type = OrgBillingEmailType.TrialSurvey,
            send = now.plus(1.days),
            expire = now.plus(2.days),
        )
        store.create(
            orgBillingId = orgBillingB.id,
            type = OrgBillingEmailType.TrialOneDayRemaining,
            send = now.minus(1.hours),
            expire = now.plus(1.hours),
        )
        store.create(
            orgBillingId = orgBillingB.id,
            type = OrgBillingEmailType.TrialSurvey,
            send = now.plus(1.days),
            expire = now.plus(2.days),
        )
        store.create( // Should not be created because we already have an email of type TrialPitch
            orgBillingId = orgBillingA.id,
            type = OrgBillingEmailType.TrialPitch,
            send = now.plus(1.days),
            expire = now.plus(2.days),
        )

        val pending = store.getNextPendingBatch().also { results ->
            assertThat(results).hasSize(2)
            assertThat(results).allMatch { it.state == OrgBillingEmailState.Pending }
            assertThat(results.count { it.orgBillingId == orgBillingA.id }).isEqualTo(1)
            assertThat(results.count { it.orgBillingId == orgBillingB.id }).isEqualTo(1)
        }
        val orgBillingAPending = pending.first { it.orgBillingId == orgBillingA.id }
        val orgBillingBPending = pending.first { it.orgBillingId == orgBillingB.id }

        // Set the state of the first email to sent
        store.setState(orgBillingEmailId = orgBillingAPending.id, state = OrgBillingEmailState.Sent)
        assertThat(store.getNextPendingBatch()).containsExactly(orgBillingBPending)

        // Cancel all pending emails for orgBillingB
        store.cancelAllPending(orgBillingId = orgBillingB.id)
        assertThat(store.getNextPendingBatch()).isEmpty()

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingA.id }.map { it.asDataModel() }
        }.also { emails ->
            assertThat(emails).hasSize(2)
            assertThat(emails.count { it.state == OrgBillingEmailState.Sent }).isEqualTo(1)
            assertThat(emails.count { it.state == OrgBillingEmailState.Pending }).isEqualTo(1)
        }

        suspendedTransaction {
            OrgBillingEmailDAO.find { OrgBillingEmailModel.orgBilling eq orgBillingB.id }.map { it.asDataModel() }
        }.also { emails ->
            assertThat(emails).hasSize(2)
            assertThat(emails.count { it.state == OrgBillingEmailState.Cancelled }).isEqualTo(2)
        }
    }
}
