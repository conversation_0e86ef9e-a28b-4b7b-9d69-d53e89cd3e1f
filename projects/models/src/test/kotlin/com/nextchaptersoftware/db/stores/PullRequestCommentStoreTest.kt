package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makePullRequest
import com.nextchaptersoftware.db.ModelBuilders.makePullRequestComment
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.PullRequestCommentId
import com.nextchaptersoftware.db.models.PullRequestCommentModel
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlin.math.absoluteValue
import kotlin.random.Random
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.update
import org.junit.jupiter.api.Test

class PullRequestCommentStoreTest : DatabaseTestsBase() {
    private val store = Stores.pullRequestCommentStore

    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repo: RepoDAO
    private lateinit var member: MemberDAO
    private lateinit var pullRequest: PullRequestDAO

    private val pullRequestId: PullRequestId
        get() = pullRequest.id.value

    private suspend fun setup() {
        scmTeam = makeScmTeam()
        repo = makeRepo(scmTeam = scmTeam)
        member = makeMember(scmTeam = scmTeam)
        pullRequest = makePullRequest(repo = repo)
    }

    @Test
    fun find() = suspendingDatabaseTest {
        setup()
        val comment = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()

        suspendedTransaction {
            assertThat(store.find(trx = this, pullRequestId = pullRequest.id.value, id = comment.id)).isNotNull
            assertThat(store.find(trx = this, pullRequestId = PullRequestId.random(), id = comment.id)).isNull()
            assertThat(store.find(trx = this, pullRequestId = pullRequest.id.value, id = PullRequestCommentId.random())).isNull()
        }
    }

    @Test
    fun list() = suspendingDatabaseTest {
        setup()
        val commentA = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        val commentB = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        makePullRequestComment(pullRequest = makePullRequest(repo = repo), author = member) // Make a comment for another PR

        val results = suspendedTransaction {
            store.list(trx = this, pullRequestId = pullRequestId)
        }
        assertThat(results.map { it.id }).containsExactly(commentA.id, commentB.id)
        assertThat(results).allMatch { it.pullRequestId == pullRequestId }
    }

    @Test
    fun `list with modifiedSince and limit`() = suspendingDatabaseTest {
        setup()

        suspend fun list(modifiedSince: Instant) = suspendedTransaction {
            store.list(trx = this, pullRequestId = pullRequestId, modifiedSince = modifiedSince, limit = 1)
        }

        val nowA = Instant.nowWithMicrosecondPrecision()
        assertThat(list(nowA)).isEmpty()

        val commentA = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        assertThat(list(nowA).map { it.id }).containsExactly(commentA.id)

        val nowB = Instant.nowWithMicrosecondPrecision()
        assertThat(list(nowB)).isEmpty()

        val commentB = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        assertThat(list(nowB).map { it.id }).containsExactly(commentB.id)

        val nowC = Instant.nowWithMicrosecondPrecision()
        assertThat(list(nowC)).isEmpty()
        makePullRequestComment(pullRequest = makePullRequest(repo = repo), author = member) // Make a comment for another PR
        assertThat(list(nowC)).isEmpty()
    }

    @Test
    fun findByCommentId() = suspendingDatabaseTest {
        setup()
        val prCommentId = Random.nextInt().absoluteValue.toString()
        val comment = makePullRequestComment(pullRequest = pullRequest, author = member, prCommentId = prCommentId).asDataModel()
        makePullRequestComment(pullRequest = makePullRequest(repo = repo), author = member) // Make a comment for another PR

        val result = suspendedTransaction {
            store.findByCommentId(
                trx = this,
                pullRequestId = pullRequestId,
                prCommentId = prCommentId,
            )
        }

        assertThat(result).isNotNull
        result?.let { assertThat(result.id.value).isEqualTo(comment.id) }
    }

    @Test
    fun deleteByCommentId() = suspendingDatabaseTest {
        setup()
        val prCommentId = Random.nextInt().absoluteValue.toString()
        val commentA = makePullRequestComment(pullRequest = pullRequest, author = member, prCommentId = prCommentId).asDataModel()
        val commentB = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()

        val before = suspendedTransaction {
            store.list(trx = this, pullRequestId = pullRequestId)
        }

        assertThat(before.map { it.id }).containsExactly(commentA.id, commentB.id)

        suspendedTransaction {
            store.deleteByCommentId(
                trx = this,
                pullRequestId = pullRequestId,
                prCommentId = prCommentId,
            )
        }

        val after = suspendedTransaction {
            store.list(trx = this, pullRequestId = pullRequestId)
        }

        assertThat(after.filterNot { it.isDeleted }.map { it.id }).containsExactly(commentB.id)
    }

    @Test
    fun countByPullRequestIds() = suspendingDatabaseTest {
        setup()

        val otherPullRequest = makePullRequest(repo = repo)
        val otherPullRequestId = otherPullRequest.id.value

        suspend fun getCounts() = store.countByPullRequestIds(
            pullRequestIds = listOf(pullRequestId, otherPullRequestId),
        )

        assertThat(getCounts()).isEmpty()

        suspendedTransaction {
            makePullRequestComment(trx = this, pullRequest = pullRequest, author = member)
            makePullRequestComment(trx = this, pullRequest = pullRequest, author = member).also { it.isDeleted = true }
            makePullRequestComment(trx = this, pullRequest = pullRequest, author = member)
        }

        getCounts().let {
            assertThat(it[pullRequestId]).isEqualTo(2)
            assertThat(it[otherPullRequestId]).isNull()
        }

        suspendedTransaction {
            makePullRequestComment(trx = this, pullRequest = otherPullRequest, author = member)
        }

        getCounts().let {
            assertThat(it[pullRequestId]).isEqualTo(2)
            assertThat(it[otherPullRequestId]).isEqualTo(1)
        }

        suspendedTransaction {
            PullRequestCommentModel.update({ PullRequestCommentModel.pullRequest eq pullRequestId }) {
                it[this.isDeleted] = true
            }
        }

        getCounts().let {
            assertThat(it[pullRequestId]).isNull()
            assertThat(it[otherPullRequestId]).isEqualTo(1)
        }
    }

    @Test
    fun create() = suspendingDatabaseTest {
        setup()

        val commentId = UUID.randomUUID()
        val author = makeMember(scmTeam = scmTeam)
        val authorOrgMember = suspendedTransaction { author.orgMember }
        val createdAt = Instant.nowWithMicrosecondPrecision()

        val comment = suspendedTransaction {
            store.create(
                trx = this,
                pullRequestId = pullRequest.id.value,
                authorId = author.idValue,
                authorOrgMemberId = authorOrgMember.idValue,
                content = "Hi there".asMessageBody(),
                createdAt = createdAt,
                prCommentId = commentId.toString(),
                prCommentUrl = "https://example.com".asUrl,
                prCommentBodyHash = Hash("1234567890".toByteArray()),
            )
        }

        assertThat(comment).isNotNull
        assertThat(comment.pullRequestId).isEqualTo(pullRequest.id.value)
        assertThat(comment.authorId).isEqualTo(author.idValue)
        assertThat(comment.authorOrgMemberId).isEqualTo(authorOrgMember.id.value)
        assertThat(comment.createdAt).isEqualTo(createdAt)
        assertThat(comment.content).isEqualTo("Hi there".asMessageBody().toByteArray())
        assertThat(comment.contentVersion).isEqualTo("1")
        assertThat(comment.prCommentId).isEqualTo(commentId.toString())
        assertThat(comment.prCommentUrl).isEqualTo("https://example.com".asUrl)
        assertThat(comment.prCommentBodyHash).isEqualTo(Hash("1234567890".toByteArray()))
    }

    @Test
    fun delete() = suspendingDatabaseTest {
        setup()

        val comment = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        assertThat(comment.content).isNotEqualTo(ByteArray(0))
        assertThat(comment.isDeleted).isFalse

        suspendedTransaction {
            store.delete(trx = this, pullRequestId = pullRequest.id.value, id = comment.id)

            val commentAfterDelete = store.find(trx = this, pullRequestId = pullRequest.id.value, id = comment.id)
            assertThat(commentAfterDelete?.content).isEqualTo(ByteArray(0))
            assertThat(commentAfterDelete?.isDeleted).isTrue
        }
    }

    @Test
    fun update() = suspendingDatabaseTest {
        setup()

        val content = UUID.randomUUID().toString().toByteArray()
        val comment = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        assertThat(comment.content).isNotEqualTo(content)
        assertThat(comment.editedAt).isNull()

        suspendedTransaction {
            val result = store.update(trx = this, pullRequestId = pullRequest.id.value, id = comment.id, content = content, contentVersion = "1")
            assertThat(result.content).isEqualTo(content)
            assertThat(result.editedAt).isNotNull
        }
    }

    @Test
    fun findByPullRequest() = suspendingDatabaseTest {
        setup()

        val comment = makePullRequestComment(pullRequest = pullRequest, author = member).asDataModel()
        makePullRequestComment(pullRequest = makePullRequest(repo = repo), author = member) // comment on another pull request

        val results = store.findByPullRequest(pullRequestId = pullRequest.id.value)
        assertThat(results.map { it.id }).containsExactly(comment.id)
    }
}
