package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeSourcePoint
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.ModelBuilders.makeThreadParticipant
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ThreadBundleStoreByIdsTest : DatabaseTestsBase() {
    private val store = Stores.threadBundleStore

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repo: RepoDAO
    private lateinit var meOrgMember: OrgMemberDAO
    private lateinit var me: MemberDAO
    private lateinit var notMeOrgMember: OrgMemberDAO
    private lateinit var notMe: MemberDAO
    private lateinit var now: Instant

    suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        repo = makeRepo(scmTeam = scmTeam)
        meOrgMember = makeOrgMember(org = org)
        me = makeMember(scmTeam = scmTeam, orgMember = meOrgMember)
        notMeOrgMember = makeOrgMember(org = org)
        notMe = makeMember(scmTeam = scmTeam, orgMember = notMeOrgMember)
        now = Instant.nowWithMicrosecondPrecision()
    }

    @Test
    fun `getThreadBundlesByIds for many threads`() = suspendingDatabaseTest {
        setup()

        val threadA = suspendedTransaction {
            makeThread(trx = this, org = org, repo = repo).also { thread ->
                makeThreadParticipant(trx = this, thread = thread, member = me)
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repo, thread = thread).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
            }
        }

        val threadB = suspendedTransaction {
            makeThread(trx = this, org = org, repo = repo).also { thread ->
                makeThreadParticipant(trx = this, thread = thread, member = me)
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repo, thread = thread).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
            }
        }

        // Private NotMe. Hide
        val threadC = suspendedTransaction {
            makeThread(trx = this, org = org, repo = repo, isPrivate = true).also { thread ->
                makeThreadParticipant(trx = this, thread = thread, member = notMe)
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repo, thread = thread).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
            }
        }

        // Private Me. Show
        val threadD = suspendedTransaction {
            makeThread(trx = this, org = org, repo = repo, isPrivate = true).also { thread ->
                makeThreadParticipant(trx = this, thread = thread, member = me)
                makeSourceMark(trx = this, scmTeam = scmTeam, repo = repo, thread = thread).also { mark ->
                    makeSourcePoint(trx = this, scmTeam = scmTeam, sourceMark = mark, isOriginal = true)
                }
            }
        }

        store.getThreadBundlesByIds(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            threadIds = listOf(threadA.idValue, threadB.idValue, threadC.idValue, threadD.idValue, ThreadId.random()),
            associatedSlackMemberIds = null,
        ).let { threadInfos ->
            assertThat(threadInfos).hasSize(3)
            assertThat(threadInfos.map { it.thread.id }).containsExactlyInAnyOrder(threadA.idValue, threadB.idValue, threadD.idValue)
        }

        store.getThreadBundlesByIds(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            threadIds = listOf(ThreadId.random()),
            associatedSlackMemberIds = null,
        ).let { threadInfos ->
            assertThat(threadInfos).isEmpty()
        }

        store.getThreadBundlesByIds(
            orgId = org.idValue,
            orgMemberId = meOrgMember.idValue,
            threadIds = emptyList(),
            associatedSlackMemberIds = null,
        ).let { threadInfos ->
            assertThat(threadInfos).isEmpty()
        }
    }
}
