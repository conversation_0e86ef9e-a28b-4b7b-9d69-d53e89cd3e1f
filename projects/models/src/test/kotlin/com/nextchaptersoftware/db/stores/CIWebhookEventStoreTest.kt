package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeCIWebhookEvent
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CIWebhookEventStoreTest : DatabaseTestsBase() {

    private val store = CIWebhookEventStore()

    @Test
    fun `test upsert inserts exactly one new record`() = suspendingDatabaseTest {
        val installation = makeInstallation()
        val label = UUID.randomUUID().toString()

        store.upsertWebhookEvent(ciInstallationId = installation.idValue, label = label)
        store.upsertWebhookEvent(ciInstallationId = installation.idValue, label = label)

        store.listWebhookEvents(ciInstallationId = installation.idValue)
            .also { assertThat(it).hasSize(1) }
            .first().also { assertThat(it.label).isEqualTo(label) }
    }

    @Test
    fun `test update updates modified timestamp`() = suspendingDatabaseTest {
        val installation = makeInstallation()
        val label = UUID.randomUUID().toString()
        val longTimeAgo = Instant.nowWithMicrosecondPrecision().minus(1.days)

        makeCIWebhookEvent(ciInstallation = installation, label = label, createdAt = longTimeAgo, modifiedAt = longTimeAgo)

        store.listWebhookEvents(ciInstallationId = installation.idValue)
            .also { assertThat(it).hasSize(1) }
            .first().also {
                assertThat(it.createdAt).isEqualTo(longTimeAgo)
                assertThat(it.modifiedAt).isEqualTo(longTimeAgo)
            }

        store.upsertWebhookEvent(ciInstallationId = installation.idValue, label = label)

        store.listWebhookEvents(ciInstallationId = installation.idValue)
            .also { assertThat(it).hasSize(1) }
            .first().also {
                assertThat(it.createdAt).isEqualTo(longTimeAgo)
                assertThat(it.modifiedAt).isGreaterThan(longTimeAgo)
            }
    }
}
