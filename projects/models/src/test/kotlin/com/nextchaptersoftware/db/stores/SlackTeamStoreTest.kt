package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class SlackTeamStoreTest : DatabaseTestsBase() {
    private val store = Stores.slackTeamStore

    private lateinit var org: OrgDAO
    private lateinit var installation: InstallationDAO
    private lateinit var slackTeam1: SlackTeamDAO
    private lateinit var slackTeam2: SlackTeamDAO

    private suspend fun setup() {
        org = makeOrg()
        installation = makeInstallation(org = org)
        slackTeam1 = makeSlackTeam(org = org, installation = installation, slackExternalTeamId = "slackTeam1")
        slackTeam2 = makeSlackTeam(org = org, installation = installation, slackExternalTeamId = "slackTeam2")
    }

    @Test
    fun getInstalledSlackTeams() = suspendingDatabaseTest {
        setup()

        val slackTeams = store.getInstalledSlackTeams()
        assertThat(slackTeams).hasSize(2)
    }

    @Test
    fun upsertSlackTeam() = suspendingDatabaseTest {
        setup()

        val upsertedSlackTeam = suspendedTransaction {
            store.upsert(
                trx = this,
                slackExternalTeamId = "richie",
                installationId = installation.idValue,
                name = "richie",
                domain = "richie",
                htmlUrl = "http://richie.com".asUrl,
                avatarUrl = "http://richie.com".asUrl,
                botAccessToken = Ciphertext("richieBotToken".toByteArray()),
                botUserId = "richieBotId",
                botScope = "richieBotScope",
                userAccessToken = Ciphertext("richieUserToken".toByteArray()),
                userScope = "richieUserScope",
                userId = "richieUserId",
            ).asDataModel()
        }

        val slackTeam = checkNotNull(store.findById(slackTeamId = upsertedSlackTeam.id))

        // AssertJ validations
        assertThat(slackTeam).isNotNull
        assertThat(slackTeam.slackExternalTeamId).isEqualTo("richie")
        assertThat(slackTeam.installationId).isEqualTo(installation.idValue)
        assertThat(slackTeam.name).isEqualTo("richie")
        assertThat(slackTeam.domain).isEqualTo("richie")
        assertThat(slackTeam.htmlUrl.asString).isEqualTo("http://richie.com")
        assertThat(slackTeam.avatarUrl?.asString).isEqualTo("http://richie.com")
        assertThat(slackTeam.botAccessToken.value).isEqualTo("richieBotToken".toByteArray())
        assertThat(slackTeam.botUserId).isEqualTo("richieBotId")
        assertThat(slackTeam.botScope).isEqualTo("richieBotScope")
        assertThat(slackTeam.userAccessToken?.value).isEqualTo("richieUserToken".toByteArray())
        assertThat(slackTeam.userId).isEqualTo("richieUserId")
        assertThat(slackTeam.userScope).isEqualTo("richieUserScope")
    }

    @Test
    fun `update slack team with new information`() = suspendingDatabaseTest {
        setup()

        val upsertedSlackTeam = suspendedTransaction {
            store.upsert(
                trx = this,
                slackExternalTeamId = "richie",
                installationId = installation.idValue,
                name = "richie",
                domain = "richie",
                htmlUrl = "http://richie.com".asUrl,
                avatarUrl = "http://richie.com".asUrl,
                botAccessToken = Ciphertext("richieBotToken".toByteArray()),
                botUserId = "richieBotId",
                botScope = "richieBotScope",
                userAccessToken = Ciphertext("richieUserToken".toByteArray()),
                userScope = "richieUserScope",
                userId = "richieUserId",
            ).asDataModel()
        }

        suspendedTransaction {
            store.update(
                trx = this,
                slackExternalTeamId = "richie",
                installationId = installation.idValue,
                name = "richieUpdated",
                domain = "richieUpdated",
                htmlUrl = "http://richieUpdated.com".asUrl,
            )
        }

        val slackTeam = checkNotNull(store.findById(slackTeamId = upsertedSlackTeam.id))

        // AssertJ validations
        assertThat(slackTeam).isNotNull
        assertThat(slackTeam.slackExternalTeamId).isEqualTo("richie")
        assertThat(slackTeam.installationId).isEqualTo(installation.idValue)
        assertThat(slackTeam.name).isEqualTo("richieUpdated")
        assertThat(slackTeam.domain).isEqualTo("richieUpdated")
        assertThat(slackTeam.htmlUrl.asString).isEqualTo("http://richieUpdated.com")
        assertThat(slackTeam.avatarUrl?.asString).isEqualTo("http://richie.com")
    }

    @Test
    fun findBySlackExternalTeamId() = suspendingDatabaseTest {
        setup()

        val resultA = suspendedTransaction {
            store.findBySlackExternalTeamId(trx = this, slackExternalTeamId = slackTeam1.slackExternalTeamId)
        }

        assertThat(resultA).isNotNull
        assertThat(resultA?.slackTeam?.id?.value).isEqualTo(slackTeam1.id.value)
        assertThat(resultA?.org?.id?.value).isEqualTo(org.id.value)
        assertThat(resultA?.installation?.id?.value).isEqualTo(installation.id.value)
    }

    @Test
    fun deleteBySlackTeamId() = suspendingDatabaseTest {
        setup()

        val resultA = suspendedTransaction {
            store.findBySlackExternalTeamId(trx = this, slackExternalTeamId = slackTeam1.slackExternalTeamId)
        }

        assertThat(resultA).isNotNull

        suspendedTransaction {
            store.deleteBySlackTeamId(trx = this, orgId = org.idValue, slackExternalTeamId = slackTeam1.slackExternalTeamId)
        }

        val resultB = suspendedTransaction {
            store.findBySlackExternalTeamId(trx = this, slackExternalTeamId = slackTeam1.slackExternalTeamId)
        }

        assertThat(resultB).isNull()

        val resultC = suspendedTransaction {
            store.findBySlackExternalTeamId(trx = this, slackExternalTeamId = slackTeam2.slackExternalTeamId)
        }

        assertThat(resultC).isNotNull
    }

    @Test
    fun `getOrgId and getInstallationId`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation = makeInstallation(
            org = org,
        )
        val slackTeam = makeSlackTeam(
            org = org,
            installation = installation,
        )

        assertThat(
            store.getOrgId(slackExternalTeamId = slackTeam.slackExternalTeamId),
        ).isEqualTo(
            org.idValue,
        )

        assertThat(
            store.getInstallationId(slackTeamId = slackTeam.idValue),
        ).isEqualTo(
            installation.idValue,
        )
    }

    @Nested
    inner class FindByIdAndOrgTest {
        @Test
        fun `findByIdAndOrg -- when orgId is unkwnon`() = suspendingDatabaseTest {
            val installation = makeInstallation()
            val slackTeam = makeSlackTeam(installation = installation)

            assertThat(
                store.findByIdAndOrg(
                    orgId = OrgId.random(),
                    slackTeamId = slackTeam.idValue,
                ),
            ).isNull()
        }

        @Test
        fun `findByIdAndOrg -- when orgId is incorrect`() = suspendingDatabaseTest {
            val org = makeOrg()
            val slackTeam = makeSlackTeam()

            assertThat(
                store.findByIdAndOrg(
                    orgId = org.idValue,
                    slackTeamId = slackTeam.idValue,
                ),
            ).isNull()
        }

        @Test
        fun `findByIdAndOrg -- happy path`() = suspendingDatabaseTest {
            val installation = makeInstallation()
            val slackTeam = makeSlackTeam(installation = installation)
            assertThat(
                store.findByIdAndOrg(
                    orgId = installation.org.idValue,
                    slackTeamId = slackTeam.idValue,
                ),
            )
            .isEqualTo(
                slackTeam.asDataModel(),
            )
        }
    }
}
