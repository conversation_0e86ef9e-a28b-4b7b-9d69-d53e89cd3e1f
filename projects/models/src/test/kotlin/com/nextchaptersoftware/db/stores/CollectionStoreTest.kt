package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeCollection
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.cursors.CollectionCursor
import com.nextchaptersoftware.db.cursors.OpaqueCursor
import com.nextchaptersoftware.db.models.CollectionDAO
import com.nextchaptersoftware.db.models.CollectionId
import com.nextchaptersoftware.db.models.CollectionModel
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import io.ktor.http.Url
import kotlin.time.Duration.Companion.milliseconds
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CollectionStoreTest : DatabaseTestsBase() {
    private val store = Stores.collectionStore

    private lateinit var orgA: OrgDAO
    private lateinit var orgB: OrgDAO
    private lateinit var installationA: InstallationDAO
    private lateinit var installationB: InstallationDAO

    private suspend fun setup() {
        orgA = makeOrg()
        orgB = makeOrg()
        installationA = makeInstallation(provider = Provider.CustomIntegration, org = orgA)
        installationB = makeInstallation(provider = Provider.CustomIntegration, org = orgB)
    }

    @Test
    fun list() = suspendingDatabaseTest {
        setup()

        assertThat(store.list(orgId = orgA.idValue, limit = 1, after = null, before = null)).isEmpty()
        assertThat(store.list(orgId = orgB.idValue, limit = 1, after = null, before = null)).isEmpty()

        val collectionA = makeCollection(installation = installationA).asDataModel()

        assertThat(store.list(orgId = orgA.idValue, limit = 1, after = null, before = null)).containsExactly(collectionA)
        assertThat(store.list(orgId = orgB.idValue, limit = 1, after = null, before = null)).isEmpty()

        val collectionB = makeCollection(installation = installationA).asDataModel()
        val collectionC = makeCollection(installation = installationA).asDataModel()
        val collectionD = makeCollection(installation = installationA).asDataModel()

        assertThat(
            store.list(
                orgId = orgA.idValue,
                limit = 10,
                after = null,
                before = null,
            ),
        ).containsExactly(collectionD, collectionC, collectionB, collectionA)

        assertThat(
            store.list(
                orgId = orgA.idValue,
                limit = 10,
                after = OpaqueCursor.toCursor(CollectionCursor(collectionD.createdAt)),
                before = null,
            ),
        ).containsExactly(collectionD, collectionC, collectionB, collectionA) // after is inclusive

        assertThat(
            store.list(
                orgId = orgA.idValue,
                limit = 10,
                after = OpaqueCursor.toCursor(CollectionCursor(collectionD.createdAt.minus(1.milliseconds))),
                before = null,
            ),
        ).containsExactly(collectionC, collectionB, collectionA)

        assertThat(
            store.list(
                orgId = orgA.idValue,
                limit = 10,
                after = null,
                before = OpaqueCursor.toCursor(CollectionCursor(collectionB.createdAt)),
            ),
        ).containsExactly(collectionD, collectionC) // before is exclusive

        assertThat(
            store.list(
                orgId = orgA.idValue,
                limit = 10,
                after = null,
                before = OpaqueCursor.toCursor(CollectionCursor(collectionB.createdAt.minus(1.milliseconds))),
            ),
        ).containsExactly(collectionD, collectionC, collectionB) // before is exclusive
    }

    @Test
    fun `create and count`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value })
                .isEmpty()
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value })
                .isEmpty()
        }

        val collectionA = store.create(
            trx = null,
            id = CollectionId.random(),
            installationId = installationA.idValue,
            name = "Collection",
            description = "Description",
            iconUrl = "asdf.com",
        )

        suspendedTransaction {
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.map { it.asDataModel() })
                .containsExactly(collectionA)
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value })
                .isEmpty()
        }

        // Should fail to create another collection in a team with the same name as another collection
        assertThrows<Exception> {
            store.create(
                trx = null,
                id = CollectionId.random(),
                installationId = installationA.idValue,
                name = "Collection",
                description = "Description",
                iconUrl = "asdf.com",
            )
        }
        val collectionB = store.create(
            trx = null,
            id = CollectionId.random(),
            installationId = installationB.idValue,
            name = "Collection",
            description = "Description",
            iconUrl = "asdf.com",
        )

        suspendedTransaction {
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.map { it.asDataModel() })
                .containsExactly(collectionA)
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }.map { it.asDataModel() })
                .containsExactly(collectionB)
        }

        assertThat(store.count(orgA.idValue)).isEqualTo(1)
        assertThat(store.count(orgB.idValue)).isEqualTo(1)
    }

    @Test
    fun find() = suspendingDatabaseTest {
        setup()

        val collectionId = CollectionId.random()

        assertThat(store.find(orgId = orgA.idValue, id = collectionId)).isNull()
        assertThat(store.find(orgId = orgB.idValue, id = collectionId)).isNull()
        assertThat(store.find(orgId = orgA.idValue, installationId = installationA.idValue)).isNull()

        val collection = makeCollection(id = collectionId, installation = installationA).asDataModel()

        assertThat(store.find(orgId = orgA.idValue, id = collectionId)).isEqualTo(collection)
        assertThat(store.find(orgId = orgB.idValue, id = collectionId)).isNull()
        assertThat(store.find(orgId = orgA.idValue, installationId = installationA.idValue)).isEqualTo(collection)
        assertThat(store.find(orgId = orgB.idValue, installationId = installationA.idValue)).isNull()
    }

    @Test
    fun update() = suspendingDatabaseTest {
        setup()

        val collection = makeCollection(installation = installationA).asDataModel()

        suspendedTransaction {
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.single().asDataModel()).isEqualTo(collection)
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }).isEmpty()
        }

        store.update(orgId = orgB.idValue, id = collection.id, name = "a", description = "b", iconUrl = "www.google.com")

        suspendedTransaction { // Assert not updated
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.single().asDataModel()).isEqualTo(collection)
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }).isEmpty()
        }

        store.update(orgId = orgA.idValue, id = collection.id, name = "a", description = "b", iconUrl = "www.google.com")

        suspendedTransaction { // Assert not updated
            val updated = CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.single().asDataModel()
            assertThat(updated.id).isEqualTo(collection.id)
            assertThat(updated.installationId).isEqualTo(installationA.idValue)
            assertThat(updated.name).isEqualTo("a")
            assertThat(updated.description).isEqualTo("b")
            assertThat(updated.iconUrl).isEqualTo(Url("www.google.com"))
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }).isEmpty()
        }
    }

    @Test
    fun `find and delete`() = suspendingDatabaseTest {
        setup()

        val collectionA = makeCollection(installation = installationA).asDataModel()
        val collectionB = makeCollection(installation = installationB).asDataModel()

        assertThat(store.find(id = collectionA.id)).isEqualTo(collectionA)
        assertThat(store.find(id = collectionB.id)).isEqualTo(collectionB)

        suspendedTransaction {
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.map { it.asDataModel() })
                .containsExactly(collectionA)
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }.map { it.asDataModel() })
                .containsExactly(collectionB)
        }

        store.delete(orgId = orgB.idValue, id = collectionA.id)
        store.delete(orgId = orgA.idValue, id = collectionB.id)

        assertThat(store.find(id = collectionA.id)).isEqualTo(collectionA)
        assertThat(store.find(id = collectionB.id)).isEqualTo(collectionB)

        suspendedTransaction { // Assert no changes
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value }.map { it.asDataModel() })
                .containsExactly(collectionA)
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }.map { it.asDataModel() })
                .containsExactly(collectionB)
        }

        store.delete(orgId = orgA.idValue, id = collectionA.id)

        assertThat(store.find(id = collectionA.id)).isNull()
        assertThat(store.find(id = collectionB.id)).isEqualTo(collectionB)

        suspendedTransaction {
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationA.id.value })
                .isEmpty()
            assertThat(CollectionDAO.find { CollectionModel.installation eq installationB.id.value }.map { it.asDataModel() })
                .containsExactly(collectionB)
        }
    }
}
