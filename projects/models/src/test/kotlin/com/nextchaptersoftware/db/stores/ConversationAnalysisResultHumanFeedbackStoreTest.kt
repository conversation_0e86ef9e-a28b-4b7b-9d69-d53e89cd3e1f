package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeMessageFeedback
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.models.ConversationAnalysis
import com.nextchaptersoftware.db.models.ConversationAnalysisId
import com.nextchaptersoftware.db.models.ConversationAnalysisResultHumanFeedbackId
import com.nextchaptersoftware.db.models.ExecutionState
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.MessageFeedbackDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ConversationAnalysisResultHumanFeedbackStoreTest : DatabaseTestsBase() {
    private val store = Stores.conversationAnalysisResultHumanFeedbackStore
    private lateinit var org: OrgDAO
    private lateinit var thread: ThreadDAO
    private lateinit var feedback: MessageFeedbackDAO
    private lateinit var analysis: ConversationAnalysis

    private val feedbackType = FeedbackType.Negative

    private suspend fun setup() {
        org = makeOrg()
        thread = makeThread(org = org)
        feedback = createFeedback(thread)
        analysis = Stores.conversationAnalysisStore.idempotentCreateAnalysis(
            analysisId = ConversationAnalysisId.random(),
            totalConversationsCount = 1,
        )
    }

    private suspend fun createFeedback(thread: ThreadDAO): MessageFeedbackDAO {
        val message = makeMessage(thread = thread)
        val person = makePerson()
        val orgMember = makeOrgMember(org = org, person = person)

        return makeMessageFeedback(
            thread = thread,
            message = message,
            orgMember = orgMember,
            feedbackDescription = "Test feedback",
        )
    }

    @Test
    fun `idempotentCreate - creates new result with correct initial state`() = suspendingDatabaseTest {
        // Given: Basic test entities and result ID
        setup()
        val resultId = ConversationAnalysisResultHumanFeedbackId.random()

        // When: Creating new analysis result
        val result = store.idempotentCreate(
            resultId = resultId,
            analysisId = analysis.id,
            feedbackId = feedback.id.value,
            threadId = thread.id.value,
            feedbackTypeValue = feedbackType,
        )

        // Then: Result should be created with expected values and Pending state
        assertThat(result.id).isEqualTo(resultId)
        assertThat(result.analysisId).isEqualTo(analysis.id)
        assertThat(result.feedbackId).isEqualTo(feedback.id.value)
        assertThat(result.threadId).isEqualTo(thread.id.value)
        assertThat(result.executionState).isEqualTo(ExecutionState.Pending)
        assertThat(result.feedbackType).isEqualTo(feedbackType)
    }

    @Test
    fun `idempotentCreate - returns existing result on duplicate creation`() = suspendingDatabaseTest {
        // Given: Existing analysis result
        setup()
        val resultId = ConversationAnalysisResultHumanFeedbackId.random()
        val original = store.idempotentCreate(
            resultId = resultId,
            analysisId = analysis.id,
            feedbackId = feedback.id.value,
            threadId = thread.id.value,
            feedbackTypeValue = feedbackType,
        )

        // When: Attempting to create duplicate
        val duplicate = store.idempotentCreate(
            resultId = resultId,
            analysisId = analysis.id,
            feedbackId = feedback.id.value,
            threadId = thread.id.value,
            feedbackTypeValue = feedbackType,
        )

        // Then: Should return same result without creating new record
        assertThat(duplicate.modifiedAt).isGreaterThan(original.modifiedAt)
        val duplicateWithTheSameModifiedAt = duplicate.copy(modifiedAt = original.modifiedAt)
        assertThat(duplicateWithTheSameModifiedAt).isEqualTo(original)
        assertThat(store.find(analysisId = analysis.id)).hasSize(1)
    }

    @Test
    fun `find - returns all results for analysis with correct limit`() = suspendingDatabaseTest {
        // Given: Multiple analysis results for same analysis
        setup()
        val resultCount = 3
        val results = (1..resultCount).map {
            val fb = createFeedback(thread)
            store.idempotentCreate(
                resultId = ConversationAnalysisResultHumanFeedbackId.random(),
                analysisId = analysis.id,
                feedbackId = fb.id.value,
                threadId = thread.id.value,
                feedbackTypeValue = feedbackType,
            )
        }

        // When: Finding results with different limits
        val allResults = store.find(analysisId = analysis.id, limit = resultCount)
        val limitedResults = store.find(analysisId = analysis.id, limit = 2)

        // Then: Should respect limits and return correct results
        assertThat(allResults).hasSize(resultCount)
        assertThat(limitedResults).hasSize(2)
        assertThat(allResults.map { it.id }).containsAll(results.map { it.id })
    }

    @Test
    fun `update - updates the result correctly`() = suspendingDatabaseTest {
        // Given: Existing analysis result in Pending state
        setup()
        val result = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            feedbackId = feedback.id.value,
            threadId = thread.id.value,
            feedbackTypeValue = feedbackType,
        )
        assertThat(result.executionState).isEqualTo(ExecutionState.Pending)

        // When: Updating state to Completed
        store.update(
            resultId = result.id,
            executionState = ExecutionState.Completed,
            humanFeedbackLocationTurn = 1,
            averageTimeToAnswer = 1,
            numberOfTurns = 1,
            conversationDuration = 1,
            conversationSummary = "Test summary",
            userFeedbackSummary = "User Test summary",
            querySummary = "Test query summary",
            language = "en",
            conversationSummaryEmbedding = byteArrayOf(1, 2, 3),
            userFeedbackSummaryEmbedding = byteArrayOf(4, 5, 6),
            querySummaryEmbedding = byteArrayOf(7, 8, 9),
            clusterMembership = listOf("cluster1", "cluster2"),
        )

        // Then: State should be updated
        val updated = store.findById(id = result.id)
        assertThat(updated?.executionState).isEqualTo(ExecutionState.Completed)
        assertThat(updated?.humanFeedbackLocationTurn).isEqualTo(1)
        assertThat(updated?.averageTimeToAnswer).isEqualTo(1)
        assertThat(updated?.numberOfTurns).isEqualTo(1)
        assertThat(updated?.conversationDuration).isEqualTo(1)
        assertThat(updated?.conversationSummary).isEqualTo("Test summary")
        assertThat(updated?.userFeedbackSummary).isEqualTo("User Test summary")
        assertThat(updated?.querySummary).isEqualTo("Test query summary")
        assertThat(updated?.language).isEqualTo("en")
        assertThat(updated?.conversationSummaryEmbedding).isEqualTo(byteArrayOf(1, 2, 3))
        assertThat(updated?.userFeedbackSummaryEmbedding).isEqualTo(byteArrayOf(4, 5, 6))
        assertThat(updated?.querySummaryEmbedding).isEqualTo(byteArrayOf(7, 8, 9))
        assertThat(updated?.clusterMembership).isEqualTo(listOf("cluster1", "cluster2"))
        assertThat(updated?.feedbackType).isEqualTo(feedbackType)
    }

    @Test
    fun `update - only updates specified fields`() = suspendingDatabaseTest {
        // Given: Existing analysis result with some fields
        setup()
        val result = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            feedbackId = feedback.id.value,
            threadId = thread.id.value,
            feedbackTypeValue = feedbackType,
        )

        store.update(
            resultId = result.id,
            executionState = ExecutionState.Completed,
            humanFeedbackLocationTurn = 1,
            averageTimeToAnswer = 1,
            numberOfTurns = 1,
            conversationDuration = 1,
            conversationSummary = "Test summary",
            userFeedbackSummary = "User Test summary",
            language = "en",
            conversationSummaryEmbedding = byteArrayOf(1, 2, 3),
            userFeedbackSummaryEmbedding = byteArrayOf(4, 5, 6),
        )

        // When: Updating clusterMembership only
        store.update(
            resultId = result.id,
            clusterMembership = listOf("cluster1", "cluster2"),
        )

        // Then: Only clusterMembership should be updated
        val updated = store.findById(id = result.id)
        assertThat(updated?.executionState).isEqualTo(ExecutionState.Completed)
        assertThat(updated?.humanFeedbackLocationTurn).isEqualTo(1)
        assertThat(updated?.averageTimeToAnswer).isEqualTo(1)
        assertThat(updated?.numberOfTurns).isEqualTo(1)
        assertThat(updated?.conversationDuration).isEqualTo(1)
        assertThat(updated?.conversationSummary).isEqualTo("Test summary")
        assertThat(updated?.userFeedbackSummary).isEqualTo("User Test summary")
        assertThat(updated?.language).isEqualTo("en")
        assertThat(updated?.conversationSummaryEmbedding).isEqualTo(byteArrayOf(1, 2, 3))
        assertThat(updated?.userFeedbackSummaryEmbedding).isEqualTo(byteArrayOf(4, 5, 6))
        assertThat(updated?.clusterMembership).isEqualTo(listOf("cluster1", "cluster2"))
    }

    @Test
    fun `find - returns empty list for non-existent analysis`() = suspendingDatabaseTest {
        // Given: No results exist for analysis
        setup()

        // When: Finding results for random analysis ID
        val results = store.find(analysisId = ConversationAnalysisId(UUID.randomUUID()))

        // Then: Should return empty list
        assertThat(results).isEmpty()
    }

    @Test
    fun `find by analysis id returns all results`() = suspendingDatabaseTest {
        setup()

        // Given: Multiple analysis results for same analysis
        val analysis2 = Stores.conversationAnalysisStore.idempotentCreateAnalysis(
            analysisId = ConversationAnalysisId.random(),
            totalConversationsCount = 1,
        )
        val feedback2 = createFeedback(thread)

        val result1 = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            threadId = thread.idValue,
            feedbackId = feedback.idValue,
            feedbackTypeValue = feedbackType,
        )
        val result2 = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            threadId = thread.idValue,
            feedbackId = feedback2.idValue,
            feedbackTypeValue = feedbackType,
        )

        // Create result for second analysis
        store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis2.id,
            threadId = thread.idValue,
            feedbackId = feedback.idValue,
            feedbackTypeValue = feedbackType,
        )

        // When
        val results = store.findByAnalysisIdAndIncludeFeedback(analysisId = analysis.id)

        // Then Should return correct results
        assertThat(results).hasSize(2)
        assertThat(results.map { it.result.id })
            .containsExactlyInAnyOrder(result1.id, result2.id)
        assertThat(results.map { it.result.analysisId })
            .containsOnly(analysis.id)

        // And correct feedback
        assertThat(results.map { it.messageFeedback.id })
            .containsExactlyInAnyOrder(feedback.id.value, feedback2.id.value)
        assertThat(results.map { it.orgId }).containsExactlyInAnyOrder(org.idValue, org.idValue)
    }

    @Test
    fun `find by analysis id and cluster filter`() = suspendingDatabaseTest {
        setup()
        // Given: Multiple analysis results for same analysis beloning to different clusters
        val result1 = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            threadId = thread.idValue,
            feedbackId = feedback.idValue,
            feedbackTypeValue = feedbackType,
        )
        store.update(resultId = result1.id, clusterMembership = listOf("cluster1"))
        val feedback2 = createFeedback(thread)
        val result2 = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            threadId = thread.idValue,
            feedbackId = feedback2.idValue,
            feedbackTypeValue = feedbackType,
        )
        store.update(resultId = result2.id, clusterMembership = listOf("cluster2"))

        // When
        val results = store.findByAnalysisIdAndIncludeFeedback(analysisId = analysis.id, clusterIds = listOf("cluster1"))

        // Then Should return correct results
        assertThat(results).hasSize(1)
        assertThat(results.map { it.result.id })
            .containsExactlyInAnyOrder(result1.id)
        assertThat(results.map { it.result.analysisId })
            .containsOnly(analysis.id)
    }

    @Test
    fun `find by analysis id returns empty list for non-existent analysis`() = suspendingDatabaseTest {
        setup()

        val results = store.findByAnalysisIdAndIncludeFeedback(
            analysisId = ConversationAnalysisId(UUID.randomUUID()),
        )

        assertThat(results).isEmpty()
    }

    @Test
    fun `findByAnalysisId all results`() = suspendingDatabaseTest {
        setup()
        // Given: Multiple analysis results for same analysis
        val result1 = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            threadId = thread.idValue,
            feedbackId = feedback.idValue,
            feedbackTypeValue = feedbackType,
        )
        val fb2 = createFeedback(thread)
        val result2 = store.idempotentCreate(
            resultId = ConversationAnalysisResultHumanFeedbackId.random(),
            analysisId = analysis.id,
            threadId = thread.idValue,
            feedbackId = fb2.idValue,
            feedbackTypeValue = feedbackType,
        )

        // When
        val results = store.findByAnalysisId(analysisId = analysis.id)

        // Then Should return correct results
        assertThat(results).hasSize(2)
        assertThat(results.map { it.id }).containsExactlyInAnyOrder(result1.id, result2.id)
    }
}
