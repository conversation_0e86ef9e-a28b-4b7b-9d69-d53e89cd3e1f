package com.nextchaptersoftware.db.sql

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.sql.QueryExtensions.toSQL
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAny
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.intLiteral
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class WhereExtensionsTest : DatabaseTestsBase() {

    companion object {
        private val orgId = OrgId.random()
        private const val ERROR_MESSAGE = "At least one condition must be provided."
    }

    @Nested
    inner class WhereAllTests {

        @Test
        fun `no conditions throws exception`() = suspendingDatabaseTest {
            assertThrows<IllegalArgumentException> {
                OrgModel
                    .select(intLiteral(1))
                    .whereAll()
            }.also { ex ->
                assertThat(ex).hasMessage(ERROR_MESSAGE)
            }
        }

        @Test
        fun `only null conditions throws exception`() = suspendingDatabaseTest {
            assertThrows<IllegalArgumentException> {
                OrgModel
                    .select(intLiteral(1))
                    .whereAll(null, null)
            }.also { ex ->
                assertThat(ex).hasMessage(ERROR_MESSAGE)
            }
        }

        @Test
        fun `single TRUE condition returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAll(Op.TRUE)
                .toSQL()
            assertThat(sql).isEqualTo("SELECT 1 FROM orgmodel WHERE TRUE")
        }

        @Test
        fun `TRUE, FALSE, NULL returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAll(Op.TRUE, Op.FALSE, Op.nullOp())
                .toSQL()
            assertThat(sql).isEqualTo("SELECT 1 FROM orgmodel WHERE TRUE AND FALSE AND NULL")
        }

        @Test
        fun `id eq orgId returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAll(OrgModel.id eq orgId, null)
                .toSQL()
            assertThat(sql).isEqualTo("SELECT 1 FROM orgmodel WHERE (orgmodel.id = '$orgId')")
        }

        @Test
        fun `id eq orgId and ORG_EXISTS_CLAUSE returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAll(OrgModel.id eq orgId, OrgStore.ORG_EXISTS_CLAUSE)
                .toSQL()
            assertThat(sql).isEqualTo(
                "SELECT 1 FROM orgmodel WHERE (orgmodel.id = '$orgId') AND (orgmodel.\"isDeleted\" = FALSE)",
            )
        }
    }

    @Nested
    inner class WhereAnyTests {

        @Test
        fun `no conditions throws exception`() = suspendingDatabaseTest {
            assertThrows<IllegalArgumentException> {
                OrgModel
                    .select(intLiteral(1))
                    .whereAny()
            }.also { ex ->
                assertThat(ex).hasMessage(ERROR_MESSAGE)
            }
        }

        @Test
        fun `only null conditions throws exception`() = suspendingDatabaseTest {
            assertThrows<IllegalArgumentException> {
                OrgModel
                    .select(intLiteral(1))
                    .whereAny(null, null)
            }.also { ex ->
                assertThat(ex).hasMessage(ERROR_MESSAGE)
            }
        }

        @Test
        fun `single TRUE condition returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAny(Op.TRUE)
                .toSQL()
            assertThat(sql).isEqualTo("SELECT 1 FROM orgmodel WHERE TRUE")
        }

        @Test
        fun `TRUE, FALSE, NULL returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAny(Op.TRUE, Op.FALSE, Op.nullOp())
                .toSQL()
            assertThat(sql).isEqualTo("SELECT 1 FROM orgmodel WHERE TRUE OR FALSE OR NULL")
        }

        @Test
        fun `id eq orgId returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAny(OrgModel.id eq orgId, null)
                .toSQL()
            assertThat(sql).isEqualTo("SELECT 1 FROM orgmodel WHERE (orgmodel.id = '$orgId')")
        }

        @Test
        fun `id eq orgId and ORG_EXISTS_CLAUSE returns SQL`() = suspendingDatabaseTest {
            val sql = OrgModel
                .select(intLiteral(1))
                .whereAny(OrgModel.id eq orgId, OrgStore.ORG_EXISTS_CLAUSE)
                .toSQL()
            assertThat(sql).isEqualTo(
                "SELECT 1 FROM orgmodel WHERE (orgmodel.id = '$orgId') OR (orgmodel.\"isDeleted\" = FALSE)",
            )
        }
    }
}
