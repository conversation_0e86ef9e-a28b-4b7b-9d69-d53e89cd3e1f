package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.test.utils.TestData
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.dao.Entity
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource
import org.reflections.Reflections

class ServiceModelDAOsDynamicTest {

    companion object {
        val modelsPackage = OrgDAO::class.java.packageName

        private val excluded = listOf(
            ValueIdClassTest.FooDAO::class.java,
        )
    }

    object ModelClasses : TestData<Class<out Entity<*>>>(
        Reflections(modelsPackage)
            .getSubTypesOf(Entity::class.java)
            .asSequence()
            .filter { it.packageName == modelsPackage }
            .filterNot { it.kotlin.isOpen }
            .filterNot { excluded.contains(it) }
            .sortedBy { it.name },
    )

    @ParameterizedTest
    @ArgumentsSource(ModelClasses::class)
    fun `class must extend EntityExtensions`(
        daoClass: Class<out Entity<*>>,
    ) {
        assertThat(
            EntityExtensions::class.java.isAssignableFrom(daoClass),
        )
        .withFailMessage("${daoClass.name} does not extend ${EntityExtensions::class.java.name}")
        .isTrue()
    }
}
