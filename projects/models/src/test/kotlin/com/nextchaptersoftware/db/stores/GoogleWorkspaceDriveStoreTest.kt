package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GoogleWorkspaceDriveStoreTest : DatabaseTestsBase() {
    private val store = Stores.googleWorkspaceDriveStore

    @Test
    fun `create, upsert, list, and setIngestAllFiles`() = suspendingDatabaseTest {
        val installation = makeInstallation().asDataModel()
        val adminEmail = "<EMAIL>"
        val systemAccountKey = "Blah blah blah".toByteArray().let(::Ciphertext)

        store.upsert(
            trx = null,
            installationId = installation.id,
            adminEmail = adminEmail,
            systemAccountKey = systemAccountKey,
            domain = "askunblocked.com",
            ingestAllFiles = true,
        )

        val result = store.find(trx = null, installationId = installation.id) ?: error("Not found")

        assertThat(result.systemAccountKey).isEqualTo(systemAccountKey)

        val newSystemAccountKey = "New key".toByteArray().let(::Ciphertext)

        store.upsert(
            trx = null,
            installationId = installation.id,
            adminEmail = null,
            systemAccountKey = newSystemAccountKey,
            domain = null,
            ingestAllFiles = null,
        )

        val updatedResult = store.find(trx = null, installationId = installation.id) ?: error("Not found")
        assertThat(updatedResult.ingestAllFiles).isTrue()

        assertThat(updatedResult.systemAccountKey).isEqualTo(newSystemAccountKey)

        assertThat(store.list()).isEqualTo(mapOf(installation to updatedResult))

        store.setIngestAllFiles(
            installationId = installation.id,
            ingestAllFiles = false,
        )

        val finalResult = store.find(trx = null, installationId = installation.id) ?: error("Not found")

        assertThat(finalResult.ingestAllFiles).isFalse()
    }
}
