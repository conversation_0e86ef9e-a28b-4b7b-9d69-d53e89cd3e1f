package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IsQuestionRegression
import com.nextchaptersoftware.db.models.IsQuestionRegressionDAO
import com.nextchaptersoftware.db.models.IsQuestionRegressionId
import com.nextchaptersoftware.db.models.IsQuestionRegressionModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.toIsQuestionRegression
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.update

class IsQuestionRegressionStore internal constructor() {
    companion object {
        private const val IS_QUESTION_REGRESSION_TEST_LIMIT = 100
    }

    suspend fun list(): List<IsQuestionRegression> {
        return suspendedTransaction {
            IsQuestionRegressionDAO
                .all()
                .limit(IS_QUESTION_REGRESSION_TEST_LIMIT)
                .map { it.asDataModel() }
        }
    }

    suspend fun create(
        question: String,
        orgId: OrgId,
        shouldPass: <PERSON>olean,
    ): IsQuestionRegression = suspendedTransaction {
        IsQuestionRegressionModel.insert {
            it[this.question] = question
            it[this.org] = orgId
            it[this.shouldPass] = shouldPass
        }.resultedValues?.first()?.let {
            it.toIsQuestionRegression()
        } ?: error("Creating IsQuestionRegressionTest failed")
    }

    suspend fun delete(
        id: IsQuestionRegressionId,
    ) = suspendedTransaction {
        IsQuestionRegressionDAO.findById(id)?.delete()
    }

    suspend fun findById(
        id: IsQuestionRegressionId,
    ): IsQuestionRegression? = suspendedTransaction {
        IsQuestionRegressionDAO.findById(id)?.asDataModel()
    }

    suspend fun update(
        id: IsQuestionRegressionId,
        question: String,
        shouldPass: Boolean,
    ) = suspendedTransaction {
        IsQuestionRegressionModel.update({
            IsQuestionRegressionModel.id eq id
        }) {
            it[this.question] = question
            it[this.shouldPass] = shouldPass
        }
    }
}
