@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.environment.UrlBuilderProvider
import io.ktor.http.Url
import kotlinx.serialization.Serializable

/**
 * Represents a data source preset at the time a question was answered.
 */
@Serializable
data class DataSourcePresetArtifact(
    val presetId: DataSourcePresetId,
    val displayName: String,
    val avatarUrl: String? = null,
    val installations: List<DataSourcePresetInstallationArtifact>,
) {
    fun dashboardUrl(
        urlBuilderProvider: UrlBuilderProvider,
        orgId: OrgId,
    ): Url = urlBuilderProvider
        .dashboard()
        .withOrg(orgId.value)
        .withIntegrations()
        .withDataSourcePreset(presetId.value)
        .build()
}

@Serializable
data class DataSourcePresetInstallationArtifact(
    val provider: Provider,
    val displayName: String?,
    val groupCount: Int?,
)
