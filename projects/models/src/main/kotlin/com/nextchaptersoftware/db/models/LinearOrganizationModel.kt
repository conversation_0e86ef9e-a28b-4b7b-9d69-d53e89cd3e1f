@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object LinearOrganizationModel : ServiceModel<LinearOrganizationId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val installation = reference(
        name = "installation",
        foreign = InstallationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val linearOrganizationId = text(name = "linearOrganizationId")
    val name = text(name = "name")
    val avatarUrl = text(name = "avatarUrl").nullable()
    val htmlUrl = text(name = "htmlUrl").nullable()

    val linearTeamIngestionType = enumerationByDbOrdinal(
        name = "linearTeamIngestionType",
        klass = LinearTeamIngestionType::class,
    ).clientDefault { LinearTeamIngestionType.AllTeams }.nullable()

    init {
        index(isUnique = true, installation)
        index(isUnique = false, linearOrganizationId)
    }
}

fun ResultRow.toLinearOrganization(alias: String? = null) = LinearOrganizationDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toLinearOrganizationOrNull(alias: String? = null) = LinearOrganizationDAO.wrapRowOrNull(this, alias)?.asDataModel()

class LinearOrganizationDAO(id: EntityID<LinearOrganizationId>) : EntityExtensions<LinearOrganization, LinearOrganizationId>(id) {
    companion object : EntityClassExtensions<LinearOrganizationId, LinearOrganizationDAO>(LinearOrganizationModel)

    override var createdAt by LinearOrganizationModel.createdAt
    override var modifiedAt by LinearOrganizationModel.modifiedAt

    var installation by InstallationDAO referencedOn LinearOrganizationModel.installation
    var linearOrganizationId by LinearOrganizationModel.linearOrganizationId
    var name by LinearOrganizationModel.name
    var avatarUrl by LinearOrganizationModel.avatarUrl
    var htmlUrl by LinearOrganizationModel.htmlUrl

    var linearTeamIngestionType by LinearOrganizationModel.linearTeamIngestionType

    override fun asDataModel() = readValues.let {
        LinearOrganization(
            id = it[LinearOrganizationModel.id].value,
            createdAt = it[LinearOrganizationModel.createdAt],
            modifiedAt = it[LinearOrganizationModel.modifiedAt],
            installationId = it[LinearOrganizationModel.installation].value,
            linearOrganizationId = it[LinearOrganizationModel.linearOrganizationId],
            name = it[LinearOrganizationModel.name],
            avatarUrl = it[LinearOrganizationModel.avatarUrl]?.asUrl,
            htmlUrl = it[LinearOrganizationModel.htmlUrl]?.asUrl,
            linearTeamIngestionType = it[LinearOrganizationModel.linearTeamIngestionType],
        )
    }
}

object LinearOrganizationIdConverter : ValueIdConverter<LinearOrganizationId> {
    override val factory = ::LinearOrganizationId
    override val extract = LinearOrganizationId::value
}

internal object LinearOrganizationIdSerializer : ValueIdSerializer<LinearOrganizationId>(
    serialName = "LinearOrganizationId",
    converter = LinearOrganizationIdConverter,
)

@JvmInline
@Serializable(with = LinearOrganizationIdSerializer::class)
value class LinearOrganizationId(val value: UUID) : ValueId, Comparable<LinearOrganizationId> {

    companion object : ValueIdClass<LinearOrganizationId>(
        converter = LinearOrganizationIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: LinearOrganizationId) = value.compareTo(other.value)
}

data class LinearOrganization(
    val id: LinearOrganizationId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val installationId: InstallationId,
    val linearOrganizationId: String,
    val name: String,
    val avatarUrl: Url?,
    val htmlUrl: Url?,
    val linearTeamIngestionType: LinearTeamIngestionType? = null,
) {
    fun shouldIngest(team: LinearTeam): Boolean = when (linearTeamIngestionType) {
        LinearTeamIngestionType.AllTeams -> true
        LinearTeamIngestionType.SelectedTeamsOnly -> team.shouldIngest
        null -> false
    }
}
