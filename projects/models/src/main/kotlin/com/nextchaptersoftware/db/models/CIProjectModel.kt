package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object CIProjectModel : ServiceModel<CIProjectId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val ciInstallation = reference(
        name = "ciInstallation",
        foreign = InstallationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )
    val selected = bool("selected")

    // organization
    val organizationExternalId = uuid("oExternalId")
    val organizationSlug = text("oSlug")
    val organizationName = text("oName")
    val organizationUrl = text("oUrl")

    // project
    val projectExternalId = uuid("pExternalId")
    val projectSlug = text("pSlug")
    val projectName = text("pName")
    val projectUrl = text("pUrl")

    init {
        index(isUnique = true, ciInstallation, projectExternalId)
    }
}

fun ResultRow.toCIProject(alias: String? = null) = CIProjectDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toCIProjectOrNull(alias: String? = null) = CIProjectDAO.wrapRowOrNull(this, alias)?.asDataModel()

class CIProjectDAO(id: EntityID<CIProjectId>) : EntityExtensions<CIProject, CIProjectId>(id) {

    companion object : EntityClassExtensions<CIProjectId, CIProjectDAO>(CIProjectModel)

    override var createdAt by CIProjectModel.createdAt
    override var modifiedAt by CIProjectModel.modifiedAt

    var ciInstallation by InstallationDAO referencedOn CIProjectModel.ciInstallation
    var selected by CIProjectModel.selected

    // organization
    var organizationExternalId by CIProjectModel.organizationExternalId
    var organizationSlug by CIProjectModel.organizationSlug
    var organizationName by CIProjectModel.organizationName
    var organizationUrl by CIProjectModel.organizationUrl

    // project
    var projectExternalId by CIProjectModel.projectExternalId
    var projectSlug by CIProjectModel.projectSlug
    var projectName by CIProjectModel.projectName
    var projectUrl by CIProjectModel.projectUrl

    override fun asDataModel() = readValues.let {
        CIProject(
            id = it[CIProjectModel.id].value,
            createdAt = it[CIProjectModel.createdAt],
            modifiedAt = it[CIProjectModel.modifiedAt],
            ciInstallation = it[CIProjectModel.ciInstallation].value,
            selected = it[CIProjectModel.selected],

            organizationExternalId = it[CIProjectModel.organizationExternalId],
            organizationSlug = it[CIProjectModel.organizationSlug],
            organizationName = it[CIProjectModel.organizationName],
            organizationUrl = it[CIProjectModel.organizationUrl].asUrl,

            projectExternalId = it[CIProjectModel.projectExternalId],
            projectSlug = it[CIProjectModel.projectSlug],
            projectName = it[CIProjectModel.projectName],
            projectUrl = it[CIProjectModel.projectUrl].asUrl,
        )
    }
}

object CIProjectIdConverter : ValueIdConverter<CIProjectId> {
    override val factory = ::CIProjectId
    override val extract = CIProjectId::value
}

internal object CIProjectIdSerializer : ValueIdSerializer<CIProjectId>(
    serialName = "CIProjectId",
    converter = CIProjectIdConverter,
)

@JvmInline
@Serializable(with = CIProjectIdSerializer::class)
value class CIProjectId(val value: UUID) : ValueId, Comparable<CIProjectId> {

    companion object : ValueIdClass<CIProjectId>(
        converter = CIProjectIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: CIProjectId) = value.compareTo(other.value)
}

data class CIProject(
    val id: CIProjectId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val ciInstallation: InstallationId,
    val selected: Boolean,

    // organization
    override val organizationExternalId: UUID,
    override val organizationSlug: String,
    override val organizationName: String,
    override val organizationUrl: Url,

    // project
    override val projectExternalId: UUID,
    override val projectSlug: String,
    override val projectName: String,
    override val projectUrl: Url,
) : CIProjectTraits

interface CIProjectTraits {
    val organizationExternalId: UUID
    val organizationSlug: String
    val organizationName: String
    val organizationUrl: Url

    val projectExternalId: UUID
    val projectSlug: String
    val projectName: String
    val projectUrl: Url
}

@Serializable
data class CIExternalProject(
    @Contextual
    override val organizationExternalId: UUID,
    override val organizationSlug: String,
    override val organizationName: String,
    override val organizationUrl: Url,
    @Contextual
    override val projectExternalId: UUID,
    override val projectSlug: String,
    override val projectName: String,
    override val projectUrl: Url,
) : CIProjectTraits
