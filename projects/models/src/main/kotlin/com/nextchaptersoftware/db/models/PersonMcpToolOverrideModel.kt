package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.compress.decompress
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption.CASCADE
import org.jetbrains.exposed.sql.ResultRow

/**
 * Model for storing person-specific overrides of MCP tool configurations.
 * Allows admins to customize tool descriptions, parameters, and settings on a per-person basis.
 */
object PersonMcpToolOverrideModel : ServiceModel<PersonMcpToolOverrideId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val person = reference(
        name = "person",
        foreign = PersonModel,
        onDelete = CASCADE,
        onUpdate = CASCADE,
    )

    val mcpTool = reference(
        name = "mcpTool",
        foreign = McpToolModel,
        onDelete = CASCADE,
        onUpdate = CASCADE,
    )

    val displayName = text("displayName").nullable()

    val description = text("description").nullable()

    val compressedParameters = binary("compressedParameters").nullable()

    val enabled = bool("enabled").nullable()

    init {
        uniqueIndex(person, mcpTool)
        index(isUnique = false, person)
        index(isUnique = false, mcpTool)
    }
}

fun ResultRow.toPersonMcpToolOverride(alias: String? = null) =
    PersonMcpToolOverrideDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toPersonMcpToolOverrideOrNull(alias: String? = null) =
    PersonMcpToolOverrideDAO.wrapRowOrNull(this, alias)?.asDataModel()

class PersonMcpToolOverrideDAO(id: EntityID<PersonMcpToolOverrideId>) :
    EntityExtensions<PersonMcpToolOverride, PersonMcpToolOverrideId>(id) {

    companion object : EntityClassExtensions<PersonMcpToolOverrideId, PersonMcpToolOverrideDAO>(PersonMcpToolOverrideModel)

    override var createdAt by PersonMcpToolOverrideModel.createdAt
    override var modifiedAt by PersonMcpToolOverrideModel.modifiedAt

    var person by PersonDAO referencedOn PersonMcpToolOverrideModel.person
    var mcpTool by McpToolDAO referencedOn PersonMcpToolOverrideModel.mcpTool
    var displayName by PersonMcpToolOverrideModel.displayName
    var description by PersonMcpToolOverrideModel.description
    var compressedParameters by PersonMcpToolOverrideModel.compressedParameters
    var enabled by PersonMcpToolOverrideModel.enabled

    override fun asDataModel() = readValues.let {
        PersonMcpToolOverride(
            id = it[PersonMcpToolOverrideModel.id].value,
            createdAt = it[PersonMcpToolOverrideModel.createdAt],
            modifiedAt = it[PersonMcpToolOverrideModel.modifiedAt],
            personId = it[PersonMcpToolOverrideModel.person].value,
            mcpToolId = it[PersonMcpToolOverrideModel.mcpTool].value,
            displayName = it[PersonMcpToolOverrideModel.displayName],
            description = it[PersonMcpToolOverrideModel.description],
            compressedParameters = it[PersonMcpToolOverrideModel.compressedParameters],
            enabled = it[PersonMcpToolOverrideModel.enabled],
        )
    }
}

object PersonMcpToolOverrideIdConverter : ValueIdConverter<PersonMcpToolOverrideId> {
    override val factory = ::PersonMcpToolOverrideId
    override val extract = PersonMcpToolOverrideId::value
}

internal object PersonMcpToolOverrideIdSerializer : ValueIdSerializer<PersonMcpToolOverrideId>(
    serialName = "PersonMcpToolOverrideId",
    converter = PersonMcpToolOverrideIdConverter,
)

@JvmInline
@Serializable(with = PersonMcpToolOverrideIdSerializer::class)
value class PersonMcpToolOverrideId(val value: UUID) : ValueId, Comparable<PersonMcpToolOverrideId> {

    companion object : ValueIdClass<PersonMcpToolOverrideId>(
        converter = PersonMcpToolOverrideIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: PersonMcpToolOverrideId) = value.compareTo(other.value)
}

data class PersonMcpToolOverride(
    val id: PersonMcpToolOverrideId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val personId: PersonId,
    val mcpToolId: McpToolId,
    val displayName: String?,
    val description: String?,
    val compressedParameters: ByteArray?,
    val enabled: Boolean?,
) {
    val parameters: List<McpToolParameter>? by lazy {
        compressedParameters?.decompress()?.decode<List<McpToolParameter>>()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        return other is PersonMcpToolOverride && id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}
