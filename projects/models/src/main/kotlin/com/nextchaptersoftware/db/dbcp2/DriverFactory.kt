package com.nextchaptersoftware.db.dbcp2

import com.nextchaptersoftware.log.kotlin.errorSync
import java.sql.Driver
import java.sql.DriverManager
import java.sql.SQLException
import mu.KotlinLogging
import org.apache.commons.dbcp2.BasicDataSource

private val LOGGER = KotlinLogging.logger {}

@Suppress("NestedBlockDepth", "TooGenericExceptionCaught", "SwallowedException", "ThrowsCount")
object DriverFactory {
    fun createDriver(basicDataSource: BasicDataSource): Driver {
        // Load the JDBC driver class
        var driverToUse = basicDataSource.driver
        val driverClassName = basicDataSource.driverClassName
        val driverClassLoader = basicDataSource.driverClassLoader
        val url = basicDataSource.url

        if (driverToUse == null) {
            var driverFromCCL: Class<*>? = null
            if (driverClassName != null) {
                driverFromCCL = try {
                    try {
                        if (driverClassLoader == null) {
                            Class.forName(driverClassName)
                        } else {
                            Class.forName(driverClassName, true, driverClassLoader)
                        }
                    } catch (cnfe: ClassNotFoundException) {
                        Thread.currentThread().contextClassLoader.loadClass(driverClassName)
                    }
                } catch (t: Exception) {
                    val message = "Cannot load JDBC driver class '$driverClassName'"
                    LOGGER.errorSync(t) { message }
                    throw SQLException(message, t)
                }
            }
            try {
                if (driverFromCCL == null) {
                    driverToUse = DriverManager.getDriver(url)
                } else {
                    // Usage of DriverManager is not possible, as it does not
                    // respect the ContextClassLoader
                    // N.B. This cast may cause ClassCastException which is
                    // handled below
                    driverToUse = driverFromCCL.getConstructor().newInstance() as Driver
                    if (!driverToUse.acceptsURL(url)) {
                        throw SQLException("No suitable driver", "08001")
                    }
                }
            } catch (t: Exception) {
                val message = (
                    "Cannot create JDBC driver of class '" +
                        (driverClassName ?: "") + "' for connect URL '" + url + "'"
                    )
                LOGGER.errorSync(t) { message }
                throw SQLException(message, t)
            }
        }

        return driverToUse
    }
}
