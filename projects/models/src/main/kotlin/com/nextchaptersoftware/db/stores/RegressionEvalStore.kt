package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.EvalId
import com.nextchaptersoftware.db.models.ExecutionState
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.models.RegressionEval
import com.nextchaptersoftware.db.models.RegressionEvalDAO
import com.nextchaptersoftware.db.models.RegressionEvalId
import com.nextchaptersoftware.db.models.RegressionEvalModel
import com.nextchaptersoftware.db.models.RegressionTestId
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.insert
import org.jetbrains.exposed.sql.update

class RegressionEvalStore internal constructor(
    private val evalStore: EvalStore = Stores.evalStore,
) {
    suspend fun createRegressionEval(
        trx: Transaction? = null,
        templateId: MLInferenceTemplateId, // template under test
        inferenceId: MLInferenceId, // example to test against
        regressionTestId: RegressionTestId, // add to test run
    ): RegressionEval = suspendedTransaction(trx) {
        evalStore.createEval(
            trx = trx,
            evalSubjectId = inferenceId,
        ).let { oldEval ->
            RegressionEvalModel.insert {
                it[this.template] = templateId
                it[this.inference] = inferenceId
                it[this.oldEval] = oldEval.id
                it[this.executionState] = ExecutionState.Pending
                it[this.regressionTest] = regressionTestId
            }.resultedValues?.firstOrNull()?.let {
                RegressionEvalDAO.wrapRow(it)
            } ?: throw IllegalStateException("Failed to create regression eval")
        }.asDataModel()
    }

    suspend fun regressionEvalsForTest(
        trx: Transaction? = null,
        regressionTestId: RegressionTestId,
    ): List<RegressionEval> = suspendedTransaction(trx) {
        RegressionEvalDAO.find {
            RegressionEvalModel.regressionTest eq regressionTestId
        }.map { it.asDataModel() }
    }

    suspend fun findById(
        trx: Transaction? = null,
        id: RegressionEvalId,
    ): RegressionEval? = suspendedTransaction(trx) {
        RegressionEvalDAO.findById(id)?.asDataModel()
    }

    suspend fun get(
        trx: Transaction? = null,
        id: RegressionEvalId,
    ): RegressionEval = suspendedTransaction(trx) {
        RegressionEvalDAO[id].asDataModel()
    }

    suspend fun updateExecutionStateFromChildrenExecutionState(
        trx: Transaction? = null,
        regressionEvalId: RegressionEvalId,
    ): Unit = suspendedTransaction(trx) {
        val regressionEval = get(trx = trx, id = regressionEvalId)
        when (regressionEval.executionState) {
            ExecutionState.Pending,
            ExecutionState.Cancelled,
            ExecutionState.Failed,
            ExecutionState.Completed,
            -> return@suspendedTransaction

            ExecutionState.Running -> {
                val oldEval = evalStore.get(trx = trx, id = regressionEval.oldEvalId)
                val newEval = regressionEval.newEvalId?.let { evalStore.get(trx = trx, id = it) }

                if (oldEval.executionState == ExecutionState.Failed || newEval?.executionState == ExecutionState.Failed) {
                    updateExecutionState(
                        trx = trx,
                        regressionEvalId = regressionEvalId,
                        executionState = ExecutionState.Failed,
                    )
                } else if (oldEval.executionState == ExecutionState.Cancelled || newEval?.executionState == ExecutionState.Cancelled) {
                    updateExecutionState(
                        trx = trx,
                        regressionEvalId = regressionEvalId,
                        executionState = ExecutionState.Cancelled,
                    )
                } else if (oldEval.executionState == ExecutionState.Completed && newEval?.executionState == ExecutionState.Completed) {
                    updateExecutionState(
                        trx = trx,
                        regressionEvalId = regressionEvalId,
                        executionState = ExecutionState.Completed,
                    )
                }
            }
        }
    }

    suspend fun updateNewEval(
        trx: Transaction? = null,
        regressionEvalId: RegressionEvalId,
        newEvalId: EvalId,
    ): Unit = suspendedTransaction(trx) {
        RegressionEvalModel.update({ RegressionEvalModel.id eq regressionEvalId }) {
            it[this.newEval] = newEvalId
        }
    }

    suspend fun updateExecutionState(
        trx: Transaction? = null,
        regressionEvalId: RegressionEvalId,
        executionState: ExecutionState,
    ): Unit = suspendedTransaction(trx) {
        RegressionEvalModel.update({ RegressionEvalModel.id eq regressionEvalId }) {
            it[this.executionState] = executionState
        }
    }

    suspend fun updateSimilarityScore(
        trx: Transaction? = null,
        regressionEvalId: RegressionEvalId,
        similarityScore: Double,
    ): Unit = suspendedTransaction(trx) {
        RegressionEvalModel.update({ RegressionEvalModel.id eq regressionEvalId }) {
            it[this.similarityScore] = similarityScore
        }
    }
}
