@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.DbOrdinal
import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.kotlin.datetime.timestamp

enum class NotionObjectIngestionStatus(
    override val dbOrdinal: Int,
) : DbOrdinal {
    Ingested(dbOrdinal = 1),
    Error(dbOrdinal = 2),
}

enum class NotionObjectType(
    override val dbOrdinal: Int,
) : DbOrdinal {
    Page(dbOrdinal = 1),
    Database(dbOrdinal = 2),
}

object NotionObjectModel : ServiceModel<NotionObjectId>(
    overrideName = "notionpagemodel", // Do NOT change this (this model used to be called NotionPageModel)
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val installation = reference(
        name = "installation",
        foreign = InstallationModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    /**
     * The Notion-assigned id of the object (page or database).
     * */
    val externalNotionId = uuid(name = "notionPageId")

    /**
     * The type of the object.
     * */
    val type = enumerationByDbOrdinal("type", NotionObjectType::class).nullable()

    /**
     * The ingestion status of this page. Null if this page needs ingestion.
     * */
    val status = enumerationByDbOrdinal("status", NotionObjectIngestionStatus::class).nullable().index()

    /**
     * The ingestion priority of this page. Null if this page does not need to be prioritized.
     * */
    val priority = integer(name = "priority").nullable()

    /**
     * The last edited time of this page when it was last ingested.
     * */
    val lastEditedTime = timestamp(name = "lastEditedTime").nullable()

    val markForDeletion = bool("markForDeletion").nullable().index()

    init {
        index(isUnique = true, installation, externalNotionId)
        index(isUnique = false, installation, status, priority)
    }
}

fun ResultRow.toNotionObject(alias: String? = null) = NotionObjectDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toNotionObjectOrNull(alias: String? = null) = NotionObjectDAO.wrapRowOrNull(this, alias)?.asDataModel()

class NotionObjectDAO(id: EntityID<NotionObjectId>) : EntityExtensions<NotionObject, NotionObjectId>(id) {
    companion object : EntityClassExtensions<NotionObjectId, NotionObjectDAO>(NotionObjectModel)

    override var createdAt by NotionObjectModel.createdAt
    override var modifiedAt by NotionObjectModel.modifiedAt

    var installation by InstallationDAO referencedOn NotionObjectModel.installation
    var externalNotionId by NotionObjectModel.externalNotionId
    var type by NotionObjectModel.type
    var status by NotionObjectModel.status
    var priority by NotionObjectModel.priority
    var lastEditedTime by NotionObjectModel.lastEditedTime
    var markForDeletion by NotionObjectModel.markForDeletion

    override fun asDataModel() = readValues.let { row ->
        NotionObject(
            id = row[NotionObjectModel.id].value,
            createdAt = row[NotionObjectModel.createdAt],
            modifiedAt = row[NotionObjectModel.modifiedAt],
            installationId = row[NotionObjectModel.installation].value,
            externalNotionId = row[NotionObjectModel.externalNotionId],
            type = row[NotionObjectModel.type] ?: NotionObjectType.Page,
            status = row[NotionObjectModel.status],
            priority = row[NotionObjectModel.priority],
            lastEditedTime = row[NotionObjectModel.lastEditedTime],
            markForDeletion = row[NotionObjectModel.markForDeletion] ?: false,
        )
    }
}

object NotionObjectIdConverter : ValueIdConverter<NotionObjectId> {
    override val factory = ::NotionObjectId
    override val extract = NotionObjectId::value
}

internal object NotionObjectIdSerializer : ValueIdSerializer<NotionObjectId>(
    serialName = "NotionObjectId",
    converter = NotionObjectIdConverter,
)

@JvmInline
@Serializable(with = NotionObjectIdSerializer::class)
value class NotionObjectId(val value: UUID) : ValueId, Comparable<NotionObjectId> {

    companion object : ValueIdClass<NotionObjectId>(
        converter = NotionObjectIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: NotionObjectId) = value.compareTo(other.value)
}

data class NotionObject(
    val id: NotionObjectId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val installationId: InstallationId,
    val externalNotionId: UUID,
    val type: NotionObjectType,
    val status: NotionObjectIngestionStatus? = null,
    val priority: Int? = null,
    val lastEditedTime: Instant? = null,
    val markForDeletion: Boolean,
)
