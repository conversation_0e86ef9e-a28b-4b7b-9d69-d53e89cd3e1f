package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.JiraProjectIngestionType
import com.nextchaptersoftware.db.models.JiraSite
import com.nextchaptersoftware.db.models.JiraSiteDAO
import com.nextchaptersoftware.db.models.JiraSiteId
import com.nextchaptersoftware.db.models.JiraSiteModel
import com.nextchaptersoftware.db.models.toInstallation
import com.nextchaptersoftware.db.models.toJiraSite
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import io.ktor.http.Url
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.insert

class JiraSiteStore internal constructor() {
    suspend fun find(
        id: JiraSiteId,
    ): Pair<Installation, JiraSite>? = suspendedTransaction {
        JiraSiteModel
            .join(
                otherTable = InstallationModel,
                otherColumn = InstallationModel.id,
                onColumn = JiraSiteModel.installation,
                joinType = JoinType.INNER,
            )
            .select(JiraSiteModel.columns + InstallationModel.columns)
            .whereAll(
                (JiraSiteModel.id eq id),
                InstallationStore.INSTALLATION_EXISTS,
            )
            .limit(1)
            .map { it.toInstallation() to it.toJiraSite() }
            .firstOrNull()
    }

    suspend fun create(
        trx: Transaction?,
        installationId: InstallationId,
        siteId: String,
        baseUrl: Url,
        avatarUrl: Url,
        name: String,
    ): JiraSite = suspendedTransaction(trx) {
        JiraSiteModel.insert {
            it[JiraSiteModel.installation] = installationId
            it[JiraSiteModel.siteId] = siteId
            it[JiraSiteModel.baseUrl] = baseUrl.asString
            it[JiraSiteModel.avatarUrl] = avatarUrl.asString
            it[JiraSiteModel.name] = name
        }.resultedValues?.first()?.let {
            it.toJiraSite()
        } ?: error("Failed to create JiraSite")
    }

    suspend fun getInstallationIdForJiraSite(externalSiteId: String): InstallationId = suspendedTransaction {
        JiraSiteModel
            .select(JiraSiteModel.installation)
            .where { JiraSiteModel.siteId eq externalSiteId }
            .first().let { row -> row[JiraSiteModel.installation].value }
    }

    suspend fun findForInstallation(
        trx: Transaction? = null,
        installationId: InstallationId,
    ): JiraSite? = suspendedTransaction(trx) {
        JiraSiteDAO.find {
            (JiraSiteModel.installation eq installationId)
        }.firstOrNull()?.asDataModel()
    }

    suspend fun deleteForInstallation(
        installationId: InstallationId,
    ) = suspendedTransaction {
        findForInstallation(
            trx = this,
            installationId = installationId,
        )?.let { site ->
            JiraSiteDAO.findById(site.id)?.delete()
        }
    }

    suspend fun updateJiraProjectIngestionType(
        id: JiraSiteId,
        jiraProjectIngestionType: JiraProjectIngestionType,
    ) = suspendedTransaction {
        JiraSiteDAO.find {
            (JiraSiteModel.id eq id)
        }.firstOrNull()?.let {
            it.jiraProjectIngestionType = jiraProjectIngestionType
        }
    }
}
