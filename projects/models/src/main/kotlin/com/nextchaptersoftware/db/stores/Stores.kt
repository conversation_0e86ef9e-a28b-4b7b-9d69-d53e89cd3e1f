package com.nextchaptersoftware.db.stores

object Stores {
    val answersStore by lazy { AnswersStore() }
    val archivedReferenceStore by lazy { ArchivedReferenceStore() }
    val asanaProjectStore by lazy { AsanaProjectStore() }
    val asanaProjectAccessStore by lazy { AsanaProjectAccessStore() }
    val asanaProjectTasksStore by lazy { AsanaProjectTasksStore() }
    val asanaTaskAccessStore by lazy { AsanaTaskAccessStore() }
    val asanaTaskStore by lazy { AsanaTaskStore() }
    val asanaTeamMembershipStore by lazy { AsanaTeamMembershipStore() }
    val asanaTeamStore by lazy { AsanaTeamStore() }
    val asanaWorkspaceStore by lazy { AsanaWorkspaceStore() }
    val assetStore by lazy { AssetStore() }
    val atlassianForgeInstallationStore by lazy { AtlassianForgeInstallationStore() }
    val awsAccountStore by lazy { AWSAccountStore() }
    val buildJobFailureStatsStore by lazy { BuildJobFailureStatsStore() }
    val buildJobStore by lazy { BuildJobStore() }
    val buildStore by lazy { BuildStore() }
    val buildTriageExecutionStore by lazy { BuildTriageExecutionStore() }
    val buildTriageReactionStore by lazy { BuildTriageReactionStore() }
    val buildTriageStore by lazy { BuildTriageStore() }
    val ciProjectStore by lazy { CIProjectStore() }
    val ciRepoMetricsStore by lazy { CiRepoMetricsStore() }
    val ciRepoStore by lazy { CIRepoStore() }
    val ciScmStore by lazy { CIScmStore() }
    val ciTokenStore by lazy { CITokenStore() }
    val ciWebhookEventStore by lazy { CIWebhookEventStore() }
    val clientConfigStore by lazy { ClientConfigStore() }
    val clientVersionStore by lazy { ClientVersionStore() }
    val codaDocStore by lazy { CodaDocStore() }
    val codaFolderStore by lazy { CodaFolderStore() }
    val codaGroupMemberStore by lazy { CodaGroupMemberStore() }
    val codaGroupStore by lazy { CodaGroupStore() }
    val codaOrganizationStore by lazy { CodaOrganizationStore() }
    val codaPermissionStore by lazy { CodaPermissionStore() }
    val codaResourceStore by lazy { CodaResourceStore() }
    val codaWebhookStore by lazy { CodaWebhookStore() }
    val codaWorkspaceMemberStore by lazy { CodaWorkspaceMemberStore() }
    val codaWorkspaceStore by lazy { CodaWorkspaceStore() }
    val collectionDocumentStore by lazy { CollectionDocumentStore() }
    val collectionStore by lazy { CollectionStore() }
    val confluenceContentStore by lazy { ConfluenceContentStore() }
    val confluenceGroupMemberStore by lazy { ConfluenceGroupMemberStore() }
    val confluenceGroupStore by lazy { ConfluenceGroupStore() }
    val confluenceSiteStore by lazy { ConfluenceSiteStore() }
    val confluenceSpacePermissionStore by lazy { ConfluenceSpacePermissionStore() }
    val confluenceSpaceStore by lazy { ConfluenceSpaceStore() }
    val conversationAnalysisResultHumanFeedbackStore by lazy { ConversationAnalysisResultHumanFeedbackStore() }
    val conversationAnalysisStore by lazy { ConversationAnalysisStore() }
    val dataSourcePresetInstallationGroupStore by lazy { DataSourcePresetInstallationGroupStore() }
    val dataSourcePresetInstallationStore by lazy { DataSourcePresetInstallationStore() }
    val dataSourcePresetPreferencesStore by lazy { DataSourcePresetPreferencesStore() }
    val dataSourcePresetStore by lazy { DataSourcePresetStore() }
    val emailEventStore by lazy { EmailEventStore() }
    val embeddingDeleteStore by lazy { EmbeddingDeleteStore() }
    val embeddingMigrationStore by lazy { EmbeddingMigrationStore() }
    val enterpriseAppConfigStore by lazy { EnterpriseAppConfigStore() }
    val evalStore by lazy { EvalStore() }
    val gitHubIngestionStore by lazy { GitHubIngestionStore() }
    val gitHubIssuesIngestionStore by lazy { GitHubIssuesIngestionStore() }
    val googleDriveDocumentStore by lazy { GoogleDriveDocumentStore() }
    val googleDriveFileStore by lazy { GoogleDriveFileStore() }
    val googleDriveFolderStore by lazy { GoogleDriveFolderStore() }
    val googleWorkspaceDriveStore by lazy { GoogleWorkspaceDriveStore() }
    val googleWorkspaceDriveSyncStore by lazy { GoogleWorkspaceDriveSyncStore() }
    val googleWorkspaceGroupStore by lazy { GoogleWorkspaceGroupStore() }
    val groupMembershipStore by lazy { GroupMembershipStore() }
    val groupStore by lazy { GroupStore() }
    val growthMetricsStore by lazy { GrowthMetricsStore() }
    val identityStore by lazy { IdentityStore() }
    val ingestionStore by lazy { IngestionStore() }
    val installationBotStore by lazy { InstallationBotStore() }
    val installationStore by lazy { InstallationStore() }
    val installationSuppressionStore by lazy { InstallationSuppressionStore() }
    val isQuestionAnswerRegressionEvalStore by lazy { IsQuestionAnswerRegressionEvalStore() }
    val isQuestionAnswerRegressionRunStore by lazy { IsQuestionAnswerRegressionRunStore() }
    val isQuestionAnswerRegressionStore by lazy { IsQuestionAnswerRegressionStore() }
    val isQuestionRegressionStore by lazy { IsQuestionRegressionStore() }
    val jiraBoardStore by lazy { JiraBoardStore() }
    val jiraEpicStore by lazy { JiraEpicStore() }
    val jiraGroupMemberStore by lazy { JiraGroupMemberStore() }
    val jiraGroupStore by lazy { JiraGroupStore() }
    val jiraIssueStore by lazy { JiraIssueStore() }
    val jiraPermissionSchemeStore by lazy { JiraPermissionSchemeStore() }
    val jiraProjectAccessStore by lazy { JiraProjectAccessStore() }
    val jiraProjectRoleStore by lazy { JiraProjectRoleStore() }
    val jiraProjectStore by lazy { JiraProjectStore() }
    val jiraSiteStore by lazy { JiraSiteStore() }
    val jiraSprintStore by lazy { JiraSprintStore() }
    val linearIngestionStore by lazy { LinearIngestionStore() }
    val linearOrganizationStore by lazy { LinearOrganizationStore() }
    val linearProjectStore by lazy { LinearProjectStore() }
    val linearTeamIngestionStore by lazy { LinearTeamIngestionStore() }
    val linearTeamStore by lazy { LinearTeamStore() }
    val mcpInferenceStore by lazy { McpInferenceStore() }
    val mcpToolStore by lazy { McpToolStore() }
    val memberAssociationStore by lazy { MemberAssociationStore() }
    val memberStore by lazy { MemberStore() }
    val memberStoreCompat by lazy { MemberStoreCompat() }
    val messageFeedbackStore by lazy { MessageFeedbackStore() }
    val messageMentionStore by lazy { MessageMentionStore() }
    val messageStore by lazy { MessageStore() }
    val messageSuggestionStore by lazy { MessageSuggestionStore() }
    val mlEmbeddingExperimentStore by lazy { MLEmbeddingExperimentStore() }
    val mlInferenceRuntimeConfigurationStore by lazy { MLInferenceRuntimeConfigurationStore() }
    val mlInferenceStore by lazy { MLInferenceStore() }
    val mlInferenceTemplateStore by lazy { MLInferenceTemplateStore() }
    val mlRouterStore by lazy { MLRouterStore() }
    val mlSettingsStore by lazy { MLSettingsStore() }
    val notionObjectAccessStore by lazy { NotionObjectAccessStore() }
    val notionObjectAccessSyncStore by lazy { NotionObjectAccessSyncStore() }
    val notionObjectStore by lazy { NotionObjectStore() }
    val orgApiKeyStore by lazy { OrgApiKeyStore() }
    val orgBillingEmailStore by lazy { OrgBillingEmailStore() }
    val orgBillingSeatStore by lazy { OrgBillingSeatStore() }
    val orgBillingStore by lazy { OrgBillingStore() }
    val orgEmailInviteStore by lazy { OrgEmailInviteStore() }
    val orgMemberDecorator by lazy { OrgMemberDecorator() }
    val orgMemberMetadataStore by lazy { OrgMemberMetadataStore() }
    val orgMemberMigrateStore by lazy { OrgMemberMigrateStore() }
    val orgMemberRoleStore by lazy { OrgMemberRoleStore() }
    val orgMemberStore by lazy { OrgMemberStore() }
    val orgProxyStore by lazy { OrgProxyStore() }
    val orgSettingsStore by lazy { OrgSettingsStore() }
    val orgStore by lazy { OrgStore() }
    val paymentInviteStore by lazy { PaymentInviteStore() }
    val personEmailPreferencesStore by lazy { PersonEmailPreferencesStore() }
    val personMcpToolOverrideStore by lazy { PersonMcpToolOverrideStore() }
    val personMcpTemplateOverrideStore by lazy { PersonMcpTemplateOverrideStore() }
    val personPreferencesMetricsStore by lazy { PersonPreferencesMetricsStore() }
    val personPreferencesStore by lazy { PersonPreferencesStore() }
    val personStore by lazy { PersonStore() }
    val planCapabilityStore by lazy { PlanCapabilityStore() }
    val planPriceStore by lazy { PlanPriceStore() }
    val planStore by lazy { PlanStore() }
    val productFeedbackStore by lazy { ProductFeedbackStore() }
    val providerAuthenticationStateStore by lazy { ProviderAuthenticationStateStore() }
    val pullRequestBundleStore by lazy { PullRequestBundleStore() }
    val pullRequestCommentStore by lazy { PullRequestCommentStore() }
    val pullRequestIngestionStore by lazy { PullRequestIngestionStore() }
    val pullRequestReviewStore by lazy { PullRequestReviewStore() }
    val pullRequestStore by lazy { PullRequestStore() }
    val questionsStore by lazy { QuestionsStore() }
    val rapidDeleteStore by lazy { RapidDeleteStore() }
    val registeredDomainStore by lazy { RegisteredDomainStore() }
    val regressionEvalStore by lazy { RegressionEvalStore() }
    val regressionTestStore by lazy { RegressionTestStore() }
    val repoStore by lazy { RepoStore() }
    val samlIdpMetadataStore by lazy { SamlIdpMetadataStore() }
    val sampleQuestionStore by lazy { SampleQuestionStore() }
    val scmTeamStore by lazy { ScmTeamStore() }
    val searchInsightStore by lazy { SearchInsightStore() }
    val sessionEventStore by lazy { SessionEventStore() }
    val slackAutoAnswerStore by lazy { SlackAutoAnswerStore() }
    val slackChannelIngestionStore by lazy { SlackChannelIngestionStore() }
    val slackChannelMemberStore by lazy { SlackChannelMemberStore() }
    val slackChannelPatternPreferencesStore by lazy { SlackChannelPatternPreferencesStore() }
    val slackChannelPreferencesMetricsStore by lazy { SlackChannelPreferencesMetricsStore() }
    val slackChannelPreferencesStore by lazy { SlackChannelPreferencesStore() }
    val slackChannelStore by lazy { SlackChannelStore() }
    val slackChannelsIngestionStore by lazy { SlackChannelsIngestionStore() }
    val slackDefaultChannelPreferencesStore by lazy { SlackDefaultChannelPreferencesStore() }
    val slackIngestionStore by lazy { SlackIngestionStore() }
    val slackPendingQuestionStore by lazy { SlackPendingQuestionStore() }
    val slackSettingsStore by lazy { SlackSettingsStore() }
    val slackTeamIngestionStore by lazy { SlackTeamIngestionStore() }
    val slackTeamStore by lazy { SlackTeamStore() }
    val slackThreadStore by lazy { SlackThreadStore() }
    val slackUserConnectPromptHistoryStore by lazy { SlackUserConnectPromptHistoryStore() }
    val slackUserConnectPromptStore by lazy { SlackUserConnectPromptStore() }
    val socialNetworkStore by lazy { SocialNetworkStore() }
    val sourceMarkStore by lazy { SourceMarkStore() }
    val sourcePointStore by lazy { SourcePointStore() }
    val teamInviteeStore by lazy { TeamInviteeStore() }
    val teamSettingsStore by lazy { TeamSettingsStore() }
    val threadBundleStore by lazy { ThreadBundleStore() }
    val threadParticipantStore by lazy { ThreadParticipantStore() }
    val threadPullRequestStore by lazy { ThreadPullRequestStore() }
    val threadStore by lazy { ThreadStore() }
    val threadUnreadStore by lazy { ThreadUnreadStore() }
    val topicExpertStore by lazy { TopicExpertStore() }
    val topicInsightStore by lazy { TopicInsightStore() }
    val topicStore by lazy { TopicStore() }
    val userEngagementMetricsStore by lazy { UserEngagementMetricsStore() }
    val userEngagementStore by lazy { UserEngagementStore() }
    val versionInfoStore by lazy { VersionInfoStore() }
    val webIngestionSiteStore by lazy { WebIngestionSiteStore() }
}
