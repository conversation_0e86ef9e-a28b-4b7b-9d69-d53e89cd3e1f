package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decompressAndDeserialize
import com.nextchaptersoftware.api.serialization.SerializationExtensions.serializeAndCompress
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.Person
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.ProductFeedbackResponseDAO
import com.nextchaptersoftware.db.models.ProductFeedbackResponseId
import com.nextchaptersoftware.db.models.ProductFeedbackResponseInstallation
import com.nextchaptersoftware.db.models.ProductFeedbackResponseInstallations
import com.nextchaptersoftware.db.models.ProductFeedbackResponseModel
import com.nextchaptersoftware.db.models.ProductFeedbackResponseType
import com.nextchaptersoftware.db.models.toOrg
import com.nextchaptersoftware.db.models.toPerson
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insertReturning
import org.jetbrains.exposed.sql.selectAll

class ProductFeedbackStore internal constructor() {

    suspend fun hasPersonProvidedFeedback(
        orgId: OrgId,
        personId: PersonId,
    ): Boolean = suspendedTransaction {
        ProductFeedbackResponseModel
            .select(ProductFeedbackResponseModel.id.count())
            .whereAll(
                ProductFeedbackResponseModel.org eq orgId,
                ProductFeedbackResponseModel.person eq personId,
            )
            .map { it[ProductFeedbackResponseModel.id.count()] > 0L }.firstOrNull() ?: false
    }

    suspend fun insertFeedback(
        orgId: OrgId,
        personId: PersonId,
        productAgent: ProductAgentType,
        connectedInstallations: List<Installation>,
        unconnectedInstallations: List<Installation>,
        suppressedInstallations: List<Installation>,
        feedbackType: ProductFeedbackResponseType,
        feedbackDescription: String? = null,
    ): ProductFeedbackResponseDAO = suspendedTransaction {
        fun installationToFeedbackInstallation(installation: Installation): ProductFeedbackResponseInstallation {
            return ProductFeedbackResponseInstallation(
                provider = installation.provider,
                installationId = installation.id.value.toString(),
                displayName = installation.displayName,
            )
        }

        val accountsState = ProductFeedbackResponseInstallations(
            connectedInstallations = connectedInstallations.map(::installationToFeedbackInstallation),
            unconnectedInstallations = unconnectedInstallations.map(::installationToFeedbackInstallation),
            suppressedInstallations = suppressedInstallations.map(::installationToFeedbackInstallation),
        )

        ProductFeedbackResponseModel.insertReturning {
            it[this.org] = orgId
            it[this.person] = personId
            it[this.productAgent] = productAgent
            it[this.installationsState] = accountsState.serializeAndCompress()
            it[this.feedbackType] = feedbackType
            it[this.feedbackDescription] = feedbackDescription
        }.let {
            ProductFeedbackResponseDAO.wrapRow(it.first())
        }
    }

    suspend fun removeFeedback(id: ProductFeedbackResponseId) = suspendedTransaction {
        ProductFeedbackResponseModel.deleteWhere { this.id eq id }
    }

    suspend fun getProductFeedbackResponseAggregateForOrg(
        orgId: OrgId,
    ): ProductFeedbackResponseGroup = suspendedTransaction {
        ProductFeedbackResponseModel
            .join(
                joinType = JoinType.INNER,
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = ProductFeedbackResponseModel.org,
            )
            .join(
                joinType = JoinType.INNER,
                otherTable = PersonModel,
                otherColumn = PersonModel.id,
                onColumn = ProductFeedbackResponseModel.person,
            )
            .selectAll()
            .where(
                ProductFeedbackResponseModel.org eq orgId,
            )
            .orderBy(ProductFeedbackResponseModel.createdAt, SortOrder.DESC)
            .map {
                ProductFeedbackResponseAggregate(
                    id = it[ProductFeedbackResponseModel.id].value,
                    org = it.toOrg(),
                    productAgent = it[ProductFeedbackResponseModel.productAgent],
                    feedbackDescription = it[ProductFeedbackResponseModel.feedbackDescription],
                    createdAt = it[ProductFeedbackResponseModel.createdAt],
                    feedbackType = it[ProductFeedbackResponseModel.feedbackType],
                    installationsState = it[ProductFeedbackResponseModel.installationsState].decompressAndDeserialize(),
                    person = it.toPerson(),
                )
            }
            .let {
                ProductFeedbackResponseGroup(
                    all = it,
                    mapping = it.groupBy { feedback -> feedback.feedbackType },
                )
            }
    }

    suspend fun getProductFeedbackResponseAggregate(orgIds: List<OrgId>? = null): ProductFeedbackResponseGroup = suspendedTransaction {
        val where: Op<Boolean> =
            if (orgIds.isNullOrEmpty()) {
                Op.TRUE
            } else {
                OrgModel.id inList orgIds
            }

        ProductFeedbackResponseModel
            .join(
                joinType = JoinType.INNER,
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = ProductFeedbackResponseModel.org,
            )
            .join(
                joinType = JoinType.INNER,
                otherTable = PersonModel,
                otherColumn = PersonModel.id,
                onColumn = ProductFeedbackResponseModel.person,
            )
            .selectAll()
            .where(where)
            .orderBy(ProductFeedbackResponseModel.createdAt, SortOrder.DESC)
            .map {
                ProductFeedbackResponseAggregate(
                    id = it[ProductFeedbackResponseModel.id].value,
                    org = it.toOrg(),
                    productAgent = it[ProductFeedbackResponseModel.productAgent],
                    feedbackDescription = it[ProductFeedbackResponseModel.feedbackDescription],
                    createdAt = it[ProductFeedbackResponseModel.createdAt],
                    feedbackType = it[ProductFeedbackResponseModel.feedbackType],
                    installationsState = it[ProductFeedbackResponseModel.installationsState].decompressAndDeserialize(),
                    person = it.toPerson(),
                )
            }
            .let {
                ProductFeedbackResponseGroup(
                    all = it,
                    mapping = it.groupBy { feedback -> feedback.feedbackType },
                )
            }
    }

    data class ProductFeedbackResponseGroup(
        val all: List<ProductFeedbackResponseAggregate>,
        val mapping: Map<ProductFeedbackResponseType, List<ProductFeedbackResponseAggregate>>,
    )

    data class ProductFeedbackResponseAggregate(
        val id: ProductFeedbackResponseId,
        val org: Org,
        val person: Person,
        val productAgent: ProductAgentType,
        val feedbackType: ProductFeedbackResponseType,
        val feedbackDescription: String?,
        val createdAt: Instant,
        val installationsState: ProductFeedbackResponseInstallations,
    )
}
