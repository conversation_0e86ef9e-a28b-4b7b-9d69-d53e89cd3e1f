@file:Suppress("ktlint:standard:function-naming")

package com.nextchaptersoftware.db.common.datetime

import kotlin.time.Instant
import kotlinx.datetime.LocalDate
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.Function
import org.jetbrains.exposed.sql.IntegerColumnType
import org.jetbrains.exposed.sql.LiteralOp
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.vendors.H2Dialect
import org.jetbrains.exposed.sql.vendors.currentDialect
import org.jetbrains.exposed.sql.vendors.h2Mode

object KotlinInstantExtensions {
    val KOTLIN_LOCAL_DATE_COLUMN_TYPE_INSTANCE = KotlinLocalDateColumnType()

    val KOTLIN_INSTANT_COLUMN_TYPE_INSTANCE = KotlinInstantColumnType()

    internal class DateInternal(val expr: Expression<*>) : Function<LocalDate>(KOTLIN_LOCAL_DATE_COLUMN_TYPE_INSTANCE) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
            currentDialect.functionProvider.date(expr, queryBuilder)
        }
    }

    /** Represents an SQL function that extracts the date part from a given timestamp [expr]. */
    @JvmName("InstantDateFunction")
    @Suppress("FunctionNaming")
    fun <T : Instant?> Date(expr: Expression<T>): Function<LocalDate> = DateInternal(expr)

    /** Returns the date from this timestamp expression. */
    @JvmName("InstantDateExt")
    fun <T : Instant?> Expression<T>.date() = Date(this)

    internal class HourInternal(val expr: Expression<*>) : Function<Int>(IntegerColumnType()) {
        override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
            val dialect = currentDialect
            val functionProvider = when (dialect.h2Mode) {
                H2Dialect.H2CompatibilityMode.SQLServer -> (dialect as H2Dialect).originalFunctionProvider
                else -> dialect.functionProvider
            }
            functionProvider.hour(expr, queryBuilder)
        }
    }

    /** Represents an SQL function that extracts the hour field from a given timestamp [expr]. */
    @JvmName("InstantHourFunction")
    @Suppress("FunctionNaming")
    fun <T : Instant?> Hour(expr: Expression<T>): Function<Int> = HourInternal(expr)

    /** Returns the hour from this timestamp expression, as an integer between 0 and 23 inclusive. */
    @JvmName("InstantHourExt")
    fun <T : Instant?> Expression<T>.hour() = Hour(this)

    /** Returns the specified [value] as a timestamp literal. */
    fun timestampLiteral(value: Instant): LiteralOp<Instant> = LiteralOp(KOTLIN_INSTANT_COLUMN_TYPE_INSTANCE, value)
}
