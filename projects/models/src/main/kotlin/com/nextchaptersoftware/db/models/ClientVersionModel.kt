@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object ClientVersionModel : ServiceModel<ClientVersionId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val identity = reference(
        name = "identity",
        foreign = IdentityModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )
    val productAgent = enumerationByDbOrdinal("productAgent", ProductAgentType::class)
    val productNumber = integer("productNumber")

    init {
        uniqueIndex(identity, productAgent)
    }
}

fun ResultRow.toClientVersion(alias: String? = null) = ClientVersionDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toClientVersionOrNull(alias: String? = null) = ClientVersionDAO.wrapRowOrNull(this, alias)?.asDataModel()

class ClientVersionDAO(id: EntityID<ClientVersionId>) : EntityExtensions<ClientVersion, ClientVersionId>(id) {
    companion object : EntityClassExtensions<ClientVersionId, ClientVersionDAO>(ClientVersionModel)

    override var createdAt by ClientVersionModel.createdAt
    override var modifiedAt by ClientVersionModel.modifiedAt

    var identity by IdentityDAO referencedOn ClientVersionModel.identity
    var productAgent by ClientVersionModel.productAgent
    var productNumber by ClientVersionModel.productNumber

    override fun asDataModel() = readValues.let {
        ClientVersion(
            id = it[ClientVersionModel.id].value,
            createdAt = it[ClientVersionModel.createdAt],
            modifiedAt = it[ClientVersionModel.modifiedAt],
            identityId = it[ClientVersionModel.identity].value,
            productAgent = it[ClientVersionModel.productAgent],
            productNumber = it[ClientVersionModel.productNumber],
        )
    }
}

object ClientVersionIdConverter : ValueIdConverter<ClientVersionId> {
    override val factory = ::ClientVersionId
    override val extract = ClientVersionId::value
}

internal object ClientVersionIdSerializer : ValueIdSerializer<ClientVersionId>(
    serialName = "ClientVersionId",
    converter = ClientVersionIdConverter,
)

@JvmInline
@Serializable(with = ClientVersionIdSerializer::class)
value class ClientVersionId(val value: UUID) : ValueId, Comparable<ClientVersionId> {

    companion object : ValueIdClass<ClientVersionId>(
        converter = ClientVersionIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ClientVersionId) = value.compareTo(other.value)
}

data class ClientVersion(
    val id: ClientVersionId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val identityId: IdentityId,
    val productAgent: ProductAgentType,
    val productNumber: Int,
)
