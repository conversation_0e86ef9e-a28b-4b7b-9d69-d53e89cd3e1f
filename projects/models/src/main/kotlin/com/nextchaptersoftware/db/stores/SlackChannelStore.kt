@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.api.models.SlackChannelSortOrder
import com.nextchaptersoftware.api.models.SortDirection
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.OperatorExtensions.isNullOrFalse
import com.nextchaptersoftware.db.models.DataSourcePresetModel
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.SlackChannel
import com.nextchaptersoftware.db.models.SlackChannelDAO
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.SlackChannelMemberModel
import com.nextchaptersoftware.db.models.SlackChannelModel
import com.nextchaptersoftware.db.models.SlackChannelPreferencesModel
import com.nextchaptersoftware.db.models.SlackChannelsSearchConfig
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.models.SlackTeamModel
import com.nextchaptersoftware.db.models.toSlackChannel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.AnyOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import org.jetbrains.exposed.sql.Case
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.JoinType.INNER
import org.jetbrains.exposed.sql.JoinType.LEFT
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.OrOp
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.like
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.upsertReturning

class SlackChannelStore internal constructor() {
    companion object {
        val IS_PUBLIC_CLAUSE = AllOp(
            SlackChannelModel.isPrivate.isNullOrFalse(),
            SlackChannelModel.isIm.isNullOrFalse(),
            SlackChannelModel.isMpim.isNullOrFalse(),
            SlackChannelModel.isArchived.isNullOrFalse(),
        )

        val IS_PUBLIC_NOT_SHARED_CLAUSE = AllOp(
            IS_PUBLIC_CLAUSE,
            SlackChannelModel.isShared.isNullOrFalse(),
        )

        val IS_PRIVATE_CLAUSE = (SlackChannelModel.isPrivate eq true)

        val IS_IM_CLAUSE = AnyOp(
            SlackChannelModel.isMpim eq true,
            SlackChannelModel.isIm eq true,
        )

        val IS_PRIVATE_OR_IM_CLAUSE = AnyOp(IS_PRIVATE_CLAUSE, IS_IM_CLAUSE)

        val HAS_PREFERENCES_SORT_EXPR = Case()
            .When(SlackChannelPreferencesModel.id.isNotNull(), intLiteral(0))
            .Else(intLiteral(1))
    }

    suspend fun deleteById(
        trx: Transaction? = null,
        id: SlackChannelId,
    ) = suspendedTransaction(trx = trx) {
        SlackChannelModel.deleteWhere {
            SlackChannelModel.id eq id
        }
    }

    suspend fun findDAOById(trx: Transaction? = null, id: SlackChannelId) = suspendedTransaction(trx = trx) {
        SlackChannelDAO.findById(id = id)
    }

    suspend fun findById(trx: Transaction? = null, id: SlackChannelId) = suspendedTransaction(trx = trx) {
        SlackChannelDAO.findById(id = id)?.asDataModel()
    }

    suspend fun findByIds(
        trx: Transaction? = null,
        ids: List<SlackChannelId>,
    ): List<SlackChannel> = suspendedTransaction(trx) {
        SlackChannelModel
            .selectAll()
            .where {
                SlackChannelModel.id inList ids
            }
            .map { it.toSlackChannel() }
    }

    suspend fun getChannelNamesMap(
        trx: Transaction? = null,
        ids: Collection<SlackChannelId>,
    ): Map<SlackChannelId, String> = suspendedTransaction(trx) {
        if (ids.isEmpty()) {
            return@suspendedTransaction emptyMap()
        }

        SlackChannelModel
            .select(SlackChannelModel.id, SlackChannelModel.name)
            .where {
                SlackChannelModel.id inList ids
            }
            .associate {
                it[SlackChannelModel.id].value to it[SlackChannelModel.name]
            }
    }

    suspend fun findAllBySlackExternalChannelId(
        trx: Transaction? = null,
        slackExternalTeamId: String,
        slackExternalChannelId: String,
    ): List<SlackChannelDAO> = suspendedTransaction(trx) {
        SlackChannelModel
            .join(
                otherColumn = SlackTeamModel.id,
                otherTable = SlackTeamModel,
                joinType = INNER,
                onColumn = SlackChannelModel.slackTeam,
            )
            .select(SlackChannelModel.columns)
            .whereAll(
                SlackChannelModel.slackExternalChannelId eq slackExternalChannelId,
                SlackTeamModel.slackExternalTeamId eq slackExternalTeamId,
            )
            .map {
                SlackChannelDAO.wrapRow(it)
            }
    }

    suspend fun getOrgId(
        trx: Transaction? = null,
        slackChannelId: SlackChannelId,
    ): OrgId = suspendedTransaction(trx) {
        SlackChannelModel
            .join(
                otherColumn = SlackTeamModel.id,
                otherTable = SlackTeamModel,
                joinType = INNER,
                onColumn = SlackChannelModel.slackTeam,
            )
            .join(
                otherColumn = InstallationModel.id,
                otherTable = InstallationModel,
                joinType = INNER,
                onColumn = SlackTeamModel.installation,
            )
            .join(
                otherColumn = OrgModel.id,
                otherTable = OrgModel,
                joinType = INNER,
                onColumn = InstallationModel.org,
            )
            .select(OrgModel.id)
            .where {
                SlackChannelModel.id eq slackChannelId
            }
            .map { it[OrgModel.id].value }
            .firstOrNull()
            .let(::checkNotNull)
    }

    suspend fun findByIdWithDaos(
        trx: Transaction? = null,
        id: SlackChannelId,
    ): SlackChannelDaos? = suspendedTransaction(trx) {
        val andClause = AllOp(
            SlackChannelModel.id eq id,
        )

        SlackChannelModel
            .join(
                otherColumn = SlackTeamModel.id,
                otherTable = SlackTeamModel,
                joinType = INNER,
                onColumn = SlackChannelModel.slackTeam,
            )
            .join(
                otherColumn = InstallationModel.id,
                otherTable = InstallationModel,
                joinType = INNER,
                onColumn = SlackTeamModel.installation,
            )
            .join(
                onColumn = InstallationModel.org,
                joinType = INNER,
                otherColumn = OrgModel.id,
                otherTable = OrgModel,
            )
            .selectAll()
            .where(andClause)
            .map {
                SlackChannelDaos(
                    slackChannel = SlackChannelDAO.wrapRow(it),
                    org = OrgDAO.wrapRow(it),
                    slackTeam = SlackTeamDAO.wrapRow(it),
                    installation = InstallationDAO.wrapRow(it),
                )
            }.firstOrNull()
    }

    suspend fun findBySlackExternalChannelId(
        trx: Transaction? = null,
        slackTeamId: SlackTeamId,
        slackExternalChannelId: String,
    ): SlackChannelDaos? = suspendedTransaction(trx) {
        val andClause = AllOp(
            SlackChannelModel.slackExternalChannelId eq slackExternalChannelId,
            SlackChannelModel.slackTeam eq slackTeamId,
        )

        SlackChannelModel
            .join(
                otherColumn = SlackTeamModel.id,
                otherTable = SlackTeamModel,
                joinType = INNER,
                onColumn = SlackChannelModel.slackTeam,
            )
            .join(
                otherColumn = InstallationModel.id,
                otherTable = InstallationModel,
                joinType = INNER,
                onColumn = SlackTeamModel.installation,
            )
            .join(
                onColumn = InstallationModel.org,
                joinType = INNER,
                otherColumn = OrgModel.id,
                otherTable = OrgModel,
            )
            .selectAll()
            .where(andClause)
            .map {
                SlackChannelDaos(
                    slackChannel = SlackChannelDAO.wrapRow(it),
                    org = OrgDAO.wrapRow(it),
                    slackTeam = SlackTeamDAO.wrapRow(it),
                    installation = InstallationDAO.wrapRow(it),
                )
            }.firstOrNull()
    }

    suspend fun findByName(
        trx: Transaction? = null,
        slackTeamId: SlackTeamId,
        name: String,
    ): SlackChannel? = suspendedTransaction(trx) {
        SlackChannelModel
            .select(SlackChannelModel.columns)
            .whereAll(
                SlackChannelModel.slackTeam eq slackTeamId,
                SlackChannelModel.name eq name,
            )
            .withDistinct(true)
            .map {
                it.toSlackChannel()
            }.firstOrNull()
    }

    suspend fun searchSlackChannels(
        trx: Transaction? = null,
        slackTeamId: SlackTeamId,
        config: SlackChannelsSearchConfig,
        slackMemberIds: List<MemberId>? = null,
    ): List<SlackChannel> = suspendedTransaction(trx) {
        val likeQueries = config.queries.mapNotNull { it.toLikeQueryOrNullIfEmpty() }.takeIf { it.isNotEmpty() }

        // We still want to return results if query set is empty
        val likeCondition = likeQueries?.let {
            OrOp(
                likeQueries.map { likeQuery -> SlackChannelModel.name like likeQuery },
            )
        }

        val sortOrder = sortOrderExpression(config)

        SlackChannelModel
            .join(
                otherTable = SlackChannelMemberModel,
                joinType = LEFT,
                onColumn = SlackChannelModel.id,
                otherColumn = SlackChannelMemberModel.slackChannel,
            ) {
                slackMemberIds?.let { SlackChannelMemberModel.member inList slackMemberIds } ?: Op.FALSE
            }
            .join(
                otherTable = SlackChannelPreferencesModel,
                joinType = LEFT,
                onColumn = SlackChannelModel.id,
                otherColumn = SlackChannelPreferencesModel.slackChannel,
            )
            .join(
                otherTable = DataSourcePresetModel,
                joinType = LEFT,
                onColumn = SlackChannelPreferencesModel.dataSourcePreset,
                otherColumn = DataSourcePresetModel.id,
            )
            .select(
                SlackChannelModel.columns + listOf(
                    SlackChannelPreferencesModel.id,
                    SlackChannelPreferencesModel.autoAnswerMode,
                    DataSourcePresetModel.name,
                    HAS_PREFERENCES_SORT_EXPR.alias("has_prefs_sort"),
                ),
            )
            .whereAll(
                SlackChannelModel.slackTeam eq slackTeamId,
                SlackChannelModel.isChannel eq true,
                likeCondition,
                AnyOp(
                    IS_PUBLIC_CLAUSE,
                    slackMemberIds?.let { SlackChannelMemberModel.member inList slackMemberIds },
                ),
            )
            .limit(config.pagination?.limit ?: SlackChannelsSearchConfig.DEFAULT_SEARCH_LIMIT)
            .withDistinct(true)
            .orderBy(*sortOrder)
            .map {
                it.toSlackChannel()
            }
    }

    private fun sortOrderExpression(config: SlackChannelsSearchConfig): Array<Pair<Expression<*>, SortOrder>> {
        val direction = when (config.sortDirection) {
            SortDirection.descending -> SortOrder.DESC_NULLS_LAST
            SortDirection.ascending -> SortOrder.ASC_NULLS_LAST
        }

        return when (config.sortOrder) {
            SlackChannelSortOrder.memberCount ->
                arrayOf(
                    SlackChannelModel.memberCount to direction,
                    SlackChannelModel.isShared to SortOrder.ASC_NULLS_LAST,
                    SlackChannelModel.name to SortOrder.ASC,
                )

            SlackChannelSortOrder.channelName -> arrayOf(SlackChannelModel.name to direction)

            SlackChannelSortOrder.autoResponseMode -> arrayOf(
                HAS_PREFERENCES_SORT_EXPR to SortOrder.ASC,
                SlackChannelPreferencesModel.autoAnswerMode to direction,
                SlackChannelModel.name to direction,
                SlackChannelModel.id to direction,
            )

            SlackChannelSortOrder.dataSourcePresetPreference -> {
                val nullsFirstDirection = when (config.sortDirection) {
                    SortDirection.descending -> SortOrder.DESC_NULLS_LAST
                    SortDirection.ascending -> SortOrder.ASC_NULLS_FIRST
                }
                arrayOf(
                    HAS_PREFERENCES_SORT_EXPR to SortOrder.ASC,
                    DataSourcePresetModel.name to nullsFirstDirection,
                    SlackChannelModel.name to direction,
                    SlackChannelModel.id to direction,
                )
            }
        }
    }

    suspend fun findBySlackTeam(
        trx: Transaction? = null,
        slackTeamId: SlackTeamId,
        limit: Int? = null,
        offset: Long? = null,
        additionalWhereClause: Op<Boolean>? = null,
        orderBy: Expression<*>? = null,
    ): List<SlackChannel> = suspendedTransaction(trx) {
        SlackChannelModel
            .selectAll()
            .whereAll(
                SlackChannelModel.slackTeam eq slackTeamId,
                additionalWhereClause,
            )
            .apply {
                limit?.also { limit(it) }
                offset?.also { offset(it) }
                orderBy?.also { orderBy(orderBy) }
            }
            .map {
                it.toSlackChannel()
            }
    }

    suspend fun setIsMember(
        trx: Transaction? = null,
        slackTeamId: SlackTeamId,
        slackChannelIds: List<SlackChannelId>,
    ) = suspendedTransaction(trx) {
        SlackChannelModel.update(
            {
                (SlackChannelModel.slackTeam eq slackTeamId) and (SlackChannelModel.id inList slackChannelIds)
            },
        ) {
            it[this.isMember] = true
        }

        SlackChannelModel.update(
            {
                (SlackChannelModel.slackTeam eq slackTeamId) and (SlackChannelModel.id notInList slackChannelIds)
            },
        ) {
            it[this.isMember] = false
        }
    }

    suspend fun isSlackChannelPrivate(
        trx: Transaction? = null,
        slackChannelId: SlackChannelId,
    ): Boolean? = suspendedTransaction(trx) {
        SlackChannelModel
            .select(SlackChannelModel.isPrivate)
            .where {
                SlackChannelModel.id eq slackChannelId
            }
            .map { it[SlackChannelModel.isPrivate] }
            .firstOrNull()
    }

    suspend fun getFirstMessageTsBySlackExternalChannelId(
        trx: Transaction? = null,
        slackExternalChannelId: String,
    ): String? = suspendedTransaction(trx) {
        SlackChannelModel
            .select(SlackChannelModel.firstMessageTs)
            .where {
                SlackChannelModel.slackExternalChannelId eq slackExternalChannelId
            }
            .limit(1)
            .map { it[SlackChannelModel.firstMessageTs] }
            .firstOrNull()
    }

    @Suppress("CyclomaticComplexMethod")
    suspend fun upsert(
        trx: Transaction? = null,
        slackTeamId: SlackTeamId,
        name: String,
        slackExternalChannelId: String,
        isChannel: Boolean?,
        isShared: Boolean?,
        isExtShared: Boolean?,
        isOrgShared: Boolean?,
        isPrivate: Boolean?,
        isArchived: Boolean?,
        isIm: Boolean?,
        isMpim: Boolean?,
        isMember: Boolean?,
        conversationHostId: String?,
        memberCount: Int?,
        creator: String?,
        user: String?,
        firstMessageTs: String?,
    ) = suspendedTransaction(trx) {
        SlackChannelModel.upsertReturning(
            keys = arrayOf(SlackChannelModel.slackTeam, SlackChannelModel.slackExternalChannelId),
            onUpdateExclude = SlackChannelModel.columns - setOfNotNull(
                SlackChannelModel.name,
                isChannel?.let { SlackChannelModel.isChannel },
                isShared?.let { SlackChannelModel.isShared },
                isExtShared?.let { SlackChannelModel.isExtShared },
                isOrgShared?.let { SlackChannelModel.isOrgShared },
                isPrivate?.let { SlackChannelModel.isPrivate },
                isArchived?.let { SlackChannelModel.isArchived },
                isIm?.let { SlackChannelModel.isIm },
                isMpim?.let { SlackChannelModel.isMpim },
                isMember?.let { SlackChannelModel.isMember },
                memberCount?.let { SlackChannelModel.memberCount },
                firstMessageTs?.let { SlackChannelModel.firstMessageTs },
                conversationHostId?.let { SlackChannelModel.conversationHostId },
                creator?.let { SlackChannelModel.creator },
                user?.let { SlackChannelModel.user },
            ),
        ) { insertStatement ->
            insertStatement[slackTeam] = slackTeamId
            insertStatement[this.slackExternalChannelId] = slackExternalChannelId
            insertStatement[this.name] = name
            insertStatement[this.isChannel] = isChannel
            insertStatement[this.isShared] = isShared
            insertStatement[this.isExtShared] = isExtShared
            insertStatement[this.isOrgShared] = isOrgShared
            insertStatement[this.isPrivate] = isPrivate
            insertStatement[this.isArchived] = isArchived
            insertStatement[this.isIm] = isIm
            insertStatement[this.isMpim] = isMpim
            insertStatement[this.isMember] = isMember
            insertStatement[this.memberCount] = memberCount
            insertStatement[this.firstMessageTs] = firstMessageTs
            insertStatement[this.conversationHostId] = conversationHostId
            insertStatement[this.creator] = creator
            insertStatement[this.user] = user
        }.let {
            SlackChannelDAO.wrapRow(it.first())
        }
    }
}

data class SlackChannelDaos(
    val slackChannel: SlackChannelDAO,
    val org: OrgDAO,
    val installation: InstallationDAO,
    val slackTeam: SlackTeamDAO,
)
