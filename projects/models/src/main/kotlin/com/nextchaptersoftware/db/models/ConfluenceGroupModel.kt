@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object ConfluenceGroupModel : ServiceModel<ConfluenceGroupId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val confluenceSite = reference(
        name = "confluence_site",
        foreign = ConfluenceSiteModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val groupId = text(name = "groupId")

    init {
        uniqueIndex(confluenceSite, groupId)
    }
}

fun ResultRow.toConfluenceGroup(alias: String? = null) = ConfluenceGroupDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toConfluenceGroupOrNull(alias: String? = null) = ConfluenceGroupDAO.wrapRowOrNull(this, alias)?.asDataModel()

class ConfluenceGroupDAO(id: EntityID<ConfluenceGroupId>) : EntityExtensions<ConfluenceGroup, ConfluenceGroupId>(id) {
    companion object : EntityClassExtensions<ConfluenceGroupId, ConfluenceGroupDAO>(ConfluenceGroupModel)

    override var createdAt by ConfluenceGroupModel.createdAt
    override var modifiedAt by ConfluenceGroupModel.modifiedAt

    var confluenceSite by ConfluenceSiteDAO referencedOn ConfluenceGroupModel.confluenceSite
    var groupId by ConfluenceGroupModel.groupId

    override fun asDataModel() = readValues.let { row ->
        ConfluenceGroup(
            id = row[ConfluenceGroupModel.id].value,
            createdAt = row[ConfluenceGroupModel.createdAt],
            modifiedAt = row[ConfluenceGroupModel.modifiedAt],
            confluenceSiteId = row[ConfluenceGroupModel.confluenceSite].value,
            groupId = row[ConfluenceGroupModel.groupId],
        )
    }
}

object ConfluenceGroupIdConverter : ValueIdConverter<ConfluenceGroupId> {
    override val factory = ::ConfluenceGroupId
    override val extract = ConfluenceGroupId::value
}

internal object ConfluenceGroupIdSerializer : ValueIdSerializer<ConfluenceGroupId>(
    serialName = "ConfluenceGroupId",
    converter = ConfluenceGroupIdConverter,
)

@JvmInline
@Serializable(with = ConfluenceGroupIdSerializer::class)
value class ConfluenceGroupId(val value: UUID) : ValueId, Comparable<ConfluenceGroupId> {

    companion object : ValueIdClass<ConfluenceGroupId>(
        converter = ConfluenceGroupIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ConfluenceGroupId) = value.compareTo(other.value)
}

data class ConfluenceGroup(
    val id: ConfluenceGroupId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val confluenceSiteId: ConfluenceSiteId,
    val groupId: String,
)
