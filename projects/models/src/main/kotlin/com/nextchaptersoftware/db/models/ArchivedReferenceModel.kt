@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object ArchivedReferenceModel : ServiceModel<ArchivedReferenceId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    val org = reference(
        name = "org",
        foreign = OrgModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val archivedBy = reference(
        name = "archivedByOrgMember",
        foreign = OrgMemberModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val archivedReason = enumerationByDbOrdinal(
        name = "archivedReason",
        klass = ArchivedReason::class,
    ).nullable()

    val comment = text(
        name = "comment",
    ).nullable()

    // region the thread+message where this reference was obsoleted (null if the thread or pull request was directly archived)
    val thread = optReference(
        name = "thread",
        foreign = ThreadModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val message = optReference(
        name = "message",
        foreign = MessageModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()
    // endregion

    //region references (exactly one of these should be set)
    val threadReference = optReference(
        name = "threadReference",
        foreign = ThreadModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val pullRequestReference = optReference(
        name = "pullRequestReference",
        foreign = PullRequestModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    ).index()

    val documentReferenceId = text(name = "documentReferenceId").nullable().index()
    // endregion
}

fun ResultRow.toArchivedReference(alias: String? = null) = ArchivedReferenceDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toArchivedReferenceOrNull(alias: String? = null) = ArchivedReferenceDAO.wrapRowOrNull(this, alias)?.asDataModel()

class ArchivedReferenceDAO(id: EntityID<ArchivedReferenceId>) : EntityExtensions<ArchivedReference, ArchivedReferenceId>(id) {
    companion object : EntityClassExtensions<ArchivedReferenceId, ArchivedReferenceDAO>(ArchivedReferenceModel)

    override var createdAt by ArchivedReferenceModel.createdAt
    override var modifiedAt by ArchivedReferenceModel.modifiedAt

    var org by OrgDAO referencedOn ArchivedReferenceModel.org
    var archivedBy by OrgMemberDAO referencedOn ArchivedReferenceModel.archivedBy
    var thread by ThreadDAO optionalReferencedOn ArchivedReferenceModel.thread
    var message by MessageDAO optionalReferencedOn ArchivedReferenceModel.message
    var archivedReason by ArchivedReferenceModel.archivedReason
    var comment by ArchivedReferenceModel.comment

    var threadReference by ThreadDAO optionalReferencedOn ArchivedReferenceModel.threadReference
    var pullRequestReference by PullRequestDAO optionalReferencedOn ArchivedReferenceModel.pullRequestReference
    var documentReferenceId by ArchivedReferenceModel.documentReferenceId

    override fun asDataModel() = readValues.let {
        ArchivedReference(
            id = it[ArchivedReferenceModel.id].value,
            orgId = it[ArchivedReferenceModel.org].value,
            createdAt = it[ArchivedReferenceModel.createdAt],
            modifiedAt = it[ArchivedReferenceModel.modifiedAt],
            threadId = it[ArchivedReferenceModel.thread]?.value,
            messageId = it[ArchivedReferenceModel.message]?.value,
            archivedBy = it[ArchivedReferenceModel.archivedBy].value,
            archivedReason = it[ArchivedReferenceModel.archivedReason],
            comment = it[ArchivedReferenceModel.comment],
            threadReferenceId = it[ArchivedReferenceModel.threadReference]?.value,
            pullRequestReferenceId = it[ArchivedReferenceModel.pullRequestReference]?.value,
            documentReferenceId = it[ArchivedReferenceModel.documentReferenceId],
        )
    }
}

object ArchivedReferenceIdConverter : ValueIdConverter<ArchivedReferenceId> {
    override val factory = ::ArchivedReferenceId
    override val extract = ArchivedReferenceId::value
}

internal object ArchivedReferenceIdSerializer : ValueIdSerializer<ArchivedReferenceId>(
    serialName = "ArchivedReferenceId",
    converter = ArchivedReferenceIdConverter,
)

@JvmInline
@Serializable(with = ArchivedReferenceIdSerializer::class)
value class ArchivedReferenceId(val value: UUID) : ValueId, Comparable<ArchivedReferenceId> {

    companion object : ValueIdClass<ArchivedReferenceId>(
        converter = ArchivedReferenceIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: ArchivedReferenceId) = value.compareTo(other.value)
}

data class ArchivedReference(
    val id: ArchivedReferenceId,
    val orgId: OrgId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val archivedBy: OrgMemberId,
    val archivedReason: ArchivedReason?,
    val comment: String?,
    val threadId: ThreadId?,
    val messageId: MessageId?,
    val threadReferenceId: ThreadId?,
    val pullRequestReferenceId: PullRequestId?,
    val documentReferenceId: String?,
)
