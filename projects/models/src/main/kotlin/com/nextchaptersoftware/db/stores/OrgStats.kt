package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.models.OrgId

data class OrgStats(
    val orgId: OrgId,
    val joined: Int, // primary team members that have joined
    val notYetJoined: Int, // primary team members that have not yet joined
    val notYetJoinedButHaveUsedUnblocked: Int, // primary *and* non-primary team members that have not yet joined and have used the product
    val totalMembers: Int, // total number of primary team members
    val totalPrivateQuestions: Int,
    val totalQuestions: Int,
    val questionsFeedback: QuestionsFeedback,
) {
    val activeMembers: Int
        get() = joined + notYetJoinedButHaveUsedUnblocked
}
