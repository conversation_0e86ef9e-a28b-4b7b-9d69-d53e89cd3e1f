package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.NotionObjectAccessModel
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import java.util.UUID
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.insertIgnore

class NotionObjectAccessStore internal constructor() {
    suspend fun upsert(
        identityId: IdentityId,
        externalNotionId: UUID,
    ) {
        suspendedTransaction {
            NotionObjectAccessModel.insertIgnore {
                it[NotionObjectAccessModel.identity] = identityId
                it[NotionObjectAccessModel.externalNotionId] = externalNotionId
            }
        }
    }

    suspend fun delete(
        externalNotionId: UUID,
    ) {
        suspendedTransaction {
            NotionObjectAccessModel.deleteWhere {
                NotionObjectAccessModel.externalNotionId eq externalNotionId
            }
        }
    }

    suspend fun delete(
        identityId: IdentityId,
        externalNotionId: UUID,
    ) {
        suspendedTransaction {
            NotionObjectAccessModel.deleteWhere {
                (NotionObjectAccessModel.identity eq identityId) and (NotionObjectAccessModel.externalNotionId eq externalNotionId)
            }
        }
    }

    suspend fun filterExists(
        identityId: IdentityId,
        externalNotionIds: Set<UUID>,
    ): List<UUID> {
       return suspendedTransaction {
            NotionObjectAccessModel
                .select(NotionObjectAccessModel.externalNotionId)
                .whereAll(
                    NotionObjectAccessModel.identity eq identityId,
                    NotionObjectAccessModel.externalNotionId inList externalNotionIds,
                )
                .map { it[NotionObjectAccessModel.externalNotionId] }
        }
    }

    suspend fun findIdentityIdsByExternalNotionId(
        externalNotionId: UUID,
    ): List<IdentityId> {
        return suspendedTransaction {
            NotionObjectAccessModel
                .select(NotionObjectAccessModel.identity)
                .where { NotionObjectAccessModel.externalNotionId eq externalNotionId }
                .map { it[NotionObjectAccessModel.identity].value }
        }
    }
}
