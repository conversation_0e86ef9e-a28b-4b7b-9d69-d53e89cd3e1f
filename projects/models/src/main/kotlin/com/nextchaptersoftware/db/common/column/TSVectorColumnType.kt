package com.nextchaptersoftware.db.common.column

import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.ColumnType
import org.jetbrains.exposed.sql.Table

typealias TSVector = Map<String, List<Int>>

class TSVectorColumnType<TSVector> : ColumnType<TSVector>() {
    override fun sqlType(): String = "TSVECTOR"

    override fun valueFromDB(value: Any): TSVector {
        return parseTSVector(value.toString())
    }

    @Suppress("UNCHECKED_CAST")
    private fun parseTSVector(tsvector: String): TSVector {
        val result: TSVector = emptyMap<String, List<Int>>() as TSVector

        tsvector.split(" ").forEach { termWithPositions ->
            val parts = termWithPositions.split(":")
            require(parts.size == 2) { "Invalid format for term: $termWithPositions" }

            val term = parts[0].trim('\'')
            val positions = parts[1].split(",").map { it.toInt() }
            term to positions
        }

        return result
    }
}

class TSQueryColumnType : ColumnType<String>() {
    override fun sqlType(): String = "TSQUERY"

    override fun valueFromDB(value: Any): String {
        return value.toString()
    }
}

fun Table.tsvector(name: String): Column<TSVector> = registerColumn(name, TSVectorColumnType())
