package com.nextchaptersoftware.db.common.functions

import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.CustomFunction
import org.jetbrains.exposed.sql.DoubleColumnType
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.IColumnType
import org.jetbrains.exposed.sql.QueryBuilder

/**
 * https://www.postgresql.org/docs/current/functions-datetime.html#FUNCTIONS-DATETIME-EXTRACT
 */
class Extract<R : Any>(
    private val field: String,
    private val source: Expression<Instant>,
    columnType: IColumnType<R>,
) : CustomFunction<R>(
    functionName = "extract",
    columnType = columnType,
) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        append("EXTRACT(")
        append(field)
        append(" FROM ")
        append(source)
        append(")")
    }
}

fun Expression<Instant>.extractEpoch() = Extract(
    field = "epoch",
    source = this,
    columnType = DoubleColumnType(),
)
