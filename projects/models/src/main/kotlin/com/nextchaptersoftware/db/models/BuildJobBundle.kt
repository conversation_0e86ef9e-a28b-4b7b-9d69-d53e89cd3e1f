package com.nextchaptersoftware.db.models

import org.jetbrains.exposed.sql.ResultRow

data class BuildJobBundle(
    val scmTeam: ScmTeam,
    val ciInstallation: Installation,
    val repo: Repo,
    val pullRequest: PullRequest,
    val build: Build,
    val job: BuildJob,
)

fun ResultRow.toBuildJobBundle() = BuildJobBundle(
    scmTeam = toScmTeam(),
    ciInstallation = toInstallation(),
    repo = toRepo(),
    pullRequest = toPullRequest(),
    build = toBuild(),
    job = toBuildJob(),
)
