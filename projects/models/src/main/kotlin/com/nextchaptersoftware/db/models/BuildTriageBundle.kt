package com.nextchaptersoftware.db.models

import org.jetbrains.exposed.sql.ResultRow

data class BuildTriageBundle(
    val org: Org,
    val scmTeam: ScmTeam,
    val repo: Repo,
    val pullRequest: PullRequest,
    val pullRequestCreator: Identity,
    val build: Build,
    val triage: BuildTriage,
    val triageReviewer: Identity?,
    // aggregates
    val reactions: List<BuildTriageReaction>? = null,
)

fun ResultRow.toBuildTriageBundle() = BuildTriageBundle(
    org = toOrg(),
    scmTeam = toScmTeam(),
    repo = toRepo(),
    pullRequest = toPullRequest(),
    pullRequestCreator = toIdentity(alias = "pullRequestCreator"),
    build = toBuild(),
    triage = toBuildTriage(),
    triageReviewer = toIdentityOrNull(alias = "triageReviewer"),
)
