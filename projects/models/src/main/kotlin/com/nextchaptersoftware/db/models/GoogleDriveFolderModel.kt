@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object GoogleDriveFolderModel : ServiceModel<GoogleDriveFolderId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {
    // The ultimate parent of this folder
    val googleDriveFile = reference(
        name = "googleDriveFile",
        foreign = GoogleDriveFileModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    val googleDriveId = text(name = "googleDriveId")
    val parentId = text(name = "parentId")
    val name = text(name = "name")

    init {
        uniqueIndex(googleDriveFile, googleDriveId)
    }
}

fun ResultRow.toGoogleDriveFolder(alias: String? = null) = GoogleDriveFolderDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toGoogleDriveFolderOrNull(alias: String? = null) = GoogleDriveFolderDAO.wrapRowOrNull(this, alias)?.asDataModel()

class GoogleDriveFolderDAO(id: EntityID<GoogleDriveFolderId>) : EntityExtensions<GoogleDriveFolder, GoogleDriveFolderId>(id) {
    companion object : EntityClassExtensions<GoogleDriveFolderId, GoogleDriveFolderDAO>(GoogleDriveFolderModel)

    override var createdAt by GoogleDriveFolderModel.createdAt
    override var modifiedAt by GoogleDriveFolderModel.modifiedAt

    var googleDriveFile by GoogleDriveFileDAO referencedOn GoogleDriveFolderModel.googleDriveFile
    var googleDriveId by GoogleDriveFolderModel.googleDriveId
    var parentId by GoogleDriveFolderModel.parentId
    var name by GoogleDriveFolderModel.name

    override fun asDataModel() = readValues.let { row ->
        GoogleDriveFolder(
            id = row[GoogleDriveFolderModel.id].value,
            createdAt = row[GoogleDriveFolderModel.createdAt],
            modifiedAt = row[GoogleDriveFolderModel.modifiedAt],
            googleDriveFileId = row[GoogleDriveFolderModel.googleDriveFile].value,
            googleDriveId = row[GoogleDriveFolderModel.googleDriveId],
            parentId = row[GoogleDriveFolderModel.parentId],
            name = row[GoogleDriveFolderModel.name],
        )
    }
}

object GoogleDriveFolderIdConverter : ValueIdConverter<GoogleDriveFolderId> {
    override val factory = ::GoogleDriveFolderId
    override val extract = GoogleDriveFolderId::value
}

internal object GoogleDriveFolderIdSerializer : ValueIdSerializer<GoogleDriveFolderId>(
    serialName = "GoogleDriveFolderId",
    converter = GoogleDriveFolderIdConverter,
)

@JvmInline
@Serializable(with = GoogleDriveFolderIdSerializer::class)
value class GoogleDriveFolderId(val value: UUID) : ValueId, Comparable<GoogleDriveFolderId> {

    companion object : ValueIdClass<GoogleDriveFolderId>(
        converter = GoogleDriveFolderIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: GoogleDriveFolderId) = value.compareTo(other.value)
}

data class GoogleDriveFolder(
    val id: GoogleDriveFolderId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val googleDriveFileId: GoogleDriveFileId,
    val googleDriveId: String,
    val parentId: String,
    val name: String,
)
