package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.CodaDoc
import com.nextchaptersoftware.db.models.CodaDocDAO
import com.nextchaptersoftware.db.models.CodaDocId
import com.nextchaptersoftware.db.models.CodaDocIngestionStatus
import com.nextchaptersoftware.db.models.CodaDocModel
import com.nextchaptersoftware.db.models.CodaOrganizationId
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.upsert

class CodaDocStore internal constructor() {
    companion object {
        private const val BATCH_SIZE = 20
    }

    suspend fun findById(
        id: CodaDocId,
    ): CodaDoc? {
        return suspendedTransaction {
            CodaDocDAO.findById(id)?.asDataModel()
        }
    }

    suspend fun findByIds(
        organizationIds: Set<CodaOrganizationId>,
        ids: Set<CodaDocId>,
    ): List<CodaDoc> {
        return suspendedTransaction {
            CodaDocDAO
                .find { (CodaDocModel.codaOrganization inList organizationIds) and (CodaDocModel.id inList ids) }
                .map { it.asDataModel() }
        }
    }

    suspend fun findByOrganizationIdAndDocId(
        organizationId: CodaOrganizationId,
        docId: String,
    ): CodaDoc? {
        return suspendedTransaction {
            CodaDocDAO
                .find { (CodaDocModel.codaOrganization eq organizationId) and (CodaDocModel.docId eq docId) }
                .limit(1)
                .firstOrNull()
                ?.asDataModel()
        }
    }

    private suspend fun upsert(
        codaOrganizationId: CodaOrganizationId,
        docId: String,
        folderId: String,
        owner: String,
        status: CodaDocIngestionStatus?,
        priority: Int?,
        updatedAt: Instant? = null,
    ) {
        suspendedTransaction {
            CodaDocModel.upsert(
                keys = arrayOf(CodaDocModel.codaOrganization, CodaDocModel.docId),
                onUpdateExclude = CodaDocModel.columns - setOfNotNull(
                    CodaDocModel.folderId,
                    CodaDocModel.owner,
                    CodaDocModel.status, // always set this value even if null
                    CodaDocModel.priority, // always set this value even if null
                    updatedAt?.let { CodaDocModel.updatedAt },
                ),
            ) { insertStatement ->
                insertStatement[this.codaOrganization] = codaOrganizationId
                insertStatement[this.docId] = docId
                insertStatement[this.folderId] = folderId
                insertStatement[this.owner] = owner
                insertStatement[this.status] = status
                insertStatement[this.priority] = priority
                updatedAt?.let { insertStatement[this.updatedAt] = it }
            }
        }
    }

    suspend fun markForIngestion(
        codaOrganizationId: CodaOrganizationId,
        docId: String,
        folderId: String,
        owner: String,
        priority: Int?,
    ) {
        upsert(
            codaOrganizationId = codaOrganizationId,
            docId = docId,
            folderId = folderId,
            owner = owner,
            status = null,
            priority = priority,
        )
    }

    suspend fun markAsIngested(
        codaOrganizationId: CodaOrganizationId,
        docId: String,
        folderId: String,
        owner: String,
        updatedAt: Instant,
    ) {
        upsert(
            codaOrganizationId = codaOrganizationId,
            docId = docId,
            folderId = folderId,
            owner = owner,
            status = CodaDocIngestionStatus.Ingested,
            priority = null,
            updatedAt = updatedAt,
        )
    }

    suspend fun markAsErrored(
        codaOrganizationId: CodaOrganizationId,
        docId: String,
    ) {
        suspendedTransaction {
            CodaDocDAO.find {
                AllOp(
                    CodaDocModel.codaOrganization eq codaOrganizationId,
                    CodaDocModel.docId eq docId,
                )
            }.limit(1).firstOrNull()?.also {
                it.status = CodaDocIngestionStatus.Error
                it.priority = null
            }
        }
    }

    suspend fun getNextDocsToIngest(
        codaOrganizationId: CodaOrganizationId,
        limit: Int = BATCH_SIZE,
    ): List<CodaDoc> {
        return suspendedTransaction {
            CodaDocDAO.find {
                AllOp(
                    CodaDocModel.codaOrganization eq codaOrganizationId,
                    CodaDocModel.status.isNull(),
                )
            }
                .limit(limit)
                .orderBy(CodaDocModel.priority to SortOrder.DESC_NULLS_LAST)
                .map { it.asDataModel() }
        }
    }

    suspend fun delete(
        codaOrganizationId: CodaOrganizationId,
        docId: String,
    ) {
        suspendedTransaction {
            CodaDocDAO.find {
                AllOp(
                    CodaDocModel.codaOrganization eq codaOrganizationId,
                    CodaDocModel.docId eq docId,
                )
            }.limit(1).firstOrNull()?.delete()
        }
    }

    suspend fun updatedAt(
        codaOrganizationId: CodaOrganizationId,
        docId: String,
    ): Instant? {
        return suspendedTransaction {
            CodaDocModel
                .select(CodaDocModel.updatedAt)
                .whereAll(
                    CodaDocModel.codaOrganization eq codaOrganizationId,
                    CodaDocModel.docId eq docId,
                )
                .limit(1)
                .firstOrNull()
                ?.let { it[CodaDocModel.updatedAt] }
        }
    }
}
