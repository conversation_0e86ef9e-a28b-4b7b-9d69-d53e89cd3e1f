package com.nextchaptersoftware.db.common.config

import software.amazon.awssdk.regions.Region

data class AwsDbConfigProvider(
    override val applicationName: String,
    override val userName: String,
    override val dbName: String,
    override val hostName: String,
    override val port: Int,
    override val region: String,
    override val reWriteBatchedInserts: Boolean,
    override val enableSqlLogging: Boolean,
    override val initializeSchemas: Boolean,
    override val connectionPool: DbConnectionPoolConfig?,
) : DbConfigProvider {
    override val password: String by AwsStsPasswordDelegate(
        userName = userName,
        hostName = hostName,
        port = port,
        region = Region.of(region),
    )

    override val url by StandardJdbcUrlProviderDelegate(
        hostName = hostName,
        port = port,
        dbName = dbName,
    )

    override val connectionProperties by StandardConnectionPropertiesDelegate(
        applicationName = applicationName,
        userName = userName,
        password = password,
        reWriteBatchedInserts = reWriteBatchedInserts,
    )
}
