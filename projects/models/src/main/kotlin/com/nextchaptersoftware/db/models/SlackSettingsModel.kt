@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.db.models

import com.nextchaptersoftware.db.common.EntityClassExtensions
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.serialization.Serializable
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.ReferenceOption
import org.jetbrains.exposed.sql.ResultRow

object SlackSettingsModel : ServiceModel<SlackSettingsId>(
    idColumnType = ValueIdConverter.idColumnType(),
) {

    /** The organization these settings belongs to */
    val org = reference(
        name = "org",
        foreign = OrgModel,
        onDelete = ReferenceOption.CASCADE,
        onUpdate = ReferenceOption.CASCADE,
    )

    /**
     * Slack user acceptance testing.
     *
     * Do not remove this in lieu of [com.nextchaptersoftware.models.clientconfig.ClientCapabilityType.SlackUserAcceptanceTesting].
     * There are instances where we want to disable frontend features (such as auto-answer, etc.)
     * while still enabling the regular flow for authentication.
     *
     * In particular for slack penetration testing.
     */
    val slackUserAcceptanceTesting = bool("slackUserAcceptanceTesting").nullable().index()

    /** Question & Answers from teams that want to validate Unblocked */
    val slackQAValidationChannel = text("slackQAValidationChannel").nullable()

    /** Toggle to control whether slack bot disclaimers are posted when bot is invited or when user joins a channel with bot invited */
    val slackBotDisclaimer = bool("slackBotDisclaimer").nullable()

    /** Toggle to control where slack bot will output suggested questions in addition to the answer */
    val slackBotSuggestedQuestions = bool("slackBotSuggestedQuestions").nullable()

    /** Wide slack thread ingestion override specifically for legacy orgs */
    val wideSlackThreadIngestion = bool("wideSlackThreadIngestion").nullable()

    /** Disable auto response eval filters */
    val disableAutoResponseEvalFilters = bool("disableAutoResponseEvalFilters").nullable()

    /** Enable progress messages during Q&A responses */
    val enableProgressMessages = bool("enableProgressMessages").nullable()

    /**
     * Disable forcing the user to login after an initial set of questions
     * @see com.nextchaptersoftware.slack.services.SlackUserConnectService
     */
    val disableForcedLogin = bool("disableForcedLogin").nullable()

    /**
     * Shadow channel for auto-response approvals.
     */
    val shadowApprovalChannelId = text("shadowApprovalChannelId").nullable()

    init {
        uniqueIndex(org)
    }
}

fun ResultRow.toSlackSettings(alias: String? = null) = SlackSettingsDAO.wrapRow(this, alias).asDataModel()

fun ResultRow.toSlackSettingsOrNull(alias: String? = null) = SlackSettingsDAO.wrapRowOrNull(this, alias)?.asDataModel()

class SlackSettingsDAO(id: EntityID<SlackSettingsId>) : EntityExtensions<SlackSettings, SlackSettingsId>(id) {

    companion object : EntityClassExtensions<SlackSettingsId, SlackSettingsDAO>(SlackSettingsModel)

    override var createdAt by SlackSettingsModel.createdAt
    override var modifiedAt by SlackSettingsModel.modifiedAt

    var org by OrgDAO referencedOn SlackSettingsModel.org

    var slackBotDisclaimer by SlackSettingsModel.slackBotDisclaimer
    var slackBotSuggestedQuestions by SlackSettingsModel.slackBotSuggestedQuestions
    var slackQAValidationChannel by SlackSettingsModel.slackQAValidationChannel
    var wideSlackThreadIngestion by SlackSettingsModel.wideSlackThreadIngestion
    var disableAutoResponseEvalFilters by SlackSettingsModel.disableAutoResponseEvalFilters
    var disableForcedLogin by SlackSettingsModel.disableForcedLogin
    var shadowApprovalChannelId by SlackSettingsModel.shadowApprovalChannelId
    var enableProgressMessages by SlackSettingsModel.enableProgressMessages

    override fun asDataModel() = readValues.let {
        SlackSettings(
            id = it[SlackSettingsModel.id].value,
            orgId = it[SlackSettingsModel.org].value,
            createdAt = it[SlackSettingsModel.createdAt],
            modifiedAt = it[SlackSettingsModel.modifiedAt],
            slackUserAcceptanceTesting = it[SlackSettingsModel.slackUserAcceptanceTesting] ?: false,
            slackBotDisclaimer = it[SlackSettingsModel.slackBotDisclaimer],
            slackBotSuggestedQuestions = it[SlackSettingsModel.slackBotSuggestedQuestions],
            slackQAValidationChannel = it[SlackSettingsModel.slackQAValidationChannel],
            wideSlackThreadIngestion = it[SlackSettingsModel.wideSlackThreadIngestion] ?: false,
            disableAutoResponseEvalFilters = it[SlackSettingsModel.disableAutoResponseEvalFilters] ?: false,
            enableProgressMessages = it[SlackSettingsModel.enableProgressMessages] ?: false,
            disableForcedLogin = it[SlackSettingsModel.disableForcedLogin] ?: false,
            shadowApprovalChannelId = it[SlackSettingsModel.shadowApprovalChannelId],
        )
    }
}

object SlackSettingsIdConverter : ValueIdConverter<SlackSettingsId> {
    override val factory = ::SlackSettingsId
    override val extract = SlackSettingsId::value
}

internal object SlackSettingsIdSerializer : ValueIdSerializer<SlackSettingsId>(
    serialName = "SlackSettingsId",
    converter = SlackSettingsIdConverter,
)

@JvmInline
@Serializable(with = SlackSettingsIdSerializer::class)
value class SlackSettingsId(val value: UUID) : ValueId, Comparable<SlackSettingsId> {

    companion object : ValueIdClass<SlackSettingsId>(
        converter = SlackSettingsIdConverter,
    )

    override fun toString() = value.toString()

    override fun compareTo(other: SlackSettingsId) = value.compareTo(other.value)
}

data class SlackSettings(
    val id: SlackSettingsId,
    val orgId: OrgId,
    val createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    val slackUserAcceptanceTesting: Boolean,
    val slackBotDisclaimer: Boolean?,
    val slackBotSuggestedQuestions: Boolean?,
    val slackQAValidationChannel: String?,
    val wideSlackThreadIngestion: Boolean,
    val disableAutoResponseEvalFilters: Boolean,
    val enableProgressMessages: Boolean,
    val disableForcedLogin: Boolean,
    val shadowApprovalChannelId: String?,
) {
    companion object {
        fun getDefault(orgId: OrgId) = SlackSettings(
            id = SlackSettingsId.random(),
            orgId = orgId,
            slackUserAcceptanceTesting = false,
            slackQAValidationChannel = null,
            slackBotDisclaimer = false,
            slackBotSuggestedQuestions = false,
            wideSlackThreadIngestion = false,
            disableAutoResponseEvalFilters = false,
            enableProgressMessages = false,
            disableForcedLogin = false,
            shadowApprovalChannelId = null,
        )
    }
}
