package com.nextchaptersoftware.ingest.notion.jobs

import com.nextchaptersoftware.notion.ingestion.services.NotionObjectAccessService
import com.nextchaptersoftware.service.BackgroundJob

class NotionObjectAccessSyncJob(
    private val notionObjectAccessService: NotionObjectAccessService,
) : BackgroundJob {
    override val name: String = javaClass.simpleName

    override suspend fun run() {
        notionObjectAccessService.run()
    }
}
