package com.nextchaptersoftware.scmservice.handlers.github.services

import com.nextchaptersoftware.ghdiscussions.services.GitHubDiscussionService
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.github.models.GitHubDiscussionCommentEvent
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class GitHubDiscussionCommentEventService(
    private val teamAndRepoProvider: TeamAndRepoProvider,
    private val gitHubDiscussionService: GitHubDiscussionService,
) {
    suspend fun process(
        scm: Scm,
        event: GitHubDiscussionCommentEvent,
    ): Unit = withLoggingContextAsync(
        "ownerExternalId" to event.ownerExternalId,
        "repoExternalId" to event.repoExternalId,
        "discussionNumber" to event.discussion.number,
    ) {
        val (scmTeam, repo) = teamAndRepoProvider.get(
            scm = scm,
            ownerExternalId = event.ownerExternalId,
            repoExternalId = event.repoExternalId,
        ) ?: run {
            LOGGER.debugAsync("ownerExternalId" to event.ownerExternalId, "repoExternalId" to event.repoExternalId) {
                "Team and repo not found"
            }
            return@withLoggingContextAsync
        }

        LOGGER.debugAsync(
            "scmTeamId" to scmTeam.id,
            "repoId" to repo.id,
        ) {
            "Handling discussion comment event"
        }

        when (event.action) {
            GitHubDiscussionCommentEvent.Action.Created,
            GitHubDiscussionCommentEvent.Action.Edited,
            -> {
                gitHubDiscussionService.ingest(
                    scmTeam = scmTeam.asDataModel(),
                    repo = repo,
                    discussionNumber = event.discussion.number,
                )
            }

            GitHubDiscussionCommentEvent.Action.Deleted,
            -> {
                gitHubDiscussionService.delete(
                    orgId = scmTeam.orgId,
                    repoId = repo.id,
                    discussionNumber = event.discussion.number,
                )
            }
        }
    }
}
