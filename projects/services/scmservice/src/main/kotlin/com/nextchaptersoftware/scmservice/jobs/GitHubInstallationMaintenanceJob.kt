package com.nextchaptersoftware.scmservice.jobs

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.toScmTeam
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.github.GitHubAppApi
import com.nextchaptersoftware.scm.github.models.GitHubAccountType
import com.nextchaptersoftware.scm.github.models.GitHubInstallation
import com.nextchaptersoftware.service.BackgroundJob
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList

private val LOGGER = KotlinLogging.logger {}

/**
 * The purpose of this class is to discover GitHub installs that were missed by webhooks.
 * Not relevant for any other SCMs, because installation is an explicit API action for non-GitHub SCMs.
 */
class GitHubInstallationMaintenanceJob(
    private val scmAppApiFactory: ScmAppApiFactory,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val repoStore: RepoStore = Stores.repoStore,
) : BackgroundJob {

    override val name: String
        get() = javaClass.simpleName

    private suspend fun getInstallationsForApp(v3AppClient: GitHubAppApi.V3App): List<GitHubInstallation> {
        return v3AppClient.installations()
            .filter { installation ->
                when (installation.targetType) {
                    GitHubAccountType.Organization -> true
                    GitHubAccountType.Bot -> false
                    GitHubAccountType.User -> true
                }
            }.toList()
    }

    override suspend fun run() {
        val installContexts = scmAppApiFactory.getAllApis().mapNotNull { api: GitHubAppApi ->
            runSuspendCatching {
                val internalScmInstallationIds = api.scm.let { scm ->
                    suspendedTransaction {
                        ScmTeamModel
                            .select(ScmTeamModel.providerExternalInstallationId)
                            .whereAll(
                                ScmTeamStore.SCM_TEAM_EXISTS_CLAUSE,
                                ScmTeamStore.SCM_TEAM_CONNECTED_CLAUSE,
                                ScmTeamModel.provider.eq(scm.provider),
                                scm.providerEnterpriseId?.let { ScmTeamModel.providerEnterprise eq it },
                            )
                            .mapNotNull { it[ScmTeamModel.providerExternalInstallationId] }
                            .toSet()
                    }
                }

                val externalInstallations = api.v3App().use { v3AppClient: GitHubAppApi.V3App ->
                    getInstallationsForApp(v3AppClient).toList()
                }

                InstallationContext(
                    api = api,
                    internalScmInstallationIds = internalScmInstallationIds,
                    externalScmInstallationIds = externalInstallations.map { it.installationId.toString() }.toSet(),
                )
            }.getOrElse {
                LOGGER.errorAsync(
                    it,
                    "provider" to api.scm.provider.displayName,
                    "enterpriseId" to api.scm.providerEnterpriseId,
                ) { "Error occurred while fetching installations for App" }
                null
            }
        }

        // Uninstall internal SCM installations that do not exist in the external SCM (GitHub)
        installContexts.forEach { context ->
            context.internalScmInstallationIds
                .minus(context.externalScmInstallationIds)
                .nullIfEmpty()
                ?.also { handleUninstalls(context.api.scm, it) }
        }
    }

    private suspend fun handleUninstalls(scm: Scm, installationIdsToUninstall: Set<String>) {
        val scmTeams = suspendedTransaction {
            ScmTeamModel
                .select(ScmTeamModel.columns)
                .whereAll(
                    ScmTeamModel.provider eq scm.provider,
                    scm.providerEnterpriseId?.let { ScmTeamModel.providerEnterprise eq it },
                    ScmTeamModel.providerExternalInstallationId inList installationIdsToUninstall,
                ).map { it.toScmTeam() }
        }

        scmTeams.forEach { scmTeam ->
            LOGGER.infoAsync("teamId" to scmTeam.id) { "Uninstalling installation for team" }
            scmTeamStore.uninstallProvider(teamId = scmTeam.id, installationId = scmTeam.providerExternalInstallationId.toString())
            repoStore.disconnectAllRepos(teamId = scmTeam.id, provider = scmTeam.provider)
        }
    }
}

private data class InstallationContext(
    val api: GitHubAppApi,
    val externalScmInstallationIds: Set<String>,
    val internalScmInstallationIds: Set<String>,
)
