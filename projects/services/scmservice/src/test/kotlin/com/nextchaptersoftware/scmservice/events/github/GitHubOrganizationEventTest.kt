package com.nextchaptersoftware.scmservice.events.github

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.scm.github.models.GitHubAccount
import com.nextchaptersoftware.scm.github.models.GitHubAccountType
import com.nextchaptersoftware.scm.github.models.GitHubInstallationAccount
import com.nextchaptersoftware.scm.github.models.GitHubOrg
import com.nextchaptersoftware.scm.github.models.GitHubOrgMembershipRole
import com.nextchaptersoftware.test.utils.TestUtils
import io.ktor.http.Url
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class GitHubOrganizationEventTest {
    private val expectedEvent = GitHubOrganizationEvent(
        action = GitHubOrganizationEvent.Action.Renamed,
        organization = GitHubOrg(
            id = *********,
            login = "Richie-Test-Org",
            avatarUrl = Url("https://avatars.githubusercontent.com/u/*********?v=4"),
        ),
        installation = GitHubInstallationAccount(
            id = ********,
        ),
    )

    @Test
    fun `organization renamed`() {
        val raw = TestUtils.getResource(this, "/events/github/organization.renamed.json")
        val event = raw.decode<GitHubOrganizationEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                organization = expectedEvent.organization.copy(
                    login = "Richie-Acme-Org",
                ),
            ),
        )
    }

    @Test
    fun `organization deleted`() {
        val raw = TestUtils.getResource(this, "/events/github/organization.deleted.json")
        val event = raw.decode<GitHubOrganizationEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubOrganizationEvent.Action.Deleted,
                organization = expectedEvent.organization,
            ),
        )
    }

    @Test
    fun `organization member_added as member`() {
        val raw = TestUtils.getResource(this, "/events/github/organization.member_added.member.json")
        val event = raw.decode<GitHubOrganizationEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubOrganizationEvent.Action.MemberAdded,
                membership = GitHubOrganizationEvent.Membership(
                    role = GitHubOrgMembershipRole.Member,
                    user = GitHubAccount(
                        id = *********,
                        login = "richardbresnanchat",
                        type = GitHubAccountType.User,
                        avatarUrl = Url("https://avatars.githubusercontent.com/u/*********?v=4"),
                        htmlUrl = Url("https://github.com/richardbresnanchat"),
                    ),
                ),
            ),
        )
    }

    @Test
    fun `organization member_added as owner`() {
        val raw = TestUtils.getResource(this, "/events/github/organization.member_added.owner.json")
        val event = raw.decode<GitHubOrganizationEvent>()
        assertThat(event).isEqualTo(
            GitHubOrganizationEvent(
                action = GitHubOrganizationEvent.Action.MemberAdded,
                organization = GitHubOrg(
                    id = *********,
                    login = "TheUnblockedDemo",
                    avatarUrl = Url("https://avatars.githubusercontent.com/u/*********?v=4"),
                ),
                membership = GitHubOrganizationEvent.Membership(
                    role = GitHubOrgMembershipRole.Owner,
                    user = GitHubAccount(
                        id = ********,
                        login = "benedict-jw",
                        type = GitHubAccountType.User,
                        avatarUrl = Url("https://avatars.githubusercontent.com/u/********?v=4"),
                        htmlUrl = Url("https://github.com/benedict-jw"),
                    ),
                ),
                installation = GitHubInstallationAccount(
                    id = ********,
                ),
            ),
        )
    }

    @Test
    fun `organization member_removed`() {
        val raw = TestUtils.getResource(this, "/events/github/organization.member_removed.json")
        val event = raw.decode<GitHubOrganizationEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubOrganizationEvent.Action.MemberRemoved,
                membership = GitHubOrganizationEvent.Membership(
                    role = GitHubOrgMembershipRole.Unaffiliated,
                    user = GitHubAccount(
                        id = *********,
                        login = "richardbresnanchat",
                        type = GitHubAccountType.User,
                        avatarUrl = Url("https://avatars.githubusercontent.com/u/*********?v=4"),
                        htmlUrl = Url("https://github.com/richardbresnanchat"),
                    ),
                ),
            ),
        )
    }
}
