package com.nextchaptersoftware.scmservice.events.github

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.scm.github.models.GitHubInstallationAccount
import com.nextchaptersoftware.scm.github.models.GitHubOrg
import com.nextchaptersoftware.scm.github.models.GitHubRepo
import com.nextchaptersoftware.test.utils.TestUtils
import io.ktor.http.Url
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class GitHubRepositoryEventTest {
    private val expectedEvent = GitHubRepositoryEvent(
        action = GitHubRepositoryEvent.Action.Renamed,
        repository = GitHubRepo(
            id = *********,
            name = "First-One",
            isArchived = false,
            isDisabled = false,
            isFork = false,
            isPrivate = true,
            approximateSizeKb = 0,
            hasWiki = true,
            owner = GitHubOrg(
                id = *********,
                login = "Richie-Test-Org",
                htmlUrl = Url("https://github.com/Richie-Test-Org"),
                avatarUrl = Url("https://avatars.githubusercontent.com/u/*********?v=4"),
            ),
            fullName = "Richie-Test-Org/First-One",
            htmlUrl = Url("https://github.com/Richie-Test-Org/First-One"),
            httpUrl = Url("https://github.com/Richie-Test-Org/First-One.git"),
            scpUrl = "**************:Richie-Test-Org/First-One.git",
            createdAt = Instant.parse("2022-06-21T05:25:53Z"),
            pushedAt = Instant.parse("2022-06-21T05:25:53Z"),
            description = "Best repo.",
        ),
        installation = GitHubInstallationAccount(
            id = ********,
        ),
    )

    @Test
    fun `repo renamed`() {
        val raw = TestUtils.getResource(this, "/events/github/repository.renamed.json")
        val event = raw.decode<GitHubRepositoryEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubRepositoryEvent.Action.Renamed,
            ),
        )
    }

    @Test
    fun `repo publicized`() {
        val raw = TestUtils.getResource(this, "/events/github/repository.publicized.json")
        val event = raw.decode<GitHubRepositoryEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubRepositoryEvent.Action.Publicized,
                repository = expectedEvent.repository.copy(
                    isPrivate = false,
                ),
            ),
        )
    }

    @Test
    fun `repo privatized`() {
        val raw = TestUtils.getResource(this, "/events/github/repository.privatized.json")
        val event = raw.decode<GitHubRepositoryEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubRepositoryEvent.Action.Privatized,
                repository = expectedEvent.repository.copy(
                    isPrivate = true,
                ),
            ),
        )
    }

    @Test
    fun `repo archived`() {
        val raw = TestUtils.getResource(this, "/events/github/repository.archived.json")
        val event = raw.decode<GitHubRepositoryEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubRepositoryEvent.Action.Archived,
                repository = expectedEvent.repository.copy(
                    isArchived = true,
                ),
            ),
        )
    }

    @Test
    fun `repo unarchived`() {
        val raw = TestUtils.getResource(this, "/events/github/repository.unarchived.json")
        val event = raw.decode<GitHubRepositoryEvent>()
        assertThat(event).isEqualTo(
            expectedEvent.copy(
                action = GitHubRepositoryEvent.Action.Unarchived,
                repository = expectedEvent.repository,
            ),
        )
    }
}
