package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.auth.services.url.login.ScmLoginSource
import com.nextchaptersoftware.api.models.EnterpriseProvider
import com.nextchaptersoftware.api.models.EnterpriseProviderOutcome
import com.nextchaptersoftware.api.models.EnterpriseProviderOutcomeError
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.api.services.enterprise.ScmEnterpriseDelegateInterface
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSAClientServerCryptoSystem
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.apps.AppManifestNegotiation
import com.nextchaptersoftware.scm.config.ScmSecretConfig
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray
import com.nextchaptersoftware.utils.StringUuidExtensions.isHex
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.http.authority
import io.ktor.http.hostWithPort
import kotlin.time.Instant

internal class ScmEnterpriseDelegateImpl(
    private val enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
    private val loginUrlService: LoginUrlService,
    private val scmNoAuthApiFactory: ScmNoAuthApiFactory,
    private val scmSecretConfig: ScmSecretConfig = ScmSecretConfig.INSTANCE,
) : ScmEnterpriseDelegateInterface {

    private val bitbucketDataCenterSecretDecryption by lazy {
        RSAClientServerCryptoSystem.RSADecryption(scmSecretConfig.bitbucketDataCenter.appSecretsPrivateKey.value)
    }

    private val gheSecretEncryption by lazy {
        AESCryptoSystem.AESEncryption(
            AESCryptoSystem.importKey(scmSecretConfig.githubEnterprise.appSecretsAesKey.value),
        )
    }

    private val gitLabSecretDecryption by lazy {
        RSAClientServerCryptoSystem.RSADecryption(scmSecretConfig.gitlabSelfHosted.appSecretsPrivateKey.value)
    }

    companion object {
        // Bitbucket
        const val BITBUCKET_DATA_CENTER_APPLICATION_ID_LENGTH = 32
        const val BITBUCKET_DATA_CENTER_APPLICATION_SECRET_LENGTH = 64

        // Gitlab
        const val GITLAB_APPLICATION_ID_LENGTH = 64
        const val GITLAB_APPLICATION_LEGACY_SECRET_LENGTH = 64
        const val GITLAB_APPLICATION_SECRET_LENGTH = 70
        const val GITLAB_APPLICATION_SECRET_PREFIX = "gloas-"
    }

    override suspend fun createBitbucketDataCenterEnterpriseProvider(
        orgId: OrgId?,
        applicationId: String,
        displayName: String,
        encryptedApplicationSecret: String,
    ): EnterpriseProviderOutcome {
        val encryptedApplicationSecretByteArray = encryptedApplicationSecret.base64DecodeAsByteArray()

        val applicationSecret = bitbucketDataCenterSecretDecryption.decrypt(
            Ciphertext(encryptedApplicationSecretByteArray),
        )

        val fieldValidationErrors: Map<String, String> = buildMap {
            when {
                applicationId.length != BITBUCKET_DATA_CENTER_APPLICATION_ID_LENGTH -> {
                    put(
                        "applicationId",
                        "Expected a string of length $BITBUCKET_DATA_CENTER_APPLICATION_ID_LENGTH, but found ${applicationId.length} characters.",
                    )
                }

                !applicationId.isHex -> {
                    put("applicationId", "Expected a hex string, but found non-hex characters.")
                }
            }
            when {
                applicationSecret.value.length != BITBUCKET_DATA_CENTER_APPLICATION_SECRET_LENGTH -> {
                    put(
                        "encryptedApplicationSecret",
                        "Expected a string of length $BITBUCKET_DATA_CENTER_APPLICATION_SECRET_LENGTH, " +
                                "but found ${applicationSecret.value.length} characters.",
                    )
                }

                !applicationSecret.value.isHex -> {
                    put("encryptedApplicationSecret", "Expected a hex string, but found non-hex characters.")
                }
            }
        }

        if (fieldValidationErrors.isNotEmpty()) {
            return EnterpriseProviderOutcome(
                status = EnterpriseProviderOutcome.Status.error,
                error = EnterpriseProviderOutcomeError(
                    fields = fieldValidationErrors,
                ),
            )
        }

        return enterpriseAppConfigStore.update(
            orgId = orgId,
            createdAt = Instant.nowWithMicrosecondPrecision(),
            externalAppId = applicationId,
            provider = Provider.BitbucketDataCenter,
            hostAndPort = displayName,
            oauthClientId = applicationId,
            oauthClientSecretEncrypted = encryptedApplicationSecretByteArray,
            slug = null,
            owner = null,
            appHtmlUrl = null,
            webhookSecretEncrypted = null,
            privateKeyPemEncrypted = null,
        ).let { config ->
            EnterpriseProviderOutcome(
                status = EnterpriseProviderOutcome.Status.success,
                error = null,
                enterpriseProvider = EnterpriseProvider(
                    id = config.id.value,
                    displayName = config.authority,
                    provider = ApiProvider.bitbucketDataCenter,
                    oauthUrl = loginUrlService.buildLoginUrl(
                        loginSource = ScmLoginSource(Scm.BitbucketDataCenter(config.id)),
                        clientSecret = null,
                        agentType = null,
                        authRedirectOverrideUrl = null,
                        clientState = null,
                    ).asString,
                ),
            )
        }
    }

    override suspend fun createGitHubEnterpriseProvider(
        orgId: OrgId?,
        code: String,
        negotiation: AppManifestNegotiation,
    ): EnterpriseProvider {
        val scmNoAuthApi = scmNoAuthApiFactory.fromAuthority(
            orgIds = setOfNotNull(orgId),
            authority = negotiation.enterpriseAuthority,
            provider = Provider.GitHubEnterprise,
        )

        val appConfig = scmNoAuthApi.completeAppManifest(code)

        fun Secret.gheEncrypt(): ByteArray {
            return gheSecretEncryption.encrypt(this).value
        }

        return enterpriseAppConfigStore.update(
            orgId = orgId,
            createdAt = appConfig.createdAt,
            externalAppId = appConfig.id.toString(),
            provider = Provider.GitHubEnterprise,
            hostAndPort = appConfig.appHtmlUrl.hostWithPort,
            slug = appConfig.slug,
            owner = appConfig.owner.login,
            appHtmlUrl = appConfig.appHtmlUrl,
            oauthClientId = appConfig.oauthClientId,
            oauthClientSecretEncrypted = Secret(appConfig.oauthClientSecret).gheEncrypt(),
            webhookSecretEncrypted = Secret(appConfig.webhookSecret).gheEncrypt(),
            privateKeyPemEncrypted = Secret(appConfig.privateKeyPem).gheEncrypt(),
        ).let { config ->
            EnterpriseProvider(
                id = config.id.value,
                displayName = appConfig.appHtmlUrl.authority,
                provider = ApiProvider.githubEnterprise,
                oauthUrl = loginUrlService.buildLoginUrl(
                    loginSource = ScmLoginSource(Scm.GitHubEnterprise(config.id)),
                    clientSecret = negotiation.clientSecret,
                    agentType = negotiation.agentType,
                    authRedirectOverrideUrl = negotiation.redirectUrl,
                    clientState = negotiation.clientState,
                ).asString,
            )
        }
    }

    override suspend fun createGitLabEnterpriseProvider(
        orgId: OrgId?,
        applicationId: String,
        displayName: String,
        encryptedApplicationSecret: String,
    ): EnterpriseProviderOutcome {
        val encryptedApplicationSecretByteArray = encryptedApplicationSecret.base64DecodeAsByteArray()

        val applicationSecret = gitLabSecretDecryption.decrypt(
            Ciphertext(encryptedApplicationSecretByteArray),
        )

        val fieldValidationErrors: Map<String, String> = buildMap {
            when {
                applicationId.length != GITLAB_APPLICATION_ID_LENGTH -> {
                    put("applicationId", "Expected a string of length $GITLAB_APPLICATION_ID_LENGTH, but found ${applicationId.length} characters.")
                }

                !applicationId.isHex -> {
                    put("applicationId", "Expected a hex string, but found non-hex characters.")
                }
            }
            when (applicationSecret.value.length) {
                GITLAB_APPLICATION_LEGACY_SECRET_LENGTH -> {
                    when {
                        !applicationSecret.value.isHex -> {
                            put(
                                "encryptedApplicationSecret",
                                "Expected a string of hexadecimal characters, but found non-hexadecimal characters.",
                            )
                        }
                    }
                }

                GITLAB_APPLICATION_SECRET_LENGTH -> {
                    when {
                        !applicationSecret.value.startsWith(GITLAB_APPLICATION_SECRET_PREFIX) -> {
                            put("encryptedApplicationSecret", "Expected a string starting with '$GITLAB_APPLICATION_SECRET_PREFIX'.")
                        }

                        !applicationSecret.value.drop(GITLAB_APPLICATION_SECRET_PREFIX.length).isHex -> {
                            put(
                                "encryptedApplicationSecret",
                                "Expected a string starting with '$GITLAB_APPLICATION_SECRET_PREFIX' followed by a hex string.",
                            )
                        }
                    }
                }

                else -> {
                    put(
                        "encryptedApplicationSecret",
                        "Expected a string of length $GITLAB_APPLICATION_LEGACY_SECRET_LENGTH or $GITLAB_APPLICATION_SECRET_LENGTH," +
                                " but found ${applicationSecret.value.length} characters.",
                    )
                }
            }
        }

        if (fieldValidationErrors.isNotEmpty()) {
            return EnterpriseProviderOutcome(
                status = EnterpriseProviderOutcome.Status.error,
                error = EnterpriseProviderOutcomeError(
                    fields = fieldValidationErrors,
                ),
            )
        }

        return enterpriseAppConfigStore.update(
            orgId = orgId,
            createdAt = Instant.nowWithMicrosecondPrecision(),
            externalAppId = applicationId,
            provider = Provider.GitLabSelfHosted,
            hostAndPort = displayName,
            oauthClientId = applicationId,
            oauthClientSecretEncrypted = encryptedApplicationSecretByteArray,
            slug = null,
            owner = null,
            appHtmlUrl = null,
            webhookSecretEncrypted = null,
            privateKeyPemEncrypted = null,
        ).let { config ->
            EnterpriseProviderOutcome(
                status = EnterpriseProviderOutcome.Status.success,
                error = null,
                enterpriseProvider = EnterpriseProvider(
                    id = config.id.value,
                    displayName = config.authority,
                    provider = ApiProvider.gitlabSelfHosted,
                    oauthUrl = loginUrlService.buildLoginUrl(
                        loginSource = ScmLoginSource(Scm.GitLabSelfHosted(config.id)),
                        clientSecret = null,
                        agentType = null,
                        authRedirectOverrideUrl = null,
                        clientState = null,
                    ).asString,
                ),
            )
        }
    }
}
