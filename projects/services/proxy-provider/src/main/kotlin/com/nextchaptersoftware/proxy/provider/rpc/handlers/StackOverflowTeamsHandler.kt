package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.StackOverflowTeamsService
import com.nextchaptersoftware.api.models.StackOverflowTeam
import com.nextchaptersoftware.rpc.calls.StackOverflowTeamsCalls
import com.nextchaptersoftware.rpc.calls.StackOverflowTeamsCalls.StackOverflowTeamsUpsertParams
import com.sksamuel.hoplite.Secret

class StackOverflowTeamsHandler(
    private val stackOverflowTeamsService: StackOverflowTeamsService,
) : StackOverflowTeamsCalls {

    override suspend fun stackOverflowTeamsUpsert(
        params: StackOverflowTeamsUpsertParams,
    ): StackOverflowTeam {
        return stackOverflowTeamsService.upsert(
            id = params.stackOverflowTeamId,
            orgId = params.orgId,
            personId = params.personId,
            orgMemberId = params.orgMemberId,
            hostUrl = params.hostUrl,
            accessToken = params.accessToken.let(::Secret),
        )
    }
}
