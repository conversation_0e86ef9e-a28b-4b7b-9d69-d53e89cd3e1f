package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.integration.extension.services.google.GoogleDriveConfigurationService
import com.nextchaptersoftware.api.models.GoogleDriveConfiguration
import com.nextchaptersoftware.api.models.GoogleDriveWorkspace
import com.nextchaptersoftware.api.models.SearchGoogleDriveFilesResponse
import com.nextchaptersoftware.rpc.calls.GoogleDriveCalls
import com.nextchaptersoftware.rpc.calls.GoogleDriveCalls.GoogleDriveSearchFilesParams
import com.sksamuel.hoplite.Secret

class GoogleDriveHandler(
    private val googleDriveConfigurationService: GoogleDriveConfigurationService,
) : GoogleDriveCalls {

    override suspend fun googleDriveSearchFiles(
        params: GoogleDriveSearchFilesParams,
    ): SearchGoogleDriveFilesResponse {
        return googleDriveConfigurationService.search(
            orgId = params.orgId,
            installationId = params.installationId,
            orgMemberId = params.orgMemberId,
            query = params.query,
        )
    }

    override suspend fun googleDriveGetConfiguration(
        params: GoogleDriveCalls.GoogleDriveGetConfigurationParams,
    ): GoogleDriveConfiguration {
        return googleDriveConfigurationService.getConfiguration(
            orgId = params.orgId,
            installationId = params.installationId,
            orgMemberId = params.orgMemberId,
        )
    }

    override suspend fun googleDriveSaveConfiguration(
        params: GoogleDriveCalls.GoogleDriveSaveConfigurationParams,
    ) {
        return googleDriveConfigurationService.saveConfiguration(
            orgId = params.orgId,
            installationId = params.installationId,
            orgMemberId = params.orgMemberId,
            configuration = params.configuration,
        )
    }

    override suspend fun googleDriveUpsertGoogleWorkspaceDrive(
        params: GoogleDriveCalls.GoogleDriveUpsertGoogleWorkspaceDriveParams,
    ): GoogleDriveWorkspace {
        return googleDriveConfigurationService.upsertGoogleWorkspaceDrive(
            orgId = params.orgId,
            installationId = params.installationId,
            serviceAccountKey = Secret(params.serviceAccountKey),
            adminEmail = params.adminEmail,
        )
    }
}
