package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.StackOverflowTeamsDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.asStackOverflowTeam
import com.nextchaptersoftware.api.models.StackOverflowTeam
import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.config.StackOverflowTeamsConfig
import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.client.org.HttpClientFactory
import com.nextchaptersoftware.ktor.utils.HttpExtensions.isClientError
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.trace.ktor.KtorClientTracing
import com.nextchaptersoftware.user.secret.UserSecretServiceInterface
import com.sksamuel.hoplite.Secret
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.get
import io.ktor.client.request.head
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.contentType
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

internal class StackOverflowTeamsDelegateImpl(
    private val installationStore: InstallationStore = Stores.installationStore,
    private val userSecretService: UserSecretServiceInterface,
    private val client: StackOverflowTeamsClient,
    private val slackNotifier: SlackNotifier,
) : StackOverflowTeamsDelegateInterface {

    companion object {
        private const val HTML_URL_BASE = "https://stackoverflowteams.com/c/"
        private const val ERROR_INVALID_URL = "The host URL provided is invalid. Please verify and try again."
        private const val ERROR_INVALID_TOKEN = "The PAT provided is invalid. Verify that the PAT entered is for the specified host."
    }

    class StackOverflowTeamsClient(
        private val config: StackOverflowTeamsConfig,
    ) {
        private val client = HttpClientFactory.createHttpClient(orgIds = null) {
            install(ContentNegotiation) {
                installJsonSerializer()
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
            }
            install(HttpRequestRetry)
            install(KtorClientTracing)
            followRedirects = false
        }

        suspend fun exists(url: Url): Boolean {
            return client.head(url).let {
                if (it.status.isClientError) {
                    LOGGER.infoAsync("url" to url, "status" to it.status) {
                        "StackOverflowTeamsClient.exists"
                    }
                }
                !it.status.isClientError
            }
        }

        suspend fun isValid(teamName: String, accessToken: Secret): Boolean {
            val url = URLBuilder(config.baseApiUri).let {
                it.pathSegments = listOf("2.3", "questions")
                it.parameters["team"] = teamName
                it.parameters["pagesize"] = "0"
                it.build()
            }

            return !client.get(url) { this.headers["X-API-Access-Token"] = accessToken.value }.status.isClientError
        }
    }

    @Suppress("ThrowsCount")
    suspend fun validate(
        hostUrl: String,
        accessToken: Secret,
    ): String = withLoggingContextAsync(
        "hostUrl" to hostUrl,
    ) {
        if (!hostUrl.startsWith(HTML_URL_BASE)) {
            LOGGER.warnAsync { "Malformed Stack Overflow for Teams URL" }
            throw UserVisibleException(statusCode = HttpStatusCode.BadRequest, title = ERROR_INVALID_URL, detail = null, url = null)
        }

        val teamName = Url(hostUrl).rawSegments.filter { it.isNotBlank() }.getOrElse(1) {
            LOGGER.warnAsync { "Could not parse team name from URL" }
            throw UserVisibleException(statusCode = HttpStatusCode.BadRequest, title = ERROR_INVALID_URL, detail = null, url = null)
        }

        if (!client.exists(Url("$HTML_URL_BASE$teamName"))) {
            LOGGER.warnAsync { "Team doesn't exist" }
            throw UserVisibleException(statusCode = HttpStatusCode.BadRequest, title = ERROR_INVALID_URL, detail = null, url = null)
        }

        validateToken(teamName = teamName, accessToken = accessToken)

        teamName
    }

    private suspend fun validateToken(
        teamName: String,
        accessToken: Secret,
    ) {
        if (accessToken.value.isBlank()) {
            LOGGER.warnAsync { "Access token is empty" }
            throw UserVisibleException(statusCode = HttpStatusCode.BadRequest, title = ERROR_INVALID_TOKEN, detail = null, url = null)
        }

        if (!client.isValid(teamName = teamName, accessToken = accessToken)) {
            LOGGER.warnAsync { "Access token is invalid" }
            throw UserVisibleException(statusCode = HttpStatusCode.BadRequest, title = ERROR_INVALID_TOKEN, detail = null, url = null)
        }
    }

    override suspend fun upsert(
        installationId: InstallationId,
        orgId: OrgId,
        personId: PersonId,
        orgMemberId: OrgMemberId,
        hostUrl: String?,
        accessToken: Secret,
    ): StackOverflowTeam {
        return Database.suspendedTransaction {
            when (val existing = installationStore.findById(trx = this, installationId = installationId)) {
                null -> {
                    val teamName = validate(hostUrl = hostUrl ?: "", accessToken = accessToken)

                    installationStore.insert(
                        trx = this,
                        id = installationId,
                        orgId = orgId,
                        installationExternalId = teamName,
                        displayName = teamName,
                        provider = Provider.StackOverflowTeams,
                        htmlUrl = Url("$HTML_URL_BASE$teamName"),
                        rawAccessToken = userSecretService.encrypt(accessToken),
                    ).also {
                        slackNotifier.announceIntegrationAdded(installation = it, personId = personId)
                    }
                }

                else -> {
                    validateToken(teamName = existing.installationExternalId, accessToken = accessToken)

                    installationStore.updateRawAccessToken(
                        trx = this,
                        id = installationId,
                        orgId = orgId,
                        rawAccessToken = userSecretService.encrypt(accessToken),
                    )

                    existing
                }
            }
        }.asStackOverflowTeam
    }
}
