package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.api.integration.extension.services.DataSourcePresetsConfigurationDelegateInterface
import com.nextchaptersoftware.api.models.AvailableDataSourcePresetInstallation
import com.nextchaptersoftware.api.models.DataSourcePreset
import com.nextchaptersoftware.api.models.DataSourcePresetSummary
import com.nextchaptersoftware.api.models.UpdateDataSourcePresetRequest
import com.nextchaptersoftware.data.preset.config.DataSourcePresetService
import com.nextchaptersoftware.data.preset.config.providers.DataSourcePresetInstallationProvider
import com.nextchaptersoftware.db.models.DataSourcePresetId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.ktor.NotFoundException

class DataSourcePresetsConfigurationDelegateImpl(
    private val dataSourcePresetInstallationProvider: DataSourcePresetInstallationProvider,
    private val dataSourcePresetService: DataSourcePresetService,
) : DataSourcePresetsConfigurationDelegateInterface {
    override suspend fun getAvailableDataSourcePresetInstallations(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
    ): List<AvailableDataSourcePresetInstallation> {
        return dataSourcePresetInstallationProvider.get(
            orgId = orgId,
            orgMemberId = orgMemberId,
        )
    }

    override suspend fun getDataSourcePreset(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dataSourcePresetId: DataSourcePresetId,
    ): DataSourcePreset {
        return dataSourcePresetService.get(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dataSourcePresetId = dataSourcePresetId,
        ) ?: throw NotFoundException()
    }

    override suspend fun getDataSourcePresets(
        orgId: OrgId,
    ): List<DataSourcePresetSummary> {
        return dataSourcePresetService.getSummaries(
            orgId = orgId,
        )
    }

    override suspend fun removeDataSourcePreset(
        orgId: OrgId,
        dataSourcePresetId: DataSourcePresetId,
    ) {
        dataSourcePresetService.delete(
            orgId = orgId,
            dataSourcePresetId = dataSourcePresetId,
        )
    }

    override suspend fun updateDataSourcePreset(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dataSourcePresetId: DataSourcePresetId,
        updateDataSourcePresetRequest: UpdateDataSourcePresetRequest,
    ): DataSourcePreset {
        return dataSourcePresetService.upsert(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dataSourcePresetId = dataSourcePresetId,
            updateDataSourcePresetRequest = updateDataSourcePresetRequest,
        )
    }
}
