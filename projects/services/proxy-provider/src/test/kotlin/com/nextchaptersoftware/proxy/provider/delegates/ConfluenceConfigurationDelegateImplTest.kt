package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterApiProvider
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterGroupsApi
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterUsersApi
import com.nextchaptersoftware.confluence.models.DataCenterGroup
import com.nextchaptersoftware.confluence.models.DataCenterResults
import com.nextchaptersoftware.confluence.models.DataCenterUser
import com.nextchaptersoftware.crypto.Decryption
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.ModelBuilders.makeConfluenceSite
import com.nextchaptersoftware.db.ModelBuilders.makeConfluenceSpace
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeIngestion
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ConfluenceSiteDAO
import com.nextchaptersoftware.db.models.ConfluenceSiteModel
import com.nextchaptersoftware.db.models.ConfluenceSpaceDAO
import com.nextchaptersoftware.db.models.ConfluenceSpaceIngestionType
import com.nextchaptersoftware.db.models.ConfluenceSpaceModel
import com.nextchaptersoftware.db.models.EmbeddingDeleteDAO
import com.nextchaptersoftware.db.models.EmbeddingDeleteModel
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IngestionDAO
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.proxy.provider.delegates.providers.ConfluenceSpaceProvider
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.http.authority
import io.ktor.http.protocolWithAuthority
import java.util.UUID
import kotlin.time.Instant
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock

class ConfluenceConfigurationDelegateImplTest : DatabaseTestsBase() {
    private val user = DataCenterUser(
        email = "<EMAIL>",
        type = "known",
        status = "active",
        displayName = "asdf",
        username = "asdf",
        userKey = "asdf",
    )

    private val group = DataCenterGroup(
        type = "group",
        name = "confluence-administrators",
    )

    private val datacenterUsersApi = mock<ConfluenceDataCenterUsersApi>().also {
        runBlocking {
            `when`(
                it.getUser(
                    orgIds = anyOrNull(),
                    url = any(),
                    accessToken = any(),
                ),
            ).thenReturn(user)
        }
    }
    private val datacenterGroupsApi = mock<ConfluenceDataCenterGroupsApi>().also {
        val results = DataCenterResults(results = listOf(group), links = DataCenterResults.Links())
        runBlocking {
            `when`(
                it.getGroups(
                    orgIds = anyOrNull(),
                    url = any(),
                    accessToken = any<Secret>(),
                ),
            ).thenReturn(results)
        }
    }
    private val datacenterApiProvider = mock<ConfluenceDataCenterApiProvider>().also {
        `when`(it.usersApi).thenReturn(datacenterUsersApi)
        `when`(it.groupsApi).thenReturn(datacenterGroupsApi)
    }

    private val tokenSecretDecryption = mock<Decryption>().also {
        `when`(it.decrypt(any())).thenReturn(Secret(""))
    }

    private val confluenceSpaceProvider = mock<ConfluenceSpaceProvider>()

    private val service = ConfluenceConfigurationDelegateImpl(
        slackNotifier = mock(),
        confluenceDataCenterApiProvider = datacenterApiProvider,
        confluenceEventEnqueueService = mock(),
        tokenSecretDecryption = tokenSecretDecryption,
        confluenceSpaceProvider = confluenceSpaceProvider,
    )

    private lateinit var org: OrgDAO
    private lateinit var orgMember: OrgMemberDAO
    private lateinit var installation: InstallationDAO
    private lateinit var ingestion: IngestionDAO
    private lateinit var identity: IdentityDAO
    private lateinit var member: MemberDAO
    private lateinit var confluenceSite: ConfluenceSiteDAO
    private lateinit var confluenceSpaceA: ConfluenceSpaceDAO
    private lateinit var confluenceSpaceB: ConfluenceSpaceDAO
    private lateinit var confluenceSpaceC: ConfluenceSpaceDAO

    private val siteId = UUID.randomUUID().toString()
    private val lastSynced = Instant.nowWithMicrosecondPrecision()

    private suspend fun setup() {
        org = makeOrg()
        orgMember = makeOrgMember(org = org)
        installation = makeInstallation(org = org, provider = Provider.Confluence, installationExternalId = siteId)
        identity = makeIdentity(provider = Provider.Confluence)
        member = makeMember(orgMember = orgMember, identity = identity, installation = installation)
        ingestion = makeIngestion(provider = Provider.Confluence, installation = installation, lastSynced = lastSynced)
        confluenceSite = makeConfluenceSite(
            installation = installation,
            siteId = siteId,
            confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.AllSpaces,
        )
        confluenceSpaceA = makeConfluenceSpace(site = confluenceSite)
        confluenceSpaceB = makeConfluenceSpace(site = confluenceSite)
        confluenceSpaceC = makeConfluenceSpace(site = confluenceSite)

        `when`(confluenceSpaceProvider.accessibleSpaces(any(), any(), any())).thenReturn(
            listOf(
                confluenceSpaceA.asDataModel(),
                confluenceSpaceB.asDataModel(),
                confluenceSpaceC.asDataModel(),
            ),
        )
    }

    @Test
    fun updateConfluenceConfiguration() = suspendingDatabaseTest {
        setup()

        service.updateConfluenceConfiguration(
            orgId = org.idValue,
            confluenceSiteId = confluenceSite.id.value,
            confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.SelectedSpacesOnly,
            confluenceSpaceIds = listOf(confluenceSpaceA.id.value),
            orgMemberId = orgMember.idValue,
        )

        suspendedTransaction {
            ConfluenceSiteDAO[confluenceSite.id].asDataModel()
        }.also { site ->
            assertThat(site.confluenceSpaceIngestionType).isEqualTo(ConfluenceSpaceIngestionType.SelectedSpacesOnly)
        }

        suspendedTransaction {
            ConfluenceSpaceDAO.find { ConfluenceSpaceModel.confluenceSite eq confluenceSite.id }.map { it.asDataModel() }
        }.also { spaces ->
            assertThat(spaces.first { it.id == confluenceSpaceA.id.value }.shouldIngest).isTrue
            assertThat(spaces.first { it.id == confluenceSpaceB.id.value }.shouldIngest).isFalse
            assertThat(spaces.first { it.id == confluenceSpaceB.id.value }.shouldIngest).isFalse
        }

        suspendedTransaction {
            IngestionDAO[ingestion.id].asDataModel()
        }.also {
            assertThat(it.lastSynced).isEqualTo(ingestion.lastSynced) // Should not have updated
        }

        suspendedTransaction {
            EmbeddingDeleteDAO.find { EmbeddingDeleteModel.namespaceId eq org.idValue }.map { it.asDataModel() }
        }.also { deletes ->
            assertThat(deletes).hasSize(2)
            assertThat(deletes).allMatch { it.installationId == installation.idValue }
            assertThat(deletes.map { it.groupId }).containsExactlyInAnyOrder(confluenceSpaceB.id.value.value, confluenceSpaceC.id.value.value)
        }
    }

    private suspend fun assertEmbeddingDeletes(spacesIds: List<UUID>) {
        val embeddingDeletes = suspendedTransaction {
            EmbeddingDeleteDAO.find { EmbeddingDeleteModel.namespaceId eq org.idValue }.map { it.asDataModel() }
        }
        assertThat(embeddingDeletes).allMatch { it.installationId == installation.idValue }
        when (spacesIds.isEmpty()) {
            true -> assertThat(embeddingDeletes).isEmpty()
            else -> assertThat(embeddingDeletes.map { it.groupId }).containsExactlyInAnyOrderElementsOf(spacesIds)
        }
    }

    @Test
    fun `deleteEmbeddings -- should not create models`() = suspendingDatabaseTest {
        setup()

        service.deleteEmbeddings(
            installation = installation.asDataModel(),
            siteAfterUpdate = confluenceSite.asDataModel().copy(confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.AllSpaces),
            spacesIngestedBeforeUpdate = listOf(
                confluenceSpaceA.asDataModel(),
                confluenceSpaceB.asDataModel(),
                confluenceSpaceC.asDataModel(),
            ),
        )

        assertEmbeddingDeletes(emptyList())
    }

    @Test
    fun `deleteEmbeddings -- should create models`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            ConfluenceSpaceDAO[confluenceSpaceB.id].shouldIngest = true
        }

        service.deleteEmbeddings(
            installation = installation.asDataModel(),
            siteAfterUpdate = confluenceSite.asDataModel().copy(confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.SelectedSpacesOnly),
            spacesIngestedBeforeUpdate = listOf(
                confluenceSpaceA.asDataModel(),
                confluenceSpaceB.asDataModel(),
                confluenceSpaceC.asDataModel(),
            ),
        )

        assertEmbeddingDeletes(listOf(confluenceSpaceA.id.value.value, confluenceSpaceC.id.value.value))
    }

    @Test
    fun `deleteEmbeddings -- should only create necessary models`() = suspendingDatabaseTest {
        setup()

        service.deleteEmbeddings(
            installation = installation.asDataModel(),
            siteAfterUpdate = confluenceSite.asDataModel().copy(confluenceSpaceIngestionType = ConfluenceSpaceIngestionType.SelectedSpacesOnly),
            spacesIngestedBeforeUpdate = listOf(confluenceSpaceB.asDataModel()),
        )

        assertEmbeddingDeletes(listOf(confluenceSpaceB.id.value.value))
    }

    @Test
    fun upsertConfluenceDataCenter() = suspendingDatabaseTest {
        setup()
        val scmTeam = makeScmTeam()
        val teamMember = makeMember(scmTeam = scmTeam)
        val installationId = InstallationId.random()
        val hostUrl = "http://confluence-${UUID.randomUUID()}.com"
        val encryptedToken = Ciphertext("accessToken".toByteArray())

        val result = service.upsertConfluenceDataCenter(
            installationId = installationId,
            orgId = org.idValue,
            personId = PersonId.random(),
            orgMemberId = teamMember.orgMemberId,
            hostUrl = hostUrl,
            encryptedToken = encryptedToken,
        )

        val installation = suspendedTransaction { InstallationDAO[installationId].asDataModel() }
        val site = suspendedTransaction { ConfluenceSiteDAO.find { ConfluenceSiteModel.installation eq installationId }.single().asDataModel() }

        assertThat(result.id).isEqualTo(installation.id.value)
        assertThat(installation.provider).isEqualTo(Provider.ConfluenceDataCenter)
        assertThat(installation.installationExternalId).isEqualTo(hostUrl.asUrl.authority)
        assertThat(site.id.value).isEqualTo(result.confluenceSiteId)
        assertThat(site.baseUrl.asString).isEqualTo(result.hostUrl)
        assertThat(site.installationId).isEqualTo(installation.id)
        assertThat(site.siteId).isEqualTo(hostUrl.asUrl.authority)
        assertThat(site.baseUrl.asString).isEqualTo(hostUrl.asUrl.protocolWithAuthority)

        val newEncryptedToken = Ciphertext("bleepbleepbleep".toByteArray())

        val updated = service.upsertConfluenceDataCenter(
            installationId = installationId,
            orgId = org.idValue,
            personId = PersonId.random(),
            orgMemberId = teamMember.orgMemberId,
            hostUrl = null,
            encryptedToken = newEncryptedToken,
        )

        assertThat(updated).isEqualTo(result)
    }
}
