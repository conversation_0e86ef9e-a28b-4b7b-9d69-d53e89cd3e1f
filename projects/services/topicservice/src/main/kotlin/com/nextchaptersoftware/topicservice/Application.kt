package com.nextchaptersoftware.topicservice

import com.nextchaptersoftware.db.health.PostgresHealthChecker
import com.nextchaptersoftware.redis.health.RedisHealthChecker
import com.nextchaptersoftware.service.bootstrap.ServiceBootstrapBuilder

fun main() {
    ServiceBootstrapBuilder.bootstrap {
        withHealthCheckers(listOf(PostgresHealthChecker(), RedisHealthChecker()))
        withLogs()
        withHoneycomb()
        withDatabase()
    }.startHttpServer { serviceLifecycle ->
        module(
            serviceLifecycle = serviceLifecycle,
        )
    }
}
