package com.nextchaptersoftware.publicapiservice.plugins

import com.nextchaptersoftware.trace.ktor.KtorServerTracing
import com.nextchaptersoftware.trace.ktor.extractor.attributes.CallAttribute
import com.nextchaptersoftware.trace.ktor.extractor.attributes.CallAttributesExtractor
import com.nextchaptersoftware.trace.ktor.extractor.baggage.CallParameter
import com.nextchaptersoftware.trace.ktor.extractor.baggage.CallParameterBaggageExtractor
import com.nextchaptersoftware.trace.service.attributes.ServiceAttributes
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.opentelemetry.api.GlobalOpenTelemetry
import org.openapitools.server.infrastructure.CallAttributes

fun Application.configureTracing() {
    install(KtorServerTracing) {
        openTelemetry = GlobalOpenTelemetry.get()

        attributesExtractors = attributesExtractors.plus(
            CallAttributesExtractor(
                listOf(
                    CallAttribute(CallAttributes.operationId<PERSON>ey, ServiceAttributes.SERVICE_CALL_ATTRIBUTE_OPERATION_ID),
                ),
            ),
        ).plus(
            CallApiKeyPrincipalAttributesExtractor(
                listOf(
                    CallApiKeyPrincipalAttribute(ServiceAttributes.SERVICE_CALL_PRINCIPAL_SUBJECT) { orgId.toString() },
                ),
            ),
        )

        baggageExtractors = baggageExtractors.plus(
            CallParameterBaggageExtractor(
                listOf(
                    CallParameter("documentId", ServiceAttributes.SERVICE_CALL_PARAMETER_DOCUMENT_ID),
                    CallParameter("collectionId", ServiceAttributes.SERVICE_CALL_PARAMETER_COLLECTION_ID),
                ),
            ),
        )
    }
}
