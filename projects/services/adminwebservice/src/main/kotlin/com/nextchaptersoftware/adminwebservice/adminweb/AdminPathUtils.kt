package com.nextchaptersoftware.adminwebservice.adminweb

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.models.MLRouter
import com.nextchaptersoftware.db.models.MLRouterDAO
import com.nextchaptersoftware.db.models.MLRouterId
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.SlackTeam
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.stores.ScmTeamWithOrg
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.mlInferenceStore
import com.nextchaptersoftware.db.stores.Stores.mlInferenceTemplateStore
import com.nextchaptersoftware.db.stores.Stores.slackTeamStore
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.asUUID
import io.ktor.http.Parameters
import java.util.UUID
import kotlin.time.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn

object AdminPathUtils {

    fun Parameters.optionalBoolean(name: String): Boolean? = this[name]?.toBooleanStrict()

    fun Parameters.boolean(name: String): Boolean = optionalBoolean(name).required()

    fun Parameters.optionalInteger(name: String): Int? = this[name]?.toIntOrNull()

    fun Parameters.integer(name: String): Int = optionalInteger(name).required()

    inline fun <reified E : Enum<E>> Parameters.optionalEnum(
        name: String,
    ): E? {
        return this[name]?.let { name ->
            E::class.java.enumConstants.firstOrNull {
                it.name == name
            }
        }
    }

    inline fun <reified E : Enum<E>> Parameters.requiredEnum(
        name: String,
    ): E {
        return optionalEnum<E>(name).required { "Missing parameter '$name'" }
    }

    // UUID Helper Functions (General)
    fun Parameters.optionalId(name: String): UUID? {
        return this[name]?.asUUID()
    }

    fun <T> Parameters.optionalId(
        name: String,
        transform: (UUID) -> T,
    ): T? {
        return optionalId(name)?.let(transform)
    }

    fun Parameters.requiredId(name: String): UUID {
        return optionalId(name).required { "Missing parameter '$name'" }
    }

    fun <T> Parameters.requiredId(
        name: String,
        transform: (UUID) -> T,
    ): T {
        return requiredId(name).let(transform)
    }

    // Org Functions
    fun Parameters.optionalOrgId(): OrgId? {
        return optionalId("orgId", ::OrgId)
    }

    fun Parameters.orgId(): OrgId {
        return requireNotNull(
            optionalOrgId(),
        )
    }

    suspend fun Parameters.optionalOrg(): Org? = optionalOrgId()?.let { orgId ->
        suspendedTransaction { OrgDAO[orgId] }.asDataModel()
    }

    suspend fun Parameters.org(): Org {
        return suspendedTransaction { OrgDAO[orgId()] }.asDataModel()
    }

    // Person Functions
    fun Parameters.optionalPersonId(): PersonId? {
        return optionalId("personId", ::PersonId)
    }

    fun Parameters.personId(): PersonId {
        return requireNotNull(
            optionalPersonId(),
        )
    }

    // ScmTeam Functions
    fun Parameters.optionalScmTeamId(): ScmTeamId? {
        return optionalId("teamId", ::ScmTeamId)
    }

    fun Parameters.scmTeamId(): ScmTeamId {
        return requireNotNull(
            optionalScmTeamId(),
        )
    }

    suspend fun Parameters.optionalScmTeam(): ScmTeam? = optionalScmTeamId()?.let { scmTeamId ->
        suspendedTransaction { ScmTeamDAO[scmTeamId] }.asDataModel()
    }

    suspend fun Parameters.scmTeam(): ScmTeam {
        return suspendedTransaction { ScmTeamDAO[scmTeamId()] }.asDataModel()
    }

    // Repo Functions
    fun Parameters.optionalRepoId(): RepoId? {
        return optionalId("repoId", ::RepoId)
    }

    fun Parameters.repoId(): RepoId {
        return optionalRepoId().required()
    }

    suspend fun Parameters.optionalRepo(): Repo? = optionalRepoId()?.let { repoId ->
        suspendedTransaction { RepoDAO[repoId].asDataModel() }
    }

    suspend fun Parameters.repo(): Repo {
        return optionalRepo().required()
    }

    // Installation Functions
    fun Parameters.optionalInstallationId(): InstallationId? {
        return optionalId("installationId", ::InstallationId)
    }

    suspend fun Parameters.optionalInstallation(): Installation? = optionalInstallationId()?.let { installationId ->
        installationStore.findById(installationId = installationId, includeDeleted = true)
    }

    fun Parameters.optionalSlackTeamId(): SlackTeamId? {
        return optionalId("slackTeamId", ::SlackTeamId)
    }

    suspend fun Parameters.optionalSlackTeam(): SlackTeam? = optionalSlackTeamId()?.let { slackTeamId ->
        slackTeamStore.findById(slackTeamId = slackTeamId)
    }

    fun Parameters.optionalInferenceId(): MLInferenceId? {
        return optionalId("inferenceId", ::MLInferenceId)
    }

    fun Parameters.inferenceId(): MLInferenceId {
        return optionalInferenceId().required()
    }

    suspend fun Parameters.inference(): MLInference {
        return mlInferenceStore.get(
            inferenceId = inferenceId(),
        )
    }

    /**
     * Extracts an Instant from a query parameter value.
     *
     * @param queryParamName The name of the query parameter.
     * @return An Instant if the query parameter is present and can be parsed into a LocalDate;
     *         otherwise null.
     */
    fun Parameters.getInstantFromParameter(queryParamName: String): Instant? {
        return this[queryParamName]?.let {
            when (it) {
                "" -> null
                else -> LocalDate.parse(it).atStartOfDayIn(TimeZone.UTC)
            }
        }
    }

    suspend fun getRequiredTemplate(parameters: Parameters): MLInferenceTemplate {
        return parameters.requiredId("templateId", ::MLInferenceTemplateId).let {
            mlInferenceTemplateStore.get(it)
        }
    }

    suspend fun getOrgAndTeam(parameters: Parameters): ScmTeamWithOrg {
        return ScmTeamWithOrg(
            org = parameters.org(),
            team = parameters.scmTeam(),
        )
    }

    suspend fun getRepo(parameters: Parameters): Repo {
        return parameters.repo()
    }

    suspend fun getMemberOrNull(parameters: Parameters): Member? {
        return parameters.optionalId("memberId", ::MemberId)?.let {
            suspendedTransaction {
                MemberDAO[it].asDataModel()
            }
        }
    }

    suspend fun getMember(parameters: Parameters): Member {
        return requireNotNull(getMemberOrNull(parameters))
    }

    private suspend fun getRouterOrNull(parameters: Parameters): MLRouter? {
        return parameters.optionalId("mlRouterId", ::MLRouterId)?.let {
            suspendedTransaction {
                MLRouterDAO[it].asDataModel()
            }
        }
    }

    suspend fun getRouter(parameters: Parameters): MLRouter {
        return requireNotNull(getRouterOrNull(parameters))
    }
}
