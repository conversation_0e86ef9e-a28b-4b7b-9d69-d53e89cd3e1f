package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrgId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.utils.escapedForCsv
import com.nextchaptersoftware.adminwebservice.adminweb.utils.toBootstrapDatePickerFormat
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.conversationanalysis.ConversationAnalysisService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.MLInferenceModel
import com.nextchaptersoftware.db.models.MessageFeedback
import com.nextchaptersoftware.db.models.MessageFeedbackModel
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.toMessageFeedback
import com.nextchaptersoftware.db.stores.MemberInfo
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.environment.AdminUrlBuilder
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.utils.ensureTrailingSlash
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.ContentDisposition
import io.ktor.http.HttpHeaders
import io.ktor.http.Parameters
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.request.uri
import io.ktor.server.response.header
import io.ktor.server.response.respondFile
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.days
import kotlin.time.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.FormMethod
import kotlinx.html.InputAutoComplete
import kotlinx.html.InputType
import kotlinx.html.ThScope
import kotlinx.html.button
import kotlinx.html.div
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h5
import kotlinx.html.id
import kotlinx.html.input
import kotlinx.html.label
import kotlinx.html.onClick
import kotlinx.html.role
import kotlinx.html.small
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder

private data class MessageFeedbackAggregate(
    val orgId: OrgId,
    val inferenceId: MLInferenceId,
    val feedback: MessageFeedback,
    val productAgent: ProductAgentType?,
    val question: String,
    val response: String,
)

private enum class FeedbackViewOption(val label: String) {
    ALL("All Feedback"),
    NONE("No Feedback"),
    POSITIVE("Positive"),
    NEUTRAL("Neutral"),
    NEGATIVE("Negative"),
    ;

    fun toFeedbackType(): FeedbackType? = when (this) {
        ALL -> null
        NONE -> FeedbackType.None
        POSITIVE -> FeedbackType.Positive
        NEUTRAL -> FeedbackType.Neutral
        NEGATIVE -> FeedbackType.Negative
    }
}

private const val FROM_DATE = "fromDate"
private const val TO_DATE = "toDate"
private const val ANALYSIS_NAME = "analysisName"
private const val FEEDBACK_TYPE = "feedbackType"

object MessageFeedbackPage {
    private const val MAX_FEEDBACK_COUNT = 1000

    suspend fun RoutingContext.renderMessageFeedbackPage(
        page: AdminPage,
        insiderService: InsiderServiceInterface,
        orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        val orgId = call.parameters.optionalOrgId()
        val viewOption = call.parameters.getFeedbackViewOption()

        val feedbackAggregates = listFeedback(
            insiderService = insiderService,
            orgId = orgId,
            feedbackType = viewOption.toFeedbackType(),
        )

        val orgsById = orgStore.find(orgIds = feedbackAggregates.map { it.orgId }.distinct()).associateBy { it.id }

        // FIXME: gross hack for identity rendering
        val orgMemberIds = feedbackAggregates.groupBy { it.orgId }.mapValues { (_, feedbacks) ->
            feedbacks.map { it.feedback.orgMemberId }
        }.toMap()

        val orgMemberBundlesByOrgMemberId = orgMemberIds.keys.mapNotNull {
            orgMemberIds[it]?.let { orgMemberIds ->
                orgMemberStore.findOrgMembersAndBestIdentity(
                    orgId = it,
                    orgMemberIds = orgMemberIds,
                )
            }
        }.flatten().associateBy { it.orgMember.id }

        val path = call.request.path()

        val downloadAction = listOf(
            MenuItem(
                href = "$path/downloadFeedback",
                label = "Download Aggregated Feedback",
                description = "Downloads feedback as a CSV",
            ),
        )

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +page.label }
                div(classes = "d-flex justify-content-between") {
                    div {
                        div(classes = "col-5") {
                            renderActionMenu(downloadAction, "Download Feedback")
                        }
                        form(action = call.request.uri) {
                            h5(classes = "text-muted pt-4") { +"Select Feedback Type" }
                            renderFeedbackViewButtons(viewOption)
                        }
                    }
                    renderAnalysisMenu(path)
                }
                renderFeedback(feedbackAggregates, orgsById, orgMemberBundlesByOrgMemberId, orgId == null)
            }
        }
    }

    private fun Parameters.getFeedbackViewOption(): FeedbackViewOption {
        return get("feedbackType")?.let { param ->
            FeedbackViewOption.entries.find { it.name == param.uppercase() }
        } ?: FeedbackViewOption.ALL
    }

    private fun FlowContent.renderAnalysisMenu(path: String) {
        val today = Instant.nowWithMicrosecondPrecision()
        div(classes = "card bg-dark text-white mb-4") {
            div(classes = "card-header") {
                h5(classes = "mb-0") { +"Analyze human feedback" }
            }
            div(classes = "card-body") {
                form(action = path.ensureTrailingSlash + "analyze", method = FormMethod.post, classes = "d-flex flex-column") {
                    small { +"Analyze feedback for given time period" }
                    small(classes = "text-muted") { +"Note that this will take a few minutes and costs some money" }
                    div(classes = "mt-2") {
                        div(classes = "form-group me-2") {
                            label {
                                htmlFor = FROM_DATE
                                +"From:"
                            }
                            input(type = InputType.date, name = FROM_DATE, classes = "form-control") {
                                id = FROM_DATE
                                value = today.minus(7.days).toBootstrapDatePickerFormat()
                            }
                        }
                        div(classes = "form-group me-2") {
                            label {
                                htmlFor = TO_DATE
                                +"To:"
                            }
                            input(type = InputType.date, name = TO_DATE, classes = "form-control") {
                                id = TO_DATE
                                value = today.toBootstrapDatePickerFormat()
                            }
                        }
                        div(classes = "form-group me-2") {
                            label {
                                htmlFor = ANALYSIS_NAME
                                +"Analysis Name (optional):"
                            }
                            input(type = InputType.text, name = ANALYSIS_NAME, classes = "form-control") {
                                id = ANALYSIS_NAME
                                placeholder = "Analysis Name"
                            }
                        }

                        div(classes = "form-group me-2 mt-3") {
                            label { +"Feedback Type:" }
                            div(classes = "btn-group mt-2") {
                                role = "group"

                                input(type = InputType.radio, classes = "btn-check", name = FEEDBACK_TYPE) {
                                    id = "btnradio_downvotes"
                                    value = "negative"
                                    checked = true
                                }
                                label(classes = "btn btn-outline-primary") {
                                    htmlFor = "btnradio_downvotes"
                                    +"Downvotes"
                                }

                                input(type = InputType.radio, classes = "btn-check", name = FEEDBACK_TYPE) {
                                    id = "btnradio_upvotes"
                                    value = "positive"
                                }
                                label(classes = "btn btn-outline-primary") {
                                    htmlFor = "btnradio_upvotes"
                                    +"Upvotes"
                                }
                            }
                        }
                    }

                    button(type = ButtonType.submit, classes = "btn btn-primary mt-4") {
                        +"Analyze"
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.analyzeFeedback(conversationAnalysisService: ConversationAnalysisService) {
        val orgId: OrgId? = call.parameters.optionalOrgId()
        val bodyParams = call.receiveParameters()
        val fromDate = when (val it = bodyParams[FROM_DATE]) {
            null, "" -> throw IllegalArgumentException("fromDate is required")
            else -> LocalDate.parse(it).atStartOfDayIn(TimeZone.UTC)
        }
        val toDate = when (val it = bodyParams[TO_DATE]) {
            null, "" -> throw IllegalArgumentException("toDate is required")

            // Take the start of the next day to make toDate inclusive
            else -> LocalDate.parse(it).atStartOfDayIn(TimeZone.UTC).plus(1.days)
        }
        val name = bodyParams[ANALYSIS_NAME]
        val feedbackTypeStr = bodyParams[FEEDBACK_TYPE] ?: "negative"
        val feedbackType = when (feedbackTypeStr.lowercase()) {
            "positive" -> FeedbackType.Positive
            else -> FeedbackType.Negative
        }
        conversationAnalysisService.analyzeFeedback(fromDate, toDate, orgId, name, feedbackType)

        // Construct the redirect URL based on whether we have an orgId
        val redirectPath = AdminUrlBuilder()
            .also { builder ->
                orgId?.also { orgId ->
                    builder.withOrg(orgId.value).withMachineLearning()
                } ?: run {
                    builder.withPath("analytics")
                }
            }
            .withPath("conversationAnalysis")
            .build()
            .toString()

        call.respondRedirect(redirectPath)
    }

    suspend fun RoutingContext.downloadFeedback(
        insiderService: InsiderServiceInterface,
        urlBuilderProvider: UrlBuilderProvider,
        orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    ) {
        val orgId = call.parameters.optionalOrgId()
        val viewOption = call.parameters.getFeedbackViewOption()
        val feedbackAggregates = listFeedback(insiderService, orgId, viewOption.toFeedbackType())
        val orgsById = orgStore.find(orgIds = feedbackAggregates.map { it.orgId }.distinct()).associateBy { it.id }
        val orgMemberIds = feedbackAggregates.groupBy { it.orgId }.mapValues { (_, feedbacks) ->
            feedbacks.map { it.feedback.orgMemberId }
        }.toMap()

        val orgMemberBundlesByOrgMemberId = orgMemberIds.keys.mapNotNull {
            orgMemberIds[it]?.let { orgMemberIds ->
                orgMemberStore.findOrgMembersAndBestIdentity(
                    orgId = it,
                    orgMemberIds = orgMemberIds,
                )
            }
        }.flatten().associateBy { it.orgMember.id }

        val header = "Org,Member,FeedbackId,ExampleId,Created,Client,Sentiment,Query,Response,Feedback,AdminURL,Analysis\n"
        val rows = feedbackAggregates.joinToString("\n") { (orgId, inferenceExampleId, feedback, agent, question, response) ->
            val org = orgsById[orgId] ?: return@joinToString ""
            val orgMemberBundle = orgMemberBundlesByOrgMemberId[feedback.orgMemberId] ?: return@joinToString ""
            val url = urlBuilderProvider.admin().withOrg(orgId.value).withMachineLearning().withInference(inferenceExampleId.value).build()
            listOf(
                org.displayName,
                orgMemberBundle.identity.username,
                feedback.id,
                inferenceExampleId,
                feedback.createdAt.toString(),
                agent?.name ?: "unknown",
                feedback.feedbackType.name,
                question.escapedForCsv(),
                response.escapedForCsv(),
                feedback.feedbackDescription?.escapedForCsv() ?: "-",
                url,
                "",
            ).joinToString(",")
        }

        val data = (header + rows)

        call.response.header(
            HttpHeaders.ContentDisposition,
            ContentDisposition.Attachment.withParameter(ContentDisposition.Parameters.FileName, "feedback_aggregate.csv")
                .toString(),
        )
        val tempFile = kotlin.io.path.createTempFile("feedback_aggregates", ".csv").toFile()
        tempFile.writeText(data)
        call.respondFile(tempFile)
    }

    private fun FlowContent.renderFeedbackViewButtons(selectedView: FeedbackViewOption) {
        div(classes = "btn-group py-3") {
            role = "group"
            FeedbackViewOption.entries.forEach { view ->
                input(type = InputType.radio, classes = "btn-check", name = "feedbackType") {
                    val viewName = view.name.lowercase()
                    val btnId = "btnradio_feedback_$viewName"

                    autoComplete = InputAutoComplete.off
                    checked = selectedView == view
                    id = btnId
                    onClick = "this.form.submit();"
                    value = viewName
                    label(classes = "btn btn-outline-primary") {
                        attributes["for"] = btnId
                        style = "width: 120px;"
                        +view.label
                    }
                }
            }
        }
    }

    private suspend fun listFeedback(
        insiderService: InsiderServiceInterface,
        orgId: OrgId?,
        feedbackType: FeedbackType?,
    ): List<MessageFeedbackAggregate> {
        return suspendedTransaction {
            MessageFeedbackModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MLInferenceModel,
                    otherColumn = MLInferenceModel.botMessage,
                    onColumn = MessageFeedbackModel.message,
                ) {
                    when (orgId) {
                        null -> MLInferenceModel.org notInList insiderService.insiderOrgs
                        else -> MLInferenceModel.org eq orgId
                    }
                }
                .select(
                    MessageFeedbackModel.columns +
                            MLInferenceModel.org +
                            MLInferenceModel.productAgent +
                            MLInferenceModel.id +
                            MLInferenceModel.query +
                            MLInferenceModel.response,
                )
                .let { query ->
                    when (feedbackType) {
                        null -> query
                        else -> query.where { MessageFeedbackModel.feedbackType eq feedbackType }
                    }
                }
                .orderBy(MessageFeedbackModel.createdAt to SortOrder.DESC)
                .limit(MAX_FEEDBACK_COUNT)
                .map { row ->
                    MessageFeedbackAggregate(
                        orgId = row[MLInferenceModel.org].value,
                        inferenceId = row[MLInferenceModel.id].value,
                        feedback = row.toMessageFeedback(),
                        productAgent = row[MLInferenceModel.productAgent],
                        question = row[MLInferenceModel.query],
                        response = row[MLInferenceModel.response],
                    )
                }
        }
    }

    private fun FlowContent.renderFeedback(
        feedbackAggregates: List<MessageFeedbackAggregate>,
        orgsById: Map<OrgId, Org>,
        orgMemberBundlesByOrgMemberId: Map<OrgMemberId, MemberInfo>,
        showOrg: Boolean,
    ) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    if (showOrg) {
                        th(scope = ThScope.col) { +"Org" }
                    }
                    th(scope = ThScope.col) { +"Member" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col) { +"Client" }
                    th(scope = ThScope.col) { +"Sentiment" }
                    th(scope = ThScope.col) { +"Feedback" }
                }
            }
            tbody(classes = "table-dark") {
                feedbackAggregates.forEach { (orgId, inferenceExampleId, feedback, agent, _, _) ->
                    val org = orgsById[orgId] ?: return@forEach
                    val orgMemberBundle = orgMemberBundlesByOrgMemberId[feedback.orgMemberId] ?: return@forEach
                    tr {
                        attributes["onclick"] =
                            onClickAction("$WEB_ROOT/orgs/$orgId/machineLearning/inferences/$inferenceExampleId")
                        style = "cursor: pointer;"

                        if (showOrg) {
                            td { profile(org) }
                        }
                        td { profile(orgMemberBundle.identity) }
                        td { timeAgo(feedback.createdAt) }
                        td { agent?.also { asBadge(agent) } }
                        td { asBadge(feedback.feedbackType) }
                        td { +(feedback.feedbackDescription ?: "-") }
                    }
                }
            }
        }
    }
}
