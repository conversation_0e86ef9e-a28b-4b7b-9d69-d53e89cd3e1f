package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminActivity.renderActivity
import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminEmailEventActivity.renderEmailEventActivity
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalPersonId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.personId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.DropDownOption
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.ToggleItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.component.badge
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.component.renderToggleList
import com.nextchaptersoftware.adminwebservice.adminweb.component.submittingDropDownList
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.models.EngagementMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.renderIdentities
import com.nextchaptersoftware.adminwebservice.adminweb.page.MembersPage.renderTeamAndMembers
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgMembersPage.renderOrgAndOrgMembers
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.clientconfig.ClientConfigBundle
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ClientVersion
import com.nextchaptersoftware.db.models.EmailEventId
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.Person
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonEmailPreferences
import com.nextchaptersoftware.db.models.PersonEmailPreferencesModel
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.PersonOnboardingState
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ReleaseChannel
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.toIdentityOrNull
import com.nextchaptersoftware.db.models.toPerson
import com.nextchaptersoftware.db.models.toPersonEmailPreferences
import com.nextchaptersoftware.db.models.toScmTeam
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.EmailEventStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.OrgMemberAndIdentity
import com.nextchaptersoftware.db.stores.PersonPreferencesStore
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.SampleQuestionStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.SessionEventStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.UserEngagementStore
import com.nextchaptersoftware.db.stores.toOrgMemberAndIdentity
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.maintenance.IdentityMaintenance
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.notification.events.email.models.EmailTrigger
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationEvent
import com.nextchaptersoftware.scm.services.ProfileService
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.join
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.days
import kotlin.time.Instant
import kotlinx.html.FlowContent
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.h3
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.selectAll

object PersonPage {
    suspend fun RoutingContext.renderPersonPage(
        page: AdminPage,
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
        insiderService: InsiderServiceInterface,
        clientConfigService: ClientConfigService = ClientConfigService(),
        emailEventStore: EmailEventStore = Stores.emailEventStore,
        sessionEventStore: SessionEventStore = Stores.sessionEventStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val path = call.request.path()

        val personId = call.parameters.personId()

        val related = listOf(
            MenuItem(href = "$path/mcp-tool-overrides", label = "MCP Tool Overrides", description = "Person-specific MCP tool customizations."),
        )

        val objects = getPersonRelatedObjects(personId, userEngagementStore)

        val adminIdentity = call.getAdminIdentity()
        val actions = getActions(
            path = path,
            adminIdentity = adminIdentity.identity,
            identities = objects.identities.filter { it.provider.isSignInCapable },
        )
        val clientConfig = clientConfigService.getPersonConfig(personId)
        val engagement = engagementMetrics(person = objects.person)
        val emailEvents = emailEventStore.getEmailEventsForPerson(personId = personId)
        val referrals = sessionEventStore.findSessionReferralsForPerson(objects.person.id)

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            relatedMenu { renderRelatedMenu(related) }
            actionMenu { renderActionMenu(actions, sort = false) }
            content {
                h1 { +page.label }
                renderPerson(
                    insiderService,
                    objects.person,
                    engagement, path,
                    objects.identities.flatMap { it.emails }.distinct().sortedBy { it.value },
                    referrals.map { it.campaign.name }.sorted(),
                )

                h3(classes = "mt-5") { +"Org Memberships" }
                objects.teamMembersAndIdentities.let {
                    when (it.isEmpty()) {
                        true -> +"No org members."

                        else -> renderOrgAndOrgMembers(
                            orgs = it.map(OrgMemberAndIdentity::org).distinct(),
                            orgMembers = it,
                            insiderService = insiderService,
                        )
                    }
                }

                h3(classes = "mt-5") { +"Team Memberships" }
                objects.teamMembersAndIdentities.let {
                    when (it.isEmpty()) {
                        true -> +"No team members."
                        else -> renderTeamAndMembers(scmTeams = objects.scmTeams, members = it, lastActiveAts = objects.lastThreadsViewedAt)
                    }
                }

                h3(classes = "mt-5") { +"Identities" }
                renderIdentities(objects.identities)

                h3(classes = "mt-5") { +"Email Preferences" }
                renderPersonEmailPreferences(path, objects.personEmailPreferences)

                h3(classes = "mt-5") { +"Features" }
                renderClientConfig(path, clientConfig)

                h3(classes = "mt-5") { +"Onboarding States" }
                renderOnboardingStates(path, objects.person)

                if (emailEvents.isNotEmpty()) {
                    h3(classes = "mt-5") { +"Emails Sent" }
                    renderEmailEventActivity(
                        emailEvents,
                        deleteUrl = {
                            "$path/deleteEmailEvent?emailEventId=${it.id}"
                        },
                    )
                }

                if (engagement.activity.isNotEmpty()) {
                    h3(classes = "mt-5") { +"Activity" }
                    renderActivity(engagement.activity)
                }
            }
        }
    }

    private fun FlowContent.renderClientConfig(path: String, clientConfig: ClientConfigBundle) {
        div {
            renderToggleList(
                href = "$path/updatePersonCapability",
                tristate = true,
                items = ClientCapabilityType.allActiveConfigs().map { type ->
                    ToggleItem(
                        label = type.name,
                        name = type.name,
                        checked = clientConfig.capabilities[type]?.value,
                        description = type.description,
                    )
                },
            )
        }
    }

    @Suppress("LongMethod")
    private fun getActions(
        path: String,
        adminIdentity: Identity,
        identities: List<Identity>,
    ): List<MenuItem> {
        return buildList {
            if (identities.isNotEmpty()) {
                val identityImpersonated = identities.firstOrNull { it.id == adminIdentity.impersonatingIdentity }
                if (identityImpersonated != null) {
                    add(
                        MenuItem(
                            href = "$WEB_ROOT/impersonate/stop",
                            label = "Stop Impersonating",
                            style = BootstrapStyle.Success,
                            description = """
                            Stop impersonating ${identityImpersonated.displayName ?: identityImpersonated.username}.
                            """,
                        ),
                    )
                } else {
                    identities.forEach { identity ->
                        add(
                            MenuItem(
                                href = "$WEB_ROOT/impersonate/start?impersonatingIdentityId=${identity.id}",
                                label = when (identities.size) {
                                    1 -> "Impersonate"
                                    else -> "Impersonate ${identity.provider.displayName} Account"
                                },
                                style = BootstrapStyle.Success,
                                description = """
                                Impersonate ${identity.displayNameOrUsername}'s ${identity.provider.displayName} account in all clients.
                                """,
                            ),
                        )
                    }
                }
            }
            add(
                MenuItem(
                    href = "$path/resetVSCodeOnboardingStates",
                    label = "Reset VSCode onboarding states",
                    description = """
                    Clears the VSCode onboarding states for this customer.
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/resetIntellijOnboardingStates",
                    label = "Reset Intellij onboarding states",
                    description = """
                    Clears the Intellij onboarding states for this customer.
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/resetHubOnboardingStates",
                    label = "Reset Hub onboarding states",
                    description = """
                    Clears the Hub onboarding states for this customer.
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/refreshProfile",
                    label = "Refresh profile",
                    description = """
                    Refreshes the profile information for each of this person's login identities.
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/sendWelcomeEmail",
                    label = "Send Welcome Email",
                    description = "Send welcome email",
                ),
            )
            add(
                MenuItem(
                    href = "$path/removeFromInactiveFollowupLists",
                    label = "Remove from Inactive Followup Emails",
                    description = """
                        Removes users from all Inactive Followup Email lists.
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/resetPersonPreferences",
                    label = "Reset preferences",
                    style = BootstrapStyle.Danger,
                    description = """
                    Resets all preferences for this person (i.e. incognito mode).
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/deauthorizePerson",
                    label = "Deauthorize person",
                    style = BootstrapStyle.Danger,
                    description = """
                    Remotely deauthorize OAuth authorization grant for this person in the SCM.
                    The user will be prompted to re-authorize their SCM account the next time they try to sign in.
                    """,
                ),
            )
            add(
                MenuItem(
                    href = "$path/deletePerson",
                    label = "Delete person",
                    style = BootstrapStyle.Danger,
                    description = """
                    Permanently delete this person. This is not recoverable.
                    Also remotely deauthorizes the OAuth authorization grant for this person in the SCM.
                    """,
                ),
            )
        }
    }

    private fun FlowContent.renderPersonEmailPreferences(path: String, personEmailPreferences: PersonEmailPreferences) {
        div {
            renderToggleList(
                href = "$path/updatePersonEmailPreferences",
                tristate = false,
                items = listOf(
                    ToggleItem(
                        label = "digestEmails",
                        name = "digestEmails",
                        checked = personEmailPreferences.digestEmails,
                        description = "Send scheduled digest email.",
                    ),
                    ToggleItem(
                        label = "threadInviteEmails",
                        name = "threadInviteEmails",
                        checked = personEmailPreferences.threadInviteEmails,
                        description = "Send emails when added to a thread as a participant.",
                    ),
                    ToggleItem(
                        label = "recommendedInviteeEmails",
                        name = "recommendedInviteeEmails",
                        checked = personEmailPreferences.recommendedInviteeEmails,
                        description = "Send email to recommend inviting teammates user work closely with to Unblocked.",
                    ),
                ),
            )
        }
    }

    private fun FlowContent.renderOnboardingStates(path: String, person: Person) {
        div {
            renderToggleList(
                href = "$path/updateOnboardingStates",
                tristate = false,
                sort = false,
                items = PersonOnboardingState.entries.map {
                    ToggleItem(
                        label = it.name,
                        name = it.name,
                        checked = person.hasOnboardingStateSet(state = it),
                    )
                },
            )
        }
    }

    private fun FlowContent.renderPerson(
        insiderService: InsiderServiceInterface,
        person: Person,
        engagement: EngagementMetrics,
        path: String,
        identityEmails: List<EmailAddress>,
        campaigns: List<String>,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Person") { avatar(person) }
                property("Display Name", person.customDisplayName)
                property("Primary Email") {
                    submittingDropDownList(
                        href = "$path/updatePrimaryEmail",
                        name = "primaryEmail",
                        options = (identityEmails + person.primaryEmail).distinct().map { DropDownOption(it.value) },
                        selectedValue = DropDownOption(person.primaryEmail.value),
                    )
                }
                property("Release Channel") {
                    submittingDropDownList(
                        href = "$path/updateSubscribedReleaseChannel",
                        name = "subscribedReleaseChannel",
                        default = DropDownOption(
                            value = "none",
                            description = "-",
                        ),
                        options = ReleaseChannel.entries.filterNot {
                            it == ReleaseChannel.NotReleased
                        }.map {
                            DropDownOption(
                                value = it.name,
                                description = it.name,
                            )
                        },
                        selectedValue = DropDownOption(
                            value = person.subscribedReleaseChannel?.name ?: "none",
                            description = person.subscribedReleaseChannel?.name ?: "-",
                        ),
                    )
                }
                property("ID", person.id)
                property("Created", person.createdAt)
                property("Modified", person.modifiedAt)
                property("Last Active", engagement.lastActiveAt)
                engagement.engagement7day?.also { property("Active N/7", it) }
                engagement.engagement30day?.also { property("Active N/30", it) }
                property("Traits") {
                    if (insiderService.isInsiderPerson(personId = person.id)) {
                        badge(bootstrapStyle = BootstrapStyle.Light) { +"Internal" }
                    }
                }
                property("Marketing Campaigns", campaigns.distinct().join(maxWords = 3))
            }
        }
    }

    @Suppress("LongMethod")
    private suspend fun getPersonRelatedObjects(
        personId: PersonId,
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
    ) = suspendedTransaction {
        val (person, identities) = suspendedTransaction {
            PersonModel
                .leftJoin(IdentityModel)
                .selectAll()
                .where { PersonModel.id eq personId }
                .map { row ->
                    Pair(
                        row.toPerson(),
                        row.toIdentityOrNull(),
                    )
                }
                .let { personIdentities: List<Pair<Person, Identity?>> ->
                    Pair(personIdentities.first().first, personIdentities.mapNotNull { it.second })
                }
        }

        val clientVersions = Stores.clientVersionStore.find(identities.map { it.id })

        val teams = suspendedTransaction {
            ScmTeamModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MemberModel,
                    otherColumn = MemberModel.installation,
                    onColumn = ScmTeamModel.installation,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                ) {
                    IdentityModel.person eq person.id
                }
                .select(ScmTeamModel.columns)
                .where {
                    ScmTeamStore.SCM_TEAM_EXISTS_CLAUSE
                }
                .map {
                    it.toScmTeam()
                }
        }

        val teamMembersAndIdentities = suspendedTransaction {
            OrgMemberModel
                .join(
                    otherTable = MemberModel,
                    otherColumn = MemberModel.orgMember,
                    onColumn = OrgMemberModel.id,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                )
                .join(otherTable = IdentityModel, otherColumn = IdentityModel.id, onColumn = MemberModel.identity, joinType = JoinType.INNER)
                .join(
                    joinType = JoinType.INNER,
                    otherTable = PersonModel,
                    onColumn = IdentityModel.person,
                    otherColumn = PersonModel.id,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgModel,
                    otherColumn = OrgModel.id,
                    onColumn = InstallationModel.org,
                )
                .select(
                    OrgModel.columns +
                            OrgMemberModel.columns +
                            MemberModel.columns +
                            IdentityModel.columns,
                )
                .whereAll(
                    PersonModel.id eq person.id,
                    InstallationStore.INSTALLATION_EXISTS,
                    IdentityModel.provider inList Provider.signInCapableProviders,
                )
                .map { it.toOrgMemberAndIdentity() }
        }

        val personEmailPreferences = suspendedTransaction {
            PersonEmailPreferencesModel
                .selectAll()
                .where { PersonEmailPreferencesModel.person eq person.id }
                .map {
                    it.toPersonEmailPreferences()
                }
                .firstOrNull() ?: PersonEmailPreferences.getDefault(person.id)
        }

        val lastThreadsViewedAt = userEngagementStore.getMembersLastActive(
            teamMembersAndIdentities.map { it.member.orgMemberId }.distinct(),
        )

        PersonObjects(
            person = person,
            identities = identities,
            clientVersions = clientVersions,
            scmTeams = teams,
            teamMembersAndIdentities = teamMembersAndIdentities,
            personEmailPreferences = personEmailPreferences,
            lastThreadsViewedAt = lastThreadsViewedAt,
        )
    }

    data class PersonObjects(
        val person: Person,
        val identities: List<Identity>,
        val scmTeams: List<ScmTeam>,
        val teamMembersAndIdentities: List<OrgMemberAndIdentity>,
        val personEmailPreferences: PersonEmailPreferences,
        val lastThreadsViewedAt: Map<OrgMemberId, Instant>,
        val clientVersions: Map<IdentityId, List<ClientVersion>>,
    )

    suspend fun RoutingContext.resetVSCodeOnboardingStates(
        personStore: PersonStore = Stores.personStore,
    ) {
        call.parameters.personId().also { personId ->
            personStore.resetVSCodeOnboardingStates(id = personId)
        }
    }

    suspend fun RoutingContext.resetIntellijOnboardingStates(
        personStore: PersonStore = Stores.personStore,
    ) {
        call.parameters.personId().also { personId ->
            personStore.resetIntellijOnboardingStates(id = personId)
        }
    }

    suspend fun RoutingContext.resetHubOnboardingStates(
        personStore: PersonStore = Stores.personStore,
    ) {
        call.parameters.personId().also { personId ->
            personStore.resetHubOnboardingStates(id = personId)
        }
    }

    suspend fun RoutingContext.updateOnboardingStates(
        personStore: PersonStore = Stores.personStore,
    ) {
        call.parameters.personId().also { personId ->
            val bodyParams = call.receiveParameters()
            PersonOnboardingState.entries.firstNotNullOfOrNull { state ->
                bodyParams[state.name]?.let { state to it.toBooleanStrict() }
            }?.also {
                personStore.setOnboardingFlags(id = personId, flags = mapOf(it))
            }
        }
    }

    suspend fun RoutingContext.updatePersonEmailPreferences() {
        call.parameters.personId().also { personId ->
            val bodyParams = call.receiveParameters()
            suspendedTransaction {
                PersonModel
                    .selectAll()
                    .where { PersonModel.id eq personId }
                    .firstOrNull()
                    ?.let { PersonDAO.wrapRow(it) }
                    ?.let { person ->
                        Stores.personEmailPreferencesStore.upsert(
                            trx = this,
                            person = person,
                            threadInviteEmails = bodyParams["threadInviteEmails"]?.toBooleanStrict(),
                            digestEmails = bodyParams["digestEmails"]?.toBooleanStrict(),
                        )
                    }
            }
        }
    }

    suspend fun RoutingContext.updatePersonCapability(clientConfigService: ClientConfigService) {
        call.parameters.personId().also { personId ->
            val bodyParams = call.receiveParameters()
            bodyParams.names().map { ClientCapabilityType.valueOf(it) }.single().also { key ->
                bodyParams[key.name]?.toBooleanStrictOrNull().also { value ->
                    when (value) {
                        null -> clientConfigService.deletePersonCapability(key = key, personId = personId)
                        else -> clientConfigService.setPersonCapability(key = key, value = value, personId = personId)
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.refreshProfileForPerson(profileService: ProfileService) {
        call.parameters.personId().also { personId ->
            profileService.refreshProfileForPerson(personId)
        }
    }

    suspend fun RoutingContext.deletePerson(
        identityMaintenance: IdentityMaintenance,
        personStore: PersonStore = Stores.personStore,
        sampleQuestionStore: SampleQuestionStore = Stores.sampleQuestionStore,
    ) {
        call.parameters.personId().also { personId ->
            sampleQuestionStore.deleteAllForPerson(personId)
            deauthorizePerson(personId, identityMaintenance)
            personStore.deletePerson(personId)
        }

        call.respondRedirect("$WEB_ROOT/people")
    }

    suspend fun RoutingContext.resetPersonPreferences(
        personPreferencesStore: PersonPreferencesStore = Stores.personPreferencesStore,
    ) {
        call.parameters.personId().also { personId ->
            personPreferencesStore.delete(personId = personId)
        }
    }

    suspend fun RoutingContext.deauthorizePerson(identityMaintenance: IdentityMaintenance) {
        call.parameters.personId().also { personId ->
            deauthorizePerson(personId, identityMaintenance)
        }
    }

    private suspend fun deauthorizePerson(
        personId: PersonId,
        identityMaintenance: IdentityMaintenance,
        personStore: PersonStore = Stores.personStore,
    ) {
        personStore.getIdentitiesForPerson(personId = personId).forEach { identity ->
            runSuspendCatching {
                identityMaintenance.remotelyRevoke(identity.id)
            }
        }
    }

    suspend fun RoutingContext.updatePrimaryEmail() {
        call.parameters.optionalPersonId()?.also { personId ->
            val bodyParams = call.receiveParameters()
            bodyParams["primaryEmail"]?.let(EmailAddress::of)?.also { primaryEmail ->
                runSuspendCatching {
                    Stores.personStore.updatePrimaryEmail(personId, primaryEmail)
                }
            }
        }
    }

    fun RoutingContext.sendWelcomeEmail(
        notificationEventEnqueueService: NotificationEventEnqueueService,
    ) {
        call.parameters.optionalPersonId()?.also { personId ->
            runSuspendCatching {
                notificationEventEnqueueService.enqueueEvent(
                    NotificationEvent.WelcomeEmailEvent(
                        personId = personId,
                        trigger = EmailTrigger.ADMIN_CONSOLE,
                    ),
                )
            }
        }
    }

    suspend fun RoutingContext.deleteEmailEvent(
        emailEventStore: EmailEventStore = Stores.emailEventStore,
    ) {
        call.request.queryParameters.requiredId("emailEventId", ::EmailEventId).also { emailEventId ->
            runSuspendCatching {
                emailEventStore.removeEvent(id = emailEventId)
            }
        }
    }

    @Suppress("MagicNumber")
    private suspend fun engagementMetrics(userEngagementStore: UserEngagementStore = Stores.userEngagementStore, person: Person): EngagementMetrics {
        return EngagementMetrics(
            lastActiveAt = userEngagementStore.getLatestViewedForPerson(trx = null, personId = person.id)?.createdAt,
            engagement7day = when {
                person.createdAt > Instant.nowWithMicrosecondPrecision().minus(7.days) -> null
                else -> userEngagementStore.engagementScoreByPerson(listOf(person.id), 7)[person.id]
            },
            engagement30day = when {
                person.createdAt > Instant.nowWithMicrosecondPrecision().minus(30.days) -> null
                else -> userEngagementStore.engagementScoreByPerson(listOf(person.id), 30)[person.id]
            },
            activity = userEngagementStore.getAggregateEventsForPerson(personId = person.id),
        )
    }

    suspend fun RoutingContext.updatePersonSubscribedReleaseChannel() {
        call.parameters.personId().also { personId ->
            val bodyParams = call.receiveParameters()
            bodyParams["subscribedReleaseChannel"]?.also { channel ->
                val releaseChannel = runSuspendCatching {
                    ReleaseChannel.valueOf(channel)
                }.getOrNull()

                suspendedTransaction {
                    PersonDAO.findById(personId)?.subscribedReleaseChannel = releaseChannel
                }
            }
        }
    }

    fun RoutingContext.removeFromInactiveFollowupLists(
        notificationEventEnqueueService: NotificationEventEnqueueService,
    ) {
        call.parameters.optionalPersonId()?.also { personId ->
            runSuspendCatching {
                notificationEventEnqueueService.enqueueEvent(
                    NotificationEvent.RemoveFromInactiveFollowupEvent(
                        personId = personId,
                    ),
                )
            }
        }
    }
}
