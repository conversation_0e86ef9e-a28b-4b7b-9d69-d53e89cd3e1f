package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.ProductFeedbackResponseInstallations
import com.nextchaptersoftware.db.models.ProductFeedbackResponseType
import com.nextchaptersoftware.db.stores.ProductFeedbackStore.ProductFeedbackResponseAggregate
import com.nextchaptersoftware.db.stores.Stores
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.uri
import io.ktor.server.routing.RoutingContext
import kotlinx.html.DIV
import kotlinx.html.FlowContent
import kotlinx.html.InputAutoComplete
import kotlinx.html.InputType
import kotlinx.html.TD
import kotlinx.html.ThScope
import kotlinx.html.div
import kotlinx.html.fieldSet
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h5
import kotlinx.html.id
import kotlinx.html.input
import kotlinx.html.label
import kotlinx.html.onClick
import kotlinx.html.role
import kotlinx.html.span
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object ProductFeedbackPage {

    enum class PageViewOption(val label: String, val description: String) {
        All("All Product Feedback", "Product Feedback from all Orgs"),
        EnterpriseTrial(
            "Current Enterprise Trial Product Feedback",
            "Product Feedback from Orgs on valid Enterprise Trial",
        ),
    }

    suspend fun RoutingContext.renderProductFeedbackPage(page: AdminPage) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        val viewOption = call.request.queryParameters["view"]
            ?.let { PageViewOption.valueOf(it) }
            ?: PageViewOption.All

        val orgIds = when (viewOption) {
            PageViewOption.All -> null
            PageViewOption.EnterpriseTrial -> Stores.orgStore.findAllOnTrial().map { it.id }
        }

        val data = Stores.productFeedbackStore.getProductFeedbackResponseAggregate(orgIds = orgIds)

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            content {
                div(classes = "d-flex justify-content-between") {
                    h1 {
                        style = "margin-right: auto;"
                        +viewOption.label
                    }
                    form(action = call.request.uri) {
                        fieldSet(classes = "form-group") {
                            renderViewButtons(viewOption)
                        }
                    }
                }

                div(classes = "row mt-4") {
                    div(classes = "col-md-3") {
                        renderProductFeedbackCard(
                            responseType = null,
                            data.all.size,
                        )
                    }
                    div(classes = "col-md-4") {
                        renderProductFeedbackCard(
                            ProductFeedbackResponseType.Positive,
                            data.mapping[ProductFeedbackResponseType.Positive]?.size ?: 0,
                        )
                    }
                    div(classes = "col-md-4") {
                        renderProductFeedbackCard(
                            ProductFeedbackResponseType.Negative,
                            data.mapping[ProductFeedbackResponseType.Negative]?.size ?: 0,
                        )
                    }
                }

                div(classes = "row mt-4") {
                    data.all.let { feedbackList ->
                        when (feedbackList.isEmpty()) {
                            true -> +"No product feedback found for this org."
                            else -> renderProductFeedbackResponses(feedbackList)
                        }
                    }
                }
            }
        }
    }

    private fun FlowContent.renderViewButtons(selectedView: PageViewOption) {
        div(classes = "btn-group py-3") {
            role = "group"
            PageViewOption.entries.forEach { view ->
                input(type = InputType.radio, classes = "btn-check", name = "view") {
                    val viewName = view.name
                    val btnId = "btnradio_view_$viewName"

                    autoComplete = InputAutoComplete.off
                    checked = selectedView == view
                    id = btnId
                    onClick = "this.form.submit();"
                    value = viewName
                    label(classes = "btn btn-outline-primary") {
                        attributes["for"] = btnId
                        style = "width: 100px;"
                        +viewName
                    }
                }
            }
        }
    }

    private fun FlowContent.renderProductFeedbackResponses(
        feedbackResponses: List<ProductFeedbackResponseAggregate>,
    ) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Org" }
                    th(scope = ThScope.col) { +"Org Member" }
                    th(scope = ThScope.col) { +"Sentiment" }
                    th(scope = ThScope.col, classes = "noSort") { +"Description" }
                    th(scope = ThScope.col, classes = "noSort") { +"Installations" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col) { +"Product Agent" }
                }
            }
            tbody(classes = "table-dark") {
                feedbackResponses.forEach { feedbackResponse ->
                    tr {
                        attributes["onclick"] = onClickAction("$WEB_ROOT/orgs/${feedbackResponse.org.id}/productFeedback")
                        td { profile(feedbackResponse.org) }
                        td { profile(feedbackResponse.person) }
                        td { asBadge(feedbackResponse.feedbackType) }
                        td { +(feedbackResponse.feedbackDescription ?: "-") }
                        td { renderInstallationState(feedbackResponse.installationsState) }
                        td { timeAgo(feedbackResponse.createdAt) }
                        td { asBadge(feedbackResponse.productAgent) }
                    }
                }
            }
        }
    }

    private fun DIV.renderProductFeedbackCard(responseType: ProductFeedbackResponseType? = null, count: Int) {
        val cardBackground = when (responseType) {
            ProductFeedbackResponseType.Positive -> "bg-success"
            ProductFeedbackResponseType.Negative -> "bg-danger"
            else -> ""
        }
        div(classes = "card $cardBackground") {
            div(classes = "card-body") {
                h5(classes = "card-title") { +(responseType?.name ?: "Total") }
                h1(classes = "display-3") { +"$count" }
            }
        }
    }

    @Suppress("MagicNumber")
    private fun TD.renderInstallationState(installationsState: ProductFeedbackResponseInstallations) {
        val connectedInstallations = installationsState.connectedInstallations.size
        val unconnectedInstallations = installationsState.unconnectedInstallations.size
        val suppressedInstallations = installationsState.suppressedInstallations?.size ?: 0

        return div {
            span(classes = "badge text-bg-primary") {
                +"$connectedInstallations Connected"
            }
            span(classes = "badge text-bg-danger") {
                +"$suppressedInstallations Suppressed"
            }
            span(classes = "badge text-bg-secondary") {
                +"$unconnectedInstallations Unconnected"
            }
        }
    }
}
