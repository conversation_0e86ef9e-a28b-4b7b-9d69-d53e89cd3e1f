package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalSlackTeamId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.SlackChannel
import com.nextchaptersoftware.db.models.SlackChannelDAO
import com.nextchaptersoftware.db.models.SlackChannelModel
import com.nextchaptersoftware.db.models.SlackChannelPatternPreferences
import com.nextchaptersoftware.db.models.SlackChannelPatternPreferencesDAO
import com.nextchaptersoftware.db.models.SlackChannelPatternPreferencesModel
import com.nextchaptersoftware.db.models.SlackChannelPreferences
import com.nextchaptersoftware.db.models.SlackChannelPreferencesDAO
import com.nextchaptersoftware.db.models.SlackChannelPreferencesModel
import com.nextchaptersoftware.db.models.SlackTeam
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.models.SlackTeamModel
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import org.jetbrains.exposed.sql.update

object SlackTeamPage {
    private data class SlackTeamObjects(
        val slackTeam: SlackTeam,
        val org: Org,
        val slackChannelPatternPreferences: List<SlackChannelPatternPreferences>,
        val slackChannelPreferences: List<SlackChannelPreferences>,
        val slackChannelsConfiguredForIngestion: List<SlackChannel>,
    )

    @Suppress("LongMethod")
    private suspend fun getSlackTeamRelatedObjects(slackTeamId: SlackTeamId) = suspendedTransaction {
        val slackTeam = requireNotNull(SlackTeamDAO.findById(slackTeamId))
        val slackChannelPatternPreferences = SlackChannelPatternPreferencesDAO.find {
            SlackChannelPatternPreferencesModel.slackTeam eq slackTeamId
        }
        val slackChannelPreferences = SlackChannelPreferencesDAO.find { SlackChannelPreferencesModel.slackTeam eq slackTeamId }
        val slackChannels = SlackChannelDAO.find {
            SlackChannelModel.id inList slackChannelPreferences.map { it.asDataModel().slackChannelId }
        }

        SlackTeamObjects(
            slackTeam = slackTeam.asDataModel(),
            org = slackTeam.installation.org.asDataModel(),
            slackChannelPatternPreferences = slackChannelPatternPreferences.map { it.asDataModel() },
            slackChannelPreferences = slackChannelPreferences.map { it.asDataModel() },
            slackChannelsConfiguredForIngestion = slackChannels.map { it.asDataModel() },
        )
    }

    suspend fun RoutingContext.renderSlackTeamPage(
        page: AdminPage,
    ) {
        val breadcrumb = call.makeBreadcrumb()

        val path = call.request.path()
        val related = listOf(
            MenuItem(href = "$path/slackChannels", label = "Slack Channels", description = "Slack channels for this slack team."),
            MenuItem(href = "$path/slackIngestions", label = "Slack Ingestions", description = "Slack ingestions for this slack team."),
        )

        val adminIdentity = call.getAdminIdentity()
        val org = call.parameters.org()
        val slackTeamId = call.parameters.requiredId("slackTeamId").let(::SlackTeamId)

        val objects = getSlackTeamRelatedObjects(slackTeamId)
        val actions = slackTeamActions(path = path)

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            actionMenu { renderActionMenu(actions, sort = false) }
            relatedMenu { renderRelatedMenu(related) }
            content {
                h1 { +page.label }
                renderSlackTeam(slackTeam = objects.slackTeam)

                h3(classes = "mt-5") { +"Slack Channel Preferences" }
                renderSlackChannelPreferences(
                    slackChannelPreferences = objects.slackChannelPreferences,
                    slackChannelsConfiguredForIngestion = objects.slackChannelsConfiguredForIngestion,
                )

                h3(classes = "mt-5") { +"Slack Channel Pattern Preferences" }
                renderSlackChannelPatternPreferences(slackChannelPatternPreferences = objects.slackChannelPatternPreferences)
            }
        }
    }

    private fun FlowContent.renderSlackTeam(
        slackTeam: SlackTeam,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Name", slackTeam.name)
                property("Avatar") { avatar(slackTeam) }
                property("Created", slackTeam.createdAt)
                property("Modified", slackTeam.modifiedAt)
                property("Slack Team Id", slackTeam.slackExternalTeamId)
                property("Domain", slackTeam.domain)
                property("Bot User Id", slackTeam.botUserId)
                property("Bot Scope", slackTeam.botScope)
                property("Authed User Id", slackTeam.userId)
                property("Authed User Scope", slackTeam.userScope)
            }
        }
    }

    private fun FlowContent.renderSlackChannelPatternPreferences(
        slackChannelPatternPreferences: List<SlackChannelPatternPreferences>,
    ) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"id" }
                    th(scope = ThScope.col) { +"Created At" }
                    th(scope = ThScope.col) { +"Modified At" }
                    th(scope = ThScope.col) { +"Slack Channel Pattern" }
                    th(scope = ThScope.col) { +"Auto Answer Mode" }
                }
            }
            tbody(classes = "table-dark") {
                slackChannelPatternPreferences.forEach { slackChannelPatternPreference ->
                    tr {
                        td { +(slackChannelPatternPreference.id.toString()) }
                        td { timeAgo(slackChannelPatternPreference.createdAt) }
                        td { timeAgo(slackChannelPatternPreference.modifiedAt) }
                        td { +(slackChannelPatternPreference.slackChannelPattern) }
                        td { asBadge(slackChannelPatternPreference.autoAnswerMode) }
                    }
                }
            }
        }
    }

    private fun FlowContent.renderSlackChannelPreferences(
        slackChannelPreferences: List<SlackChannelPreferences>,
        slackChannelsConfiguredForIngestion: List<SlackChannel>,
    ) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"id" }
                    th(scope = ThScope.col) { +"Created At" }
                    th(scope = ThScope.col) { +"Modified At" }
                    th(scope = ThScope.col) { +"Slack Channel" }
                    th(scope = ThScope.col) { +"IsChannel" }
                    th(scope = ThScope.col) { +"IsPrivate" }
                    th(scope = ThScope.col) { +"Auto Answer Mode" }
                }
            }
            tbody(classes = "table-dark") {
                slackChannelPreferences.forEach { slackChannelPreference ->
                    val slackChannel = slackChannelsConfiguredForIngestion.firstOrNull { it.id == slackChannelPreference.slackChannelId }
                    tr {
                        td { +(slackChannelPreference.id.toString()) }
                        td { timeAgo(slackChannelPreference.createdAt) }
                        td { timeAgo(slackChannelPreference.modifiedAt) }
                        td { +(slackChannel?.name ?: "") }
                        td { +(slackChannel?.isChannel?.toString() ?: "") }
                        td { +(slackChannel?.isPrivate?.toString() ?: "") }
                        td { asBadge(slackChannelPreference.autoAnswerMode) }
                    }
                }
            }
        }
    }

    @Suppress("LongMethod")
    private fun slackTeamActions(
        path: String,
    ) = buildList {
        addAll(
            listOf(
                MenuItem(
                    href = "$path/discardSlackCommandScopes",
                    label = "Set slack team bot command scopes to empty value",
                    description = """
                    Set slack team bot command scopes to empty value.
                    """.trimIndent(),
                ),
                MenuItem(
                    href = "$path/discardSlackPrivateUserScopes",
                    label = "Set slack team user private scopes to empty value",
                    description = """
                    Set slack team user private scopes to empty value.
                    """.trimIndent(),
                ),
            ),
        )
    }

    suspend fun RoutingContext.discardSlackCommandScopes() {
        call.parameters.optionalSlackTeamId()?.let { slackTeamId ->
            suspendedTransaction {
                val currentScope = SlackTeamModel
                    .select(SlackTeamModel.botScope)
                    .where {
                        SlackTeamModel.id eq slackTeamId
                    }
                    .map { it[SlackTeamModel.botScope] }
                    .first()

                SlackTeamModel.update({ SlackTeamModel.id eq slackTeamId }) {
                    it[botScope] = currentScope.replace("commands,", "")
                }
            }
        }
    }

    suspend fun RoutingContext.discardSlackPrivateUserScopes() {
        call.parameters.optionalSlackTeamId()?.let { slackTeamId ->
            suspendedTransaction {
                SlackTeamModel.update({ SlackTeamModel.id eq slackTeamId }) {
                    it[userScope] = ""
                }
            }
        }
    }
}
