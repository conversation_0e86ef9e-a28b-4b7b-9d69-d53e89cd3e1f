@file:Suppress("ktlint:nextchaptersoftware:no-nano-datetime")

package com.nextchaptersoftware.adminwebservice.auth

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTVerifier
import com.auth0.jwt.algorithms.Algorithm
import com.nextchaptersoftware.adminwebservice.config.AdminConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.security.RSAKeyLoader
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.date
import io.ktor.util.decodeBase64String
import kotlin.time.Clock
import kotlin.time.Duration.Companion.seconds
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class AdminAuthenticationProvider(
    adminConfig: AdminConfig,
    private val insiderService: InsiderServiceInterface,
) {
    companion object {
        private const val ADMIN_AUTHENTICATION_AUDIENCE = "com.unblocked.token.admin"
        private val LEEWAY = 10.seconds.inWholeSeconds
    }

    private val tokenExpiry = adminConfig.adminAuthentication.adminAuthTokenExpiry
    val tokenExpiryInSeconds = tokenExpiry.inWholeSeconds
    private val adminTokenIssuer = adminConfig.adminAuthentication.adminTokenIssuer
    val adminSessionCookieName = adminConfig.adminAuthentication.adminAuthCookieName

    fun createSessionToken(userEmail: EmailAddress): String? {
        val identityId = insiderService.getAdminIdentityByEmail(userEmail)
            ?: return null

        return generateAdminAuthToken(
            email = userEmail,
            identityId = identityId,
            permissions = listOf("all"),
        )
    }

    fun verifySessionToken(token: String) = runSuspendCatching {
        adminAuthTokenVerifier.verify(token)
        true
    }.getOrElse {
        false
    }

    private val adminTokenSigningAlgorithm = runSuspendCatching {
        Algorithm.RSA256(
            null,
            RSAKeyLoader.createPrivateKey(adminConfig.adminAuthentication.adminTokenPrivateKey.value.decodeBase64String()),
        )
    }.onFailure {
        LOGGER.errorSync(it) { "Failed to load signing algorithm. This is not necessarily an issue depending on the service." }
        Algorithm.HMAC512("dummy signer")
    }.getOrThrow()

    private val adminTokenVerificationAlgorithm = runSuspendCatching {
        Algorithm.RSA256(
            RSAKeyLoader.createPublicKey(adminConfig.adminAuthentication.adminTokenPublicKey.value.decodeBase64String()),
            null,
        )
    }.onFailure {
        LOGGER.errorSync(it) { "Failed to load verification algorithm. This is not necessarily an issue depending on the service." }
        Algorithm.HMAC512("always reject")
    }.getOrThrow()

    private val adminAuthTokenVerifier: JWTVerifier = JWT
        .require(adminTokenVerificationAlgorithm)
        .withIssuer(adminTokenIssuer)
        .withAudience(ADMIN_AUTHENTICATION_AUDIENCE)
        .acceptLeeway(LEEWAY)
        .build()

    private fun generateAdminAuthToken(
        email: EmailAddress,
        identityId: IdentityId,
        permissions: List<String>,
    ): String {
        val now = Clock.System.now()
        val expiry = now + tokenExpiry
        val builder = JWT.create()
            .withAudience(ADMIN_AUTHENTICATION_AUDIENCE)
            .withIssuer(adminTokenIssuer)
            .withSubject(email.value)
            .withIssuedAt(now.date)
            .withNotBefore(now.date)
            .withExpiresAt(expiry.date)

        val arrayClaim: Array<String> = permissions.toTypedArray()
        builder.withArrayClaim(Jwt.Claim.AdminPermissions.value, arrayClaim)
        builder.withClaim(Jwt.Claim.AdminIdentityId.value, identityId.toString())
        return builder.sign(adminTokenSigningAlgorithm)
    }
}
