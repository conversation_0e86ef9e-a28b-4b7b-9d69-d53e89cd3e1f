package com.nextchaptersoftware.adminwebservice.adminweb.utils

import kotlin.time.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * Formats a given [Instant] as a [String] that can be directly used in a
 * Bootstrap date picker.
 *
 * The format is "yyyy-MM-dd".
 *
 * @return the formatted date string
 */
fun Instant.toBootstrapDatePickerFormat() = this.toLocalDateTime(TimeZone.UTC).date.toString()
