package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.asProgressBar
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.component.badge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.models.BulkEngagementMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgsPage.renderOrgBadges
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ClientVersion
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgMember
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgMemberAndIdentity
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.db.stores.UserEngagementStore
import com.nextchaptersoftware.db.stores.toOrgMemberAndIdentity
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.utils.epoch
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.days
import kotlin.time.Instant
import kotlinx.html.FlowContent
import kotlinx.html.InputAutoComplete
import kotlinx.html.InputType
import kotlinx.html.TBODY
import kotlinx.html.ThScope
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.id
import kotlinx.html.input
import kotlinx.html.label
import kotlinx.html.role
import kotlinx.html.span
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.title
import kotlinx.html.tr
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.selectAll

/**
 * Designed to show a list of members from an Org.
 * See [MembersPage] for a similar page that shows members from an Installation or an ScmTeam.
 */
object OrgMembersPage {

    enum class OrgMemberView {
        /**
         * Show [Current] members only.
         */
        Current,

        /**
         * Show [Past] members only.
         */
        Past,

        /**
         * Show all [Current] and [Past] members, with their associated members.
         */
        Associated,
    }

    data class MemberObjects(
        val lastActiveAts: Map<OrgMemberId, Instant> = mapOf(),
        val clientVersions: Map<IdentityId, List<ClientVersion>> = mapOf(),
    )

    @Suppress("LongMethod")
    suspend fun RoutingContext.renderOrgMembersPage(
        page: AdminPage,
        orgMemberView: OrgMemberView = OrgMemberView.Past,
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
        memberStore: MemberStore = Stores.memberStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val org = call.parameters.org()

        val rbac = orgSettingsStore.getRoleBasedAccessControl(orgId = org.id)

        val members = suspendedTransaction {
            MemberModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgMemberModel,
                    otherColumn = OrgMemberModel.id,
                    onColumn = MemberModel.orgMember,
                )
                .join(
                    joinType = JoinType.INNER,
                    onColumn = MemberModel.identity,
                    otherColumn = IdentityModel.id,
                    otherTable = IdentityModel,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                ) {
                    InstallationModel.org eq org.id
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgModel,
                    otherColumn = OrgModel.id,
                    onColumn = InstallationModel.org,
                )
                .selectAll()
                .whereAll(
                    MemberStore.IS_PRIMARY_MEMBER_CLAUSE,
                    when (orgMemberView) {
                        OrgMemberView.Current -> MemberStore.IS_CURRENT_MEMBER_CLAUSE
                        OrgMemberView.Past -> MemberModel.isCurrentMember eq false
                        OrgMemberView.Associated -> null
                    },
                )
                .map { it.toOrgMemberAndIdentity() }
        }

        val associatedProfiles = if (orgMemberView == OrgMemberView.Associated) {
            val associatedIds = Stores.memberAssociationStore.resolveMemberIds(
                orgId = org.id,
                memberIds = members.map { it.member.id }.toSet(),
            )
            memberStore.getOrgMemberIdentityBundles(orgId = org.id, memberIds = associatedIds)
        } else {
            mapOf()
        }.values

        val objects: MemberObjects = when (orgMemberView) {
            OrgMemberView.Associated -> MemberObjects()

            OrgMemberView.Past -> MemberObjects()

            OrgMemberView.Current -> MemberObjects(
                lastActiveAts = userEngagementStore.getMembersLastActive(
                    members.filter { it.identity.hasAccount }.map { it.orgMember.id },
                ),
                clientVersions = Stores.clientVersionStore.find(
                    members
                        .filter { (_, _, identity) -> identity.hasAccount }
                        .map { (_, _, identity) -> identity.id },
                ),
            )
        }
        val engagement = engagementMetrics(
            userEngagementStore = userEngagementStore,
            members = members.filter { it.identity.hasAccount },
        )

        val adminIdentity = call.getAdminIdentity()

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org = org) }
            content {
                val title = "${orgMemberView.name} Members"

                div(classes = "d-flex justify-content-between") {
                    h1 {
                        style = "margin-right: auto;"
                        +title
                    }
                    renderMemberButtons(orgMemberView)
                }

                when (orgMemberView) {
                    OrgMemberView.Past -> {
                        renderMembers(members = members, showCurrentStatus = false)
                    }

                    OrgMemberView.Current -> {
                        renderCurrentMembers(
                            members = members,
                            clientVersions = objects.clientVersions,
                            lastActiveAts = objects.lastActiveAts,
                            engagement = engagement,
                            showUnblockedRoles = rbac,
                        )
                    }

                    OrgMemberView.Associated -> {
                        renderAssociatedMembers(
                            org = org,
                            members = members.filter { it.member.isCurrentMember },
                            associatedProfiles = associatedProfiles,
                            label = "Current",
                        )
                        renderAssociatedMembers(
                            org = org,
                            members = members.filterNot { it.member.isCurrentMember },
                            associatedProfiles = associatedProfiles,
                            label = "Past",
                        )
                    }
                }
            }
        }
    }

    fun FlowContent.renderOrgAndOrgMembers(
        orgs: List<Org>,
        orgMembers: List<OrgMemberAndIdentity>,
        insiderService: InsiderServiceInterface,
    ) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Org" }
                    th(scope = ThScope.col) { +"Provider" }
                    th(scope = ThScope.col) { +"Traits" }
                }
            }
            tbody(classes = "table-dark") {
                orgs
                    .sortedBy { it.displayName }
                    .forEach { org ->
                        val found = orgMembers.firstOrNull { org.id == it.orgMember.orgId } ?: return@forEach
                        renderOrgAndOrgMember(
                            org = org,
                            orgMember = found.orgMember,
                            identity = found.identity,
                            insiderService = insiderService,
                        )
                    }
            }
        }
    }

    private fun TBODY.renderOrgAndOrgMember(
        org: Org,
        orgMember: OrgMember,
        identity: Identity,
        insiderService: InsiderServiceInterface,
    ) {
        tr {
            attributes["onclick"] = onClickAction("$WEB_ROOT/orgs/${org.id}/org_members/${orgMember.id}")
            style = "cursor: pointer;"

            td {
                avatar(org)
                div(classes = "small text-muted d-none d-lg-inline-block") {
                    +org.displayName
                }
            }
            td {
                this.attributes["data-sort"] = identity.provider.displayName
                asBadge(identity.provider)
            }
            td {
                renderOrgBadges(
                    org = org,
                    insiderService = insiderService,
                )
            }
        }
    }

    private fun FlowContent.renderCurrentMembers(
        members: List<OrgMemberAndIdentity>,
        clientVersions: Map<IdentityId, List<ClientVersion>>,
        lastActiveAts: Map<OrgMemberId, Instant>,
        engagement: BulkEngagementMetrics<OrgMemberId>,
        showUnblockedRoles: Boolean = false,
    ) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    th(scope = ThScope.col, classes = "noSort noSearch") { }
                    th(scope = ThScope.col) { +"Member" }
                    th(scope = ThScope.col) { +"Providers" }
                    if (showUnblockedRoles) {
                        th(scope = ThScope.col) { +"Unblocked Role" }
                    }
                    th(scope = ThScope.col) { +"SCM Role" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Modified" }
                    th(scope = ThScope.col, classes = "noSearch") {
                        +"Last Active"
                        span {
                            title = "The last time the user viewed content in the Desktop app or Dashboard, " +
                                    "or viewed the tutorial, a thread, a PR, the sidebar, or insights panel in an IDE."
                            +" ℹ️"
                        }
                    }
                    th(scope = ThScope.col) { +"Traits" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Active N/7" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Active N/30" }
                    th(scope = ThScope.col) { +"Versions" }
                }
            }
            tbody(classes = "table-dark") {
                members
                    .sortedWith { a, b -> compareOrgMembers(lastActiveAts, a, b) }
                    .groupBy { it.orgMember.id }
                    .forEach { (_, orgMemberAndIdentities) ->
                        renderCurrentMember(
                            orgMemberAndIdentities = orgMemberAndIdentities,
                            clientVersions = orgMemberAndIdentities.map { it.identity.id }.mapNotNull { clientVersions[it] }.flatten(),
                            lastActiveAts = lastActiveAts,
                            engagement = engagement,
                            showUnblockedRoles = showUnblockedRoles,
                        )
                    }
            }
        }
    }

    private fun TBODY.renderCurrentMember(
        orgMemberAndIdentities: List<OrgMemberAndIdentity>,
        clientVersions: List<ClientVersion>,
        lastActiveAts: Map<OrgMemberId, Instant>,
        engagement: BulkEngagementMetrics<OrgMemberId>,
        showUnblockedRoles: Boolean,
    ) {
        val (orgMember, member, identity, org) = orgMemberAndIdentities.sortedWith(
            compareByDescending<OrgMemberAndIdentity> {
                it.identity.provider.isSignInCapable
            }.thenByDescending {
                it.identity.provider.isScmProvider
            }.thenBy {
                it.member.createdAt
            },
        ).first()

        tr {
            attributes["onclick"] = onClickAction("$WEB_ROOT/orgs/${org.id}/installations/${member.installationId}/members/${member.id}")
            style = "cursor: pointer;"

            td(classes = "index") { }
            td { profile(identity) }
            td { orgMemberAndIdentities.map { it.identity }.distinctBy { it.id }.map { asBadge(it.provider) } }
            if (showUnblockedRoles) {
                td { asBadge(orgMember.selectedRole) }
            }
            td { asBadge(member.providerRole) }
            td { timeAgo(member.createdAt) }
            td { timeAgo(member.modifiedAt) }
            td { timeAgo(lastActiveAts[orgMember.id]) }
            td {
                if (identity.isBot) {
                    badge(bootstrapStyle = BootstrapStyle.Light) { +"Bot" }
                }
                if (member.ignoreThreads) {
                    badge(bootstrapStyle = BootstrapStyle.Warning) { +"Ignore Threads" }
                }
            }
            td { engagement.engagement7day[orgMember.id]?.let { asProgressBar(it) } ?: +"-" }
            td { engagement.engagement30day[orgMember.id]?.let { asProgressBar(it) } ?: +"-" }
            td { clientVersions.distinctBy { it.productAgent to it.productNumber }.sortedBy { it.productAgent.name }.map(::asBadge) }
        }
    }

    private fun FlowContent.renderMembers(
        members: List<OrgMemberAndIdentity>,
        clientVersions: Map<IdentityId, List<ClientVersion>>? = null,
        lastActiveAts: Map<OrgMemberId, Instant>? = null,
        engagement: BulkEngagementMetrics<OrgMemberId>? = null,
        showCurrentStatus: Boolean = true,
        showUnblockedRoles: Boolean = false,
        showProviderRoles: Boolean = false,
    ) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    th(scope = ThScope.col, classes = "noSort noSearch") { }
                    th(scope = ThScope.col) { +"Member" }
                    if (showCurrentStatus) {
                        th(scope = ThScope.col, classes = "noSearch") { +"Is Current Member" }
                    }
                    th(scope = ThScope.col) { +"Provider" }
                    if (showUnblockedRoles) {
                        th(scope = ThScope.col) { +"Unblocked Role" }
                    }
                    if (showProviderRoles) {
                        th(scope = ThScope.col) { +"SCM Role" }
                    }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Modified" }
                    lastActiveAts?.also {
                        th(scope = ThScope.col, classes = "noSearch") {
                            +"Last Active"
                            span {
                                title = "The last time the user viewed content in the Desktop app or Dashboard, " +
                                        "or viewed the tutorial, a thread, a PR, the sidebar, or insights panel in an IDE."
                                +" ℹ️"
                            }
                        }
                    }
                    th(scope = ThScope.col) { +"Traits" }
                    engagement?.also {
                        th(scope = ThScope.col, classes = "noSearch") { +"Active N/7" }
                        th(scope = ThScope.col, classes = "noSearch") { +"Active N/30" }
                    }
                    clientVersions?.also {
                        th(scope = ThScope.col) { +"Versions" }
                    }
                }
            }
            tbody(classes = "table-dark") {
                members
                    .sortedWith { a, b -> compareOrgMembers(lastActiveAts, a, b) }
                    .forEach { orgMemberAndIdentity ->
                        renderMember(
                            org = orgMemberAndIdentity.org,
                            orgMember = orgMemberAndIdentity.orgMember,
                            member = orgMemberAndIdentity.member,
                            identity = orgMemberAndIdentity.identity,
                            clientVersions = clientVersions,
                            lastActiveAts = lastActiveAts,
                            engagement = engagement,
                            showCurrentStatus = showCurrentStatus,
                            showUnblockedRoles = showUnblockedRoles,
                            showProviderRoles = showProviderRoles,
                        )
                    }
            }
        }
    }

    private fun TBODY.renderMember(
        org: Org,
        orgMember: OrgMember,
        member: Member,
        identity: Identity,
        clientVersions: Map<IdentityId, List<ClientVersion>>? = null,
        lastActiveAts: Map<OrgMemberId, Instant>? = null,
        engagement: BulkEngagementMetrics<OrgMemberId>? = null,
        showCurrentStatus: Boolean = true,
        showUnblockedRoles: Boolean,
        showProviderRoles: Boolean,
    ) {
        tr {
            attributes["onclick"] = onClickAction("$WEB_ROOT/orgs/${org.id}/installations/${member.installationId}/members/${member.id}")
            style = "cursor: pointer;"

            td(classes = "index") { }
            td { profile(identity) }
            if (showCurrentStatus) {
                td { asBadge(member.isCurrentMember) }
            }
            td {
                attributes["data-sort"] = identity.provider.displayName
                asBadge(identity.provider)
            }
            if (showUnblockedRoles) {
                td { asBadge(orgMember.selectedRole) }
            }
            if (showProviderRoles) {
                td { asBadge(member.providerRole) }
            }
            td { timeAgo(member.createdAt) }
            td { timeAgo(member.modifiedAt) }
            lastActiveAts?.also {
                td { timeAgo(lastActiveAts[member.orgMemberId]) }
            }
            td {
                if (identity.isBot) {
                    badge(bootstrapStyle = BootstrapStyle.Light) { +"Bot" }
                }
                if (member.ignoreThreads) {
                    badge(bootstrapStyle = BootstrapStyle.Warning) { +"Ignore Threads" }
                }
            }
            engagement?.also {
                td {
                    engagement.engagement7day[member.orgMemberId]?.let { asProgressBar(it) } ?: +"-"
                }
                td {
                    engagement.engagement30day[member.orgMemberId]?.let { asProgressBar(it) } ?: +"-"
                }
            }
            clientVersions?.also {
                td {
                    clientVersions[identity.id]?.sortedBy { it.productAgent.name }?.map(::asBadge)
                }
            }
        }
    }

    @Suppress("CyclomaticComplexMethod", "ReturnCount")
    internal fun compareOrgMembers(
        lastActiveAts: Map<OrgMemberId, Instant>?,
        a: OrgMemberAndIdentity,
        b: OrgMemberAndIdentity,
    ): Int {
        // unblocked user
        if (a.identity.hasAccount != b.identity.hasAccount) {
            return when {
                a.identity.hasAccount -> -1
                else -> 1
            }
        }

        // last viewed
        val aMember = a.member
        val bMember = b.member
        lastActiveAts?.also {
            val aLastActive = lastActiveAts[aMember.orgMemberId]?.epochSeconds ?: Instant.epoch.epochSeconds
            val bLastActive = lastActiveAts[bMember.orgMemberId]?.epochSeconds ?: Instant.epoch.epochSeconds
            if (aLastActive != bLastActive) {
                return (bLastActive - aLastActive).toInt()
            }
        }

        // is current member
        if (aMember.isCurrentMember != bMember.isCurrentMember) {
            return bMember.isCurrentMember.compareTo(aMember.isCurrentMember)
        }

        // unblocked role
        if (a.orgMember.selectedRole != b.orgMember.selectedRole) {
            return (b.orgMember.selectedRole?.dbOrdinal ?: 0).compareTo(a.orgMember.selectedRole?.dbOrdinal ?: 0)
        }

        // provider role
        if (aMember.providerRole != bMember.providerRole) {
            return (bMember.providerRole?.dbOrdinal ?: 0).compareTo(aMember.providerRole?.dbOrdinal ?: 0)
        }

        // created at
        if (bMember.createdAt.epochSeconds != aMember.createdAt.epochSeconds) {
            return bMember.createdAt.epochSeconds.compareTo(aMember.createdAt.epochSeconds)
        }

        // id
        return (bMember.id.value.compareTo(aMember.id.value))
    }

    @Suppress("MagicNumber")
    private suspend fun engagementMetrics(
        userEngagementStore: UserEngagementStore = Stores.userEngagementStore,
        members: List<OrgMemberAndIdentity>,
        personStore: PersonStore = Stores.personStore,
    ): BulkEngagementMetrics<OrgMemberId> {
        val membersWithAccountCreation = members.mapNotNull {
            it.identity.person
                ?.let { personId -> personStore.findById(id = personId)?.createdAt }
                ?.let { personCreatedAt -> it.orgMember.id to personCreatedAt }
        }

        return BulkEngagementMetrics(
            engagement7day = membersWithAccountCreation
                .filter { (_, personCreatedAt) -> personCreatedAt < Instant.nowWithMicrosecondPrecision().minus(7.days) }
                .map { (memberId, _) -> memberId }
                .let { memberIds -> userEngagementStore.engagementScoreByOrgMember(memberIds, 7) },
            engagement30day = membersWithAccountCreation
                .filter { (_, personCreatedAt) -> personCreatedAt < Instant.nowWithMicrosecondPrecision().minus(30.days) }
                .map { (memberId, _) -> memberId }
                .let { memberIds -> userEngagementStore.engagementScoreByOrgMember(memberIds, 30) },
        )
    }

    private fun FlowContent.renderMemberButtons(orgMemberView: OrgMemberView) {
        div(classes = "btn-group py-3") {
            role = "group"
            OrgMemberView.entries.forEach { status ->
                input(type = InputType.radio, classes = "btn-check", name = "btnradio") {
                    val name = status.name
                    id = "btnradio$name"
                    autoComplete = InputAutoComplete.off
                    checked = orgMemberView == status
                    label(classes = "btn btn-outline-primary") {
                        attributes["for"] = "btnradio$name"
                        attributes["onclick"] = onClickAction(name)
                        style = "width: 100px;"
                        +name
                    }
                }
            }
        }
    }

    private fun FlowContent.renderAssociatedMembers(
        org: Org,
        members: List<OrgMemberAndIdentity>,
        associatedProfiles: Collection<OrgMemberAndIdentity>,
        label: String,
    ) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"$label Primary Member" }
                    Provider.entries.filter { it.hasNonSignInCapableMembers }.sortedBy { it.displayName }.forEach { provider ->
                        th(scope = ThScope.col) { +provider.name }
                    }
                }
            }
            tbody(classes = "table-dark") {
                members
                    .sortedWith { a, b -> compareOrgMembers(emptyMap(), a, b) }
                    .forEach { (_, member, identity) ->
                        tr {
                            attributes["onclick"] = onClickAction(
                                "$WEB_ROOT/orgs/${org.id}/installations/${member.installationId}/members/${member.id}",
                            )
                            style = "cursor: pointer;"

                            td { profile(identity) }
                            Provider.entries.filter { it.hasNonSignInCapableMembers }.sortedBy { it.displayName }.forEach { provider ->
                                td {
                                    associatedProfiles
                                        .filter { it.identity.provider == provider }
                                        .filter { it.member.primaryAssociation == member.id }
                                        .forEach { profile(it.identity) }
                                }
                            }
                        }
                    }
            }
        }
    }
}

private val Provider.hasNonSignInCapableMembers: Boolean
    get() = when (this) {
        Provider.Asana,
        Provider.Confluence,
        Provider.GoogleDrive,
        Provider.GoogleDriveWorkspace,
        Provider.Jira,
        Provider.Linear,
        Provider.Notion,
        Provider.Slack,
        Provider.Unblocked,
            -> true

        Provider.Aws,
        Provider.AwsIdentityCenter,
        Provider.AzureDevOps,
        Provider.Bitbucket,
        Provider.BitbucketDataCenter,
        Provider.BitbucketPipelines,
        Provider.Buildkite,
        Provider.CircleCI,
        Provider.Coda,
        Provider.ConfluenceDataCenter,
        Provider.CustomIntegration,
        Provider.GenericSaml,
        Provider.GitHub,
        Provider.GitHubActions,
        Provider.GitHubEnterprise,
        Provider.GitLab,
        Provider.GitLabPipelines,
        Provider.GitLabSelfHosted,
        Provider.GoogleWorkspace,
        Provider.JiraDataCenter,
        Provider.MicrosoftEntra,
        Provider.Okta,
        Provider.PingOne,
        Provider.StackOverflowTeams,
        Provider.Web,
            -> false
    }
