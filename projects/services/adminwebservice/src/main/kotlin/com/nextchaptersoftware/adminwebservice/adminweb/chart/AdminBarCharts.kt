package com.nextchaptersoftware.adminwebservice.adminweb.chart

import com.nextchaptersoftware.adminwebservice.adminweb.chart.Chart.renderChart
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.Instant
import kotlinx.html.FlowContent

object AdminBarCharts {

    private const val DEFAULT_BINS = 30
    private const val PERCENTAGE = 100.0

    inline fun <reified X : Number, reified Y : Number> FlowContent.renderTimeSeriesChart(
        datasets: List<TimeSeriesChartDataset<X, Y>>,
        minTimeUnit: ChartScale.TimeUnit? = null,
        xAxisType: ChartScale.Type? = null,
        xAxisTitle: String? = null,
        xAxisMax: X? = null,
        xAxisMin: X? = null,
        yAxisType: ChartScale.Type? = null,
        yAxisTitle: String? = null,
        yAxisMax: Y? = null,
        yAxisMin: Y? = null,
        isStacked: Boolean = true,
        chartType: ChartType,
        interaction: ChartInteraction? = null,
        plugins: ChartPlugins? = null,
    ) {
        val options = ChartOptions(
            interaction = interaction ?: ChartInteraction(
                mode = ChartInteraction.Mode.x,
                axis = ChartInteraction.Axis.y,
            ),
            scales = TimeSeriesChartScales<X, Y>(
                x = ChartScale(
                    type = xAxisType ?: ChartScale.Type.time,
                    title = xAxisTitle?.let { ChartScale.ScaleTitle(display = true, text = xAxisTitle) },
                    time = minTimeUnit?.let { ChartScale.TimeOptions(minUnit = it) },
                    stacked = isStacked,
                    min = xAxisMin,
                    max = xAxisMax,
                ),
                y = ChartScale(
                    type = yAxisType ?: ChartScale.Type.linear,
                    beginAtZero = true,
                    title = yAxisTitle?.let { ChartScale.ScaleTitle(display = true, text = yAxisTitle) },
                    stacked = isStacked,
                    min = yAxisMin,
                    max = yAxisMax,
                ),
            ),
            plugins = plugins ?: ChartPlugins(
                zoom = ChartZoom.defaultZoom,
            ),
        )

        val config = ChartConfig(
            type = chartType,
            data = TimeSeriesChartData(datasets = datasets),
            options = options,
        )

        renderChart(config)
    }

    fun FlowContent.renderBarChart(
        datasets: List<TimeSeriesChartDataset<Double, Double>>,
        xAxisTitle: String? = null,
        yAxisTitle: String? = null,
    ) {
        val options = ChartOptions(
            interaction = ChartInteraction(
                mode = ChartInteraction.Mode.point,
                axis = ChartInteraction.Axis.x,
            ),
            scales = TimeSeriesChartScales<Double, Double>(
                x = ChartScale(
                    type = ChartScale.Type.linear,
                    min = 0.0,
                    title = xAxisTitle?.let { ChartScale.ScaleTitle(display = true, text = xAxisTitle) },
                ),
                y = ChartScale(
                    type = ChartScale.Type.linear,
                    beginAtZero = true,
                    title = yAxisTitle?.let { ChartScale.ScaleTitle(display = true, text = yAxisTitle) },
                ),
            ),
            plugins = ChartPlugins(
                zoom = ChartZoom(zoom = ChartZoom.defaultDragZoom),
            ),
        )

        val config = ChartConfig(
            type = ChartType.bar,
            data = TimeSeriesChartData(datasets = datasets),
            options = options,
        )

        renderChart(config)
    }

    fun FlowContent.renderSingleSeriesBarChart(
        labels: List<String>,
        datasets: List<SingleSeriesChartDataset<Long>>,
    ) {
        val options = ChartOptions(
            interaction = ChartInteraction(
                mode = ChartInteraction.Mode.x,
                axis = ChartInteraction.Axis.y,
            ),
            scales = SingleSeriesChartScales<Long, Long>(
                x = ChartScale(
                    stacked = true,
                ),
                y = ChartScale(
                    stacked = true,
                ),
            ),
        )

        val config = ChartConfig(
            type = ChartType.bar,
            data = SingleSeriesChartData(labels = labels, datasets = datasets),
            options = options,
        )

        renderChart(config)
    }

    fun transformLatenciesToFrequency(
        latencies: Map<String, List<Pair<Instant, Duration>>>,
        durationUnit: DurationUnit,
    ): List<TimeSeriesChartDataset<Double, Double>> {
        return latencies.map { (label, durationByDate) ->
            TimeSeriesChartDataset(
                label = label,
                data = frequencyDistribution(
                    durations = durationByDate.map { it.second },
                ).map { (duration, count) ->
                    TimeSeriesChartDataPoint(
                        x = duration.toDouble(durationUnit),
                        y = count,
                    )
                },
            )
        }
    }

    /**
     * Calculate the normalized frequency distribution of durations.
     */
    private fun frequencyDistribution(
        durations: List<Duration>,
        bins: Int = DEFAULT_BINS,
    ): List<Pair<Duration, Double>> {
        val min = durations.minOrNull() ?: return emptyList()
        val max = durations.maxOrNull() ?: return emptyList()

        val binSize = (max - min) / bins
        val binCounts = durations.groupBy { duration ->
            val bin = ((duration - min) / binSize).toInt()
            bin.coerceIn(0, bins - 1)
        }.mapValues { it.value.size }

        return (0 until bins).map { bin ->
            val binStart = min + binSize * bin
            val binEnd = binStart + binSize
            val count = binCounts[bin] ?: 0
            Pair((binStart + binEnd) / 2, count.toDouble() / durations.size * PERCENTAGE)
        }
    }
}
