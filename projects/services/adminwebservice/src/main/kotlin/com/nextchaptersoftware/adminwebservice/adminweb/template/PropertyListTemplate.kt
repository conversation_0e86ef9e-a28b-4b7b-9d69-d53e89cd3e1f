package com.nextchaptersoftware.adminwebservice.adminweb.template

import io.ktor.server.html.Placeholder
import io.ktor.server.html.Template
import io.ktor.server.html.insert
import kotlinx.html.FlowContent
import kotlinx.html.TBODY
import kotlinx.html.div
import kotlinx.html.table
import kotlinx.html.tbody

class PropertyListTemplate : Template<FlowContent> {
    val propertyList = Placeholder<TBODY>()

    override fun FlowContent.apply() {
        div {
            table(classes = "table align-middle") {
                tbody(classes = "table-dark") {
                    insert(propertyList)
                }
            }
        }
    }
}
