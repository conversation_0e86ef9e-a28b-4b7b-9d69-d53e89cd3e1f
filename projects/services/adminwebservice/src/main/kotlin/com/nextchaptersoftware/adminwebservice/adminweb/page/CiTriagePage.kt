package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalBoolean
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrg
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalScmTeam
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.AdminTitles.title
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItemInput
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildBundle
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildFetchMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildGetLogsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobFetchMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobGetAnnotationsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobGetFocusLogsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobGetLogsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriageEphemeralReRunPage.triageEphemeralReRunMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestPage.pullRequestBundle
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestPage.pullRequestId
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentityId
import com.nextchaptersoftware.ci.enqueue.TriageEventEnqueueService
import com.nextchaptersoftware.ci.payloads.CiTriageEvent
import com.nextchaptersoftware.ci.payloads.CiTriageOptions
import com.nextchaptersoftware.ci.writer.ReportFactory
import com.nextchaptersoftware.db.models.Build
import com.nextchaptersoftware.db.models.BuildJob
import com.nextchaptersoftware.db.models.BuildTriage
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.BuildTriageId
import com.nextchaptersoftware.db.models.BuildTriageReviewStatus
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.stores.Stores.buildTriageStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.environment.LandingPageUrlBuilder
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.nullIfBlank
import io.ktor.http.Parameters
import io.ktor.http.escapeIfNeeded
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtml
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.InputType
import kotlinx.html.ScriptType
import kotlinx.html.TextAreaWrap
import kotlinx.html.code
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.id
import kotlinx.html.script
import kotlinx.html.textArea
import kotlinx.html.unsafe

object CiTriagePage {

    suspend fun RoutingContext.renderCiTriagePage(page: AdminPage) {
        val path = call.request.path()
        val adminIdentity = call.getAdminIdentity()
        val breadcrumb = call.makeBreadcrumb()

        val org = call.parameters.optionalOrg()
        val scmTeam = call.parameters.optionalScmTeam()

        val bundle = call.parameters.triageId().let {
            buildTriageStore.findAllBundles(triagesIds = listOf(it)).required().first()
        }

        val triage = bundle.triage
        val build = bundle.build
        val jobs = triage.jobs()
        val pullRequest = bundle.pullRequest

        val basePath = path.removeSuffix("/triages/${triage.id}")

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org = org, scmTeam = scmTeam) }
            relatedMenu {
                renderRelatedMenu(
                    items = relatedMenuItems(
                        basePath = basePath,
                        build = build,
                    ),
                )
            }
            actionMenu {
                renderActionMenu(
                    items = actionMenuItems(
                        path = path,
                        triage = triage,
                        build = build,
                        jobs = jobs,
                    ),
                )
            }
            content {
                h1 { +page.label }

                insert(PropertyListTemplate()) {
                    propertyList {
                        property("PR", title(pullRequest))
                        property("Group", triage.groupKey)
                        property("header") {
                            code { triage.header }
                        }
                        property("State", triage.state)
                        property("Is Visible") {
                            asBadge(triage.isVisible)
                        }
                        property("Reactions", bundle.reactions)
                        property("Created", triage.createdAt)
                        property("Updated", triage.modifiedAt)
                    }
                }

                insert(PropertyListTemplate()) {
                    propertyList {
                        property("Build", bundle.build)
                        property("Job (legacy)", triage.jobId)
                        property("Attempt", bundle.build.attempt)
                        property("Commit sha", triage.commitIssueSha)
                        property("Fix sha", triage.commitFixedSha)
                        jobs?.onEachIndexed { i, job ->
                            property("Job ${i + 1}", job)
                        }
                    }
                }

                insert(PropertyListTemplate()) {
                    propertyList {
                        property("Reviewer") {
                            bundle.triageReviewer?.let { profile(it) } ?: +"-"
                        }
                        property("Status", bundle.triage.reviewStatus)
                        property("Root Cause", bundle.triage.reviewRootCause)
                        property("Comment", bundle.triage.reviewComment)
                    }
                }

                h3(classes = "mt-5") { +"Content" }
                renderTriageContent(
                    triage = triage,
                )

                triage.debug?.let {
                    h3(classes = "mt-5") { +"Debug" }
                    renderTriageDebug(
                        triage = triage,
                    )
                }
            }
        }
    }

    internal fun Parameters.triageId() = requiredId("triageId", ::BuildTriageId)

    internal suspend fun Parameters.triage() = buildTriageStore.findById(triageId = triageId()).required()

    private fun FlowContent.renderTriageContent(
        triage: BuildTriage,
    ) {
        textArea(rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
            id = "triage-content"
        }
        val details = triage.details.escapeIfNeeded()
        script(type = ScriptType.textJavaScript) {
            unsafe {
                +"""
                    var markdownEditor = new SimpleMDE({ element: document.getElementById("triage-content") });
                    markdownEditor.value($details);
                    markdownEditor.togglePreview();
                """.trimIndent()
            }
        }
    }

    private fun FlowContent.renderTriageDebug(
        triage: BuildTriage,
    ) {
        textArea(rows = "1", cols = "1", wrap = TextAreaWrap.soft) {
            id = "triage-debug"
        }
        val debug = triage.debug?.escapeIfNeeded()
        script(type = ScriptType.textJavaScript) {
            unsafe {
                +"""
                    var markdownEditor = new SimpleMDE({ element: document.getElementById("triage-debug") });
                    markdownEditor.value($debug);
                    markdownEditor.togglePreview();
                """.trimIndent()
            }
        }
    }

    private fun relatedMenuItems(
        basePath: String,
        build: Build?,
    ) = listOfNotNull(
        build?.let {
            MenuItem(
                href = "$basePath/builds/${build.id}",
                label = "CI Build",
                description = "The Build for this Triage",
            )
        },
        build?.pullRequestId?.let { pullRequestId ->
            MenuItem(
                href = "$basePath/pullRequests/$pullRequestId",
                label = "Pull Request",
                description = "The Pull Request for this Triage",
            )
        },
    )

    private fun actionMenuItems(
        path: String,
        triage: BuildTriage,
        build: Build,
        jobs: List<BuildJob>?,
    ) = listOfNotNull(
        triageReviewMenuItem(
            path = path,
            triage = triage,
        ),
        buildFetchMenuItem(
            path = path,
            build = build,
        ),
        buildGetLogsMenuItem(
            path = path,
            buildId = build.id,
        ),
        triageRefreshMenuItem(
            path = path,
            pullRequestId = build.pullRequestId,
        ),
        triageRerunMenuItem(
            path = path,
            build = build,
            description = "Trigger re-triage for the original failing build",
        ),
        jobs?.let {
            buildJobFetchMenuItem(
                path = path,
                jobs = jobs,
                buildId = build.id,
            )
        },
        jobs?.let {
            buildJobGetLogsMenuItem(
                path = path,
                jobs = jobs,
            )
        },
        jobs?.let {
            buildJobGetFocusLogsMenuItem(
                path = path,
                jobs = jobs,
            )
        },
        jobs?.let {
            buildJobGetAnnotationsMenuItem(
                path = path,
                jobs = jobs,
            )
        },
        triageEphemeralReRunMenuItem(
            path = path,
        ),
    )

    fun triageRefreshMenuItem(
        path: String,
        pullRequestId: PullRequestId,
        description: String? = null,
    ) = MenuItem(
        href = "$path/triageRefresh",
        label = "CI Triage: Refresh",
        description = "${description ?: "Refresh the posted triage comment at SCM"} (user visible)",
        style = BootstrapStyle.Danger,
        inputs = listOf(
            MenuItemInput.TextInput(
                name = "pullRequestId",
                label = "",
                defaultValue = "$pullRequestId",
                inputType = InputType.hidden,
            ),
            triageRefreshDisablePullRequestMuteCheckMenuItemInput,
            triageRefreshForceCommentUpdateMenuItemInput,
        ),
    )

    private val triageRefreshForceCommentUpdateMenuItemInput = MenuItemInput.SelectInput(
        name = "forceCommentUpdate",
        label = "Force update?",
        defaultValue = "false",
        items = listOf(
            MenuItemInput.SelectInput.Item(
                label = "yes, force update the comment",
                value = "true",
            ),
            MenuItemInput.SelectInput.Item(
                label = "no, only post if its contents changed",
                value = "false",
            ),
        ),
    )

    private val triageRefreshDisablePullRequestMuteCheckMenuItemInput = MenuItemInput.SelectInput(
        name = "disablePullRequestMuteCheck",
        label = "Disable PR mute check?",
        defaultValue = "false",
        items = listOf(
            MenuItemInput.SelectInput.Item(
                label = "yes, process PRs in any state",
                value = "true",
            ),
            MenuItemInput.SelectInput.Item(
                label = "no, process only PRs in open state",
                value = "false",
            ),
        ),
    )

    suspend fun RoutingContext.triageRefresh(
        triageEventEnqueueService: TriageEventEnqueueService,
    ) {
        val bodyParams = call.receiveParameters()
        val bundle = bodyParams.pullRequestBundle().required { "Unknown pull request" }

        triageEventEnqueueService.enqueueTriagePublishEvent(
            scmTeamId = bundle.scmTeam.id,
            repoId = bundle.repo.id,
            pullRequestId = bundle.pullRequest.id,
            options = CiTriageOptions(
                forceCommentUpdate = bodyParams.optionalBoolean("forceCommentUpdate"),
                disablePullRequestMuteCheck = bodyParams.optionalBoolean("disablePullRequestMuteCheck"),
            ),
        )
    }

    fun triageRenderMenuItem(
        path: String,
        pullRequestId: PullRequestId,
    ) = MenuItem(
        href = "$path/triageRender",
        label = "CI Triage: Render Comment",
        description = "Renders the triage comment locally, as it would be posted on SCM",
        style = BootstrapStyle.Info,
        inputs = listOf(
            MenuItemInput.SelectInput(
                name = "format",
                label = "format",
                defaultValue = "text",
                items = listOf(
                    MenuItemInput.SelectInput.Item(
                        label = "rich formatting",
                        value = "html",
                    ),
                    MenuItemInput.SelectInput.Item(
                        label = "plain text",
                        value = "text",
                    ),
                ),
            ),
            MenuItemInput.TextInput(
                name = "pullRequestId",
                label = "",
                defaultValue = "$pullRequestId",
                inputType = InputType.hidden,
            ),
        ),
    )

    suspend fun RoutingContext.triageRender() {
        val bodyParams = call.receiveParameters()
        val bundle = bodyParams.pullRequestBundle().required { "Unknown pull request" }

        val text = bundle.pullRequest.triages()?.let { triages ->
            ReportFactory.create(
                provider = bundle.repo.provider,
                triages = triages,
                inviteUrl = LandingPageUrlBuilder().build(),
            )
        }
            ?.asText()
            ?: "No triages found"

        when (bodyParams["format"]) {
            "html" -> call.respondHtml { unsafe { +text } }
            else -> call.respondText(text)
        }
    }

    fun triageRerunMenuItem(
        path: String,
        build: Build,
        description: String? = null,
    ) = triageRerunMenuItem(
        path = path,
        builds = build.let(::listOf),
        description = description ?: "Trigger re-triage for this build",
    )

    fun triageRerunMenuItem(
        path: String,
        builds: Collection<Build>,
        description: String = "Trigger re-triage for a build",
    ) = MenuItem(
        label = "CI Triage: Re-run",
        description = "$description (user visible)",
        href = "$path/triageRerun",
        style = BootstrapStyle.Danger,
        inputs = listOf(
            when (builds.size) {
                1 -> MenuItemInput.TextInput(
                    label = "",
                    name = "buildId",
                    defaultValue = "${builds.first().id}",
                    inputType = InputType.hidden,
                )

                else -> MenuItemInput.SelectInput(
                    label = "Build",
                    name = "buildId",
                    items = builds.map {
                        MenuItemInput.SelectInput.Item(
                            label = "${it.displayNumberPretty} -- ${it.displayName} -- ${it.status} -- ${it.result}",
                            value = "${it.id}",
                        )
                    },
                )
            },
            triageRefreshDisablePullRequestMuteCheckMenuItemInput,
            triageRerunDisableTriageExistsCheckItemInput,
        ),
    )

    private val triageRerunDisableTriageExistsCheckItemInput = MenuItemInput.SelectInput(
        name = "disableTriageExistsCheck",
        label = "Disable triage exists check?",
        defaultValue = "false",
        items = listOf(
            MenuItemInput.SelectInput.Item(
                label = "no, do not overwrite existing triages",
                value = "false",
            ),
            MenuItemInput.SelectInput.Item(
                label = "yes, overwrite existing triages",
                value = "true",
            ),
        ),
    )

    suspend fun RoutingContext.triageRerun(
        triageEventEnqueueService: TriageEventEnqueueService,
    ) {
        val bodyParams = call.receiveParameters()
        val bundle = bodyParams.buildBundle()

        triageEventEnqueueService.enqueueTriageRequestEvent(
            ciInstallationId = bundle.ciInstallation.id,
            scmTeamId = bundle.scmTeam.id,
            repoId = bundle.repo.id,
            pullRequestId = bundle.pullRequest.id,
            buildId = bundle.build.id,
            options = CiTriageOptions(
                disablePullRequestMuteCheck = bodyParams.optionalBoolean("disablePullRequestMuteCheck"),
                disableTriageExistsCheck = bodyParams.optionalBoolean("disableTriageExistsCheck"),
            ),
        )
    }

    fun triageDeleteMenuItem(
        path: String,
        pullRequestId: PullRequestId,
        description: String,
    ) = MenuItem(
        href = "$path/triageDelete",
        label = "CI Triage: Delete",
        description = "$description (user visible)",
        style = BootstrapStyle.Danger,
        inputs = listOf(
            MenuItemInput.TextInput(
                name = "pullRequestId",
                label = "",
                defaultValue = "$pullRequestId",
                inputType = InputType.hidden,
            ),
        ),
    )

    suspend fun RoutingContext.triageDelete(
        triageEventEnqueueService: TriageEventEnqueueService,
    ) {
        val bodyParams = call.receiveParameters()
        triageEventEnqueueService.enqueueTriageCommandEvent(
            pullRequestId = bodyParams.pullRequestId(),
            command = CiTriageEvent.TriageCommandEvent.Delete,
        )
    }

    fun triageMuteToggleMenuItem(
        path: String,
        pullRequestId: PullRequestId,
        action: String,
        description: String,
    ) = MenuItem(
        href = "$path/triageMuteToggle",
        label = "CI Triage: $action",
        description = description,
        inputs = listOf(
            MenuItemInput.TextInput(
                name = "pullRequestId",
                label = "",
                defaultValue = "$pullRequestId",
                inputType = InputType.hidden,
            ),
        ),
    )

    suspend fun RoutingContext.triageMuteToggle() {
        val bodyParams = call.receiveParameters()
        val pullRequest = bodyParams.pullRequestId().let {
            pullRequestStore.findById(prId = it).required()
        }
        pullRequestStore.updateCiTriageIsMuted(
            pullRequestId = pullRequest.id,
            isMuted = pullRequest.ciTriageMuted?.not() ?: false,
        )
    }

    private fun triageReviewMenuItem(
        path: String,
        triage: BuildTriage,
    ) = MenuItem(
        href = "$path/triageReview",
        label = "CI Triage: Review",
        description = "Review this triage",
        style = BootstrapStyle.Success,
        inputs = listOf(
            MenuItemInput.TextInput(
                name = "triageId",
                label = "",
                defaultValue = "${triage.id}",
                inputType = InputType.hidden,
            ),
            MenuItemInput.Select(
                name = "status",
                label = "Status:",
                defaultValue = triage.reviewStatus?.name,
                items = BuildTriageReviewStatus.entries.map { it.name },
            ),
            MenuItemInput.TextInput(
                name = "cause",
                label = "Root cause:",
                defaultValue = triage.reviewRootCause ?: "",
                inputType = InputType.text,
            ),
            MenuItemInput.TextInput(
                name = "comment",
                label = "Comment:",
                defaultValue = triage.reviewComment ?: "",
                inputType = InputType.text,
            ),
        ),
    )

    suspend fun RoutingContext.triageReview() {
        val bodyParams = call.receiveParameters()
        buildTriageStore.review(
            triageId = bodyParams.triageId(),
            reviewerId = call.getAdminIdentityId(),
            status = bodyParams["status"]?.let(BuildTriageReviewStatus::valueOf),
            rootCause = bodyParams["cause"]?.nullIfBlank(),
            comment = bodyParams["comment"]?.nullIfBlank(),
        )
    }

    fun BuildTriageFilterStage.asFilterCategory(): Pair<String, BootstrapStyle>? = when (this) {
        BuildTriageFilterStage.TriageInsufficientLogsInference,
        BuildTriageFilterStage.TriageNoValidLogSummaryForBuild,
        BuildTriageFilterStage.TriageInsufficientLogsFocused,
        BuildTriageFilterStage.TriageFailedToCompressJobLogs,
            -> Pair("Logs", BootstrapStyle.Secondary)

        BuildTriageFilterStage.DiffTooLarge,
        BuildTriageFilterStage.TriageInvalidDiff,
        BuildTriageFilterStage.TriageFailedToCompressDiff,
            -> Pair("Diff", BootstrapStyle.Secondary)

        BuildTriageFilterStage.CiDisabled,
        BuildTriageFilterStage.MissingCriticalData,
            -> Pair("Config", BootstrapStyle.Secondary)

        BuildTriageFilterStage.TriageMatchPreviousTriage,
            -> Pair("Duplicated", BootstrapStyle.Secondary)

        BuildTriageFilterStage.TriageFailedRetrieveDocuments,
            -> Pair("Documents", BootstrapStyle.Warning)

        BuildTriageFilterStage.TriageDecidedNotToSuggestFix,
            -> Pair("No Fix", BootstrapStyle.Warning)

        BuildTriageFilterStage.TriageEvalFailed,
            -> Pair("Eval", BootstrapStyle.Warning)

        BuildTriageFilterStage.TriageFailedToGenerateResponse,
            -> Pair("Model Error", BootstrapStyle.Danger)

        BuildTriageFilterStage.Published,
            -> null
    }
}
