package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.renderTooltipFooter
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.renderZoomControls
import com.nextchaptersoftware.adminwebservice.adminweb.chart.AdminChartSyncing.syncZoomHandlers
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.getAnswerMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.getCompletionLatencies
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.getFeedback
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.getSlackAnswers
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.getWaitLatencies
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnswerCompleteTimeHeatmap
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnswerCompleteTimeHistogram
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnswerCountMonthlyBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnswerCountWeeklyBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnswerWaitTimeHeatmap
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnswerWaitTimeHistogram
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderFeedbackCountMonthlyBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderFeedbackCountWeeklyBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderSlackMonthlyBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderSlackWeeklyBarChart
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.getDistinctMemberTimeSeriesByProductAgentForOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.getMemberJoinedTimestamps
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.renderAgentUsageChartMonthly
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.renderAgentUsageChartWeekly
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.renderMemberGrowthChart
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import kotlinx.html.div
import kotlinx.html.h1

object OrgAnalyticsPage {

    suspend fun RoutingContext.renderOrgAnalyticsPage(
        page: AdminPage,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        val org = call.parameters.org()
        val feedbackByDate = getFeedback(orgId = org.id)
        val joinTimestamps = getMemberJoinedTimestamps(orgId = org.id)
        val distinctMemberTimeSeriesByProductAgent = getDistinctMemberTimeSeriesByProductAgentForOrg(orgId = org.id)
        val answerMetricsByDate = getAnswerMetrics(orgId = org.id)
        val waitLatenciesByDate = getWaitLatencies(orgId = org.id)
        val completionLatenciesByDate = getCompletionLatencies(orgId = org.id)
        val (slackAutoAnswersByDate, slackNonAutoAnswersByDate) = getSlackAnswers(orgId = org.id)

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            content {
                div(classes = "d-flex justify-content-between") {
                    h1 { +page.label }
                    renderTooltipFooter()
                    renderZoomControls()
                    syncZoomHandlers()
                }

                div(classes = "row") {
                    div(classes = "col-12") {
                        renderMemberGrowthChart(joinTimestamps)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAgentUsageChartWeekly(distinctMemberTimeSeriesByProductAgent)
                    }
                    div(classes = "col-6") {
                        renderAgentUsageChartMonthly(distinctMemberTimeSeriesByProductAgent)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAnswerCountWeeklyBarChart(answerMetricsByDate)
                    }
                    div(classes = "col-6") {
                        renderAnswerCountMonthlyBarChart(answerMetricsByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderSlackWeeklyBarChart(slackNonAutoAnswersByDate, slackAutoAnswersByDate)
                    }
                    div(classes = "col-6") {
                        renderSlackMonthlyBarChart(slackNonAutoAnswersByDate, slackAutoAnswersByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderFeedbackCountWeeklyBarChart(feedbackByDate)
                    }
                    div(classes = "col-6") {
                        renderFeedbackCountMonthlyBarChart(feedbackByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAnswerWaitTimeHeatmap(waitLatenciesByDate)
                    }
                    div(classes = "col-6") {
                        renderAnswerWaitTimeHistogram(waitLatenciesByDate)
                    }
                }

                div(classes = "row") {
                    div(classes = "col-6") {
                        renderAnswerCompleteTimeHeatmap(completionLatenciesByDate)
                    }
                    div(classes = "col-6") {
                        renderAnswerCompleteTimeHistogram(completionLatenciesByDate)
                    }
                }
            }
        }
    }
}
