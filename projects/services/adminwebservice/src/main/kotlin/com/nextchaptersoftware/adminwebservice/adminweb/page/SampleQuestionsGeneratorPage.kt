package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.orgId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentRetrievalPage.renderInputFieldSet
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.DataSourceAccessControlManager
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorImpl
import io.ktor.http.ContentType
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlinx.html.ButtonType
import kotlinx.html.DIV
import kotlinx.html.FlowContent
import kotlinx.html.InputAutoComplete
import kotlinx.html.InputType
import kotlinx.html.button
import kotlinx.html.div
import kotlinx.html.fieldSet
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.h5
import kotlinx.html.id
import kotlinx.html.img
import kotlinx.html.input
import kotlinx.html.li
import kotlinx.html.stream.appendHTML
import kotlinx.html.ul
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

object SampleQuestionsGeneratorPage {
    enum class SampleQuestionGenerationParameters {
        NumGeneratedQuestions,
        MinDocScore,
        UseCommitsAsSeedDataForSampleQuestionGeneration,
        UseDiffsAsSeedDataForSampleQuestionGeneration,
        UsePRsAsSeedDataForSampleQuestionGeneration,
        UseSeedDataOnlyForSampleQuestionGeneration,
    }

    data class SampleQuestionGenerationConfig(
        val retrievalConfig: DocumentRetrievalPage.RetrievalConfig,
        val numGeneratedQuestions: Int,
        val minDocScore: Float,
        val useCommitsAsSeedDataForSampleQuestionGeneration: Boolean,
        val useDiffsAsSeedDataForSampleQuestionGeneration: Boolean,
        val usePRsAsSeedDataForSampleQuestionGeneration: Boolean,
        val useSeedDataOnlyForSampleQuestionGeneration: Boolean,
    )

    suspend fun RoutingContext.renderSampleQuestionGeneratorPage(
        page: AdminPage,
        templateService: MLInferenceTemplateService,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val path = call.request.path()
        val org = call.parameters.org()
        val template = requireNotNull(templateService.orgTemplate(org.id, MLInferenceTemplateKind.SampleQuestionGenerator))
        val adminIdentity = call.getAdminIdentity()
        val memberId = call.parameters.requiredId("memberId", ::MemberId)
        val config = parseQuery(call.request.queryParameters, template, memberId)
        val repos = Stores.repoStore.listActiveReposUnsafe(orgId = org.id)

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            content {
                h1 { +page.label }
                renderInputForm(path, config, repos)

                if (!call.request.queryParameters.isEmpty()) {
                    h3(classes = "mt-5") { +"Questions" }
                    div {
                        val parameters = call.request.queryParameters
                        attributes["hx-get"] = "$path/generate?${parameters.formUrlEncode()}"
                        attributes["hx-trigger"] = "load"
                        +"Loading ... "
                        img(alt = "Loading ...", src = "$WEB_ROOT/images/bars.svg")
                    }
                }
            }
        }
    }

    private fun parseQuery(
        queryParameters: Parameters,
        template: MLInferenceTemplate,
        memberId: MemberId,
    ): SampleQuestionGenerationConfig {
        return SampleQuestionGenerationConfig(
            retrievalConfig = DocumentRetrievalPage.parseQuery(queryParameters, template, memberId),
            numGeneratedQuestions = queryParameters[SampleQuestionGenerationParameters.NumGeneratedQuestions.name]?.toIntOrNull()
                ?: template.numGeneratedSampleQuestions,
            useCommitsAsSeedDataForSampleQuestionGeneration = queryParameters
                .getAll(SampleQuestionGenerationParameters.UseCommitsAsSeedDataForSampleQuestionGeneration.name)?.lastOrNull()?.toBoolean()
                ?: template.useCommitsAsSeedDataForSampleQuestionGeneration,
            useDiffsAsSeedDataForSampleQuestionGeneration = queryParameters
                .getAll(SampleQuestionGenerationParameters.UseDiffsAsSeedDataForSampleQuestionGeneration.name)?.lastOrNull()?.toBoolean()
                ?: template.useDiffsAsSeedDataForSampleQuestionGeneration,
            usePRsAsSeedDataForSampleQuestionGeneration = queryParameters
                .getAll(SampleQuestionGenerationParameters.UsePRsAsSeedDataForSampleQuestionGeneration.name)?.lastOrNull()?.toBoolean()
                ?: template.usePRsAsSeedDataForSampleQuestionGeneration,
            useSeedDataOnlyForSampleQuestionGeneration = queryParameters
                .getAll(SampleQuestionGenerationParameters.UseSeedDataOnlyForSampleQuestionGeneration.name)?.lastOrNull()?.toBoolean()
                ?: template.useSeedDataOnlyForSampleQuestionGeneration,
            minDocScore = queryParameters[SampleQuestionGenerationParameters.MinDocScore.name]?.toFloatOrNull()
                ?: template.minRankScore,
        )
    }

    private fun FlowContent.renderInputForm(path: String, config: SampleQuestionGenerationConfig, repos: List<Repo>) {
        form(action = path) {
            fieldSet {
                renderInputFieldSet(
                    config = config.retrievalConfig,
                    repos = repos,
                    renderMemberSettings = false,
                    renderQueryInput = false,
                    additionalRightColumnContent = {
                        div(classes = "row mt-4") {
                            div(classes = "col-6") { renderNumGeneratedQuestions(config) }
                            div(classes = "col-6") { renderUseCommitsAsSeedDataForSampleQuestionGeneration(config) }
                        }
                        div(classes = "row mt-4") {
                            div(classes = "col-6") { renderUsePRsAsSeedDataForSampleQuestionGeneration(config) }
                            div(classes = "col-6") { renderUseDiffsAsSeedDataForSampleQuestionGeneration(config) }
                        }
                        div(classes = "row mt-4") {
                            div(classes = "col-6") { renderUseSeedDataOnlyForSampleQuestionGeneration(config) }
                        }
                    },
                )

                div(classes = "row mt-4") {
                    div(classes = "col-12 d-grid") {
                        button(type = ButtonType.submit, classes = "btn btn-primary mt-4") { +"Generate" }
                    }
                }
            }
        }
    }

    private fun DIV.renderNumGeneratedQuestions(config: SampleQuestionGenerationConfig) {
        h5 { +"Num Generated Questions" }
        div(classes = "form-group") {
            input(classes = "form-number", type = InputType.number) {
                autoComplete = InputAutoComplete.off
                min = "1"
                max = "10"
                step = "1"
                value = "${config.numGeneratedQuestions}"
                name = SampleQuestionGenerationParameters.NumGeneratedQuestions.name
                id = SampleQuestionGenerationParameters.NumGeneratedQuestions.name
            }
        }
    }

    private fun DIV.renderUseCommitsAsSeedDataForSampleQuestionGeneration(config: SampleQuestionGenerationConfig) {
        h5 { +"Use Commits to generate answer (second priority)" }
        div(classes = "form-group") {
            input(classes = "form-check-input", type = InputType.hidden) {
                id = SampleQuestionGenerationParameters.UseCommitsAsSeedDataForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UseCommitsAsSeedDataForSampleQuestionGeneration.name
                checked = false
                value = false.toString()
            }
            input(classes = "form-check-input", type = InputType.checkBox) {
                id = SampleQuestionGenerationParameters.UseCommitsAsSeedDataForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UseCommitsAsSeedDataForSampleQuestionGeneration.name
                checked = config.useCommitsAsSeedDataForSampleQuestionGeneration
                value = true.toString()
            }
        }
    }

    private fun DIV.renderUseDiffsAsSeedDataForSampleQuestionGeneration(config: SampleQuestionGenerationConfig) {
        h5 { +"Use Diffs to generate answer" }
        div(classes = "form-group") {
            input(classes = "form-check-input", type = InputType.hidden) {
                id = SampleQuestionGenerationParameters.UseDiffsAsSeedDataForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UseDiffsAsSeedDataForSampleQuestionGeneration.name
                checked = false
                value = false.toString()
            }
            input(classes = "form-check-input", type = InputType.checkBox) {
                id = SampleQuestionGenerationParameters.UseDiffsAsSeedDataForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UseDiffsAsSeedDataForSampleQuestionGeneration.name
                checked = config.useDiffsAsSeedDataForSampleQuestionGeneration
                value = true.toString()
            }
        }
    }

    private fun DIV.renderUsePRsAsSeedDataForSampleQuestionGeneration(config: SampleQuestionGenerationConfig) {
        h5 { +"Use PRs to generate answer (highest priority)" }
        div(classes = "form-group") {
            input(classes = "form-check-input", type = InputType.hidden) {
                id = SampleQuestionGenerationParameters.UsePRsAsSeedDataForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UsePRsAsSeedDataForSampleQuestionGeneration.name
                checked = false
                value = false.toString()
            }
            input(classes = "form-check-input", type = InputType.checkBox) {
                id = SampleQuestionGenerationParameters.UsePRsAsSeedDataForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UsePRsAsSeedDataForSampleQuestionGeneration.name
                checked = config.usePRsAsSeedDataForSampleQuestionGeneration
                value = true.toString()
            }
        }
    }

    private fun DIV.renderUseSeedDataOnlyForSampleQuestionGeneration(config: SampleQuestionGenerationConfig) {
        h5 { +"Use seed data only" }
        div(classes = "form-group") {
            input(classes = "form-check-input", type = InputType.hidden) {
                id = SampleQuestionGenerationParameters.UseSeedDataOnlyForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UseSeedDataOnlyForSampleQuestionGeneration.name
                checked = false
                value = false.toString()
            }
            input(classes = "form-check-input", type = InputType.checkBox) {
                id = SampleQuestionGenerationParameters.UseSeedDataOnlyForSampleQuestionGeneration.name
                name = SampleQuestionGenerationParameters.UseSeedDataOnlyForSampleQuestionGeneration.name
                checked = config.useSeedDataOnlyForSampleQuestionGeneration
                value = true.toString()
            }
        }
    }

    suspend fun RoutingContext.generateSampleQuestions(
        sampleQuestionGenerator: SampleQuestionGeneratorImpl,
        templateService: MLInferenceTemplateService,
    ) {
        LOGGER.debugAsync(
            "queryParameters" to call.request.queryParameters.entries().joinToString { (key, value) ->
                "$key: ${value.joinToString()}"
            },
        ) {
            "SampleQuestionsGeneratorPage::generateSampleQuestions()"
        }

        val orgId = call.parameters.orgId()
        val memberId = call.parameters.requiredId("memberId", ::MemberId)
        val orgMemberId = Stores.memberStore.getOrgMemberId(memberId)
        val template = requireNotNull(templateService.orgTemplate(orgId, MLInferenceTemplateKind.SampleQuestionGenerator))
        val config = parseQuery(call.request.queryParameters, template, memberId)

        val generatorTemplate = template.copy(
            maxDocuments = config.retrievalConfig.limit,
            documentTypes = config.retrievalConfig.documentTypes,
            insightTypes = config.retrievalConfig.insightTypes,
            sourceTypes = config.retrievalConfig.sourceTypes,
            numGeneratedSampleQuestions = config.numGeneratedQuestions,
            minRankScore = config.minDocScore,
            useRRF = config.retrievalConfig.useRRF,
            useCERR = config.retrievalConfig.useCERR,
            sparseVectorWeight = config.retrievalConfig.alpha,
            usePRsAsSeedDataForSampleQuestionGeneration = config.usePRsAsSeedDataForSampleQuestionGeneration,
            useCommitsAsSeedDataForSampleQuestionGeneration = config.useCommitsAsSeedDataForSampleQuestionGeneration,
            useDiffsAsSeedDataForSampleQuestionGeneration = config.useDiffsAsSeedDataForSampleQuestionGeneration,
            useSeedDataOnlyForSampleQuestionGeneration = config.useSeedDataOnlyForSampleQuestionGeneration,
            rerankModel = config.retrievalConfig.rerankModel,
        )

        val dsacContext = DataSourceAccessControlManager().getDsacContext(orgId = orgId, orgMemberId = orgMemberId)

        val questions = sampleQuestionGenerator.generateSampleQuestions(
            orgId = orgId,
            orgMemberId = orgMemberId,
            template = generatorTemplate,
            dsacContext = dsacContext,
        )

        val htmlFragment = buildString {
            appendHTML().div {
                renderQuestions(questions = questions)
            }
        }

        call.respondText(htmlFragment, ContentType.Text.Html)
    }

    private fun FlowContent.renderQuestions(questions: List<String>) {
        ul(classes = "list-group mt-4") {
            questions.forEach { question ->
                li(classes = "list-group-item") {
                    +question
                }
            }
        }
    }
}
