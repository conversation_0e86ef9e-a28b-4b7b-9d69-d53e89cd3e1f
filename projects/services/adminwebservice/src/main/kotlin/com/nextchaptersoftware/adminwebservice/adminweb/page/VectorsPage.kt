package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrg
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BreadcrumbContainer
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentityContext
import com.nextchaptersoftware.db.models.EmbeddingDelete
import com.nextchaptersoftware.db.models.EmbeddingModel
import com.nextchaptersoftware.db.models.EmbeddingPlatform
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.MLSettingsStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.embedding.service.stats.EmbeddingStatsFacade
import com.nextchaptersoftware.utils.asUUIDOrNull
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.routing.RoutingContext
import java.text.NumberFormat
import java.util.Locale
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.code
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.p
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object VectorsPage {

    private val NUMBER_FORMAT = NumberFormat.getInstance(Locale.CANADA)

    suspend fun RoutingContext.renderVectorsPage(
        page: AdminPage,
        mlSettingsStore: MLSettingsStore = Stores.mlSettingsStore,
        embeddingStatsFacade: EmbeddingStatsFacade,
        embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        call.parameters.optionalOrg().also { org ->
            val embeddingPlatforms = org?.let {
                mlSettingsStore.getWriteEmbeddingPlatforms(orgId = org.id)
            } ?: EmbeddingPlatform.entries.toList()

            when (org) {
                null -> renderGlobalVectorsPage(page, breadcrumb, adminIdentity, embeddingPlatforms, embeddingStatsFacade, embeddingDeleteStore)
                else -> renderTeamVectorsPage(org, page, breadcrumb, adminIdentity, embeddingPlatforms, embeddingStatsFacade, embeddingDeleteStore)
            }
        }
    }

    private suspend fun RoutingContext.renderGlobalVectorsPage(
        page: AdminPage,
        breadcrumb: BreadcrumbContainer,
        adminIdentity: AdminIdentityContext,
        embeddingPlatforms: List<EmbeddingPlatform>,
        embeddingStatsFacade: EmbeddingStatsFacade,
        embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    ) {
        val pendingDeletes = embeddingDeleteStore.list()

        val counts = embeddingPlatforms.associateWith { embeddingPlatform ->
            embeddingStatsFacade.getTotalVectorCount(embeddingPlatform = embeddingPlatform)
        }

        val topVectorOrgs = embeddingPlatforms.associateWith { embeddingPlatform ->
            embeddingStatsFacade.getTopNamespaceVectorCounts(embeddingPlatform = embeddingPlatform)
                .mapNotNull { (namespace, count) ->
                    namespace.asUUIDOrNull()?.let { uuid ->
                        OrgId(uuid) to count
                    }
                }
                .let { vectorCountsByOrgIds ->
                    val orgs = orgStore.find(orgIds = vectorCountsByOrgIds.map { it.first })
                    vectorCountsByOrgIds.map { (orgId, count) ->
                        Triple(orgId, orgs.find { it.id == orgId }, count)
                    }
                }
        }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                embeddingPlatforms.forEachIndexed { index, embeddingPlatform ->
                    h1(classes = if (index > 0) "mt-5" else "") { +(embeddingPlatform.name + " " + page.label) }
                    renderVectorStats(counts[embeddingPlatform])

                    h3(classes = "mt-5") { +"Top Orgs" }
                    p { +"Top Orgs by Vector Count" }
                    renderVectorTopOrgs(topVectorOrgs[embeddingPlatform].orEmpty())
                }

                h1(classes = "mt-5") { +"Pending Deletions" }
                renderPendingDeletes(pendingDeletes = pendingDeletes, showTeam = true)
            }
        }
    }

    private fun FlowContent.renderVectorTopOrgs(topVectorOrgs: List<Triple<OrgId, Org?, Int>>) {
        table(classes = "table table-hover align-middle") {
            thead(classes = "table-dark") {
                tr {
                    th(scope = ThScope.col) { +"Org" }
                    th(scope = ThScope.col) { +"Org ID" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Count" }
                }
            }
            tbody(classes = "table-dark") {
                topVectorOrgs.forEach { (orgId, org, count) ->
                    tr {
                        td {
                            org?.let {
                                attributes["onclick"] = onClickAction("$WEB_ROOT/orgs/${org.id}")
                                style = "cursor: pointer; width: 30%;"

                                profile(org)
                            } ?: +"-"
                        }
                        td { code { +orgId.toString() } }
                        td { +formattedNumber(count) }
                    }
                }
            }
        }
    }

    private suspend fun RoutingContext.renderTeamVectorsPage(
        org: Org,
        page: AdminPage,
        breadcrumb: BreadcrumbContainer,
        adminIdentity: AdminIdentityContext,
        embeddingPlatforms: List<EmbeddingPlatform>,
        embeddingStatsFacade: EmbeddingStatsFacade,
        embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    ) {
        val counts = embeddingPlatforms.associateWith { embeddingPlatform ->
            embeddingStatsFacade.getNamespaceVectorCount(orgId = org.id, embeddingPlatform = embeddingPlatform)
        }
        val pendingDeletes = embeddingDeleteStore.list(namespaceId = org.id)

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org = org) }
            content {
                embeddingPlatforms.forEach { embeddingPlatform ->
                    h1 { +page.label }
                    renderVectorStats(counts[embeddingPlatform])

                    h3(classes = "mt-5") { +"Pending Deletions" }
                    renderPendingDeletes(pendingDeletes = pendingDeletes, showTeam = false)
                }
            }
        }
    }

    private fun FlowContent.renderVectorStats(globalCounts: Int?) {
        table(classes = "table table-hover align-middle") {
            thead(classes = "table-dark") {
                tr {
                    th(scope = ThScope.col) { +"Embedding Model" }
                    th(scope = ThScope.col) { +"Dimension" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Count" }
                }
            }
            tbody(classes = "table-dark") {
                tr {
                    td { +EmbeddingModel.DEFAULT.name }
                    td { +formattedNumber(EmbeddingModel.DEFAULT.dimension) }
                    td { +formattedNumber(globalCounts ?: 0) }
                }
            }
        }
    }

    private fun FlowContent.renderPendingDeletes(pendingDeletes: List<EmbeddingDelete>, showTeam: Boolean) {
        table(classes = "table table-hover align-middle searchable") {
            thead(classes = "table-dark") {
                tr {
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Modified" }
                    if (showTeam) {
                        th(scope = ThScope.col) { +"Namespace" }
                    }
                    th(scope = ThScope.col) { +"Installation" }
                    th(scope = ThScope.col) { +"Group" }
                    th(scope = ThScope.col) { +"Revision" }
                    th(scope = ThScope.col) { +"Status" }
                }
            }
            tbody(classes = "table-dark") {
                pendingDeletes.forEach { delete ->
                    tr {
                        td { timeAgo(delete.createdAt) }
                        td { timeAgo(delete.modifiedAt) }
                        if (showTeam) {
                            td { code { +delete.namespaceId.toString() } }
                        }
                        td { code { +delete.installationId.toString() } }
                        td { code { +delete.groupId.toString() } }
                        td { code { +delete.revision.toString() } }
                        td { asBadge(delete.status) }
                    }
                }
            }
        }
    }

    private fun formattedNumber(number: Number): String {
        return NUMBER_FORMAT.format(number)
    }
}
