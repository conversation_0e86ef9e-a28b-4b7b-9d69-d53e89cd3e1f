package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrg
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredEnum
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItemInput
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsCiPage.ciAnalyticsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildsPage.buildsMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagesPage.triagesMenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.adminwebservice.config.AdminConfig
import com.nextchaptersoftware.db.models.CIRepoSelectionMode
import com.nextchaptersoftware.db.models.CIScmId
import com.nextchaptersoftware.db.models.CIWebhookEvent
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.stores.CIScmStore.CIScmBundle
import com.nextchaptersoftware.db.stores.InstallationBundle
import com.nextchaptersoftware.db.stores.Stores.ciScmStore
import com.nextchaptersoftware.db.stores.Stores.ciWebhookEventStore
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.utils.CollectionsUtils.onNotEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.http.Parameters
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.receiveParameters
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.code
import kotlinx.html.h1
import kotlinx.html.h2
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object CiProviderPage {

    private val adminConfig = AdminConfig.INSTANCE

    internal fun Parameters.optionalCiInstallationId() = optionalId("ciInstallationId", ::InstallationId)

    internal fun Parameters.ciInstallationId() = optionalCiInstallationId().required { "Missing ciInstallationId" }

    internal suspend fun Parameters.optionalCiInstallation() = optionalCiInstallationId()?.let {
        installationStore.findById(installationId = it)
    }

    internal suspend fun Parameters.ciInstallation() = ciInstallationId().let {
        installationStore.findById(installationId = it).required { "Unknown ciInstallation" }
    }

    internal suspend fun Parameters.ciInstallationBundle(
        includeDeleted: Boolean? = false,
    ) = ciInstallationId().let {
        installationStore.findBundle(installationId = it, includeDeleted = includeDeleted).required { "Unknown ciInstallation" }
    }

    internal fun Parameters.ciScmId() = requiredId("ciScmId", ::CIScmId)

    internal suspend fun Parameters.ciScm() = ciScmId().let {
        ciScmStore.findById(ciScmId = it).required { "Unknown ciScm" }
    }

    suspend fun RoutingContext.renderCiProviderPage(
        page: AdminPage,
    ) {
        val path = call.request.path()
        val adminIdentity = call.getAdminIdentity()
        val breadcrumb = call.makeBreadcrumb()

        val org = call.parameters.optionalOrg()
        val bundle = call.parameters.ciInstallationBundle(
            includeDeleted = true,
        )

        val lastWebhook = ciWebhookEventStore.listWebhookEvents(ciInstallationId = bundle.installation.id).maxByOrNull { it.createdAt }

        val ciScms = ciScmStore.findAllBundles(ciInstallationId = bundle.installation.id)

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org ?: bundle.org) }
            relatedMenu {
                renderRelatedMenu(
                    items = relatedMenuItems(
                        path = path,
                    ),
                )
            }
            actionMenu {
                renderActionMenu(
                    items = actionMenuItems(
                        path = path,
                        ciScms = ciScms,
                    ),
                )
            }
            content {
                h1 { +page.label }
                renderCiInstallation(
                    bundle = bundle,
                    lastWebhook = lastWebhook,
                )

                ciScms?.onNotEmpty {
                    h2 { +"Scms" }
                    renderCiScms(
                        ciScms = ciScms,
                    )
                }
            }
        }
    }

    private fun FlowContent.renderCiInstallation(
        bundle: InstallationBundle,
        lastWebhook: CIWebhookEvent?,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Id", bundle.installation.id)
                property("External Id") {
                    code { +bundle.installation.installationExternalId }
                }
                property("Provider", bundle.installation.provider)
                property("Active") {
                    asBadge(!bundle.installation.isDeleted, trueText = "Active", falseText = "Deleted")
                }
                property("Last Webhook") {
                    lastWebhook?.let { timeAgo(it.createdAt) } ?: +"never"
                }
            }
        }
    }

    private fun FlowContent.renderCiScms(
        ciScms: List<CIScmBundle>,
    ) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"#" }
                    th(scope = ThScope.col) { +"Scm Provider" }
                    th(scope = ThScope.col) { +"Scm Installation" }
                    th(scope = ThScope.col) { +"Mode" }
                    th(scope = ThScope.col) { +"Bot Required?" }
                    th(scope = ThScope.col) { +"Bot Account" }
                }
            }
            tbody(classes = "table-dark") {
                ciScms
                    .sortedByDescending { it.ciScm.createdAt }
                    .forEachIndexed { index, bundle ->
                        tr {
                            td { +"${ciScms.size - index}" }
                            td { asBadge(bundle.scmInstallation.provider) }
                            td { profile(bundle.scmInstallation) }
                            td { asBadge(bundle.ciScm.mode) }
                            td { if (bundle.scmInstallationBotRequired) +"Yes" else +"No" }
                            td { bundle.scmInstallationBot?.let { profile(it) } ?: +"-" }
                        }
                    }
            }
        }
    }

    private fun relatedMenuItems(
        path: String,
    ): List<MenuItem> = listOf(
        ciAnalyticsMenuItem(path = path, description = "All CI Analytics for this ciInstallation"),
        buildsMenuItem(path = path, description = "All CI Builds in this ciInstallation"),
        triagesMenuItem(path = path, description = "All CI Builds in this ciInstallation"),
    )

    private fun actionMenuItems(
        path: String,
        ciScms: List<CIScmBundle>?,
    ): List<MenuItem> = listOfNotNull(
        ciScms?.let {
            adminConfig.adminWeb.allowCiScmUpdate.takeIf { it }?.let {
                ciScmModeUpdateMenuItem(
                    path = path,
                    ciScms = ciScms,
                )
            }
        },
    )

    private fun ciScmModeUpdateMenuItem(
        path: String,
        ciScms: List<CIScmBundle>,
    ) = MenuItem(
        href = "$path/ciScmModeUpdate",
        label = "CIScm Mode: update",
        description = "Update the ci scm repo selection mode",
        style = BootstrapStyle.Danger,
        inputs = listOf(
            MenuItemInput.SelectInput(
                name = "ciScmId",
                label = "Ci Scm",
                items = ciScms.map {
                    MenuItemInput.SelectInput.Item(
                        label = "${it.scmInstallation.provider}: ${it.scmInstallation.displayName}",
                        value = it.ciScm.id,
                    )
                },
            ),
            MenuItemInput.SelectInput(
                name = "mode",
                label = "Mode",
                items = CIRepoSelectionMode.entries.map {
                    MenuItemInput.SelectInput.Item(
                        label = it.name,
                        value = it.name,
                    )
                },
            ),
        ),
    )

    internal suspend fun RoutingContext.ciScmModeUpdate() {
        check(adminConfig.adminWeb.allowCiScmUpdate) { "CIScm update is not allowed" }

        val bodyParams = call.receiveParameters()
        val ciScm = bodyParams.ciScm()
        val mode = bodyParams.requiredEnum<CIRepoSelectionMode>("mode")

        ciScmStore.update(
            ciScmId = ciScm.id,
            mode = mode,
        )
    }
}
