package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.org
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.coda.services.CodaTokenProvider
import com.nextchaptersoftware.db.models.CodaInstallation
import com.nextchaptersoftware.db.models.Ingestion
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.IngestionStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgress
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgressServiceProvider
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.http.ContentType
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.stream.appendHTML

object CodaInstallationPage {

    suspend fun RoutingContext.renderCodaInstallationPage(
        page: AdminPage,
        ingestionProgressServiceProvider: IngestionProgressServiceProvider,
        installationStore: InstallationStore = Stores.installationStore,
        ingestionStore: IngestionStore = Stores.ingestionStore,
    ) {
        val breadcrumb = call.makeBreadcrumb()

        val adminIdentity = call.getAdminIdentity()
        val org = call.parameters.org()
        val installationId = call.parameters.requiredId("codaInstallationId", ::InstallationId)

        val installation = installationStore.findById(installationId = installationId)
            ?: throw IllegalArgumentException("Could not find installation")

        val ingestion = ingestionStore.find(installationId = installation.id, provider = Provider.Coda)

        val ingestionProgressService = ingestionProgressServiceProvider.get(
            installationId = installation.id,
            orgId = installation.orgId,
        )
        val progress = ingestionProgressService.progress()

        val path = call.request.path()

        val actions = buildList {
            addAll(
                listOf(
                    MenuItem(
                        href = "$path/reingestCoda",
                        label = "Reingest Coda installation",
                        description = "Reingest all docs",
                    ),
                ),
            )
        }

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, org) }
            actionMenu { renderActionMenu(actions, sort = false) }
            content {
                h1 { +page.label }
                render(installation, ingestion, progress, path)
            }
        }
    }

    @Suppress("LongMethod")
    private fun FlowContent.render(
        installation: Installation,
        ingestion: Ingestion?,
        progress: IngestionProgress,
        path: String,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Name", installation.displayName)
                property("External ID", installation.installationExternalId)
                property("Created", installation.createdAt)
                property("HTML Url", installation.htmlUrl)
                property("Last Synced", ingestion?.lastSynced)
                property("Progress", progress.toString())
                property("API Token") {
                    div {
                        attributes["hx-get"] = "$path/getCodaApiToken"
                        attributes["hx-trigger"] = "click"
                        attributes["hx-swap"] = "outerHTML"
                        +"*** Click to reveal ***"
                    }
                }
            }
        }
    }

    suspend fun RoutingContext.reingestCoda(
        ingestionStore: IngestionStore = Stores.ingestionStore,
    ) {
        val installationId = call.parameters.requiredId("codaInstallationId", ::InstallationId)
        ingestionStore.setLastSynced(installationId = installationId, lastSynced = null)
    }

    suspend fun RoutingContext.getCodaApiToken(
        tokenProvider: CodaTokenProvider,
        installationStore: InstallationStore = Stores.installationStore,
    ) {
        val installationId = call.parameters.requiredId("codaInstallationId", ::InstallationId)
        val installation = installationStore.findById(installationId = installationId).required()

        val apiToken = tokenProvider.getAccessToken(installation = installation as CodaInstallation)
        val htmlFragment = buildString {
            appendHTML().div {
                +apiToken.value
            }
        }

        call.respondText(htmlFragment, ContentType.Text.Html)
    }
}
