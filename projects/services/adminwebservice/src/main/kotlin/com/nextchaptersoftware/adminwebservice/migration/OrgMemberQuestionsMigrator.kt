@file:Suppress("ktlint:nextchaptersoftware:prohibit-adminwebservice-migration-rule")

package com.nextchaptersoftware.adminwebservice.migration

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.MLInferenceModel
import com.nextchaptersoftware.db.models.MessageModel
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import kotlin.time.Instant
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.max
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.update

object OrgMemberQuestionsMigrator {

    const val BATCH_SIZE = 100

    suspend fun backfillLastQuestionAskedAt() {
        // Identify all org members that have asked a question, but don't have a last question asked at.
        val orgMemberIds = findCandidatesOrgMembers()

        // Find the last answer created at for each of those org members.
        val lastAnswerAtByMember = findLastAnswerCreatedAt(orgMemberIds)

        // Update the org member with the last question asked at.
        updateOrgMembers(lastAnswerAtByMember)
    }

    private suspend fun findCandidatesOrgMembers(): Set<OrgMemberId> {
        return suspendedTransaction {
            OrgMemberModel
                .select(OrgMemberModel.id)
                .where {
                    (OrgMemberModel.questionsAskedPublic greater 0) or
                            (OrgMemberModel.questionsAskedPrivate greater 0) and
                            OrgMemberModel.lastQuestionAskedAt.isNull()
                }
                .map { it[OrgMemberModel.id].value }
                .toSet()
        }
    }

    private suspend fun findLastAnswerCreatedAt(orgMemberIds: Set<OrgMemberId>): List<Pair<OrgMemberId, Instant>> {
        if (orgMemberIds.isEmpty()) {
            return emptyList()
        }

        val messageCreatedAlias = MessageModel.createdAt.max()

        return suspendedTransaction {
            MLInferenceModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MessageModel,
                    otherColumn = MessageModel.id,
                    onColumn = MLInferenceModel.botMessage,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = ThreadModel,
                    otherColumn = ThreadModel.id,
                    onColumn = MessageModel.thread,
                )
                .select(
                    MLInferenceModel.questionerOrgMember,
                    messageCreatedAlias,
                )
                .where { MLInferenceModel.questionerOrgMember inList orgMemberIds }
                .groupBy(MLInferenceModel.questionerOrgMember)
                .mapNotNull { row ->
                    val member = row[MLInferenceModel.questionerOrgMember]?.value ?: return@mapNotNull null
                    val lastQuestionAskedAt = row[messageCreatedAlias] ?: return@mapNotNull null
                    member to lastQuestionAskedAt
                }
        }
    }

    private suspend fun updateOrgMembers(lastAnswerCreatedAtByMember: List<Pair<OrgMemberId, Instant>>) {
        lastAnswerCreatedAtByMember.chunked(BATCH_SIZE).forEach { batch ->
            updateOrgMembersBatch(batch)
        }
    }

    private suspend fun updateOrgMembersBatch(lastQuestionAskedAts: List<Pair<OrgMemberId, Instant>>) {
        suspendedTransaction {
            lastQuestionAskedAts.forEach { (orgMemberId, lastQuestionAskedAt) ->
                OrgMemberModel.update(
                    where = {
                        AllOp(
                            OrgMemberModel.id eq orgMemberId,
                            OrgMemberModel.lastQuestionAskedAt.isNull(),
                        )
                    },
                    body = {
                        it[OrgMemberModel.lastQuestionAskedAt] = lastQuestionAskedAt
                    },
                )
            }
        }
    }
}
