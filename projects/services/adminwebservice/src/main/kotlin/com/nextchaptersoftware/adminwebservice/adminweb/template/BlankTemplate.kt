package com.nextchaptersoftware.adminwebservice.adminweb.template

import io.ktor.server.html.Placeholder
import io.ktor.server.html.Template
import io.ktor.server.html.insert
import kotlinx.html.BODY
import kotlinx.html.FlowContent
import kotlinx.html.HEAD
import kotlinx.html.HTML
import kotlinx.html.body
import kotlinx.html.div
import kotlinx.html.head
import kotlinx.html.lang

class BlankTemplate : Template<HTML> {
    val header = Placeholder<HEAD>()
    val navbar = Placeholder<BODY>()
    val content = Placeholder<FlowContent>()

    override fun HTML.apply() {
        lang = "en"
        head {
            insert(header)
        }
        body {
            insert(navbar)
            div {
                insert(content)
            }
        }
    }
}
