baseservice:
  replicaCount: 1
  resources:
    limits:
      memory: 2560Mi
    requests:
      cpu: 300m
      memory: 1536Mi
  service:
    environment: "prod"
    baseUrl: "getunblocked.com"
    envFrom:
      secretRefs:
        - unblocked-scm-service-secrets-env
        - unblocked-service-secrets-env
        - unblocked-service-user-secrets-env
        - unblocked-stripe-secrets-env
    env:
      - name: "REDIS_PASSWORD_FILE_PATH"
        value: "/secrets/redis-password"
      - name: "ACTIVEMQ_PASSWORD_FILE_PATH"
        value: "/secrets/activemq-password"
  kedaautoscaling:
    enabled: true
    minReplicaCount: 1
    maxReplicaCount: 6
    pollingInterval: 120
    triggers:
      cloudwatch:
        - enabled: true
          expression: SELECT AVG(QueueSize) FROM SCHEMA("AWS/AmazonMQ", Broker,Queue) WHERE Queue = 'billing_events'
          metricCollectionTime: "60"
          metricStat: "Average"
          metricStatPeriod: "30"
          metricEndTimeOffset: "20"
          targetMetricValue: "200.0"
          awsRegion: "us-west-2"
