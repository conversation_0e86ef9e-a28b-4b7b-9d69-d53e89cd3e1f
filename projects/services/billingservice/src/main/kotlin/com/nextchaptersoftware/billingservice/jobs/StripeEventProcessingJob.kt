package com.nextchaptersoftware.billingservice.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.BackgroundJob

class StripeEventProcessingJob(
    private val eventDequeueService: EventDequeueService,
) : BackgroundJob {
    override val name: String
    get() = "Stripe event processing job"

    override suspend fun run() {
        eventDequeueService.process()
    }
}
