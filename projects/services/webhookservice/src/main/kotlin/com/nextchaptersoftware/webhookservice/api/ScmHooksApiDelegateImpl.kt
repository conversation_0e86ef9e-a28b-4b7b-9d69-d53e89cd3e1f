@file:Suppress("ktlint:nextchaptersoftware:no-capitalized-ids", "SpreadOperator")

package com.nextchaptersoftware.webhookservice.api

import com.nextchaptersoftware.api.ScmHooksApiDelegateInterface
import com.nextchaptersoftware.ci.enqueue.CiWebhookEventEnqueueService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketWebhookEvent
import com.nextchaptersoftware.scm.gitlab.models.GitLabWebhookEvents
import com.nextchaptersoftware.scm.queue.enqueue.ScmWebhookEventEnqueueService
import com.nextchaptersoftware.scm.queue.payloads.BitbucketDataCenterWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.BitbucketWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.GitHubWebhookEvent
import com.nextchaptersoftware.scm.queue.payloads.GitHubWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.GitLabWebhookHeaders
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import io.ktor.http.hostWithPort
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class ScmHooksApiDelegateImpl(
    private val ciWebhookEventEnqueueService: CiWebhookEventEnqueueService,
    private val scmWebhookEventEnqueueService: ScmWebhookEventEnqueueService,
) : ScmHooksApiDelegateInterface {

    override suspend fun bitbucketEvent(
        context: RoutingContext,
        input: Resources.bitbucketEvent,
        xEventKey: String,
        xHookUuid: String,
        xRequestUuid: String,
        body: String,
    ): String {
        val headers = buildList {
            add(BitbucketWebhookHeaders.EventId.name to xRequestUuid)
            add(BitbucketWebhookHeaders.EventType.name to xEventKey)
            add(BitbucketWebhookHeaders.WebhookId.name to xHookUuid)
        }

        return withLoggingContextAsync(
            "Content-Length" to context.call.request.headers["Content-Length"],
            *headers.toTypedArray(),
        ) {
            when (BitbucketWebhookEvent.fromString(xEventKey)) {
                BitbucketWebhookEvent.RepoCommitStatusCreated,
                BitbucketWebhookEvent.RepoCommitStatusUpdated,
                -> {
                    enqueueCi(
                        provider = Provider.Bitbucket,
                        headers = headers,
                        body = body,
                    )
                }

                else -> enqueueScm(
                    provider = Provider.Bitbucket,
                    headers = headers,
                    body = body,
                )
            }

            "Accepted by Unblocked."
        }
    }

    override suspend fun bitbucketDataCenterEvent(
        context: RoutingContext,
        input: Resources.bitbucketDataCenterEvent,
        xEventKey: String,
        xRequestId: String,
        body: String,
    ): String {
        val headers = buildList {
            add(BitbucketDataCenterWebhookHeaders.RequestId.name to xRequestId)
            add(BitbucketDataCenterWebhookHeaders.EventType.name to xEventKey)
        }

        return withLoggingContextAsync(
            "Content-Length" to context.call.request.headers["Content-Length"],
            *headers.toTypedArray(),
        ) {
            enqueueScm(
                provider = Provider.BitbucketDataCenter,
                headers = headers,
                body = body,
            )
            "Accepted by Unblocked."
        }
    }

    override suspend fun githubEvent(
        context: RoutingContext,
        input: Resources.githubEvent,
        xGitHubDelivery: String,
        xGitHubEvent: String,
        xGitHubHookID: String,
        xGitHubHookInstallationTargetID: String,
        xGitHubHookInstallationTargetType: String,
        xHubSignature256: String,
        body: String,
    ): String {
        val headers = buildList {
            add(GitHubWebhookHeaders.GitHubDelivery.name to xGitHubDelivery)
            add(GitHubWebhookHeaders.GitHubEvent.name to xGitHubEvent)
            add(GitHubWebhookHeaders.GitHubHookID.name to xGitHubHookID)
            add(GitHubWebhookHeaders.GitHubHookInstallationTargetID.name to xGitHubHookInstallationTargetID)
            add(GitHubWebhookHeaders.GitHubHookInstallationTargetType.name to xGitHubHookInstallationTargetType)
            add(GitHubWebhookHeaders.GitHubHubSignature256.name to xHubSignature256)
        }

        return withLoggingContextAsync(
            "Content-Length" to context.call.request.headers["Content-Length"],
            *headers.toTypedArray(),
        ) {
            when (GitHubWebhookEvent.fromString(xGitHubEvent)) {
                GitHubWebhookEvent.CheckRun,
                     -> doNothing()

                GitHubWebhookEvent.CheckSuite,
                    -> enqueueCi(
                        provider = Provider.GitHub,
                        headers = headers,
                        body = body,
                    )

                else -> enqueueScm(
                    provider = Provider.GitHub,
                    headers = headers,
                    body = body,
                )
            }

            "Accepted by Unblocked."
        }
    }

    override suspend fun githubEnterpriseEvent(
        context: RoutingContext,
        input: Resources.githubEnterpriseEvent,
        xGitHubDelivery: String,
        xGitHubEvent: String,
        xGitHubHookID: String,
        xGitHubHookInstallationTargetID: String,
        xGitHubHookInstallationTargetType: String,
        xGitHubEnterpriseHost: String,
        xGitHubEnterpriseVersion: String,
        body: String,
        xHubSignature256: String?,
        xHubSignature: String?,
    ): String {
        val headers = buildList {
            add(GitHubWebhookHeaders.GitHubDelivery.name to xGitHubDelivery)
            add(GitHubWebhookHeaders.GitHubEvent.name to xGitHubEvent)
            add(GitHubWebhookHeaders.GitHubHookID.name to xGitHubHookID)
            add(GitHubWebhookHeaders.GitHubHookInstallationTargetID.name to xGitHubHookInstallationTargetID)
            add(GitHubWebhookHeaders.GitHubHookInstallationTargetType.name to xGitHubHookInstallationTargetType)
            add(GitHubWebhookHeaders.GitHubEnterpriseHost.name to xGitHubEnterpriseHost)
            add(GitHubWebhookHeaders.GitHubEnterpriseVersion.name to xGitHubEnterpriseVersion)
            add(GitHubWebhookHeaders.GitHubHubSignature256.name to xHubSignature256.orEmpty())
            add(GitHubWebhookHeaders.GitHubHubSignature.name to xHubSignature.orEmpty())
        }

        return withLoggingContextAsync(
            "Content-Length" to context.call.request.headers["Content-Length"],
            *headers.toTypedArray(),
        ) {
            when (GitHubWebhookEvent.fromString(xGitHubEvent)) {
                GitHubWebhookEvent.CheckRun,
                    -> doNothing()

                GitHubWebhookEvent.CheckSuite,
                    -> enqueueCi(
                        provider = Provider.GitHubEnterprise,
                        headers = headers,
                        body = body,
                    )

                else -> enqueueScm(
                    provider = Provider.GitHubEnterprise,
                    headers = headers,
                    body = body,
                )
            }

            "Accepted by Unblocked."
        }
    }

    override suspend fun gitlabEvent(
        context: RoutingContext,
        input: Resources.gitlabEvent,
        xGitlabInstance: String,
        xGitlabWebhookUUID: String,
        xGitlabEvent: String,
        xGitlabEventUUID: String,
        body: String,
    ): String {
        val headers = buildList {
            add(GitLabWebhookHeaders.XGitlabInstance.name to xGitlabInstance)
            add(GitLabWebhookHeaders.XGitlabWebhookUUID.name to xGitlabWebhookUUID)
            add(GitLabWebhookHeaders.XGitlabEvent.name to xGitlabEvent)
            add(GitLabWebhookHeaders.XGitlabEventUUID.name to xGitlabEventUUID)
        }

        val provider = when (xGitlabInstance.asUrl.hostWithPort) {
            "gitlab.com:443" -> Provider.GitLab
            else -> Provider.GitLabSelfHosted
        }

        return withLoggingContextAsync(
            "Content-Length" to context.call.request.headers["Content-Length"],
            *headers.toTypedArray(),
        ) {
            when (GitLabWebhookEvents.fromHookLabel(xGitlabEvent)) {
                GitLabWebhookEvents.Job,
                    -> doNothing()

                GitLabWebhookEvents.Pipeline,
                -> {
                    enqueueCi(
                        provider = provider,
                        headers = headers,
                        body = body,
                    )
                }

                else -> enqueueScm(
                    provider = provider,
                    headers = headers,
                    body = body,
                )
            }

            "Accepted by Unblocked."
        }
    }

    private fun enqueueCi(
        provider: Provider,
        headers: List<Pair<String, String>>,
        body: String,
    ) {
        if (body.isEmpty()) {
            throw BadRequestException("The webhook message body is invalid due to size")
        }
        ciWebhookEventEnqueueService.enqueueEvent(
            provider = provider,
            body = body,
            headers = headers,
        )
    }

    private fun enqueueScm(provider: Provider, headers: List<Pair<String, String>>, body: String) {
        if (body.isEmpty()) {
            throw BadRequestException("The webhook message body is invalid due to size")
        }
        scmWebhookEventEnqueueService.enqueueEvent(
            provider = provider,
            headers = headers,
            body = body,
        )
    }
}
