plugins {
    application
    kotlin("jvm")
    kotlin("plugin.serialization")
}

application {
    mainClass.set("com.nextchaptersoftware.authservice.ApplicationKt")
}

dependencies {
    implementation(project(":projects:clients:client-scm", configuration = "default"))
    implementation(project(":projects:libs:lib-api", configuration = "default"))
    implementation(project(":projects:libs:lib-api-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-api-model", configuration = "default"))
    implementation(project(":projects:libs:lib-asana-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-auth-saml", configuration = "default"))
    implementation(project(":projects:libs:lib-auth-saml-api", configuration = "default"))
    implementation(project(":projects:libs:lib-billing", configuration = "default"))
    implementation(project(":projects:libs:lib-cas", configuration = "default"))
    implementation(project(":projects:libs:lib-common", configuration = "default"))
    implementation(project(":projects:libs:lib-asana-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-confluence-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-google-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-jira-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-ktor", configuration = "default"))
    implementation(project(":projects:libs:lib-linear-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-log", configuration = "default"))
    implementation(project(":projects:libs:lib-log-kotlin", configuration = "default"))
    implementation(project(":projects:libs:lib-notion-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-repo-access-rpc", configuration = "default"))
    implementation(project(":projects:libs:lib-scim", configuration = "default"))
    implementation(project(":projects:libs:lib-security", configuration = "default"))
    implementation(project(":projects:libs:lib-service-bootstrap", configuration = "default"))
    implementation(project(":projects:libs:lib-slack-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-trace-ktor", configuration = "default"))
    implementation(project(":projects:libs:lib-trace-service", configuration = "default"))
    implementation(project(":projects:models", configuration = "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)
    testImplementation(testLibs.bundles.test.ktor)

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:clients:client-scm", "test"))
    testImplementation(project(":projects:libs:lib-api", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-config", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
    testImplementation(project(":projects:libs:lib-scm", "test"))
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlinx.serialization.ExperimentalSerializationApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
