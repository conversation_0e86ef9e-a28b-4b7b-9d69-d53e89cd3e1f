package com.nextchaptersoftware.authservice.api

import com.nextchaptersoftware.api.SAMLAuthApiDelegateInterface
import com.nextchaptersoftware.api.auth.services.LoginService
import com.nextchaptersoftware.api.auth.services.State.newState
import com.nextchaptersoftware.api.auth.services.url.login.SamlLoginSource
import com.nextchaptersoftware.api.auth.services.url.redirect.RedirectAuthOverrideService
import com.nextchaptersoftware.api.models.FindSsoProviderRequest
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.SSOProvider
import com.nextchaptersoftware.api.models.converters.SamlIdentityProviderExtensions.asApiProvider
import com.nextchaptersoftware.api.models.converters.SamlIdpMetadataExtensions.asApiSsoProvider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.auth.saml.SamlAuthProviderService
import com.nextchaptersoftware.auth.saml.SamlDiscoveryService
import com.nextchaptersoftware.auth.saml.SamlSettingsProviderService
import com.nextchaptersoftware.auth.saml.SamlUserProfile
import com.nextchaptersoftware.auth.saml.utils.ApplicationCallSamlExtensions.getServletRequest
import com.nextchaptersoftware.auth.saml.utils.ApplicationCallSamlExtensions.getServletResponse
import com.nextchaptersoftware.cas.CASKey
import com.nextchaptersoftware.cas.RedisCAS
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.db.models.AuthenticationStateDAO
import com.nextchaptersoftware.db.models.SamlIdpMetadataId
import com.nextchaptersoftware.db.stores.SamlIdpMetadataStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.maintenance.MemberMaintenance
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.scim.ScimService
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import com.nextchaptersoftware.utils.CollectionsUtils.onNotEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.onelogin.saml2.settings.Saml2Settings
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLBuilder
import io.ktor.server.routing.RoutingContext
import java.time.Instant as JavaInstant
import kotlin.time.Instant
import kotlin.time.toKotlinInstant
import mu.KotlinLogging
import org.openapitools.server.Resources

private val LOGGER = KotlinLogging.logger {}

class SAMLAuthApiDelegateImpl(
    private val config: AuthenticationConfig,
    private val loginService: LoginService,
    private val memberMaintenance: MemberMaintenance,
    private val redirectAuthOverrideService: RedirectAuthOverrideService,
    private val samlAuthProviderService: SamlAuthProviderService,
    private val samlCodeExchangeStore: RedisCAS,
    private val samlDiscoveryService: SamlDiscoveryService,
    private val samlIdpMetadataStore: SamlIdpMetadataStore = Stores.samlIdpMetadataStore,
    private val samlRelayStateStore: RedisCAS,
    private val samlSettingsProviderService: SamlSettingsProviderService,
    private val scimService: ScimService,
) : SAMLAuthApiDelegateInterface {

    // TODO all errors should be reported as `UserVisibleException`s
    override suspend fun consumeSamlAssertion(
        context: RoutingContext,
        input: Resources.consumeSamlAssertion,
    ): String {
        val saml = samlIdpMetadataStore.findByOrgId(context.orgId)
            .required { "Missing SAML provider for org" }

        val relayStateKey: String? = context.call.getServletRequest().getParameter("RelayState")
        LOGGER.debugAsync("relayStateKey" to relayStateKey) { "Consuming SAML assertion" }

        val samlRelayState = relayStateKey
            ?.let { samlRelayStateStore.retrieve(CASKey(relayStateKey)) }
            ?.decode<SamlRelayState>()
            ?: run {
                // RelayState will not exist if the login was initiated from the IdP (e.g., Okta dashboard)
                // On this path, we fabricate a RelayState with the SSO provider ID
                val state = AuthenticationStateDAO.newState()
                SamlRelayState(
                    authState = OAuthState(
                        nonce = state.nonce,
                        provider = saml.samlIdentityProvider.asApiProvider(),
                        state = null,
                        clientState = null,
                        enterpriseProviderId = null,
                        ssoProviderId = saml.id.value,
                    ),
                    overrideAuthRedirectUrl = null,
                )
            }

        // Sanity check validation
        samlRelayState.authState.ssoProviderId?.let(::SamlIdpMetadataId)
            .required { "Missing SSO provider ID" }
            .also { check(it == saml.id) { "SSO provider ID mismatch" } }

        val auth = samlAuthProviderService.getSamlAuth(
            samlIdpMetadata = saml,
            httpServletRequest = context.call.getServletRequest(),
            httpServletResponse = context.call.getServletResponse(),
        )

        // SAMLResponse is extracted from the httpServletRequest and processed in this library method
        auth.processResponse()

        auth.errors.onNotEmpty {
            LOGGER.errorAsync(
                "errors" to it.joinToString(),
                "isAuthenticated" to auth.isAuthenticated,
            ) { "Invalid SAML assertion" }
        }

        if (!auth.isAuthenticated) {
            throw BadRequestException("Not authenticated")
        }

        val sessionExpiration = auth.sessionExpiration?.let { JavaInstant.ofEpochMilli(it.millis).toKotlinInstant() }
        val samlUserProfile = SamlUserProfile.fromAuth(auth = auth).also {
            LOGGER.infoAsync(
                "relayState" to relayStateKey,
                "sessionExpiration" to sessionExpiration,
            ) { "SAML assertion consumed" }
        }

        // Upsert an identity from this SAML assertion
        val identityId = scimService.upsertScimUser(
            saml = saml,
            externalId = null, // externalId MUST be null on this SAML path
            displayName = samlUserProfile.displayName,
            email = samlUserProfile.email,
            refreshTokenExpiresAt = sessionExpiration ?: Instant.nowWithMicrosecondPrecision().plus(config.refreshTokenExpiry),
        ).also {
            memberMaintenance.upsertMember(
                orgId = saml.org,
                identity = it,
                installationId = saml.installation,
                isPrimaryMember = true,
                isCurrentMember = true,
            )
        }.id

        val redirectAuthUrl = redirectAuthOverrideService.getRedirectUrl(samlRelayState.overrideAuthRedirectUrl)

        return URLBuilder(redirectAuthUrl).apply {
            val code = samlCodeExchangeStore.store(identityId.toString())
            val clientState = samlRelayState.authState.clientState

            parameters.append("code", code.asString)
            parameters.append("state", samlRelayState.authState.encode().urlSafeBase64Encode())
            clientState?.also { parameters.append("clientState", clientState) }
        }.buildString()
    }

    override suspend fun getSamlMetadata(
        context: RoutingContext,
        input: Resources.getSamlMetadata,
    ): String {
        val orgId = context.orgId
        val settings = samlSettingsProviderService.getSettingsWithoutIdpMetadata(orgId = orgId)
        val metadata = settings.spMetadata
        val errors = Saml2Settings.validateMetadata(metadata)

        errors.onNotEmpty {
            LOGGER.errorAsync("errors" to errors.joinToString()) { "Invalid SAML metadata" }
        }

        return metadata
    }

    override suspend fun findSsoProvider(
        context: RoutingContext,
        input: Resources.findSsoProvider,
        body: FindSsoProviderRequest,
    ): SSOProvider {
        val email = EmailAddress.ofOrNull(body.email)
            ?: throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid email.",
                detail = "Please enter a valid email address.",
                url = null,
            )

        // Lookup by registered domain, or if the domain has not been registered, lookup by email
        val matchingSsoProvider = samlDiscoveryService.findByRegisteredDomain(email.domain)
            ?: samlDiscoveryService.findSamlIdpByEmail(email)

        return matchingSsoProvider
            ?.let { saml ->
                val loginUrl = loginService.buildLoginUrl(
                    loginSource = SamlLoginSource(saml),
                    clientSecret = input.clientSecret,
                    agentType = input.agentType,
                    authRedirectOverrideUrl = input.overrideRedirectUrl,
                    clientState = input.clientState,
                )

                Stores.orgStore.findById(orgId = saml.org)?.let { org ->
                    saml.asApiSsoProvider(authorizedIdentity = null, displayName = org.displayName, loginUrl = loginUrl)
                }
            }
            ?.also {
                LOGGER.debugAsync("emailDomain" to email.domain, "ssoProviderId" to it.ssoProviderId) { "Found SSO provider" }
            }
            ?: run {
                LOGGER.warnAsync("emailDomain" to email.domain) { "No SAML SSO enabled team could be found" }
                throw UserVisibleException(
                    statusCode = HttpStatusCode.NotFound,
                    title = "No SAML SSO enabled team could be found.",
                    detail = "Follow our step-by-step guide in the documentation to set up SSO for your team.",
                    url = "https://docs.getunblocked.com/team-settings/sso".asUrl,
                )
            }
    }
}
