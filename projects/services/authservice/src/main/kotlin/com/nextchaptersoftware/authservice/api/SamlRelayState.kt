package com.nextchaptersoftware.authservice.api

import com.nextchaptersoftware.api.models.OAuthState
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
internal data class SamlRelayState(
    @SerialName("authState")
    val authState: OAuthState,

    @SerialName("overrideAuthRedirectUrl")
    @Contextual
    val overrideAuthRedirectUrl: Url?,
)
