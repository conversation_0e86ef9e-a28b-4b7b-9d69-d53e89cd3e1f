package com.nextchaptersoftware.embeddingservice.jobs

import com.nextchaptersoftware.event.queue.dequeue.EventDequeueService
import com.nextchaptersoftware.service.BackgroundJob

class EmbeddingEventJob(
    private val eventDequeueService: EventDequeueService,
) : BackgroundJob {
    override val name: String
        get() = "Embedding Event Job"

    override suspend fun run() {
        eventDequeueService.process()
    }
}
