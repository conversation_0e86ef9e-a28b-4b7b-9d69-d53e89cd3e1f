plugins {
    application
    kotlin("jvm")
    kotlin("plugin.serialization")
}

application {
    mainClass.set("com.nextchaptersoftware.embeddingservice.ApplicationKt")
}

dependencies {
    implementation(project(":projects:libs:lib-common", configuration = "default"))
    implementation(project(":projects:libs:lib-document-embedding", configuration = "default"))
    implementation(project(":projects:libs:lib-embedding-events", configuration = "default"))
    implementation(project(":projects:libs:lib-ktor", configuration = "default"))
    implementation(project(":projects:libs:lib-log", configuration = "default"))
    implementation(project(":projects:libs:lib-log-kotlin", configuration = "default"))
    implementation(project(":projects:libs:lib-ml-completion-prompts", configuration = "default"))
    implementation(project(":projects:libs:lib-pinecone", configuration = "default"))
    implementation(project(":projects:libs:lib-rapid", configuration = "default"))
    implementation(project(":projects:libs:lib-search", configuration = "default"))
    implementation(project(":projects:libs:lib-security", configuration = "default"))
    implementation(project(":projects:libs:lib-service-bootstrap", configuration = "default"))
    implementation(project(":projects:libs:lib-trace-ktor", configuration = "default"))
    implementation(project(":projects:libs:lib-trace-service", configuration = "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)

    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-config", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlinx.serialization.ExperimentalSerializationApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=com.google.protobuf.kotlin.OnlyForUseByGeneratedProtoCode")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
