baseservice:
  resources:
    limits:
      cpu: 1000m
      memory: 1024Mi
    requests:
      cpu: 400m
      memory: 768Mi
  service:
    environment: "dev"
    baseUrl: "dev.getunblocked.com"
    envFrom:
      secretRefs:
        - unblocked-service-secrets-env
        - unblocked-service-user-secrets-env
    env:
      - name: "REDIS_PASSWORD_FILE_PATH"
        value: "/secrets/redis-password"
      - name: "ACTIVEMQ_PASSWORD_FILE_PATH"
        value: "/secrets/activemq-password"
  kedaautoscaling:
    enabled: true
    minReplicaCount: 1
    maxReplicaCount: 3
    triggers:
      cloudwatch:
        - enabled: true
          expression: SELECT AVG(QueueSize) FROM SCHEMA("AWS/AmazonMQ", Broker,Queue) WHERE Queue = 'google_events'
          metricCollectionTime: "60"
          metricStat: "Average"
          metricStatPeriod: "30"
          metricEndTimeOffset: "20"
          targetMetricValue: "200.0"
          awsRegion: "us-west-2"
