package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.LicensesApiDelegateInterface
import com.nextchaptersoftware.api.models.LicenseStatus
import com.nextchaptersoftware.billing.utils.OrgBillingLicenseService
import com.nextchaptersoftware.billing.utils.OrgBillingSeatService
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.models.identityId
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.orgMemberId
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.slack.notify.SlackNotifier
import io.ktor.server.routing.RoutingContext
import java.util.UUID
import org.openapitools.server.Resources

class LicensesApiDelegateImpl(
    private val notificationEventEnqueueService: NotificationEventEnqueueService,
    private val orgBillingLicenseService: OrgBillingLicenseService,
    private val orgBillingSeatService: OrgBillingSeatService,
    private val slackNotifier: SlackNotifier,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
) : LicensesApiDelegateInterface {

    override suspend fun listAvailableLicenseInstallations(
        context: RoutingContext,
        input: Resources.listAvailableLicenseInstallations,
    ): List<UUID> {
        return orgBillingSeatService.findInstallationsForAssociatedBillingSeats(
            orgId = context.orgId,
            orgMemberId = context.orgMemberId(),
        ).map { it.value }
    }

    override suspend fun getLicenseStatus(
        context: RoutingContext,
        input: Resources.getLicenseStatus,
    ): LicenseStatus {
        val orgId = context.orgId
        val orgMemberId = context.orgMemberId()
        val hasAccess = orgBillingLicenseService.hasAccess(
            orgId = orgId,
            orgMemberId = orgMemberId,
        )
        return LicenseStatus(
            hasAccess = hasAccess,
        )
    }

    override suspend fun requestLicense(context: RoutingContext, input: Resources.requestLicense) {
        val orgId = context.orgId
        val identityId = context.identityId

        // Special way to get the org member, because there may not be a person in context.
        // Typically, we would use context.orgMember(), but that requires a person in context.
        val orgMember = orgMemberStore.findByOrgAndIdentity(orgId = orgId, identityId = identityId)
            ?: throw NotFoundException()

        orgBillingSeatService.requestSeat(
            orgId = orgId,
            orgMemberId = orgMember.id,
        )

        notificationEventEnqueueService.enqueueLicenseRequested(
            orgId = orgId,
            requestingMemberId = orgMember.id,
        )

        slackNotifier.announceLicenseRequested(
            orgId = orgId,
            orgMemberId = orgMember.id,
        )
    }
}
