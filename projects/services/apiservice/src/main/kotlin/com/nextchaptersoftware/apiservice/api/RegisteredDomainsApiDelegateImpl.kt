package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.RegisteredDomainsApiDelegateInterface
import com.nextchaptersoftware.api.auth.services.AccessValidation
import com.nextchaptersoftware.api.models.RegisterDomainRequest
import com.nextchaptersoftware.api.models.RegisteredDomain as ApiRegisteredDomain
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.RegisteredDomain
import com.nextchaptersoftware.db.models.RegisteredDomainId
import com.nextchaptersoftware.db.stores.RegisteredDomainStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dns.DomainVerificationService
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.types.Hostname
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.http.HttpStatusCode
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class RegisteredDomainsApiDelegateImpl(
    private val accessValidation: AccessValidation,
    private val capabilityValidation: CapabilityValidation,
    private val registeredDomainStore: RegisteredDomainStore = Stores.registeredDomainStore,
) : RegisteredDomainsApiDelegateInterface {

    override suspend fun deleteDomain(
        context: RoutingContext,
        input: Resources.deleteDomain,
    ) {
        accessValidation.expectAdminRole(context)
        capabilityValidation.requiresCapability(context.orgId, PlanCapabilityType.SSO)

        val registeredDomainId = input.registeredDomainId.let(::RegisteredDomainId)
        registeredDomainStore.deleteDomainById(registeredDomainId)
    }

    override suspend fun getRegisteredDomain(
        context: RoutingContext,
        input: Resources.getRegisteredDomain,
    ): ApiRegisteredDomain {
        val registeredDomainId = input.registeredDomainId.let(::RegisteredDomainId)
        return registeredDomainStore.getDomainById(context.orgId, registeredDomainId).required().asApiModel
    }

    override suspend fun listRegisteredDomains(
        context: RoutingContext,
        input: Resources.listRegisteredDomains,
    ): List<ApiRegisteredDomain> {
        return registeredDomainStore.findDomainByOrgId(context.orgId)
            .map { it.asApiModel }
    }

    override suspend fun registerDomain(
        context: RoutingContext,
        input: Resources.registerDomain,
        body: RegisterDomainRequest,
    ): ApiRegisteredDomain {
        val orgId = context.orgId
        accessValidation.expectAdminRole(context)
        capabilityValidation.requiresCapability(orgId, PlanCapabilityType.SSO)

        val domain = Hostname.parseOrNull(body.domainName)
            ?: throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = "Invalid domain",
                detail = "Please enter a valid domain.",
                url = null,
            )

        val registeredDomain = registeredDomainStore.createDomainUnverified(orgId = orgId, domain = domain)
            ?: registeredDomainStore.findDomainByOrgAndName(orgId, domain)

        return checkNotNull(registeredDomain).asApiModel
    }

    override suspend fun verifyDomain(
        context: RoutingContext,
        input: Resources.verifyDomain,
    ): ApiRegisteredDomain {
        val orgId = context.orgId
        accessValidation.expectAdminRole(context)
        capabilityValidation.requiresCapability(orgId, PlanCapabilityType.SSO)

        val registeredDomainId = input.registeredDomainId.let(::RegisteredDomainId)
        val registeredDomain = registeredDomainStore.getDomainById(orgId, registeredDomainId).required()

        if (registeredDomain.isVerified.not()) {
            if (DomainVerificationService.verifyDomain(orgId, registeredDomain.domain)) {
                registeredDomainStore.verifyDomainById(registeredDomainId)
                return registeredDomainStore.getDomainById(orgId, registeredDomainId).required().asApiModel
            }
        }

        return registeredDomain.asApiModel
    }
}

private val RegisteredDomain.asApiModel: ApiRegisteredDomain
    get() {
        return ApiRegisteredDomain(
            id = id.value,
            domainName = domain.value,
            isVerified = isVerified,
            verificationToken = DomainVerificationService.getDomainVerificationToken(org, domain),
        )
    }
