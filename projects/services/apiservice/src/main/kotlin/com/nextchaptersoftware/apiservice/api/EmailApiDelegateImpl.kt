package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.EmailApiDelegateInterface
import com.nextchaptersoftware.api.models.SendEmailInvitesRequest
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.stores.OrgEmailInviteStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.models.identityId
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.orgMemberId
import com.nextchaptersoftware.notification.events.email.models.EmailTrigger
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationOrgEvent
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources.sendTeamEmailInvites
import org.openapitools.server.Resources.sendThreadEmailInvites

class EmailApiDelegateImpl(
    private val notificationEventEnqueueService: NotificationEventEnqueueService,
    private val orgEmailInviteStore: OrgEmailInviteStore = Stores.orgEmailInviteStore,
) : EmailApiDelegateInterface {

    override suspend fun sendTeamEmailInvites(
        context: RoutingContext,
        input: sendTeamEmailInvites,
        body: SendEmailInvitesRequest,
    ) {
        val emails = body.emails?.mapNotNull { EmailAddress.ofOrNull(it) }?.toSet()?.nullIfEmpty()
            ?: return

        val orgId = context.orgId
        val orgMemberId = context.orgMemberId()

        val newEmails = orgEmailInviteStore.filterPreviousInvites(
            senderId = orgMemberId,
            recipients = emails,
        ).nullIfEmpty() ?: return

        notificationEventEnqueueService.enqueueEvent(
            NotificationOrgEvent.TeamInviteEmailEvent(
                recipients = newEmails,
                orgId = orgId,
                senderIdentityId = context.identityId,
                trigger = EmailTrigger.API_CALL,
            ),
        )

        orgEmailInviteStore.addRecipients(
            senderId = orgMemberId,
            recipients = newEmails,
        )
    }

    override suspend fun sendThreadEmailInvites(
        context: RoutingContext,
        input: sendThreadEmailInvites,
        body: SendEmailInvitesRequest,
    ) {
        val orgId = context.orgId
        body.emails?.mapNotNull { EmailAddress.ofOrNull(it) }?.let { emails ->
            notificationEventEnqueueService.enqueueEvent(
                NotificationOrgEvent.ThreadInviteJoinEmailEvent(
                    orgId = orgId,
                    recipients = emails,
                    senderIdentityId = context.identityId,
                    threadId = input.threadId.let(::ThreadId),
                    trigger = EmailTrigger.API_CALL,
                ),
            )
        }
    }
}
