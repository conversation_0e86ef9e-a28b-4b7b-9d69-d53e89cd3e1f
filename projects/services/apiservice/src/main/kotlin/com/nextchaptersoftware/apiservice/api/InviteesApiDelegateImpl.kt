package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.InviteesApiDelegateInterface
import com.nextchaptersoftware.api.models.EmailInvite
import com.nextchaptersoftware.api.models.EmailInvitee
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.TeamInviteeStore
import com.nextchaptersoftware.models.identityId
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.orgMemberId
import com.nextchaptersoftware.notification.events.email.models.EmailTrigger
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationOrgEvent
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.types.EmailAddress
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class InviteesApiDelegateImpl(
    private val notificationEventEnqueueService: NotificationEventEnqueueService,
    private val teamInviteStore: TeamInviteeStore = Stores.teamInviteeStore,
    private val memberStore: MemberStore = Stores.memberStore,
    private val peerInviteSuggestionService: PeerInviteSuggestionService,
) : InviteesApiDelegateInterface {

    companion object {
        private const val MAX_INVITEES = 6
    }

    override suspend fun getInviteesV2(context: RoutingContext, input: Resources.getInviteesV2): List<EmailInvitee> {
        val orgMemberId = context.orgMemberId()

        return peerInviteSuggestionService.getInviteSuggestions(
            orgId = context.orgId,
            orgMemberId = orgMemberId,
            sociallyConnected = input.sociallyConnected ?: false,
        )
            .map { it.member.id }
            .take(MAX_INVITEES)
            .let {
                peerInviteSuggestionService.getBestEmailForInvitees(it).toList().map { (memberId, email) ->
                    EmailInvitee(teamMemberId = memberId.value, email = email)
                }
            }
    }

    override suspend fun emailInvitees(
        context: RoutingContext,
        input: Resources.emailInvitees,
        body: List<EmailInvite>,
    ) {
        val orgId = context.orgId
        val senderIdentityId = context.identityId
        val senderOrgMemberId = context.orgMemberId()

        body.forEach {
            sendInviteEmail(
                orgId = orgId,
                senderIdentityId = senderIdentityId,
                senderOrgMemberId = senderOrgMemberId,
                inviteeOrgMemberId = it.teamMemberId.let(::MemberId).let { memberId ->
                    memberStore.getOrgMemberId(memberId = memberId)
                },
                email = it.email.let(EmailAddress::of),
            )
        }
    }

    override suspend fun dismissInvitee(context: RoutingContext, input: Resources.dismissInvitee) {
        val senderOrgMemberId = context.orgMemberId()
        val inviteeMemberId = input.teamMemberId.let(::MemberId)
        val inviteeOrgMemberId = memberStore.getOrgMemberId(memberId = inviteeMemberId)

        teamInviteStore.recordInviteDismissed(
            senderOrgMemberId = senderOrgMemberId,
            inviteeOrgMemberId = inviteeOrgMemberId,
        )
    }

    private suspend fun sendInviteEmail(
        orgId: OrgId,
        senderIdentityId: IdentityId,
        senderOrgMemberId: OrgMemberId,
        inviteeOrgMemberId: OrgMemberId,
        email: EmailAddress,
    ) {
        if (teamInviteStore.inviteExists(senderOrgMemberId = senderOrgMemberId, inviteeOrgMemberId = inviteeOrgMemberId)) {
            return
        }

        teamInviteStore.recordInviteSent(
            senderOrgMemberId = senderOrgMemberId,
            inviteeOrgMemberId = inviteeOrgMemberId,
        )

        notificationEventEnqueueService.enqueueEvent(
            NotificationOrgEvent.TeamInviteEmailEvent(
                recipients = setOf(email),
                orgId = orgId,
                senderIdentityId = senderIdentityId,
                trigger = EmailTrigger.API_CALL,
            ),
        )
    }
}
