package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.models.FileContext
import com.nextchaptersoftware.api.models.SemanticSearchFileRequest
import com.nextchaptersoftware.api.models.SemanticSearchFileResponse
import com.nextchaptersoftware.apiservice.test.utils.ApiAuthContext
import com.nextchaptersoftware.apiservice.test.utils.UnblockedApiClient
import com.nextchaptersoftware.compress.CompressionBase64
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.personId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.currentCoroutineContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class SearchApiDelegateImplTest : DatabaseTestsBase() {
    private lateinit var client: UnblockedApiClient
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var repo: RepoDAO
    private lateinit var identity: IdentityDAO
    private lateinit var teamMember: MemberDAO

    suspend fun setup() {
        org = ModelBuilders.makeOrg()
        scmTeam = ModelBuilders.makeScmTeam(org = org)
        identity = ModelBuilders.makeIdentity(person = makePerson())
        repo = ModelBuilders.makeRepo(scmTeam = scmTeam)
        teamMember = ModelBuilders.makeMember(scmTeam = scmTeam, identity = identity, isPrimaryMember = true)
        client = UnblockedApiClient(
            database = currentCoroutineContext().getDatabase(),
            authContext = ApiAuthContext.Authenticated(
                identityId = identity.id.value,
                personId = checkNotNull(identity.personId),
                orgIds = setOf(org.idValue),
            ),
        )
    }

    @Test
    fun semanticSearchFile() = suspendingDatabaseTest {
        setup()
        val semanticSearchFileRequest = SemanticSearchFileRequest(
            file = FileContext(
                repoId = repo.id.value.value,
                filePath = "testFile",
                isVisible = true,
                isActive = true,
                partialContent = CompressionBase64.compress("Who is Richard bresnan?").value,
            ),
        )
        client.semanticSearchFile(orgId = scmTeam.orgId, semanticSearchFileRequest = semanticSearchFileRequest) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val actual = body<SemanticSearchFileResponse>()
            assertThat(actual.references).isEmpty()
        }
    }
}
