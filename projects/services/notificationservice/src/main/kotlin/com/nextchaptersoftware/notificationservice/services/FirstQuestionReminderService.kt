package com.nextchaptersoftware.notificationservice.services

import com.nextchaptersoftware.db.models.EmailEventType
import com.nextchaptersoftware.db.stores.EmailEventStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant

class FirstQuestionReminderService(
    private val notificationEventEnqueueService: NotificationEventEnqueueService,
    private val personStore: PersonStore = Stores.personStore,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    private val emailEventStore: EmailEventStore = Stores.emailEventStore,
) {
    suspend fun enqueueFirstQuestionReminderEmails() {
        val now = Instant.nowWithMicrosecondPrecision()

        personStore.findCreatedBetween(
            from = now.minus(7.days),
            to = now.minus(2.days),
        ).mapNotNull { person ->
            val orgMembers = orgMemberStore.findByPersonUnsafe(personId = person.id)
            when (orgMembers.any { it.hasAskedAQuestion }) {
                true -> null
                else -> orgMembers.firstOrNull { !it.hasAskedAQuestion }
            }
        }.filterNot { orgMember ->
            emailEventStore.hasExistingEvents(
                personId = requireNotNull(orgMember.personId),
                emailEventTypes = listOf(EmailEventType.FirstQuestionReminder),
            )
        }.forEach {
            notificationEventEnqueueService.enqueueFirstQuestionReminder(
                orgId = it.orgId,
                orgMemberId = it.id,
            )
        }
    }
}
