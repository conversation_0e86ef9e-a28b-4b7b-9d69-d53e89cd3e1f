baseservice:
  serviceAccount:
    name: "queueservice"
  service:
    type: NodePort
    port: 80
    targetPort: 8081
  secretProviderClass:
    create: true
    objects: |-
      - objectName: "activemq-unblocked-password-2"
        objectType: "secretsmanager"
        objectAlias: "activemq-password"
  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: "app.kubernetes.io/name"
              operator: In
              values:
              - queueservice
          topologyKey: kubernetes.io/hostname
