package com.nextchaptersoftware.ingest.linear.jobs

import com.nextchaptersoftware.linear.ingestion.services.LinearTeamIngestionService
import com.nextchaptersoftware.service.BackgroundJob

class LinearTeamIngestionJob(
    private val linearTeamIngestionService: LinearTeamIngestionService,
) : BackgroundJob {
    override val name: String = javaClass.simpleName

    override suspend fun run() {
        linearTeamIngestionService.run()
    }
}
