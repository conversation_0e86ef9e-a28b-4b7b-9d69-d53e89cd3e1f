package com.nextchaptersoftware.partitioning.incremental

import com.nextchaptersoftware.db.models.EmbeddingModel
import com.nextchaptersoftware.db.models.EmbeddingPlatform
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.embedding.models.EmbeddingDocumentId
import com.nextchaptersoftware.embedding.models.EmbeddingDocumentKey
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.ml.embedding.core.store.EmbeddingListRequest
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import java.util.UUID

/**
 * This class is used to track the partitions that have been created, embedded and persisted for a given source document.
 * This is used to determine if a document has been updated since the last time it was processed.
 */
class StandardIncrementalPartitionTracking(
    private val embeddingStoreFacade: EmbeddingStoreFacade,
) : IncrementalPartitionTracking {

    companion object {
        /**
         * The maximum number of partitions to check when determining if a document has been updated.
         */
        const val MAX_PARTITIONS_TO_CHECK = 200
    }

    override suspend fun getIncrementalRecord(
        embeddingPlatform: EmbeddingPlatform,
        embeddingModel: EmbeddingModel,
        orgId: OrgId,
        installationId: InstallationId,
        groupId: UUID?,
        revision: Int?,
        sourceId: UUID,
        sourceHash: Hash,
    ): IncrementalPartitionTracking.Result {
        val sourceIdPrefix = EmbeddingDocumentKey(
            installationId = installationId,
            groupId = groupId,
            revision = revision,
            sourceId = sourceId,
            contentHash = Hash.empty(), // dont care
            partitionNumber = 0, // dont care
        ).sourceIdPrefix

        val storedItems = embeddingStoreFacade.listByPrefix(
            orgId = orgId,
            embeddingPlatform = embeddingPlatform,
            request = EmbeddingListRequest.EmbeddingListIdPrefixRequest(
                idPrefix = sourceIdPrefix,
                maxItems = MAX_PARTITIONS_TO_CHECK,
            ),
        ).nullIfEmpty() ?: return IncrementalPartitionTracking.Result.NotFound

        return when {
            storedItems.all { it.contentHash == sourceHash } -> IncrementalPartitionTracking.Result.NotChanged

            else -> IncrementalPartitionTracking.Result.Obsolete(
                embeddingModel = embeddingModel,
                documentPartitionIds = storedItems.map { EmbeddingDocumentId.Serverless(it) },
                sourceId = sourceId,
            )
        }
    }
}
