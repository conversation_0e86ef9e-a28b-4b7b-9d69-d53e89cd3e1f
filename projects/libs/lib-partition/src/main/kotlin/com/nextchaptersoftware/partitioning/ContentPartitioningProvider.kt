package com.nextchaptersoftware.partitioning

import com.nextchaptersoftware.config.EmbeddingConfig
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType

class ContentPartitioningProvider(
    private val embeddingConfig: EmbeddingConfig,
) {
    fun contentPartitioner(
        documentType: DocumentType,
        insightType: InsightType,
    ): ContentPartitioning {
        return when (documentType) {
            DocumentType.Documentation -> {
                return when (insightType) {
                    InsightType.Slack -> TruncatedContentPartitioning(embeddingConfig.maxPartitionSizeInBytes)

                    InsightType.Answer,
                    InsightType.Issue,
                    InsightType.PullRequest,
                    InsightType.Documentation,
                    InsightType.PullRequestComment,
                    InsightType.SourceCode,
                    InsightType.Discussion,
                        -> MarkdownContentPartitioning(embeddingConfig.maxPartitionSizeInBytes)
                }
            }

            DocumentType.PullRequest -> TruncatedContentPartitioning(embeddingConfig.maxPartitionSizeInBytes)

            DocumentType.Thread -> TruncatedContentPartitioning(embeddingConfig.maxPartitionSizeInBytes)

            DocumentType.Code -> NewlineContentPartitioning()
        }
    }
}
