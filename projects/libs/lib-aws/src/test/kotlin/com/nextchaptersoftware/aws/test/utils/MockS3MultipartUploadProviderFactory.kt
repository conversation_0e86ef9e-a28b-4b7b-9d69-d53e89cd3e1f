@file:Suppress("ktlint:nextchaptersoftware:no-nano-datetime")

package com.nextchaptersoftware.aws.test.utils

import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.s3.PresignedUrl
import com.nextchaptersoftware.aws.s3.S3CreateMultipartUploadResponse
import com.nextchaptersoftware.aws.s3.S3MultipartAbortConfig
import com.nextchaptersoftware.aws.s3.S3MultipartCompleteConfig
import com.nextchaptersoftware.aws.s3.S3MultipartUploadConfig
import com.nextchaptersoftware.aws.s3.S3MultipartUploadProvider
import com.nextchaptersoftware.aws.s3.S3MultipartUploadProviderFactory
import com.nextchaptersoftware.aws.s3.S3PresignedPart
import java.net.URI
import java.time.Instant
import java.util.UUID

class MockS3MultipartUploadProviderFactory : S3MultipartUploadProviderFactory {
    override fun generate(awsClientProvider: AWSClientProvider): S3MultipartUploadProvider {
        return object : S3MultipartUploadProvider {
            override suspend fun createMultipartUpload(
                s3MultipartUploadConfig: S3MultipartUploadConfig,
            ): S3CreateMultipartUploadResponse {
                return S3CreateMultipartUploadResponse(
                    uploadId = UUID.randomUUID().toString(),
                    presignedParts = listOf(
                        S3PresignedPart(
                            url = PresignedUrl(
                                url = URI.create("http://www.mahdiupload.com/part/1").toURL(),
                                expiryAt = Instant.now(),
                            ),
                            partNum = 1,
                        ),
                        S3PresignedPart(
                            url = PresignedUrl(
                                url = URI.create("http://www.mahdiupload.com/part/2").toURL(),
                                expiryAt = Instant.now(),
                            ),
                            partNum = 2,
                        ),
                    ),
                )
            }

            override fun completeMultipartUpload(s3MultipartCompleteConfig: S3MultipartCompleteConfig) = Unit

            override fun abortMultipartUpload(s3MultipartAbortConfig: S3MultipartAbortConfig) = Unit
        }
    }
}
