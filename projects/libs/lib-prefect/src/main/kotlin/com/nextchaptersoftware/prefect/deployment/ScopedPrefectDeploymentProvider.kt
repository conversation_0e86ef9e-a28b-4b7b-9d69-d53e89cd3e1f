package com.nextchaptersoftware.prefect.deployment

import com.nextchaptersoftware.prefect.api.models.Deployment
import com.nextchaptersoftware.prefect.api.models.FlowRun
import com.nextchaptersoftware.prefect.api.models.StateType
import kotlinx.serialization.json.JsonElement

interface ScopedPrefectDeploymentProvider {
    suspend fun getDeployment(): Deployment

    suspend fun listDeploymentFlowRuns(maxResults: Int): List<FlowRun>

    suspend fun trigger(
        parameters: JsonElement,
        labels: Map<String, String>?,
    ): FlowRun

    suspend fun countDeploymentFlowRuns(stateTypes: List<StateType>): Int
}

open class StandardScopedPrefectDeploymentProvider(
    private val flowName: String,
    private val deploymentName: String,
    private val prefectDeploymentProvider: PrefectDeploymentProvider,
) : ScopedPrefectDeploymentProvider {
    override suspend fun getDeployment(): Deployment {
        return prefectDeploymentProvider.getDeployment(
            flowName = flowName,
            deploymentName = deploymentName,
        )
    }

    override suspend fun listDeploymentFlowRuns(maxResults: Int): List<FlowRun> {
        return prefectDeploymentProvider.listDeploymentFlowRuns(
            flowName = flowName,
            deploymentName = deploymentName,
            maxResults = maxResults,
        )
    }

    override suspend fun trigger(
        parameters: JsonElement,
        labels: Map<String, String>?,
    ): FlowRun {
        return prefectDeploymentProvider.triggerDeployment(
            flowName = flowName,
            deploymentName = deploymentName,
            parameters = parameters,
            labels = labels,
        )
    }

    override suspend fun countDeploymentFlowRuns(stateTypes: List<StateType>): Int {
        return prefectDeploymentProvider.countDeploymentFlowRuns(
            flowName = flowName,
            deploymentName = deploymentName,
            stateTypes = stateTypes,
        )
    }
}
