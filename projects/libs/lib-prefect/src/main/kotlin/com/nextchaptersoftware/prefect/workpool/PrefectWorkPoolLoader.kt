package com.nextchaptersoftware.prefect.workpool

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.prefect.api.PrefectApiProvider
import com.nextchaptersoftware.prefect.api.models.WorkPoolCreateRequest
import com.nextchaptersoftware.prefect.api.models.WorkPoolFilter
import com.nextchaptersoftware.prefect.api.models.WorkPoolFilterName
import com.nextchaptersoftware.prefect.api.models.WorkPoolFilterRequest
import com.nextchaptersoftware.prefect.api.models.WorkPoolUpdateRequest
import com.nextchaptersoftware.prefect.config.WorkPoolConfig
import com.nextchaptersoftware.prefect.config.WorkPoolsConfig
import com.nextchaptersoftware.utils.ReflectionUtils
import com.nextchaptersoftware.utils.ResourceLoader
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class PrefectWorkPoolLoader(
    private val prefectApiProvider: PrefectApiProvider,
) {
    suspend fun loadPrefectWorkPools(workPoolsConfig: WorkPoolsConfig) {
        val workPoolConfigs = ReflectionUtils.extractFieldsOfType<WorkPoolConfig>(workPoolsConfig)
        for (workPoolConfig in workPoolConfigs) {
            runSuspendCatching {
                loadPrefectWorkPool(workPoolConfig)
            }.onFailure {
                LOGGER.errorAsync(it, "orkPoolConfig" to workPoolConfig) { "Failed to load prefect work pool" }
            }
        }
    }

    private suspend fun loadPrefectWorkPool(workPoolConfig: WorkPoolConfig) {
        val baseJobTemplate = ResourceLoader.loadResourceAsJsonElement(resourcePath = workPoolConfig.baseJobTemplateResource)
        val filterRequest = WorkPoolFilterRequest(
            workPools = WorkPoolFilter(
                name = WorkPoolFilterName(
                    names = listOf(workPoolConfig.name),
                ),
            ),
        )
        val workPoolCount = prefectApiProvider.prefectWorkPoolApi.countWorkPools(filterRequest)

        if (workPoolCount > 0) {
            val updateRequest = WorkPoolUpdateRequest(
                description = workPoolConfig.description,
                concurrencyLimit = workPoolConfig.concurrencyLimit,
                baseJobTemplate = baseJobTemplate,
            )
            prefectApiProvider.prefectWorkPoolApi.updateWorkPool(name = workPoolConfig.name, request = updateRequest)
        } else {
            val createRequest = WorkPoolCreateRequest(
                name = workPoolConfig.name,
                type = workPoolConfig.type,
                description = workPoolConfig.description,
                concurrencyLimit = workPoolConfig.concurrencyLimit,
                baseJobTemplate = baseJobTemplate,
            )
            prefectApiProvider.prefectWorkPoolApi.createWorkPool(request = createRequest)
        }
    }
}
