plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-google", "default"))
    implementation(project(":projects:clients:client-slack", "default"))
    implementation(project(":projects:libs:lib-api-generated", "default"))
    implementation(project(":projects:libs:lib-api-model", "default"))
    implementation(project(":projects:libs:lib-billing", "default"))
    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-document-events", "default"))
    implementation(project(":projects:libs:lib-google-events", "default"))
    implementation(project(":projects:libs:lib-jira-events", "default"))
    implementation(project(":projects:libs:lib-slack", "default"))
    implementation(project(":projects:libs:lib-slack-bot-events", "default"))
    implementation(project(":projects:libs:lib-slack-events", "default"))
    implementation(project(":projects:libs:lib-web-ingestion", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(testLibs.bundles.test.postgresql)
    testImplementation(testLibs.bundles.test.core)

    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:models", "test"))
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
