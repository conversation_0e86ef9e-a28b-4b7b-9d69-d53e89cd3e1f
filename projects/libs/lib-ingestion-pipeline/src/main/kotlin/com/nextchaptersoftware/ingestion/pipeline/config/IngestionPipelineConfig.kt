package com.nextchaptersoftware.ingestion.pipeline.config

import com.nextchaptersoftware.config.AWSBucketConfig
import com.nextchaptersoftware.config.ConfigLoader
import kotlinx.serialization.Serializable

@Serializable
data class IngestionPipelineProfileConfig(
    val s3: AWSBucketConfig,
)

@Serializable
data class IngestionPipelineConfig(
    val ingestionPipeline: IngestionPipelineProfileConfig,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<IngestionPipelineConfig>("ingestion-pipeline")
    }
}
