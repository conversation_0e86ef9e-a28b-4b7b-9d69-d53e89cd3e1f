@file:Suppress("MaxLineLength")

package com.nextchaptersoftware.semantic.bot.services

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.test.mokito.MockitoExtensions.eqValue
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock

class NotifyBotServiceTest {
    private val orgId = OrgId.random()
    private val installationId = InstallationId.random()
    private val threadId = ThreadId.random()
    private val userMember = MockDataClasses.member(installationId = installationId)
    private val otherUserMember = MockDataClasses.member(installationId = installationId)
    private val deprecatedBotMember = MockDataClasses.member(installationId = installationId)
    private val botMember = MockDataClasses.member(installationId = installationId)
    private val botMembers = listOf(botMember, deprecatedBotMember)
    private val thread = MockDataClasses.thread(orgId = orgId, id = threadId)
    private val query = MockDataClasses.message(threadId = threadId)
    private val humanParticipant = MockDataClasses.threadParticipant(threadId = threadId, memberId = userMember.id)
    private val deprecatedBotParticipant = MockDataClasses.threadParticipant(threadId = threadId, memberId = deprecatedBotMember.id)
    private val botParticipant = MockDataClasses.threadParticipant(threadId = threadId, memberId = botMember.id)
    private val otherHumanParticipant = MockDataClasses.threadParticipant(threadId = threadId, memberId = otherUserMember.id)

    private val messageService = mock<MessageService>()
    private val participantService = mock<ThreadParticipantService>()
    private val service = NotifyBotService(
        messageService = messageService,
        searchPriorityEventEnqueueService = mock(),
        botAccountService = mock(),
        participantService = participantService,
        billingEventEnqueueService = mock(),
        urlBuilderProvider = StandardUrlBuilderProvider(),
        dataSourcePresetConfigurationService = mock(),
    )

    @Test
    fun `shouldNotifyBot returns false when bot is not in thread and bot is not mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, otherHumanParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }

    @Test
    fun `shouldNotifyBot returns false when bot is in thread, bot is not mentioned, but another participant is mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(
                humanParticipant,
                otherHumanParticipant,
                botParticipant,
            ),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(userMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }

    @Test
    fun `shouldNotifyBot returns true when bot is in thread, bot is mentioned, and another participant is mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(
                humanParticipant,
                otherHumanParticipant,
                botParticipant,
            ),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(userMember.id, botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns true when deprecated bot is in thread, bot is mentioned, and another participant is mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(
                humanParticipant,
                otherHumanParticipant,
                deprecatedBotParticipant,
            ),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(userMember.id, deprecatedBotMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns true when deprecated bot is in thread with one other participant and mentions are empty`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, deprecatedBotParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns true when bot is in thread with one participant and mentions are empty`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, botParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns maybe when bot is in thread with many other participants and mentions are empty`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(
                humanParticipant,
                otherHumanParticipant,
                botParticipant,
            ),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.Maybe)
    }

    @Test
    fun `shouldNotifyBot returns true when bot is not in thread but is mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, otherHumanParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns true when deprecated bot is not in thread but is mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, otherHumanParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(deprecatedBotMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns false if a bot response exists`() = runTest {
        val botResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id)

        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, botParticipant))

        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.No)

        // Should return true if all bot responses are older than the query
        val followUpQuery = MockDataClasses.message(threadId = threadId)
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse, followUpQuery))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = followUpQuery,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)

        val followUpBotResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id)
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse, followUpQuery, followUpBotResponse))
        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = followUpQuery,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }

    @Test
    fun `shouldNotifyBot returns No when bot is mentioned but has already replied after the message`() = runTest {
        val botResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id)
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, botParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }

    @Test
    fun `shouldNotifyBot returns No when bot has already replied after the message in a one-on-one conversation`() = runTest {
        val botResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id)
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, botParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }

    @Test
    fun `shouldNotifyBot returns No when bot is not in thread but is mentioned and has already replied after the message`() = runTest {
        val botResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id)
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(listOf(humanParticipant, otherHumanParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }

    @Test
    fun `shouldNotifyBot returns Maybe when bot has previously replied before the message in a group conversation`() = runTest {
        val botResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id)
        val newQuery = MockDataClasses.message(threadId = threadId)
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId)))
            .thenReturn(listOf(humanParticipant, otherHumanParticipant, botParticipant))
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(botResponse, newQuery))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = newQuery,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.Maybe)
    }

    @Test
    fun `shouldNotifyBot returns Maybe when multiple bots are in thread and none are mentioned`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(humanParticipant, otherHumanParticipant, botParticipant, deprecatedBotParticipant),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.Maybe)
    }

    @Test
    fun `shouldNotifyBot returns Yes when one of multiple bots is mentioned but none are in thread`() = runTest {
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(humanParticipant, otherHumanParticipant),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(botMember.id),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns Yes when bot reply has same timestamp as message`() = runTest {
        val botResponse = MockDataClasses.message(threadId = threadId, authorId = botMember.id, createdAt = query.createdAt)
        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(humanParticipant, botParticipant),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query, botResponse))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = emptyList(),
            ),
        ).isEqualTo(BotNotificationDecision.Yes)
    }

    @Test
    fun `shouldNotifyBot returns No when human mentions another participant`() = runTest {
        val memberNotInThread = MockDataClasses.member(installationId = installationId)

        `when`(participantService.getParticipantsForThread(anyOrNull(), eqValue(threadId))).thenReturn(
            listOf(humanParticipant, otherHumanParticipant, botParticipant),
        )
        `when`(messageService.getMessages(eqValue(threadId), anyOrNull())).thenReturn(listOf(query))

        assertThat(
            service.shouldNotifyBot(
                thread = thread,
                message = query,
                botMembers = botMembers,
                mentions = listOf(memberNotInThread.id),
            ),
        ).isEqualTo(BotNotificationDecision.No)
    }
}
