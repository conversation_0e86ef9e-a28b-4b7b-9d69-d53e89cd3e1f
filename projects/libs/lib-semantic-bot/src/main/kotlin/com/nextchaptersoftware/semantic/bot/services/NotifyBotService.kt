package com.nextchaptersoftware.semantic.bot.services

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.bot.services.DeprecatedBotAccountService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationService
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.Message
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.MessageSuggestionId
import com.nextchaptersoftware.db.models.OrgBillingSeatAssignedReason
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.events.queue.payloads.SearchPriorityEvent
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Instant

enum class BotNotificationDecision {
    Yes,
    No,
    Maybe,
}

class NotifyBotService(
    private val messageService: MessageService,
    private val searchPriorityEventEnqueueService: SearchPriorityEventEnqueueService,
    private val urlBuilderProvider: UrlBuilderProvider,
    private val deprecatedBotAccountService: DeprecatedBotAccountService = DeprecatedBotAccountService(urlBuilderProvider = urlBuilderProvider),
    private val botAccountService: BotAccountService = InstallationBotAccountService(urlBuilderProvider = urlBuilderProvider),
    private val dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
    private val billingEventEnqueueService: BillingEventEnqueueService,
    private val participantService: ThreadParticipantService,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
) {

    suspend fun maybeNotifyBot(
        personId: PersonId,
        threadAndRelatedModels: ThreadAndRelatedModels,
        mentions: List<MemberId>?,
        productAgent: ProductAgentType,
    ) {
        if (threadAndRelatedModels.isCreated) {
            val botMember = botAccountService.upsertBotAccountMember(orgId = threadAndRelatedModels.org.id)
            val botMembers = listOf(
                deprecatedBotAccountService.upsertBotAccountMember(orgId = threadAndRelatedModels.org.id),
                botMember,
            ).distinctBy { it.id }

            val botNotificationDecision = shouldNotifyBot(
                thread = threadAndRelatedModels.thread,
                message = checkNotNull(threadAndRelatedModels.message),
                botMembers = botMembers,
                mentions = mentions.orEmpty(),
            )

            when (botNotificationDecision) {
                BotNotificationDecision.Yes -> {
                    notifyBot(
                        orgId = threadAndRelatedModels.org.id,
                        thread = threadAndRelatedModels.thread,
                        message = threadAndRelatedModels.message,
                        humanPersonId = personId,
                        isSuggestion = false,
                        suggestionId = null,
                        productAgent = productAgent,
                    )
                }

                BotNotificationDecision.Maybe -> {
                    enqueueMaybeBotNotificationEvent(
                        orgId = threadAndRelatedModels.org.id,
                        thread = threadAndRelatedModels.thread,
                        message = threadAndRelatedModels.message,
                        humanPersonId = personId,
                        isSuggestion = false,
                        suggestionId = null,
                        productAgent = productAgent,
                    )
                }

                BotNotificationDecision.No -> {
                    Unit
                }
            }
        }
    }

    suspend fun maybeNotifyBot(
        orgId: OrgId,
        personId: PersonId,
        messageAndRelatedModels: MessageAndRelatedModels,
        mentions: List<MemberId>?,
        isSuggestion: Boolean,
        suggestionId: MessageSuggestionId?,
        productAgent: ProductAgentType,
    ) {
        val botMember = botAccountService.upsertBotAccountMember(orgId = orgId)
        val botMembers = listOf(
            deprecatedBotAccountService.upsertBotAccountMember(orgId = orgId),
            botMember,
        ).distinctBy { it.id }

        val botNotificationDecision = shouldNotifyBot(
            thread = messageAndRelatedModels.thread,
            message = messageAndRelatedModels.message,
            botMembers = botMembers,
            mentions = mentions.orEmpty(),
        )

        when (botNotificationDecision) {
            BotNotificationDecision.Yes -> {
                notifyBot(
                    orgId = orgId,
                    thread = messageAndRelatedModels.thread,
                    message = messageAndRelatedModels.message,
                    humanPersonId = personId,
                    isSuggestion = suggestionId != null || isSuggestion,
                    suggestionId = suggestionId,
                    productAgent = productAgent,
                )
            }

            BotNotificationDecision.Maybe -> {
                enqueueMaybeBotNotificationEvent(
                    orgId = orgId,
                    thread = messageAndRelatedModels.thread,
                    message = messageAndRelatedModels.message,
                    humanPersonId = personId,
                    isSuggestion = suggestionId != null || isSuggestion,
                    suggestionId = suggestionId,
                    productAgent = productAgent,
                )
            }

            BotNotificationDecision.No -> {
                Unit
            }
        }
    }

    internal suspend fun shouldNotifyBot(
        thread: Thread,
        message: Message,
        botMembers: List<Member>,
        mentions: List<MemberId>,
    ): BotNotificationDecision {
        val participants = participantService.getParticipantsForThread(
            threadId = thread.id,
        )

        val humanParticipants = participants.filter { participant ->
            botMembers.none { botMember ->
                participant.memberId == botMember.id
            }
        }

        val botIsInThread = participants.any {
            botMembers.any { botMember ->
                it.memberId == botMember.id
            }
        }

        // We need to check whether the Bot's MemberModel.id is in the list of mentions too because legacy clients
        // have this value cached and use it to mention the bot. We can remove late once all clients have the bot
        // org member (or when Rashin applies his usual magic)
        val botIsMentioned = mentions.map { it.value }
            .any { mention ->
                botMembers.any { botMember ->
                    botMember.orgMemberId.value == mention || botMember.id.value == mention
                }
            }

        val humanMentions = mentions.map { it.value }
            .filterNot { mention ->
                botMembers.any { botMember ->
                    botMember.orgMemberId.value == mention || botMember.id.value == mention
                }
            }

        val messages = messageService.getMessages(
            threadId = thread.id,
        )

        val botHasAlreadyReplied = messages.any { otherMessage ->
            botMembers.any { botMember ->
                otherMessage.authorId == botMember.id
            } && otherMessage.createdAt > message.createdAt
        }

        return when {
            // check for race condition
            botHasAlreadyReplied -> return BotNotificationDecision.No

            // bot is explicitly mentioned
            botIsMentioned -> return BotNotificationDecision.Yes

            // No notification if bot is not in thread and not mentioned
            !botIsInThread -> return BotNotificationDecision.No

            // Notify in 1-on-1 conversations (bot and one human) and there are no newer bot messages
            humanParticipants.size == 1 -> return BotNotificationDecision.Yes

            // Other human was mentioned
            humanMentions.isNotEmpty() -> BotNotificationDecision.No

            // Group conversation with no mentions and no newer bot messages
            else -> BotNotificationDecision.Maybe
        }
    }

    suspend fun createBotMessageAndRunRelatedTasks(
        orgId: OrgId,
        thread: Thread,
        questionerOrgMemberId: OrgMemberId,
    ): MessageAndRelatedModels {
        val botMember = botAccountService.upsertBotAccountMember(orgId = orgId)

        val dataSourcePreset = dataSourcePresetConfigurationService.configurationForOrgMember(orgId = orgId, orgMemberId = questionerOrgMemberId)

        val botMessage = this.messageService.createMessage(
            messageId = MessageId.random(),
            orgId = orgId,
            author = botMember,
            threadId = thread.id,
            content = "".asMessageBody().toByteArray(),
            userLocalContext = null,
            contentVersion = "1",
            mentions = null,
            isLoading = true,
            dataSourcePreset = dataSourcePreset.asDataSourcePresetArtifact(),
        )

        billingEventEnqueueService.enqueueSeatAssigmentRequestEvent(
            orgId = orgId,
            orgMemberId = questionerOrgMemberId,
            reason = OrgBillingSeatAssignedReason.AskedQuestion,
        )

        val isPrivate = thread.isPrivate == true
        orgMemberStore.incrementQuestionsAsked(orgMemberId = questionerOrgMemberId, isPrivate = isPrivate, createdAt = botMessage.message.createdAt)

        return botMessage
    }

    suspend fun notifyBot(
        orgId: OrgId,
        thread: Thread,
        message: Message,
        humanPersonId: PersonId,
        isSuggestion: Boolean,
        suggestionId: MessageSuggestionId?,
        productAgent: ProductAgentType,
    ) {
        val botMessage = createBotMessageAndRunRelatedTasks(
            orgId = orgId,
            thread = thread,
            questionerOrgMemberId = message.authorOrgMemberId,
        )

        searchPriorityEventEnqueueService.enqueueEvent(
            event = SearchPriorityEvent.BotQuestion(
                orgId = orgId,
                threadId = thread.id,
                humanMessageId = message.id,
                humanPersonId = humanPersonId,
                humanOrgMemberId = checkNotNull(message.authorOrgMemberId),
                botMessageId = botMessage.message.id,
                botOrgMemberId = botMessage.message.authorOrgMemberId,
                enqueuedAt = Instant.nowWithMicrosecondPrecision(),
                isSuggestion = suggestionId != null || isSuggestion,
                suggestionId = suggestionId,
                productAgent = productAgent,
            ),
            priority = MessagePriority.HIGH,
        )
    }

    private fun enqueueMaybeBotNotificationEvent(
        orgId: OrgId,
        thread: Thread,
        message: Message,
        humanPersonId: PersonId,
        isSuggestion: Boolean,
        suggestionId: MessageSuggestionId?,
        productAgent: ProductAgentType,
    ) {
        searchPriorityEventEnqueueService.enqueueEvent(
            event = SearchPriorityEvent.MaybeBotQuestion(
                orgId = orgId,
                enqueuedAt = Instant.nowWithMicrosecondPrecision(),
                threadId = thread.id,
                humanMessageId = message.id,
                humanPersonId = humanPersonId,
                humanOrgMemberId = checkNotNull(message.authorOrgMemberId),
                isSuggestion = suggestionId != null || isSuggestion,
                suggestionId = suggestionId,
                productAgent = productAgent,
            ),
            priority = MessagePriority.HIGH,
        )
    }
}
