@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.semantic.bot.services

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.MessageMentionStore
import com.nextchaptersoftware.db.stores.Stores
import org.jetbrains.exposed.sql.Transaction

class MessageMentionService(
    private val messageMentionStore: MessageMentionStore = Stores.messageMentionStore,
    private val memberStore: MemberStore = Stores.memberStore,
) {
    suspend fun getMessageMentions(
        trx: Transaction? = null,
        messageIds: List<MessageId>,
    ) = Database.suspendedTransaction(trx) {
        messageMentionStore.findForMessages(trx = this, messageIds = messageIds).map { it.asDataModel() }
    }

    suspend fun upsertMessageMentionsByIds(
        trx: Transaction? = null,
        message: MessageDAO,
        mentionedMemberIds: List<MemberId>,
    ) = Database.suspendedTransaction(trx) {
        val mentionedMembers = memberStore.find(trx = this, memberIds = mentionedMemberIds)

        upsertMessageMentions(trx = this, message = message, mentionedMembers = mentionedMembers)
    }

    suspend fun upsertMessageMentions(
        trx: Transaction? = null,
        message: MessageDAO,
        mentionedMembers: List<Member>,
    ) = Database.suspendedTransaction(trx) {
        val existingMessageMentions = messageMentionStore.findForMessages(
            trx = this,
            messageIds = listOf(message.idValue),
        )

        val updateMemberIdSet = mentionedMembers.map { it.id }.toSet()
        val existingMemberIdSet = existingMessageMentions.mapNotNull { it.member?.idValue }.toSet()

        val toDeleteMemberIds = existingMemberIdSet.subtract(updateMemberIdSet)
        val toCreateMemberIds = updateMemberIdSet.subtract(existingMemberIdSet)

        val toCreateMembers = mentionedMembers.filter { toCreateMemberIds.contains(it.id) }

        messageMentionStore.createMentions(trx = this, thread = message.thread, message = message, mentions = toCreateMembers)
        messageMentionStore.deleteMentionsByMembers(trx = this, mentions = toDeleteMemberIds.toList())
    }
}
