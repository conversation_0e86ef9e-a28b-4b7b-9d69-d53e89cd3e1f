package com.nextchaptersoftware.notion.ingestion.redis.cache

import com.nextchaptersoftware.cache.RedisOperationException
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.redis.RedisExtensions.delKeys
import io.lettuce.core.SetArgs
import io.lettuce.core.cluster.api.coroutines.RedisClusterCoroutinesCommands
import java.util.UUID
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class RedisNotionIngestionCache(
    private val redis: RedisClusterCoroutinesCommands<String, String> = Redis.API,
    defaultExpiry: Duration = 7.days,
) : NotionIngestionCache {
    private val setArgs = SetArgs().ex(defaultExpiry.inWholeSeconds)

    private fun prefix(orgId: OrgId, installationId: InstallationId): String {
        return "notion-ingestion:$orgId:$installationId"
    }

    private fun getKey(orgId: OrgId, installationId: InstallationId, pageId: UUID): String {
        return "${prefix(orgId = orgId, installationId = installationId)}:$pageId"
    }

    override suspend fun set(orgId: OrgId, installationId: InstallationId, pageId: UUID, lastEditedTime: Instant) {
        val key = getKey(orgId = orgId, installationId = installationId, pageId = pageId)
        val value = lastEditedTime.toEpochMilliseconds().toString()
        redis.set(key = key, value = value, setArgs = setArgs).also {
            if (it != "OK") {
                LOGGER.errorAsync(
                    "orgId" to orgId,
                    "installationId" to installationId,
                    "pageId" to pageId,
                    "lastEditedTime" to lastEditedTime,
                    "value" to value,
                ) {
                    "Redis operation failed"
                }
                throw RedisOperationException("$orgId: set failed")
            }
        }
    }

    override suspend fun get(orgId: OrgId, installationId: InstallationId, pageId: UUID): Instant? {
        val key = getKey(orgId = orgId, installationId = installationId, pageId = pageId)
        val value = redis.get(key)
        return value?.let { Instant.fromEpochMilliseconds(it.toLong()) }
    }

    override suspend fun delete(orgId: OrgId, installationId: InstallationId, pageId: UUID) {
        val key = getKey(orgId = orgId, installationId = installationId, pageId = pageId)
        redis.del(key)
    }

    override suspend fun clear(orgId: OrgId, installationId: InstallationId) {
        redis.delKeys(prefix = prefix(orgId = orgId, installationId = installationId))
    }
}
