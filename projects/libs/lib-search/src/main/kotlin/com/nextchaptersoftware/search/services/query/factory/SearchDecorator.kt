package com.nextchaptersoftware.search.services.query.factory

import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.models.TopicId
import com.nextchaptersoftware.db.stores.PullRequestBundleStore
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.SearchInsightResult
import com.nextchaptersoftware.db.stores.SearchInsightStore
import com.nextchaptersoftware.db.stores.SlackChannelStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.memberStore
import com.nextchaptersoftware.db.stores.ThreadBundleDecorator
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.insight.index.model.CodeInsightIndexContentModel
import com.nextchaptersoftware.insight.index.model.DocumentationInsightIndexContentModel
import com.nextchaptersoftware.insight.index.model.InsightIndexContentModel
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.search.services.query.filter.SearchResultsFilter
import com.nextchaptersoftware.search.services.query.models.CodeSearchResult
import com.nextchaptersoftware.search.services.query.models.DocumentSearchResult
import com.nextchaptersoftware.search.services.query.models.PullRequestSearchResult
import com.nextchaptersoftware.search.services.query.models.SearchResult
import com.nextchaptersoftware.search.services.query.models.ThreadSearchResult
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.StringUuidExtensions
import io.pinecone.proto.ScoredVector
import java.util.UUID
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

/**
 * This class makes no sense:
 *  - It's transformed semantic search results from Pinecone into API search results [SearchResult],
 *    which in turn are transformed downstream to [MLTypedDocument], but the intermediate transformation to an API model is weird.
 *
 * Instead, this class should be responsible for transforming [ScoredVector]s directly into [MLTypedDocument]s,
 * without transforming into intermediate [SearchInsightResult]s, [InsightIndexContentModel]s, or [SearchResult]s.
 */
class SearchDecorator(
    private val searchResultsFilter: SearchResultsFilter,
    private val threadInsightIndexContentService: ThreadInsightIndexContentService,
    private val prInsightIndexContentService: PullRequestInsightIndexContentService,
    private val documentInsightContentService: DocumentInsightContentService,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val repoStore: RepoStore = Stores.repoStore,
    private val slackChannelStore: SlackChannelStore = Stores.slackChannelStore,
    private val botAccountService: BotAccountService?,
) {

    @Suppress("LongMethod", "CyclomaticComplexMethod")
    suspend fun decorateResults(
        orgId: OrgId,
        searchInsightResults: List<SearchInsightResult>,
        orgMemberId: OrgMemberId?,
        minimal: Boolean,
    ): List<SearchResult> {
        val teamIds = scmTeamStore.listTeamIdsForOrg(orgId = orgId)

        val repoNameCache = repoStore.listActiveRepoIdsToFullName(teamIds)

        val repoInstallationCache = repoStore.listActiveRepoIdsToInstallationId(teamIds)

        val memberNameCache = mutableMapOf<MemberId, String?>()

        val searchResults = searchInsightResults.groupBy { it.documentType }.flatMap { (documentType, documents) ->
            when (documentType) {
                DocumentType.PullRequest -> documents.getAsPullRequestSearchResult(
                    minimal = minimal,
                ).also { prResults ->
                    memberNameCache.putAll(
                        memberStore.getMemberDisplayNames(
                            orgId = orgId,
                            memberIds = prResults.map { it.pullRequest.creatorId },
                        ),
                    )
                }

                DocumentType.Thread -> documents.asThreadSearchResult(
                    orgMemberId = orgMemberId,
                    botMemberIds = botAccountService?.getBotAccountMembersIds(orgId = orgId) ?: emptySet(),
                    minimal = minimal,
                ).onEach {
                    it.threadInfo.authorDisplayNames?.let { authorDisplayNames ->
                        memberNameCache.putAll(authorDisplayNames)
                    }
                }

                // FIXME richie remove all of these from this file, as has nothing to do with PG
                DocumentType.Code -> documents.asCodeSearchResult

                DocumentType.Documentation -> documentInsightContentService.documentSearchResultsFrom(documents = documents)
            }
        }

        val slackChannelIds = buildSet {
            searchResults.filterIsInstance<ThreadSearchResult>()
                .mapNotNull { it.threadInfo.thread.slackChannelId }
                .forEach { add(it) }

            searchResults.filterIsInstance<DocumentSearchResult>()
                .filter { it.provider == Provider.Slack }
                .mapNotNull { it.groupId?.let(::SlackChannelId) }
                .forEach { add(it) }
        }

        val slackChannelNameCache = slackChannelStore.getChannelNamesMap(ids = slackChannelIds)

        return searchResultsFilter.filter(
            orgId = orgId,
            orgMemberId = orgMemberId,
            searchResults = searchResults,
        ).mapNotNull { searchResult ->
            when (searchResult) {
                is ThreadSearchResult -> {
                    threadInsightIndexContentService.getThreadInsightContent(
                        threadInfo = searchResult.threadInfo,
                        asMarkdown = true,
                        repoNameCache = repoNameCache,
                        slackChannelNameCache = slackChannelNameCache,
                    )?.let {
                        ThreadSearchResult(
                            threadInfo = searchResult.threadInfo,
                            insightIndexContentModel = it,
                            score = searchResult.score,
                            documentId = searchResult.documentId,
                            searchDocumentId = searchResult.searchDocumentId,
                            provider = searchResult.provider,
                            isPrivate = searchResult.isPrivate,
                        )
                    }
                }

                is PullRequestSearchResult -> {
                    prInsightIndexContentService.getPullRequestInsightContent(
                        orgId = orgId,
                        pullRequest = searchResult.pullRequest,
                        pullRequestProvider = searchResult.provider,
                        asMarkdown = true,
                        minimal = true,
                        repoInstallationCache = repoInstallationCache,
                        repoNameCache = repoNameCache,
                        memberNameCache = memberNameCache,
                    )?.let {
                        PullRequestSearchResult(
                            pullRequest = searchResult.pullRequest,
                            commentCount = searchResult.commentCount,
                            hasSlackThreads = searchResult.hasSlackThreads,
                            topicIds = searchResult.topicIds,
                            participantOrgMemberIds = searchResult.participantOrgMemberIds,
                            insightIndexContentModel = it,
                            score = searchResult.score,
                            documentId = searchResult.documentId,
                            searchDocumentId = searchResult.searchDocumentId,
                            provider = searchResult.provider,
                        )
                    }
                }

                is CodeSearchResult -> {
                    if (searchResult.repoId !in repoNameCache) { // TODO Move this to SearchResultsFilter
                        LOGGER.warnAsync("repoId" to searchResult.repoId) { "Skipping search result for repo because it is not active" }
                        return@mapNotNull null
                    }
                    val insightIndexContentModel = CodeInsightIndexContentModel(
                        id = StringUuidExtensions.uuidFromFile(searchResult.repoId.value, searchResult.filePath),
                        orgId = orgId,
                        encodedFileContent = searchResult.encodedFileContent,
                        filePath = searchResult.filePath,
                        insightType = InsightType.SourceCode,
                        installationId = searchResult.installationId,
                        groupId = searchResult.repoId.value,
                        provider = searchResult.provider,
                        documentId = searchResult.documentId,
                    )
                    CodeSearchResult(
                        insightIndexContentModel = insightIndexContentModel,
                        filePath = searchResult.filePath,
                        score = searchResult.score,
                        encodedFileContent = searchResult.encodedFileContent,
                        installationId = searchResult.installationId,
                        repoId = searchResult.repoId,
                        documentId = searchResult.documentId,
                        searchDocumentId = searchResult.searchDocumentId,
                        provider = searchResult.provider,
                    )
                }

                is DocumentSearchResult -> {
                    val insightIndexContentModel = DocumentationInsightIndexContentModel(
                        id = UUID.randomUUID(),
                        orgId = orgId,
                        insightType = searchResult.insightType,
                        content = searchResult.content,
                        supplementaryContent = searchResult.supplementaryContent,
                        installationId = searchResult.installationId.required(),
                        groupId = searchResult.groupId,
                        documentId = searchResult.documentId,
                        title = searchResult.title,
                        provider = searchResult.provider,
                        createdAt = searchResult.createdAt,
                        slackChannelName = searchResult.groupId?.let(::SlackChannelId).let { slackChannelNameCache[it] },
                    )
                    DocumentSearchResult(
                        insightIndexContentModel = insightIndexContentModel,
                        score = searchResult.score,
                        documentId = searchResult.documentId,
                        insightType = searchResult.insightType,
                        installationId = searchResult.installationId,
                        groupId = searchResult.groupId,
                        content = searchResult.content,
                        supplementaryContent = searchResult.supplementaryContent,
                        title = searchResult.title,
                        externalUrl = searchResult.externalUrl,
                        provider = searchResult.provider,
                        sourceDocumentId = searchResult.sourceDocumentId,
                        searchDocumentId = searchResult.searchDocumentId,
                        createdAt = searchResult.createdAt,
                        sourceIdPrefix = searchResult.sourceIdPrefix,
                        isPrivate = searchResult.isPrivate,
                    )
                }
            }
        }
    }
}

// FIXME richie remove from this file, as has nothing to do with PG
private val List<SearchInsightResult>.asCodeSearchResult: List<CodeSearchResult>
    get() = map { searchInsightResult: SearchInsightResult ->
        CodeSearchResult(
            score = searchInsightResult.score,
            encodedFileContent = requireNotNull(searchInsightResult.encodedContent),
            filePath = requireNotNull(searchInsightResult.filePath),
            installationId = requireNotNull(searchInsightResult.installationId),
            repoId = requireNotNull(searchInsightResult.documentGroupId).let(::RepoId),
            documentId = requireNotNull(searchInsightResult.documentId),
            provider = searchInsightResult.provider,
            searchDocumentId = searchInsightResult.documentId,
        )
    }

private suspend fun List<SearchInsightResult>.getAsPullRequestSearchResult(
    searchInsightStore: SearchInsightStore = Stores.searchInsightStore,
    pullRequestBundleStore: PullRequestBundleStore = Stores.pullRequestBundleStore,
    minimal: Boolean,
): List<PullRequestSearchResult> {
    val prs = map { it.insight }.map(::PullRequestId).let { ids ->
        when (ids.isNotEmpty()) {
            true -> suspendedTransaction {
                val results = PullRequestDAO.find { PullRequestModel.id inList ids }
                    .map { it.asDataModel() }
                    .associateBy { it.id }

                if (!minimal) {
                    ids.filterNot { results.containsKey(it) }.forEach {
                        searchInsightStore.deleteSearchInsightModel(modelId = it.value)
                    }
                }

                results
            }

            else -> emptyMap()
        }
    }

    var commentCounts = emptyMap<PullRequestId, Int>()
    var hasSlackThreads = emptyMap<PullRequestId, Boolean>()
    var topicIds = emptyMap<UUID, List<TopicId>>()
    var participantIds = emptyMap<PullRequestId, List<OrgMemberId>>()

    if (!minimal) {
        val prIds = prs.keys.toList()
        commentCounts = pullRequestBundleStore.getCommentCounts(pullRequestIds = prIds)
        hasSlackThreads = pullRequestBundleStore.getHasSlackThreads(pullRequestIds = prIds)
        topicIds = ThreadBundleDecorator.getTopicIds(insightIds = prIds.map { it.value })
        participantIds = pullRequestBundleStore.getParticipantIds(pullRequests = prs.values)
    }

    return mapNotNull { searchInsightResult ->
        val prId = searchInsightResult.insight.let(::PullRequestId)
        prs[prId]?.let { pullRequest ->
            PullRequestSearchResult(
                pullRequest = pullRequest,
                commentCount = commentCounts[pullRequest.id] ?: 0,
                hasSlackThreads = hasSlackThreads[pullRequest.id] ?: false,
                topicIds = topicIds[pullRequest.id.value].orEmpty(),
                participantOrgMemberIds = participantIds[pullRequest.id].orEmpty(),
                score = searchInsightResult.score,
                documentId = pullRequest.id.toString(),
                searchDocumentId = searchInsightResult.documentId,
                provider = searchInsightResult.provider,
            )
        }
    }
}

private suspend fun List<SearchInsightResult>.asThreadSearchResult(
    searchInsightStore: SearchInsightStore = Stores.searchInsightStore,
    orgMemberId: OrgMemberId?,
    botMemberIds: Set<MemberId>,
    minimal: Boolean,
): List<ThreadSearchResult> {
    val threadInfos = map { it.insight.let(::ThreadId) }.let { ids ->
        when (ids.isNotEmpty()) {
            true -> runSuspendCatching {
                suspendedTransaction {
                    ThreadBundleDecorator.decorateThreads(
                        trx = this,
                        orgMemberId = orgMemberId,
                        threadIds = ids,
                        minimal = minimal,
                        filteredBotMemberIds = botMemberIds,
                    ).associateBy { it.thread.id }
                }
            }.getOrElse { // Thread no longer exists; clean up and attempt to recover
                val threadIds = suspendedTransaction {
                    ThreadModel
                        .select(ThreadModel.id)
                        .where { ThreadModel.id inList ids }
                        .map { it[ThreadModel.id].value }
                }

                ids.filterNot { threadIds.contains(it) }.forEach {
                    searchInsightStore.deleteSearchInsightModel(modelId = it.value)
                }

                suspendedTransaction {
                    ThreadBundleDecorator.decorateThreads(
                        trx = this,
                        orgMemberId = orgMemberId,
                        threadIds = threadIds,
                        minimal = minimal,
                        filteredBotMemberIds = botMemberIds,
                    ).associateBy { it.thread.id }
                }
            }

            else -> emptyMap()
        }
    }

    return mapNotNull { searchInsightResult ->
        threadInfos[searchInsightResult.insight.let(::ThreadId)]?.let { threadInfo ->
            ThreadSearchResult(
                threadInfo = threadInfo,
                score = searchInsightResult.score,
                documentId = threadInfo.thread.id.toString(),
                searchDocumentId = searchInsightResult.documentId,
                provider = searchInsightResult.provider,
                isPrivate = searchInsightResult.isPrivate,
            )
        }
    }
}
