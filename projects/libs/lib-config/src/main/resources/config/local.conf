diagnostics {
    enableCoroutineDump: true
}

database {
    hostName: localhost
    hostName: ${?DATABASE_HOSTNAME}
    dbName: maindb
    userName: demo
    password: demo
    region: local
    connectionPool: {
        maxTotal: 20
        minIdle: 3
        maxIdle: 5
        initialSize: 10
        maxConnLifetime: 1h
    }
    schemaUpdateLock: Standard
    // Batch so tests run quickly
    schemaUpdate: Flyway
}

test {
    maxTestRetries: 0
}

clientEncryption {
    publicKey: "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAzehsLwyNdZs5iJYBlVym5ZQgLmk4CLVeBhSidlXxkajyKmwR3a1onmq8eT10tjGqQdMzZytT0PMbgYRF1FLof9ifCjmE9OEmyCsozuRAFnwNqqIX3YnkTf55j/hZF9go1Ay4Xegv/da2pMDMmg0M/fpceaBLHSP0WMj7wL9u3e4KTjaDnWJQLNcjh3nOHzgb39QZTzm0K+8QHfVGMbitZjrvAE+OfhWK8OIaEk9vmhJPsaDdTDAFwEIs2snlp2uny8cJuabA9Iw8eB7WpTV7RA7mwNaHTYLWp2IGAoPZRmpzWWv21I6bX9zX5xgP6ahGFA9SgGguYrmQmqDLQxKq9N1Kv2opBRWnP7PTJRB8pRXhEPOWuDNTCZviwrP9ZesW+HvEOd842hygD+lRTam7S+xyIX9aqWCrZxxm83tAXjmttPwV+hwSI+upqOYg5XAllM1iL7PPdetIRs7PS3h7R1Z0E8xZCrSdeMMBrjxfimrJjaXbZuWgGgNWBNZWV2bk7zx2rqNwLrXjvJPsw3IPGGQW5Xl8Ez3BT2JKfXYYoCkgfo1L6+QISb/MhD3W1j/4ySWeUO2W7DsZpC88mgDyx4O6HrHZIjlPrYMd6xr8r6oV6t+/eB4NCYlXFcJxNy9URfg2h+0nk0KmUziG2XJolGskodWCpPExyXCSJUndo0UCAwEAAQ=="
}

adminWeb {
    hostName: "localhost"
    port: 8086
    targetPort: 8086
    enableTLS: false
}

service {
    port: 8080
    port: ${?SERVICE_PORT}
    targetPort: 8080
    targetPort: ${?SERVICE_TARGET_PORT}
    hostName: localhost
    enableTLS: false
}

rpc {
    proxyProvider {
        hostname: proxy-provider
        hostname: ${?SERVICE_PROXY_PROVIDER_HOST}
        port: 8080
        port: ${?SERVICE_PROXY_PROVIDER_PORT}
    }
    serviceMermaid {
        hostname: mermaidservice
        hostname: ${?SERVICE_MERMAID_HOST}
        port: 8080
        port: ${?SERVICE_MERMAID_PORT}
    }
}

dashboard {
    hostName: localhost
    port: 9000
    targetPort: 9000
    enableTLS: false
}

landingPage {
    hostName: localhost
    port: 9001
    targetPort: 9001
    enableTLS: false
}

authentication {
    secureCookies: false
    scmEnterpriseLoginUrl: "http://localhost:9000/login/enterprise"
    ssoLoginUrl: "http://localhost:9000/login/sso"
    authRedirectUrl: "http://localhost:9000/login/exchange"
    sameSite: "Lax"
    tokenIssuer: "local.getunblocked.com"
    useSecretCookie: false
}

sendGrid {
    businessPlanConfirmationTemplateId: "d-b43e88eb655547b9a47d045744539fc3"
    connectIntegrationsReminderTemplateId: "d-df5ab38d457f4d2cb5cb44a2334fb283"
    connectRepoFollowupTemplateId: "d-7f304bb9ad0d4b8ea8a41328fd27ca23"
    connectSourceCodeReminderTemplateId: "d-2ca193c45ce84e75aac09685bdd6d551"
    firstQuestionReminderTemplateId: "d-e6cf653dce89434db97c4624c3ba1212"
    integrationAddedConfirmationTemplateId: "d-c47ef831e1bd49c7be470c1d8a620dc2"
    inviteTeamReminderTemplateId: "d-5aae41bd962b46cdba213872152d3c1a"
    inviteTemplateId: "d-8a779900ba62476c93395ceff6d29e5b"
    newIntegrationCompletedProcessingTemplateId: "d-d5a86ec3d85a459c99b3a9e372584951"
    nthTrialStartTemplateId: "d-c9fb0b6c758b454b849a1245ab2195c9"
    onboardCompleteInactiveFollowupListId: "1eccb147-0967-4ec6-9d4b-fd67178946e9"
    onboardedListId: "8fabee66-b76e-481e-a000-522d886cebbc"
    paymentInviteTemplateId: "d-8edcef3ff6e84a5f8493c304441e800a"
    pitchTemplateId: "d-5d04458363eb45bf8c7a2bccec9b05fc"
    processingCompleteInactiveFollowupListId: "04e9ab5d-3b26-4123-b592-8af716fb6360"
    processingCompleteTemplateId: "d-0a0133e3d35e444a9b7e351777176308"
    recommendedInviteeTemplateId: "d-3f74a5d4468741a889c15d1f0133a689"
    requestAdminUpdateSlackPrivateScopesTemplateId: "d-89a2b9b61d074040aabc0e226328812e"
    teamInviteTemplateId: "d-8bdabb553cfa4800a65b64f696c86aa0"
    teamInvitedFollowupListId: "72c0a07f-3aa3-4731-94a3-ffa58b3bf1e1"
    threadInviteJoinTemplateId: "d-53e0e25d9e084d16a59f4be1df1ba64f"
    threadInviteTemplateId: "d-7158fcff4b5247868598c1283c2cfe80"
    trialOneDayTemplateId: "d-070455d83458456db528594a3d36e69c"
    trialOneWeekTemplateId: "d-1179a81ab8684fd08da248e684495720"
    trialStartTemplateId: "d-5c474357446e42c2a80c852980c290cb"
    trialSurveyTemplateId: "d-a71b86c6c2664fccb1e533befeeac4b3"
    trialThreeDaysTemplateId: "d-09d1aeb7440145749b61ca65f58b52a6"
    trialTwoWeeksTemplateId: "d-d732ab578af748f6a32ce07076f69d5f"
    updateSlackPrivateScopesTemplateId: "d-2777731663004fe28f573a819f65bc49yy"
    licenseRequestedTemplateId: "d-71944bb245f64ec4bc09574c18dd6455"
    licenseApprovedTemplateId: "d-81ac1098860849c488bee2d379ec7463"
    userAddedTemplateId: "d-f5adfe22626b460abb9bf53cebb612eb"
    userRemovedConfirmationTemplateId: "d-d1c52fdd079e4774a2974595c24ce420"
    welcomeEmailListId: "a10cb005-8f8d-469a-ad08-60a601ee4ec5"
    isIntegrationAddedConfirmationEnabled: true
}

versioning {
    manifestBasePath: "https://local.getunblocked.com"
    s3: {
        region: local
        bucket: releases.local.getunblocked.com
    }
}

redis {
    userName: ""
    useSsl: false
    useCluster: false
}

cors {
    hosts: [
        { // local services
            hostName: "localhost"
            schemes: [test]
        },
        { // webservice
            hostName: "localhost"
            port: 9000
            schemes: [http, https]
        },
        { // landing page
            hostName: "localhost"
            port: 9001
            schemes: [http, https]
        },
        { // webservice
            hostName: "*.ngrok.io"
            schemes: [http, https]
        },
        {
            hostName: "tunnel.secops.getunblocked.com",
            port: 9000
            schemes: [http, https]
        },
        { // for chrome extension
            hostName: "*"
            schemes: [chrome-extension]
        },
        { // for safari extension
            hostName: "*"
            schemes: [safari-web-extension]
        },
        { // for google saml
            hostName: "accounts.google.com"
            schemes: [http, https]
        },
        { // for okta saml
            hostName: "*.okta.com"
            schemes: [http, https]
        },
        {
            hostName: "*.okta-emea.com"
            schemes: [http, https]
        },
        {
            hostName: "*.okta-gov.com"
            schemes: [http, https]
        },
        {
            hostName: "*.okta.mil"
            schemes: [http, https]
        },
        {
            hostName: "*.oktapreview.com"
            schemes: [http, https]
        },
        { // for AWS Identity Center saml
            hostName: "*.amazonaws.com"
            schemes: [http, https]
        },
        { // for AWS Access Portal saml
            hostName: "*.awsapps.com"
            schemes: [http, https]
        },
        { // for Microsoft Entra ID saml
            hostName: "*.microsoftonline.com"
            schemes: [http, https]
        },
    ]
    allowCredentials: true
}

queue {
    region: local
}

mlRouter {
    dynamoDB {
        region: local
    }
}

activeMQ {
    primary: {
        port: 61616
    }
    useSsl: false
}

providers {
    asana {
        oauth = {
            clientId: "1209876123559226"
            includeResponseType: false
            oauthCallbackUrl: "https://clio-io.sainaney.com/auth/asana/exchange"
        }
    }
    slack {
        appId: "A047M7AJ5PU"
        oauth {
            // App [https://api.slack.com/apps/A047M7AJ5PU]
            clientId: "2560243177732.4259248617810"
            oauthCallbackUrl: "https://rashin.ngrok.io/api/auth/slack/exchange"
            scopes: "app_mentions:read,channels:history,channels:join,channels:read,chat:write,commands,files:read,groups:history,groups:read,im:history,im:read,team:read,users.profile:read,users:read,users:read.email"
            userScopes: "groups:read,groups:write.invites"
        }
        openId {
            // App [https://api.slack.com/apps/A047M7AJ5PU]
            clientId: "2560243177732.4259248617810"
            oauthCallbackUrl: "https://rashin.ngrok.io/api/auth/slack/exchange"
        }
        openIdSignIn {
            // App [https://api.slack.com/apps/A047M7AJ5PU]
            clientId: "2560243177732.4259248617810"
            oauthCallbackUrl: "https://rashin.ngrok.io/login/exchange"
        }
    }
    slackUAT {
        appId: "A07GHKGQLTX"
        oauth {
            // App [https://api.slack.com/apps/A0459LH81MY]
            clientId: "2560243177732.7561662836949"
            oauthCallbackUrl: "https://rashin.ngrok.io/api/auth/slack/exchange"
        }
        openId {
            // App [https://api.slack.com/apps/A0459LH81MY]
            clientId: "2560243177732.7561662836949"
            oauthCallbackUrl: "https://rashin.ngrok.io/api/auth/slack/exchange"
        }
        openIdSignIn {
            // App [https://api.slack.com/apps/A0459LH81MY]
            clientId: "2560243177732.7561662836949"
            oauthCallbackUrl: "https://rashin.ngrok.io/login/exchange"
        }
    }
    jira {
        appId: "************************8d61c1e0d52b"
        baseUrlOverride: "https://dlam.ngrok.app"
        oauth {
            // App [https://developer.atlassian.com/console/myapps/************************8d61c1e0d52b]
            clientId: "KbQyoF19cLKHg95izQ5zPnApslPeBwef"
            oauthCallbackUrl: "https://dlam.ngrok.app/api/auth/jira/exchange"
        }
    }
    confluence {
        appId: "************************42c628f9e501"
        oauth {
            // App [https://developer.atlassian.com/console/myapps/************************42c628f9e501]
            clientId: "bu1G1Vbyb8R4DG4c7taZJMQZR6JJg68f"
            oauthCallbackUrl: "https://dlam.ngrok.app/api/auth/confluence/exchange"
        }
    }
    linear {
        oauth {
            // App [https://linear.app/unblocked/settings/api/applications/a0f0b42c-44c2-4596-ac3c-7870d64003f1/edit]
            clientId: "ad4faba699dc55d79d83f83ec3ce75a3"
            oauthCallbackUrl: "https://dlam.ngrok.app/api/auth/linear/exchange"
        }
    }
    notion {
        oauth {
            // App [https://www.notion.so/my-integrations/public/b40a8ba4da524361b964d5f790335fd9]
            clientId: "b40a8ba4-da52-4361-b964-d5f790335fd9"
            oauthCallbackUrl: "https://dlam.ngrok.app/api/auth/notion/exchange"
        }
    }
    googleDrive {
        oauth {
            // Google Cloud Project [https://console.cloud.google.com/welcome?orgonly=true&project=unblocked-local]
            clientId: "1017325532068-f3lioctis2p340nggmb1rsb6447pnvpf.apps.googleusercontent.com"
            oauthCallbackUrl: "https://dlam.ngrok.app/api/auth/google/exchange"
        }
    }
    coda {
        baseApiUri: "https://coda.io"
        webhookUrl: "https://dlam.ngrok.app/api/hooks/coda"
    }
}

search {
    enableAutocomplete: true
}

honeycomb {
    enabled: false
    hostName: "localhost"
    hostName: ${?HONEYCOMB_HOSTNAME}
}

sensitiveLogging {
    dynamoDB {
        region: us-west-2
    }
}

rapid {
    dynamoDB {
        region: us-west-2
    }
}

internalSlack {
    fallbackChannel: "default-local"
}

featureFlags {
    enableAWSLiveQuery: true
    enableBuildIngestion: false
    enableGraphRag: true
    enablePrefect: true
    enableSampleQuestions: true
    enableSlackInstallProgress: true
    enableTestBillingActions: true
}

prefect {
    // YOU MUST HAVE TRAILING SLASH
    baseApiUri: "http://localhost:4200/"
    defaultTimeout: 10s
}

openSearch {
    password: "Unblocked1!"
}

billing {
    enabled: true
}
