package com.nextchaptersoftware.linear.ingestion.services

import com.nextchaptersoftware.clients.linear.commentsquery.Comment
import com.nextchaptersoftware.clients.linear.commentsquery.User
import com.nextchaptersoftware.clients.linear.inputs.CommentFilter
import com.nextchaptersoftware.clients.linear.inputs.IDComparator
import com.nextchaptersoftware.clients.linear.inputs.IssueFilter
import com.nextchaptersoftware.clients.linear.issuesquery.Issue
import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.LinearOrganizationId
import com.nextchaptersoftware.db.models.LinearTeamId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.integration.data.events.queue.enqueue.DataEventEnqueueService
import com.nextchaptersoftware.integration.data.events.queue.payloads.LinearDataEvent
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.linear.api.LinearApiProvider
import com.nextchaptersoftware.linear.api.status
import com.nextchaptersoftware.linear.services.LinearTokenProvider
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class LinearIssueIngestionService(
    private val linearTokenProvider: LinearTokenProvider,
    private val linearCommentMessageModelService: LinearCommentMessageModelService,
    private val linearIssueThreadModelService: LinearIssueThreadModelService,
    private val linearThreadFinalizationService: LinearThreadFinalizationService,
    private val linearApiProvider: LinearApiProvider,
    private val dataEventEnqueueService: DataEventEnqueueService,
) {
    suspend fun ingest(
        installation: Installation,
        linearOrganizationId: LinearOrganizationId,
        linearTeamId: LinearTeamId,
        externalLinearOrganizationId: String,
        linearIssueResponse: Issue,
    ) = withLoggingContextAsync(
        "installationId" to installation.id,
        "linearOrganizationId" to linearOrganizationId,
        "linearTeamId" to linearTeamId,
        "linearIssueId" to linearIssueResponse.id,
        "linearIssueKey" to linearIssueResponse.identifier,
        "linearIssueStatus" to linearIssueResponse.state.type,
    ) {
        if (linearIssueResponse.status == null) {
            LOGGER.debugAsync { "Skipping Linear issue ingestion because state is not known" }
            return@withLoggingContextAsync
        }

        LOGGER.debugAsync { "Ingesting Linear issue" }

        val linearComments = when (linearIssueResponse.comments.pageInfo.hasNextPage) {
            true -> { // Go grab all comments
                ingestComments(installationId = installation.id, linearIssueId = linearIssueResponse.id)
            }

            else -> {
                linearIssueResponse.comments.nodes.map { comment ->
                    Comment(
                        id = comment.id,
                        user = comment.user?.id?.let { User(id = it) },
                        createdAt = comment.createdAt,
                        body = comment.body,
                        issue = com.nextchaptersoftware.clients.linear.commentsquery.Issue(id = linearIssueResponse.id),
                        url = comment.url,
                    )
                }
            }
        }

        triggerLinearIssueDataIngestion(
            orgId = installation.orgId,
            linearOrganizationId = linearOrganizationId,
            linearTeamId = linearTeamId,
            linearIssue = linearIssueResponse,
        )

        triggerLinearCommentsDataIngestion(
            orgId = installation.orgId,
            linearOrganizationId = linearOrganizationId,
            linearTeamId = linearTeamId,
            linearComments = linearComments,
        )

        ingest(
            installation = installation,
            externalLinearOrganizationId = externalLinearOrganizationId,
            linearTeamId = linearTeamId,
            linearComments = linearComments,
            linearIssueResponse = linearIssueResponse,
        )
    }

    private suspend fun ingest(
        installation: Installation,
        externalLinearOrganizationId: String,
        linearTeamId: LinearTeamId,
        linearIssueResponse: Issue,
        linearComments: List<Comment>,
    ) {
        val thread = linearIssueThreadModelService.upsertThread(
            trx = null,
            installation = installation,
            linearOrganizationId = externalLinearOrganizationId,
            linearTeamId = linearTeamId,
            linearIssueResponse = linearIssueResponse,
        ) ?: return

        ingest(
            installation = installation,
            threadId = thread.idValue,
            linearOrganizationId = externalLinearOrganizationId,
            linearComments = linearComments,
        )

        linearThreadFinalizationService.finalizeThreadCreation(threadId = thread.idValue)
    }

    suspend fun ingest(
        installation: Installation,
        linearOrganizationId: String,
        linearComment: Comment,
    ) {
        val thread = linearIssueThreadModelService.findByLinearIssueId(orgId = installation.orgId, linearIssueId = linearComment.issue.id)
            ?: return

        ingest(
            installation = installation,
            threadId = thread.id,
            linearOrganizationId = linearOrganizationId,
            linearComments = listOf(linearComment),
        )

        linearThreadFinalizationService.finalizeThreadCreation(threadId = thread.id)
    }

    suspend fun archiveIssue(
        orgId: OrgId,
        linearIssueId: String,
    ) {
        linearIssueThreadModelService.archiveByLinearIssueId(
            orgId = orgId,
            linearIssueId = linearIssueId,
        )?.let {
            linearThreadFinalizationService.finalizeThreadCreation(threadId = it)
        }
    }

    suspend fun restoreIssue(
        orgId: OrgId,
        linearIssueId: String,
    ) {
        linearIssueThreadModelService.restoreByLinearIssueId(
            orgId = orgId,
            linearIssueId = linearIssueId,
        )?.let {
            linearThreadFinalizationService.finalizeThreadCreation(threadId = it)
        }
    }

    suspend fun deleteComment(
        installationId: InstallationId,
        linearCommentId: String,
    ) {
        linearCommentMessageModelService.deleteMessage(
            installationId = installationId,
            linearCommentId = linearCommentId,
        )
    }

    private suspend fun ingest(
        installation: Installation,
        threadId: ThreadId,
        linearOrganizationId: String,
        linearComments: List<Comment>,
    ) {
        for (linearCommentResponse in linearComments) {
            Database.suspendedTransaction {
                linearCommentMessageModelService.upsertMessage(
                    trx = this,
                    installation = installation,
                    threadId = threadId,
                    linearOrganizationId = linearOrganizationId,
                    linearCommentResponse = linearCommentResponse,
                )
            }
        }
    }

    private suspend fun ingestComments(
        installationId: InstallationId,
        linearIssueId: String,
    ): List<Comment> {
        val linearComments = mutableListOf<Comment>()

        val response = linearApiProvider.linearCommentsApi.getComments(
            accessToken = linearTokenProvider.getOAuthTokens(installationId = installationId).accessToken,
            filter = CommentFilter(
                issue = IssueFilter(
                    id = IDComparator(
                        eq = linearIssueId,
                    ),
                ),
            ),
        )

        response.errors?.forEach { error ->
            LOGGER.errorAsync("linear.response.error" to error) {
                "Received linear response error for comment paging"
            }
        }

        response.data?.comments?.nodes?.let {
            linearComments.addAll(it)
        }

        return linearComments
    }

    private fun triggerLinearCommentsDataIngestion(
        orgId: OrgId,
        linearOrganizationId: LinearOrganizationId,
        linearTeamId: LinearTeamId,
        linearComments: List<Comment>,
    ) {
        linearComments.forEach { linearComment ->
            runSuspendCatching {
                dataEventEnqueueService.enqueueLinearDataEvent(
                    LinearDataEvent.LinearCommentDataEvent(
                        orgId = orgId,
                        linearOrganizationId = linearOrganizationId,
                        linearTeamId = linearTeamId,
                        linearComment = linearComment,
                    ),
                )
            }.onFailure {
                LOGGER.errorSync(it) { "Failed to enqueue linear comment data event " }
            }
        }
    }

    private fun triggerLinearIssueDataIngestion(
        orgId: OrgId,
        linearOrganizationId: LinearOrganizationId,
        linearTeamId: LinearTeamId,
        linearIssue: Issue,
    ) {
        runSuspendCatching {
            dataEventEnqueueService.enqueueLinearDataEvent(
                LinearDataEvent.LinearIssueDataEvent(
                    orgId = orgId,
                    linearOrganizationId = linearOrganizationId,
                    linearTeamId = linearTeamId,
                    linearIssue = linearIssue,
                ),
            )
        }.onFailure {
            LOGGER.errorSync(it) { "Failed to enqueue linear issue data event " }
        }
    }
}
