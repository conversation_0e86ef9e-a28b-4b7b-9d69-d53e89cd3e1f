package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.db.models.ProductAgentType as DbAgentType
import com.nextchaptersoftware.db.models.fromProductAgentHeaderOrNull
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AgentTypeKtTest {
    @Test
    fun `test AgentType header conversion`() {
        AgentType.entries.forEach {
            assertThat(DbAgentType.fromProductAgentHeaderOrNull(it.enumValue)).isNotNull
        }
    }
}
