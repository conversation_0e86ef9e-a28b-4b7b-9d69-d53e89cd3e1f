package com.nextchaptersoftware.api.models.converters

import com.nextchaptersoftware.api.models.Message
import com.nextchaptersoftware.api.models.MessageContent
import com.nextchaptersoftware.api.models.MessageLinks
import com.nextchaptersoftware.api.models.PullRequest as ApiPullRequest
import com.nextchaptersoftware.api.models.PullRequest.State.closed
import com.nextchaptersoftware.api.models.PullRequest.State.merged
import com.nextchaptersoftware.api.models.PullRequest.State.open
import com.nextchaptersoftware.api.models.PullRequestBlock as ApiPullRequestBlock
import com.nextchaptersoftware.api.models.PullRequestDescription
import com.nextchaptersoftware.api.models.PullRequestInfo as ApiPullRequestInfo
import com.nextchaptersoftware.api.models.PullRequestLinks
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.common.model.Message.MessageBody
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestState.Closed
import com.nextchaptersoftware.db.models.PullRequestState.Merged
import com.nextchaptersoftware.db.models.PullRequestState.Open
import com.nextchaptersoftware.db.models.ThreadSource
import com.nextchaptersoftware.db.models.TopicId
import com.nextchaptersoftware.db.models.dashboardUrl
import com.nextchaptersoftware.db.stores.PullRequestInfo
import com.nextchaptersoftware.db.stores.ThreadInfo
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.ScmFileUrlProvider
import com.nextchaptersoftware.utils.asApiDateTime
import io.ktor.util.encodeBase64

fun PullRequest.asApiModel(
    orgId: OrgId,
    provider: Provider,
    commentCount: Int = 0,
    hasSlackThreads: Boolean = false,
    topicIds: List<TopicId>? = null,
    expertOrgMemberIds: List<OrgMemberId>? = null,
    participantOrgMemberIds: List<OrgMemberId>? = null,
    urlBuilderProvider: UrlBuilderProvider,
): ApiPullRequest {
    return ApiPullRequest(
        id = id.value,
        repoId = repoId.value,
        authorTeamMemberId = creatorOrgMemberId.value,
        state = when (state) {
            Open -> open
            Closed -> closed
            Merged -> merged
        },
        createdAt = createdAt.asApiDateTime(),
        mergedAt = mergedAt?.asApiDateTime(),
        mergeCommitSha = mergeCommitSha,
        number = number,
        title = title,
        htmlUrl = htmlUrl.asString,
        isStale = false, // TODO https://linear.app/unblocked/issue/UNB-216/define-and-implement-concept-of-stale-pr
        // TODO remove deprecated field
        //      'description' field should always be populated until the field is removed
        description = PullRequestDescription(
            createdAt = createdAt.asApiDateTime(),
            htmlUrl = htmlUrl.asString,
            messageContent = messageContent,
        ),
        descriptionMessage = Message(
            id = id.value,
            authorTeamMemberId = creatorOrgMemberId.value,
            messageContent = messageContent,
            createdAt = createdAt.asApiDateTime(),
            links = MessageLinks(
                dashboardUrl = null,
                externalMessageUrl = htmlUrl.asString,
            ),
        ),
        commentCount = commentCount,
        hasSlackThreads = hasSlackThreads,
        ingestionInProgress = !isIngested,
        topics = topicIds?.map { it.value },
        experts = expertOrgMemberIds?.map { it.value },
        participants = participantOrgMemberIds?.map { it.value },
        links = PullRequestLinks(
            scmUrl = htmlUrl.asString,
            dashboardUrl = dashboardUrl(orgId, urlBuilderProvider).asString,
        ),
        archivedAt = archivedAt?.asApiDateTime(),
        archivedBy = archivedBy?.value,
        provider = provider.asApiModel(),
    )
}

private val PullRequest.messageContent: MessageContent
    get() = when (val description = description) {
        null -> MessageBody.getDefaultInstance().let {
            MessageContent(content = it.toByteArray().encodeBase64(), version = it.version)
        }

        else -> MessageContent(
            content = description.encodeBase64(),
            version = descriptionVersion ?: MessageBody.parseFrom(description).version,
        )
    }

suspend fun PullRequestInfo.asApiModel(
    orgId: OrgId,
    authorizedMemberId: OrgMemberId,
    provider: Provider,
    urlBuilderProvider: UrlBuilderProvider,
    scmFileUrlProvider: ScmFileUrlProvider,
    botAccountService: BotAccountService,
): ApiPullRequestInfo {
    val (tlcThreads, codeThreads) = threads.partition { it.thread.isTopLevelCommentThread }
    val tlcThreadsAsGroups = tlcThreads.map {
        it.asPullRequestBlock(
            orgId = orgId,
            authorizedMemberId = authorizedMemberId,
            urlBuilderProvider = urlBuilderProvider,
            scmFileUrlProvider = scmFileUrlProvider,
            botAccountService = botAccountService,
        )
    }
    val codeThreadsAsGroups = codeThreads.map {
        it.asPullRequestBlock(
            orgId = orgId,
            authorizedMemberId = authorizedMemberId,
            urlBuilderProvider = urlBuilderProvider,
            scmFileUrlProvider = scmFileUrlProvider,
            botAccountService = botAccountService,
        )
    }
    val participantOrgMemberIds = (listOf(pullRequest.creatorOrgMemberId) + threads.flatMap { it.participantOrgMemberIds } + approverOrgMembers)
    val commentCount = threads.sumOf { thread -> thread.messages.count { !it.isDeleted } }
    val hasSlackThreads = threads.any { it.thread.source == ThreadSource.Slack }

    val pullRequestBlocks = tlcThreadsAsGroups.sortedBy { it.createdAt } + codeThreadsAsGroups.sortedBy { it.createdAt }

    return ApiPullRequestInfo(
        pullRequest = pullRequest.asApiModel(
            orgId = orgId,
            provider = provider,
            commentCount = commentCount,
            hasSlackThreads = hasSlackThreads,
            topicIds = topicIds,
            expertOrgMemberIds = expertOrgMemberIds,
            participantOrgMemberIds = participantOrgMemberIds.distinct(),
            urlBuilderProvider = urlBuilderProvider,
        ),
        pullRequestBlocks = pullRequestBlocks,
        participants = participantOrgMemberIds.map { it.value },
        approvers = approverOrgMembers.map { it.value },
    )
}

suspend fun ThreadInfo.asPullRequestBlock(
    orgId: OrgId,
    authorizedMemberId: OrgMemberId,
    urlBuilderProvider: UrlBuilderProvider,
    scmFileUrlProvider: ScmFileUrlProvider,
    botAccountService: BotAccountService,
) = ApiPullRequestBlock(
    id = thread.id.value,
    authorTeamMemberId = thread.authorOrgMemberId.value,
    createdAt = thread.createdAt.asApiDateTime(),
    htmlUrl = messages.minByOrNull { it.createdAt }?.prCommentUrl?.asString ?: "",
    threads = listOf(
        this.asApiModel(
            orgId = orgId,
            authorizedMemberId = authorizedMemberId,
            urlBuilderProvider = urlBuilderProvider,
            scmFileUrlProvider = scmFileUrlProvider,
            botAccountService = botAccountService,
        ),
    ),
)
