package com.nextchaptersoftware.notification.events.email.payloads

import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class TeamInviteEmailTemplatePayload(
    @SerialName("author")
    val author: String,

    @Contextual
    @SerialName("teamInviteUrl")
    val teamInviteUrl: Url,

    @SerialName("teamInfo")
    val teamInfo: NotificationOrgPayload.InviteDescriptionPayload?,
)
