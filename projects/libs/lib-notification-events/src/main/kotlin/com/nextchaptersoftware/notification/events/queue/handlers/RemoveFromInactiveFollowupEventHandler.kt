package com.nextchaptersoftware.notification.events.queue.handlers

import com.nextchaptersoftware.db.stores.Stores.personStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationEvent
import com.nextchaptersoftware.sendgrid.SendGridContactEvent
import com.nextchaptersoftware.sendgrid.SendGridContactEventHandler
import com.nextchaptersoftware.sendgrid.SendGridListType
import com.nextchaptersoftware.utils.KotlinUtils.required

class RemoveFromInactiveFollowupEventHandler(
    private val sendGridContactEventHandler: SendGridContactEventHandler,
) : TypedEventHandler<NotificationEvent.RemoveFromInactiveFollowupEvent> {
    override suspend fun handle(event: NotificationEvent.RemoveFromInactiveFollowupEvent): Boolean {
        val person = personStore.findById(id = event.personId).required()

        sendGridContactEventHandler.handle(
            SendGridContactEvent.RemoveFromList(
                email = person.primaryEmail,
                recipientName = person.customDisplayName,
                listType = SendGridListType.PROCESSING_COMPLETE_INACTIVE_FOLLOWUP,
            ),
        )
        sendGridContactEventHandler.handle(
            SendGridContactEvent.RemoveFromList(
                email = person.primaryEmail,
                recipientName = person.customDisplayName,
                listType = SendGridListType.ONBOARD_COMPLETE_INACTIVE_FOLLOWUP,
            ),
        )

        return true
    }
}
