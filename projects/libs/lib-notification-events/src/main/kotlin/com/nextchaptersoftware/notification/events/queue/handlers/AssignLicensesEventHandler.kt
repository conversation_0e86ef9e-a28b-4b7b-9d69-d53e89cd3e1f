package com.nextchaptersoftware.notification.events.queue.handlers

import com.nextchaptersoftware.db.models.OrgBillingSeatState
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgBillingSeatStore
import com.nextchaptersoftware.db.stores.OrgMemberRoleStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationEvent
import com.nextchaptersoftware.sendgrid.SendGridTemplateEvent
import com.nextchaptersoftware.sendgrid.SendGridTemplateEventHandler
import com.nextchaptersoftware.types.EmailAddress

class AssignLicensesEventHandler(
    private val sender: EmailAddress,
    private val senderName: String,
    private val sendGridTemplateEventHandler: SendGridTemplateEventHandler,
    private val memberStore: MemberStore = Stores.memberStore,
    private val orgStore: OrgStore = Stores.orgStore,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    private val orgMemberRoleStore: OrgMemberRoleStore = Stores.orgMemberRoleStore,
    private val orgBillingSeatStore: OrgBillingSeatStore = Stores.orgBillingSeatStore,
    private val urlBuilderProvider: UrlBuilderProvider,
) : TypedEventHandler<NotificationEvent.AssignLicenses> {

    companion object {
        private const val MAX_MEMBERS_TOTAL = 7
        private const val MAX_MEMBERS_TO_SHOW = 5
    }

    override suspend fun handle(event: NotificationEvent.AssignLicenses): Boolean {
        val org = requireNotNull(orgStore.findById(orgId = event.orgId)) { "org not found" }

        val membersRequestingLicenses = orgBillingSeatStore.list(
            orgId = event.orgId,
            states = listOf(OrgBillingSeatState.Restricted),
        ).let {
            resolveMemberNames(orgMemberIds = it.map { it.orgMemberId })
        }

        if (membersRequestingLicenses.isEmpty()) {
            return true // No members to assign licenses to
        }

        val manageLicensesUrl = urlBuilderProvider
            .dashboard()
            .withOrg(event.orgId.value)
            .withBilling()
            .build()
            .asString

        // Apply the member display logic
        val (membersToShow, membersRemainingCount) = prepareMembersList(membersRequestingLicenses)

        // Find all admin org members
        val adminOrgMemberIds = orgMemberRoleStore.listUnblockedAdmins(orgId = event.orgId)

        // Get their identities and email addresses
        val adminMembersWithIdentities = memberStore.findByOrgMemberWithIdentity(
            orgMemberIds = adminOrgMemberIds.toList(),
        )

        // Send email to each admin with a valid email address
        adminMembersWithIdentities.forEach { adminMemberInfo ->
            adminMemberInfo.identity.primaryEmail?.let { adminEmail ->
                sendGridTemplateEventHandler.handle(
                    SendGridTemplateEvent.AssignLicenses(
                        fromEmailAddress = sender,
                        fromName = senderName,
                        toEmailAddress = adminEmail,
                        subject = "Assign licenses to team members for ${org.displayName}",
                        members = membersToShow,
                        membersRemainingCount = membersRemainingCount,
                        teamName = org.displayName,
                        manageLicensesUrl = manageLicensesUrl,
                    ),
                )
            }
        }

        return true
    }

    private suspend fun resolveMemberNames(orgMemberIds: List<OrgMemberId>): List<String> {
        if (orgMemberIds.isEmpty()) {
            return emptyList()
        }

        return orgMemberStore.findByIdsWithPerson(orgMemberIds = orgMemberIds.toSet()).map { (orgMember, person) ->
            when (person) {
                null -> memberStore.findByOrgMemberWithIdentity(orgMemberId = orgMember.id).first().identity.displayNameOrUsername
                else -> person.customDisplayName
            }
        }
    }

    private fun prepareMembersList(
        allMembers: List<String>,
    ): Pair<List<String>, Int?> {
        return when {
            // Show all members if <= 7
            allMembers.size <= MAX_MEMBERS_TOTAL -> {
                allMembers to null
            }

            // Show first 5 and indicate remaining count
            else -> {
                val remainingCount = allMembers.size - MAX_MEMBERS_TO_SHOW
                allMembers.take(MAX_MEMBERS_TO_SHOW) to remainingCount
            }
        }
    }
}
