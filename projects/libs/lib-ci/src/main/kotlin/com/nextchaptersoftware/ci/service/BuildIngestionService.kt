package com.nextchaptersoftware.ci.service

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.models.CiExecution.CiBuild
import com.nextchaptersoftware.ci.models.CiExecution.CiJob
import com.nextchaptersoftware.ci.models.asBuildResult
import com.nextchaptersoftware.ci.models.asBuildStatus
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Build
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.stores.Stores.buildJobStore
import com.nextchaptersoftware.db.stores.Stores.buildStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveSync
import com.nextchaptersoftware.utils.KotlinUtils.required
import org.jetbrains.exposed.sql.Transaction

private val LOGGER = mu.KotlinLogging.logger {}

class BuildIngestionService(
    private val buildProcessService: BuildProcessService,
) {
    suspend fun ingestBuild(
        ciInstallation: Installation,
        scmTeamId: ScmTeamId,
        repoId: RepoId,
        pullRequest: PullRequest,
        projectContext: CIProjectContext,
        ciBuild: CiBuild,
        ciJobs: List<CiJob>? = null,
    ) = withLoggingContextAsync(
        "orgId" to ciInstallation.orgId,
        "ciInstallationId" to ciInstallation.id,
        "scmTeamId" to scmTeamId,
        "repoId" to repoId,
        "pullRequestId" to pullRequest.id,
        "ciBuild.runner" to ciBuild.runner,
        "ciBuild.type" to ciBuild.type,
        "ciBuild.externalId" to ciBuild.externalId,
        "ciBuild.displayNumber" to ciBuild.displayNumber,
        "ciBuild.status" to ciBuild.status,
        "ciBuild.result" to ciBuild.result,
    ) fn@{
        val repo = repoStore.findById(repoId = repoId).required { "Missing repo" }

        LOGGER.debugAsync { "Ingesting event: build" }

        ciJobs?.forEach { ciJob ->
            validateBuildAndJob(ciBuild, ciJob)
        }

        val build = suspendedTransaction {
            buildUpsert(
                trx = this,
                ciInstallation = ciInstallation,
                pullRequest = pullRequest,
                projectContext = projectContext,
                ciBuild = ciBuild,
            )
                .also { build ->
                    ciJobs?.forEach { ciJob ->
                        jobUpsert(
                            trx = this,
                            build = build,
                            ciJob = ciJob,
                        )
                    }
                }
        }

        buildProcessService.processBuild(
            repo = repo,
            pullRequest = pullRequest,
            build = build,
        )
    }

    private suspend fun buildUpsert(
        trx: Transaction? = null,
        ciInstallation: Installation,
        pullRequest: PullRequest,
        projectContext: CIProjectContext,
        ciBuild: CiBuild,
    ) = buildStore.upsert(
        trx = trx,
        ciInstallationId = ciInstallation.id,
        pullRequestId = pullRequest.id,
        projectContext = projectContext.encode(),
        externalId = ciBuild.externalId,
        baseSha = ciBuild.baseSha,
        headSha = ciBuild.headSha,
        groupKey = ciBuild.groupKey,
        displayName = ciBuild.displayName,
        displayNumber = ciBuild.displayNumber,
        apiUrl = ciBuild.apiUrl,
        htmlUrl = ciBuild.htmlUrl,
        type = ciBuild.type,
        runner = ciBuild.runner,
        attempt = ciBuild.attempt,
        status = ciBuild.status.asBuildStatus(),
        result = ciBuild.result.asBuildResult(),
        createdAt = ciBuild.createdAt,
        startedAt = ciBuild.startedAt,
        completedAt = ciBuild.completedAt,
    )

    private fun validateBuildAndJob(
        ciBuild: CiBuild,
        ciJob: CiJob,
    ) {
        check(ciBuild.externalId == ciJob.externalParentId) {
            LOGGER.debugSensitiveSync(
                sensitiveFields = mapOf(
                    "ciBuild" to ciBuild.toString(),
                    "ciJob" to ciJob.toString(),
                ),
            ) { "Job does not belongs to parent build" }

            "Job does not belongs to parent build"
        }
    }

    private suspend fun jobUpsert(
        trx: Transaction? = null,
        build: Build,
        ciJob: CiJob,
    ) = buildJobStore.upsert(
        trx = trx,
        buildId = build.id,
        externalId = ciJob.externalId,
        externalParentId = ciJob.externalParentId,
        displayName = ciJob.displayName,
        apiUrl = ciJob.apiUrl,
        htmlUrl = ciJob.htmlUrl,
        annotationsId = ciJob.annotationsId,
        type = ciJob.type,
        attempt = ciJob.attempt,
        status = ciJob.status.asBuildStatus(),
        result = ciJob.result.asBuildResult(),
        createdAt = ciJob.createdAt,
        startedAt = ciJob.startedAt,
        completedAt = ciJob.completedAt,
    )
}
