package com.nextchaptersoftware.bot.services

import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.stores.MemberAssociationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class InstallationBotAccountServiceTest : DatabaseTestsBase() {
    private val memberAssociationStore: MemberAssociationStore = Stores.memberAssociationStore
    private val botAccountService: InstallationBotAccountService = InstallationBotAccountService(
        urlBuilderProvider = StandardUrlBuilderProvider(),
    )

    private lateinit var org: OrgDAO
    private lateinit var scmTeamA: ScmTeamDAO
    private lateinit var scmTeamB: ScmTeamDAO
    private lateinit var botMember: Member
    private lateinit var associatedMemberA: MemberDAO
    private lateinit var memberB: MemberDAO

    private suspend fun setup() {
        org = makeOrg()
        scmTeamA = makeScmTeam(org = org)
        scmTeamB = makeScmTeam(org = org)

        botMember = botAccountService.upsertBotAccountMember(orgId = org.idValue)

        associatedMemberA = makeMember(scmTeam = scmTeamA)
        memberB = makeMember(scmTeam = scmTeamB)

        memberAssociationStore.setPrimaryMember(memberId = associatedMemberA.idValue, primaryMemberId = botMember.id)
    }

    @Test
    fun `should correctly identify bot account member`() = suspendingDatabaseTest {
        setup()

        val isBotAccountMemberA = botAccountService.isBotAccountMember(orgId = org.idValue, memberId = associatedMemberA.idValue)
        assertThat(isBotAccountMemberA).isTrue

        // Check cache works
        val isBotAccountMemberACached = botAccountService.isBotAccountMember(orgId = org.idValue, memberId = associatedMemberA.idValue)
        assertThat(isBotAccountMemberACached).isTrue

        // Check with invalid member ID
        val isBotAccountMemberInvalid = botAccountService.isBotAccountMember(orgId = org.idValue, memberId = MemberId.random())
        assertThat(isBotAccountMemberInvalid).isFalse
    }

    @Test
    fun `should retrieve bot member ids for organization`() = suspendingDatabaseTest {
        setup()

        val botMemberIdsForOrg = botAccountService.getBotAccountMembersIds(orgId = org.idValue)
        assertThat(botMemberIdsForOrg).containsExactlyInAnyOrder(botMember.id, associatedMemberA.idValue)
    }

    @Test
    fun `upsert for different org should create botMember`() = suspendingDatabaseTest {
        setup()

        val otherOrg = makeOrg()
        val otherBotMember = botAccountService.upsertBotAccountMember(orgId = otherOrg.idValue)
        assertThat(otherBotMember).isNotNull
        assertThat(otherBotMember.id).isNotEqualTo(botMember.id)
    }

    @Test
    fun `upsert should be idempotent`() = suspendingDatabaseTest {
        setup()

        val otherBotMember = botAccountService.upsertBotAccountMember(orgId = org.idValue)
        assertThat(otherBotMember).isNotNull
        assertThat(otherBotMember.id).isEqualTo(botMember.id)
    }

    @Test
    fun `should not identify unrelated members as bot accounts`() = suspendingDatabaseTest {
        setup()

        val unrelatedMember = makeMember(scmTeam = scmTeamA)
        val isBotAccountUnrelated = botAccountService.isBotAccountMember(orgId = org.idValue, memberId = unrelatedMember.idValue)
        assertThat(isBotAccountUnrelated).isFalse
    }

    @Test
    fun `should handle invalid orgId correctly`() = suspendingDatabaseTest {
        setup()

        val otherOrg = makeOrg()
        val botMemberIdsInvalidOrg = botAccountService.getBotAccountMembersIds(orgId = otherOrg.idValue)
        assertThat(botMemberIdsInvalidOrg).doesNotContain(botMember.id)
        assertThat(botMemberIdsInvalidOrg).hasSize(1)
    }
}
