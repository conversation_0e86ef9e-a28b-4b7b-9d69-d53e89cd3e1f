package com.nextchaptersoftware.linear.events.queue.payloads

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class LinearEventTest {
    @Test
    fun `test serialization`() {
        // Must be in base type format for polymorphic serialization to work
        val linearEvent: LinearEvent = LinearEvent.LinearWebhookEvent(
            payload = "blah",
            signature = "blah",
        )
        assertThat(linearEvent.encode().decode<LinearEvent>()).isEqualTo(linearEvent)
    }
}
