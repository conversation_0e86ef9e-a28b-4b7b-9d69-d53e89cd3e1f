package com.nextchaptersoftware.pr.ingestion

import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.maintenance.scm.ScmMembershipMaintenance
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestCommentThreadProvider
import com.nextchaptersoftware.pr.ingestion.providers.PullRequestIngestionServiceProvider
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestArchiveEventService
import com.nextchaptersoftware.scm.Scm
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class PullRequestIngestionHandler(
    private val scmMembershipMaintenance: ScmMembershipMaintenance,
    private val prCommentThreadProvider: PullRequestCommentThreadProvider,
    private val prIngestionServiceProvider: PullRequestIngestionServiceProvider,
    private val prReviewService: PullRequestReviewService,
    private val pullRequestService: PullRequestService,
    private val pullRequestArchiveEventService: PullRequestArchiveEventService,
    private val threadRelevancy: ThreadRelevancy = ThreadRelevancyImpl(),
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val repoStore: RepoStore = Stores.repoStore,
) {
    suspend fun ingest(
        repoId: RepoId,
        prNumber: Int,
        createThreadUnreads: Boolean,
    ) = withLoggingContextAsync(
        "repoId" to repoId,
        "prNumber" to prNumber,
    ) {
        val repo = repoStore.findById(repoId = repoId) ?: return@withLoggingContextAsync
        val scmTeamDAO = scmTeamStore.findDAOById(teamId = repo.teamId) ?: return@withLoggingContextAsync
        val scmTeam = scmTeamDAO.asDataModel()
        val scm = Scm.fromTeam(scmTeam)

        val (pullRequest, reviews, threads) = prCommentThreadProvider.get(scmTeam = scmTeam, repo = repo, scm = scm, prNumber = prNumber)
            ?: return@withLoggingContextAsync

        LOGGER.debugAsync(
            "reviewsCount" to reviews.size,
            "threadsCount" to threads.size,
        ) {
            "Ingesting PR"
        }

        val reviewAuthors = reviews.map { it.user }
        val commentAuthors = threads.flatMap { it.comments }.mapNotNull { it.user }
        val scmMemberProvider = scmMembershipMaintenance.addMembers(scmTeam = scmTeam, scmUsers = reviewAuthors + commentAuthors)

        val service = prIngestionServiceProvider.get(
            scmTeam = scmTeamDAO,
            repo = repo,
            pullRequest = pullRequest,
            scmMemberProvider = scmMemberProvider,
            createThreadUnreads = createThreadUnreads,
        )

        reviews.forEach {
            prReviewService.upsert(
                scm = scm,
                scmTeam = scmTeamDAO,
                repo = repo,
                pullRequest = pullRequest,
                reviewAuthorId = scmMemberProvider.getMemberId(it.user),
                reviewAuthorOrgMemberId = scmMemberProvider.getOrgMemberId(it.user),
                review = it,
                scmMemberProvider = scmMemberProvider,
                createThreadUnreads = createThreadUnreads,
            )
        }

        threads.forEach {
            service.ingestThread(scm = scm, prThread = it)
        }

        val orgId = scmTeamStore.getOrgId(teamId = scmTeam.id)

        pullRequestService.markIngested(pullRequest = pullRequest)
        pullRequestArchiveEventService.sendEvent(teamId = scmTeam.id, pullRequestId = pullRequest.id.value)
        threadRelevancy.archiveLowRelevanceThreadsForPullRequest(orgId = orgId, pullRequestId = pullRequest.id.value)

        LOGGER.debugAsync { "Finished ingesting PR" }
    }
}
