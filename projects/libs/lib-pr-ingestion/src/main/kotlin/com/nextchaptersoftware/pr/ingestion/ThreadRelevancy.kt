package com.nextchaptersoftware.pr.ingestion

import com.nextchaptersoftware.common.model.Message.MessageBody
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ArchivedReason
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.Message
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.MessageStore
import com.nextchaptersoftware.db.stores.SourceMarkStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.ThreadPrivacyContext
import com.nextchaptersoftware.db.stores.ThreadStore
import com.nextchaptersoftware.markdown.MessageBodyConverter.asMarkdown
import com.nextchaptersoftware.markdown.MessageBodyExtensions.hasSubstantiveContent
import com.nextchaptersoftware.scm.utils.CommentClassifier
import com.nextchaptersoftware.utils.KotlinUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.isActive
import org.jetbrains.exposed.sql.Transaction

interface ThreadRelevancy {
    suspend fun archiveLowRelevanceThreadsForPullRequest(orgId: OrgId, pullRequestId: PullRequestId)

    suspend fun archiveLowRelevanceThread(orgId: OrgId, threadId: ThreadId)
}

class NoOpThreadRelevancy : ThreadRelevancy {
    override suspend fun archiveLowRelevanceThreadsForPullRequest(orgId: OrgId, pullRequestId: PullRequestId) {
        KotlinUtils.doNothing()
    }

    override suspend fun archiveLowRelevanceThread(orgId: OrgId, threadId: ThreadId) {
        KotlinUtils.doNothing()
    }
}

class ThreadRelevancyImpl(
    private val threadStore: ThreadStore = Stores.threadStore,
    private val messageStore: MessageStore = Stores.messageStore,
    private val memberStore: MemberStore = Stores.memberStore,
    private val sourceMarkStore: SourceMarkStore = Stores.sourceMarkStore,
) : ThreadRelevancy {
    companion object {
        private const val MIN_WORD_COUNT = 15
    }

    suspend fun archiveLowRelevanceThreads(
        job: CoroutineScope,
        orgId: OrgId,
        repoId: RepoId,
    ) {
        val ignoredMembers = memberStore.ignoreMembersContent(orgId = orgId)

        if (!job.isActive) return
        val threadIds = threadStore.getPRThreadIdsForRepo(orgId = orgId, repoId = repoId)

        if (!job.isActive) return
        threadIds.forEach {
            if (!job.isActive) return
            maybeArchiveLowRelevanceThread(trx = null, threadId = it, ignoredMembers = ignoredMembers)
        }
    }

    override suspend fun archiveLowRelevanceThreadsForPullRequest(
        orgId: OrgId,
        pullRequestId: PullRequestId,
    ) {
        val ignoredMembers = memberStore.ignoreMembersContent(orgId = orgId)
        threadStore.getPRThreadIds(orgId = orgId, pullRequestId = pullRequestId).forEach {
            maybeArchiveLowRelevanceThread(trx = null, threadId = it, ignoredMembers = ignoredMembers)
        }
    }

    internal suspend fun maybeArchiveLowRelevanceThread(
        trx: Transaction?,
        threadId: ThreadId,
        ignoredMembers: Set<MemberId>,
    ): Unit = suspendedTransaction(trx) {
        // Don't touch threads for PRs that are still open to allow notifications to continue
        val pullRequestState = threadStore.pullRequestStateForPrThread(trx = this, threadId = threadId)
        if (pullRequestState == PullRequestState.Open) {
            return@suspendedTransaction
        }

        val messages = messageStore.findForThreads(
            trx = this,
            threadIds = listOf(threadId),
        ).sortedBy { it.createdAt }

        when (val archiveReason = archivedReason(messages, ignoredMembers)) {
            null -> when (threadStore.getArchivedSystemReason(this, threadId)) {
                ArchivedReason.LowRelevance,
                ArchivedReason.IgnoredAuthor,
                ArchivedReason.AutogeneratedComment,
                -> { // Restore if previously archived by system
                    sourceMarkStore.restoreByThread(this, threadId)
                    threadStore.restore(this, threadId)
                }

                ArchivedReason.User,
                ArchivedReason.Outdated,
                ArchivedReason.PrClosed,
                ArchivedReason.Incorrect,
                null,
                -> {
                } // Don't do anything for these reasons
            }

            else -> {
                sourceMarkStore.archiveByThread(this, threadId)
                threadStore.archive(
                    trx = this,
                    threadId = threadId,
                    archivedBy = null,
                    archivedReason = archiveReason,
                )
            }
        }
    }

    override suspend fun archiveLowRelevanceThread(
        orgId: OrgId,
        threadId: ThreadId,
    ) {
        val ignoredMembers = memberStore.ignoreMembersContent(orgId = orgId)

        suspendedTransaction {
            val thread = threadStore.find(trx = this, orgId = orgId, id = threadId, privacyContext = ThreadPrivacyContext.IgnorePrivacy)
                ?: return@suspendedTransaction
            if (thread.pullRequestId == null) {
                return@suspendedTransaction
            }

            maybeArchiveLowRelevanceThread(trx = this, threadId = threadId, ignoredMembers = ignoredMembers)
        }
    }

    private fun archivedReason(
        originalMessages: List<Message>,
        ignoredMembers: Set<MemberId>,
    ): ArchivedReason? {
        val messages = originalMessages.toMutableList()

        // Consider only messages from authentic authors
        messages.removeIf { ignoredMembers.contains(it.authorId) }
        if (messages.isEmpty()) {
            return ArchivedReason.IgnoredAuthor
        }

        // Consider only messages that are substantive
        messages.removeIf { !MessageBody.parseFrom(it.content).hasSubstantiveContent(MIN_WORD_COUNT) }
        if (messages.isEmpty()) {
            return ArchivedReason.LowRelevance
        }

        // Consider only messages that contain authentic content
        messages.removeIf { !isAuthenticContent(it) }
        if (messages.isEmpty()) {
            return ArchivedReason.AutogeneratedComment
        }

        return null
    }

    fun isAuthenticContent(message: Message): Boolean {
        val contents = MessageBody.parseFrom(message.content).asMarkdown()
        return CommentClassifier.isAuthentic(contents)
    }
}
