package com.nextchaptersoftware.pr.ingestion.queue.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.PullRequestCommentId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestReviewId
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.pr.ingestion.UnblockedPRCommentService
import com.nextchaptersoftware.scm.queue.payloads.UnblockedPRCommentMessagePayload
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class UnblockedPRCommentEventHandler(
    private val unblockedPRCommentService: UnblockedPRCommentService,
) : EventHandler {
    override suspend fun handle(event: String): Boolean {
        val pullRequestEvent = runSuspendCatching {
            event.decode<UnblockedPRCommentMessagePayload>()
        }.getOrElse {
            LOGGER.errorAsync(it) { "Failed to deserialize UnblockedPRCommentMessagePayload" }
            return@handle false
        }

        when (pullRequestEvent.type) {
            UnblockedPRCommentMessagePayload.Type.Comment -> {
                unblockedPRCommentService.handleComment(commentId = pullRequestEvent.id.let(::PullRequestCommentId))
            }

            UnblockedPRCommentMessagePayload.Type.Review -> {
                unblockedPRCommentService.handleReview(reviewId = pullRequestEvent.id.let(::PullRequestReviewId))
            }

            UnblockedPRCommentMessagePayload.Type.ReviewComment -> {
                unblockedPRCommentService.handle(messageId = pullRequestEvent.id.let(::MessageId))
            }

            UnblockedPRCommentMessagePayload.Type.PullRequest -> {
                unblockedPRCommentService.handlePullRequest(pullRequestId = pullRequestEvent.id.let(::PullRequestId))
            }
        }

        return true
    }
}
