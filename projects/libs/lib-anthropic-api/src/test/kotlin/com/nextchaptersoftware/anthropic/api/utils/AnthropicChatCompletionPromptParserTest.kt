package com.nextchaptersoftware.anthropic.api.utils

import com.nextchaptersoftware.anthropic.api.models.AnthropicChatRole
import com.nextchaptersoftware.test.utils.TestUtils.getResource
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AnthropicChatCompletionPromptParserTest {
    @Test
    fun `real world example`() {
        val prompt = getResource(this, "/real_world.prompt")
        val parsed = AnthropicChatCompletionPromptParser.messageBundleFromPrompt(prompt, respondWithJson = false)
        assertThat(parsed.systemPrompt).isNotNull()
        assertThat(parsed.chatMessages).hasSize(9)
        assertThat(parsed.chatMessages.none { it.content.isBlank() }).isTrue()
        assertThat(parsed.chatMessages.first().role).isEqualTo(AnthropicChatRole.USER)
        assertThat(parsed.chatMessages.last().role).isEqualTo(AnthropicChatRole.USER)
    }

    @Test
    fun `real world example with json output`() {
        val prompt = getResource(this, "/real_world.prompt")
        val parsed = AnthropicChatCompletionPromptParser.messageBundleFromPrompt(prompt, respondWithJson = true)
        assertThat(parsed.systemPrompt).isNotNull()
        assertThat(parsed.chatMessages).hasSize(10)
        assertThat(parsed.chatMessages.none { it.content.isBlank() }).isTrue()
        assertThat(parsed.chatMessages.first().role).isEqualTo(AnthropicChatRole.USER)
        assertThat(parsed.chatMessages.last().role).isEqualTo(AnthropicChatRole.ASSISTANT)
    }
}
