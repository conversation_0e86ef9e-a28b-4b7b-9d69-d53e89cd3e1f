package com.nextchaptersoftware.integration.data.events.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.integration.data.events.queue.payloads.ScmDataEvent
import com.nextchaptersoftware.scm.data.services.ScmPullRequestCommentDataPersistenceService

class ScmPullRequestCommentDataEventHandler(
    private val scmPullRequestCommentDataPersistenceService: ScmPullRequestCommentDataPersistenceService,
) : TypedEventHandler<ScmDataEvent.ScmPullRequestCommentDataEvent> {
    override suspend fun handle(event: ScmDataEvent.ScmPullRequestCommentDataEvent): Boolean {
        scmPullRequestCommentDataPersistenceService.persist(
            orgId = event.orgId,
            repoId = event.repoId,
            pullRequestNumber = event.pullRequestNumber,
            pullRequestComment = event.pullRequestComment,
        )

        return true
    }
}
