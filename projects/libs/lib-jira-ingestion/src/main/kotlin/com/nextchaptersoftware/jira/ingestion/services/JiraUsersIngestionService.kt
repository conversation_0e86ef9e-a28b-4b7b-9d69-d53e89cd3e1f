package com.nextchaptersoftware.jira.ingestion.services

import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.JiraSite
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.stores.JiraSiteStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ingestion.common.services.BaseIngestionService
import com.nextchaptersoftware.ingestion.common.services.InstallationIdentityService
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.api.JiraUsersApi
import com.nextchaptersoftware.jira.models.User
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.RedisLock
import io.ktor.http.Url
import mu.KotlinLogging
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.and

private val LOGGER = KotlinLogging.logger {}

class JiraUsersIngestionService(
    lockProvider: LockProvider,
    private val atlassianTokenProvider: AtlassianTokenProvider,
    private val jiraApiProvider: JiraApiProvider,
    private val jiraMemberMaintenance: JiraMemberMaintenance,
    private val jiraProjectAccessIngestionService: JiraProjectAccessIngestionService,
    private val memberStore: MemberStore = Stores.memberStore,
    private val jiraSiteStore: JiraSiteStore = Stores.jiraSiteStore,
    private val identityService: InstallationIdentityService = InstallationIdentityService(),
) : BaseIngestionService(
    provider = Provider.Jira,
    lockProvider = lockProvider,
) {
    override suspend fun ingest(
        installation: Installation,
        lock: RedisLock,
    ) {
        val site = jiraSiteStore.findForInstallation(installationId = installation.id) ?: run {
            LOGGER.errorAsync { "JiraUsersIngestionService.ingest: site not found" }
            return
        }

        sync(installation = installation, site = site)
    }

    suspend fun sync(
        installation: Installation,
        site: JiraSite,
    ) = withLoggingContextAsync(
        "orgId" to installation.orgId,
        "installationId" to installation.id,
        "siteId" to site.id,
    ) {
        syncAdmins(installation = installation, site = site)
        syncUsers(installation = installation, site = site)
        jiraProjectAccessIngestionService.syncProjectAccess(installation = installation, site = site)
    }

    suspend fun syncUsers(
        installation: Installation,
        site: JiraSite,
    ): Set<User> = withLoggingContextAsync(
        "orgId" to installation.orgId,
        "installationId" to installation.id,
        "siteId" to site.id,
    ) {
        // Iterate over all connected identities until we find one with access
        val users = identityService.getConnectedIdentities(installationId = installation.id).firstNotNullOfOrNull { identity ->
            runSuspendCatching {
                getUsers(orgId = installation.orgId, baseUrl = site.userAuthBaseUrl, identity = identity)
            }.getOrNull()
        } ?: run {
            LOGGER.warnAsync { "Could not fetch users for installation" }
            return@withLoggingContextAsync emptySet()
        }

        LOGGER.debugAsync { "Ingesting Jira users" }

        jiraMemberMaintenance.addMembers(installation = installation, users = users.toList())

        return@withLoggingContextAsync users
    }

    private suspend fun getUsers(
        orgId: OrgId,
        baseUrl: Url,
        identity: Identity,
    ): Set<User> {
        val users = mutableSetOf<User>()
        var url: Url? = JiraUsersApi.Companion.urlUsers(baseUrl = baseUrl)

        var tokens = atlassianTokenProvider.getTokens(identityId = identity.id, provider = identity.provider)
        while (url != null && tokens != null) {
            val results = jiraApiProvider.jiraUsersApi.getUsers(orgIds = setOf(orgId), url = url, tokens = tokens)
            users.addAll(results)
            url = when (results.isNotEmpty()) {
                true -> JiraUsersApi.Companion.urlUsers(baseUrl = baseUrl, startAt = users.size)
                else -> null
            }
            tokens = atlassianTokenProvider.getTokens(identityId = identity.id, provider = identity.provider)
        }

        return users
    }

    suspend fun syncAdmins(
        installation: Installation,
        site: JiraSite,
    ) = withLoggingContextAsync(
        "orgId" to installation.orgId,
        "installationId" to installation.id,
        "siteId" to site.id,
    ) {
        LOGGER.debugAsync { "Syncing Jira admins" }

        memberStore.findByInstallationId(
            installationId = installation.id,
            additionalWhereClause = IdentityModel.rawAccessToken.isNotNull() and IdentityModel.person.isNotNull(),
        ).forEach { (member, identity) ->
            val myself = runSuspendCatching {
                val tokens = atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)
                tokens?.let {
                    jiraApiProvider.jiraMyselfApi.getMyself(
                        orgIds = setOf(installation.orgId),
                        baseUrl = site.userAuthBaseUrl,
                        tokens = tokens,
                    )
                }
            }.getOrElse {
                LOGGER.warnAsync(it, "memberId" to member.id, "identityId" to identity.id) {
                    "Failed to get user for team member"
                }
                null
            } ?: return@forEach

            val providerRole = when (myself.groups?.isSiteAdmin) {
                true -> ProviderRole.Owner
                else -> ProviderRole.Read
            }

            memberStore.setProviderRole(
                id = member.id,
                providerRole = providerRole,
            )
        }
    }
}
