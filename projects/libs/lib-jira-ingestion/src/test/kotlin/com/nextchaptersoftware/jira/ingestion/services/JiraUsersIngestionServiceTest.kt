package com.nextchaptersoftware.jira.ingestion.services

import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgAndMemberAndIdentity
import com.nextchaptersoftware.ingestion.common.services.InstallationIdentityService
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.api.JiraMyselfApi
import com.nextchaptersoftware.jira.api.JiraUsersApi
import com.nextchaptersoftware.jira.models.User
import com.nextchaptersoftware.test.mokito.MockitoExtensions.eqValue
import com.sksamuel.hoplite.Secret
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull

class JiraUsersIngestionServiceTest {
    private val atlassianTokenProvider: AtlassianTokenProvider = mock()
    private val usersApi = mock<JiraUsersApi>()
    private val myselfApi = mock<JiraMyselfApi>()
    private val jiraMemberMaintenance = mock<JiraMemberMaintenance>()
    private val memberStore = mock<MemberStore>()
    private val identityService = mock<InstallationIdentityService>()

    private val service = JiraUsersIngestionService(
        lockProvider = mock(),
        jiraApiProvider = mock<JiraApiProvider>().also {
            `when`(it.jiraUsersApi).thenReturn(usersApi)
            `when`(it.jiraMyselfApi).thenReturn(myselfApi)
        },
        jiraMemberMaintenance = jiraMemberMaintenance,
        jiraProjectAccessIngestionService = mock(),
        memberStore = memberStore,
        identityService = identityService,
        atlassianTokenProvider = atlassianTokenProvider,
    )

    private val identity = MockDataClasses.identity(rawAccessToken = Ciphertext("asdf".toByteArray()))
    private val installation = MockDataClasses.baseInstallation(provider = Provider.Jira)
    private val site = MockDataClasses.jiraSite(installationId = installation.id)

    private val mockInstallationOAuthTokens = OAuthTokens(accessToken = Secret("installation"))
    private val mockIdentityOAuthTokens = OAuthTokens(accessToken = Secret("identity"))

    private suspend fun setup() {
        `when`(
            atlassianTokenProvider.getTokens(
                installationId = installation.id,
            ),
        ).thenReturn(mockInstallationOAuthTokens)

        `when`(
            atlassianTokenProvider.getTokens(
                identityId = eqValue(identity.id),
                provider = any(),
            ),
        ).thenReturn(mockIdentityOAuthTokens)
    }

    @Test
    fun syncUsers() = runTest {
        setup()

        val url = JiraUsersApi.urlUsers(baseUrl = site.userAuthBaseUrl)
        val nextPage = JiraUsersApi.urlUsers(baseUrl = site.userAuthBaseUrl, startAt = 1)
        val nextNextPage = JiraUsersApi.urlUsers(baseUrl = site.userAuthBaseUrl, startAt = 2)

        val userA = User(accountId = "10000", accountType = "atlassian", active = true, avatarUrls = mock())
        val userB = User(accountId = "10001", accountType = "app", active = true, avatarUrls = mock())

        `when`(identityService.getConnectedIdentities(installationId = installation.id))
            .thenReturn(listOf(identity))

        `when`(usersApi.getUsers(orgIds = setOf(installation.orgId), url = url, tokens = mockIdentityOAuthTokens)).thenReturn(
            listOf(userA),
        )

        `when`(usersApi.getUsers(orgIds = setOf(installation.orgId), url = nextPage, tokens = mockIdentityOAuthTokens)).thenReturn(
            listOf(userB),
        )

        `when`(usersApi.getUsers(orgIds = setOf(installation.orgId), url = nextNextPage, tokens = mockIdentityOAuthTokens)).thenReturn(
            emptyList(),
        )

        val result = service.syncUsers(
            installation = installation,
            site = site,
        )
        assertThat(result).containsExactlyInAnyOrder(userA, userB)

        verify(jiraMemberMaintenance, times(1)).addMembers(
            installation = installation,
            users = listOf(userA, userB),
        )
    }

    @Test
    fun syncAdmins() = runTest {
        setup()

        val userA = User(accountId = "10000", accountType = "atlassian", active = true, avatarUrls = mock())

        val org = MockDataClasses.org()
        val member = MockDataClasses.member()
        `when`(
            memberStore.findByInstallationId(
                trx = anyOrNull(),
                installationId = any(),
                additionalWhereClause = any(),
                limit = anyOrNull(),
            ),
        ).thenReturn(
            listOf(OrgAndMemberAndIdentity(member = member, identity = identity, org = org)),
        )

        val siteAdminGroup = User.Groups.Group(name = "site-admins", groupId = "asdf")
        `when`(
            myselfApi.getMyself(
                orgIds = setOf(installation.orgId),
                baseUrl = site.userAuthBaseUrl,
                tokens = mockIdentityOAuthTokens,
            ),
        ).thenReturn(
            userA.copy(groups = User.Groups(items = listOf(siteAdminGroup))),
        )

        service.syncAdmins(installation = installation, site = site)

        verify(memberStore, times(1)).setProviderRole(
            id = member.id,
            providerRole = ProviderRole.Owner,
        )
    }
}
