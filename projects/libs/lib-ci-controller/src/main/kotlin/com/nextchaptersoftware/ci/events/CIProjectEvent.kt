package com.nextchaptersoftware.ci.events

import com.nextchaptersoftware.db.models.CIProjectId
import com.nextchaptersoftware.db.models.CITokenId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class CIProjectEvent {

    @SerialName("CIProjectInstallEvent")
    @Serializable
    data class Install(
        val orgId: OrgId,
        val ciInstallationId: InstallationId,
        val ciProjectId: CIProjectId,
    ) : CIProjectEvent()

    @SerialName("CIProjectUninstallEvent")
    @Serializable
    data class Uninstall(
        val orgId: OrgId,
        val ciInstallationId: InstallationId,
        val ciProjectId: CIProjectId,
    ) : CIProjectEvent()

    @SerialName("CIProjectRefreshEvent")
    @Serializable
    data class Refresh(
        val orgId: OrgId,
        val ciInstallationId: InstallationId,
        val ciTokenId: CITokenId,
        val forceRefresh: Boolean,
    ) : CIProjectEvent()
}
