package com.nextchaptersoftware.documents.embedding.services

import com.nextchaptersoftware.compress.CompressionBase64
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.EmbeddingModel
import com.nextchaptersoftware.db.models.EmbeddingPlatform
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.embedding.models.EmbeddingDocumentKey
import com.nextchaptersoftware.embedding.models.EmbeddingMetadata
import com.nextchaptersoftware.embedding.models.EmbeddingPayload
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.ml.embedding.input.EmbeddingInput
import com.nextchaptersoftware.ml.embedding.input.embeddingInputBuilder
import java.util.UUID

class PullRequestEmbeddingService(
    private val documentEmbeddingService: DocumentEmbeddingService,
    private val pullRequestInsightIndexContentService: PullRequestInsightIndexContentService,
) : BaseDocumentEmbeddingService() {

    suspend fun upsertSourceDocument(
        orgId: OrgId,
        sourceDocumentId: UUID,
    ) {
        getEmbeddingPayload(orgId = orgId, documentId = sourceDocumentId)?.let { embeddingPayload ->
            upsertSourceDocument(
                orgId = orgId,
                embeddingPayload = embeddingPayload,
            )
        }
    }

    private suspend fun getEmbeddingPayload(
        orgId: OrgId,
        documentId: UUID,
    ): EmbeddingPayload? {
        val pullRequestInsightIndexContentModel = pullRequestInsightIndexContentService.getPullRequestInsightContent(
            orgId = orgId,
            pullRequestId = documentId.let(::PullRequestId),
            asMarkdown = true,
            minimal = true,
        ) ?: return null

        val content = embeddingInputBuilder {
            pullRequestInsightIndexContentModel.embeddableContents.filter { it.isNotEmpty() }.forEach {
                document(EmbeddingInput(it))
            }
        }.build().let { CompressionBase64.compress(it) }

        val metadata = EmbeddingMetadata(
            documentGroup = pullRequestInsightIndexContentModel.groupId,
            documentInstallation = pullRequestInsightIndexContentModel.installationId.value,
            documentOrg = orgId.value,
            documentSource = pullRequestInsightIndexContentModel.provider,
            documentType = DocumentType.PullRequest,
            insightType = InsightType.PullRequest,
            timestamp = pullRequestInsightIndexContentModel.mergedAt ?: pullRequestInsightIndexContentModel.createdAt,
        )

        return EmbeddingPayload(
            content = content,
            metadata = metadata,
            key = EmbeddingDocumentKey(
                sourceId = documentId,
                groupId = pullRequestInsightIndexContentModel.groupId,
                installationId = InsightType.PullRequest.dbOrdinalAsId(), // correct, we do not use installationId in the key for pull requests
            ),
        )
    }

    override suspend fun upsertSourceDocument(
        orgId: OrgId,
        embeddingPlatform: EmbeddingPlatform,
        embeddingModel: EmbeddingModel,
        embeddingPayload: EmbeddingPayload,
    ) {
        documentEmbeddingService.upsertSourceDocument(
            orgId = orgId,
            embeddingPlatform = embeddingPlatform,
            embeddingModel = embeddingModel,
            embeddingPayload = embeddingPayload,
        )
    }

    override suspend fun deleteSourceDocument(
        orgId: OrgId,
        embeddingPlatform: EmbeddingPlatform,
        embeddingModel: EmbeddingModel,
        embeddingDocumentKey: EmbeddingDocumentKey,
    ) {
        documentEmbeddingService.deleteSourceDocument(
            orgId = orgId,
            embeddingPlatform = embeddingPlatform,
            embeddingModel = embeddingModel,
            embeddingDocumentKey = embeddingDocumentKey,
        )
    }
}
