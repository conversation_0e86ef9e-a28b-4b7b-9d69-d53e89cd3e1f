package com.nextchaptersoftware.documents.embedding.services

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.Stores.mlSettingsStore
import com.nextchaptersoftware.embedding.models.EmbeddingDocumentKey
import com.nextchaptersoftware.embedding.models.EmbeddingPayload
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync

abstract class BaseDocumentEmbeddingService : DocumentEmbeddingService {

    override suspend fun upsertSourceDocument(
        orgId: OrgId,
        embeddingPayload: EmbeddingPayload,
    ) = withLoggingContextAsync(
        "orgId" to orgId,
        "sourceDocumentId" to embeddingPayload.key.sourceId,
    ) {
        mlSettingsStore.getWriteEmbeddingPlatforms(orgId = orgId).forEach { embeddingPlatform ->
            mlSettingsStore.getWriteEmbeddingModels(orgId = orgId).forEach { embeddingModel ->
                upsertSourceDocument(
                    orgId = orgId,
                    embeddingModel = embeddingModel,
                    embeddingPlatform = embeddingPlatform,
                    embeddingPayload = embeddingPayload,
                )
            }
        }
    }

    override suspend fun deleteSourceDocument(
        orgId: OrgId,
        embeddingDocumentKey: EmbeddingDocumentKey,
    ) {
        mlSettingsStore.getWriteEmbeddingPlatforms(orgId = orgId).forEach { embeddingPlatform ->
            mlSettingsStore.getWriteEmbeddingModels(orgId = orgId).forEach { embeddingModel ->
                deleteSourceDocument(
                    orgId = orgId,
                    embeddingModel = embeddingModel,
                    embeddingPlatform = embeddingPlatform,
                    embeddingDocumentKey = embeddingDocumentKey,
                )
            }
        }
    }
}
