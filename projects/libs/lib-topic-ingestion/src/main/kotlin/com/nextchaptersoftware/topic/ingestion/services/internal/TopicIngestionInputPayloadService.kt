package com.nextchaptersoftware.topic.ingestion.services.internal

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.ingestion.utils.AmazonSageMakerUtils
import com.sksamuel.hoplite.Secret
import io.ktor.http.URLBuilder
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * WARNING:
 * Ensure all fields are serialized to strings.
 * Otherwise, you will get errors like this:
 * [The value for the field 'BLAH' must be a STRING]
 */
@Serializable
data class ProcessEnvironment(
    @SerialName("PROCESS_ORG_ID")
    @Contextual
    val orgId: OrgId,

    @SerialName("PROCESS_REPO_ID")
    @Contextual
    val repoId: RepoId,

    @SerialName("PROCESS_REPO_HTTP_CLONE_URL")
    @Contextual
    val repoHttpCloneUrl: Url,

    @SerialName("PROCESS_REPO_HTTP_PROXY_URL")
    @Contextual
    val repoHttpProxyUrl: Url?,

    @SerialName("PROCESS_REPO_CLONE_AUTH_0")
    val repoCloneAuth0: String?,

    @SerialName("PROCESS_REPO_CLONE_AUTH_1")
    val repoCloneAuth1: String?,

    @SerialName("PROCESS_REPO_CLONE_AUTH_2")
    val repoCloneAuth2: String?,

    @SerialName("PROCESS_REPO_CLONE_AUTH_3")
    val repoCloneAuth3: String?,

    @SerialName("PROCESS_REPO_CLONE_AUTH_4")
    val repoCloneAuth4: String?,

    @SerialName("PROCESS_REPO_CLONE_AUTH_5")
    val repoCloneAuth5: String?,

    @SerialName("PROCESS_PINECONE_HYBRID_INDEX")
    val pineconeHybridIndex: String,

    @SerialName("PROCESS_PINECONE_NAMESPACE")
    val pineconeNamespace: String,
)

@Serializable
data class TopicIngestion(
    @SerialName("ProcessingJobName")
    val processingJobName: String,
    @SerialName("ProcessOutput")
    val processOutput: String,
    @SerialName("ProcessEnvironment")
    val processEnvironment: ProcessEnvironment,
)

@Serializable
data class TopicIngestionInputPayload(
    @SerialName("TeamId")
    @Contextual
    val orgId: OrgId,
    @SerialName("RepoId")
    @Contextual
    val repoId: RepoId,
    @SerialName("TopicIngestion")
    val topicIngestion: TopicIngestion,
)

class TopicIngestionInputPayloadService(
    private val keyProvider: TopicIngestionPipelineKeyProvider = TopicIngestionPipelineKeyProvider(),
) {
    data class TopicIngestionInputPayloadConfig(
        val topicS3Bucket: String,
        val orgId: OrgId,
        val topicIngestionId: UUID,
        val repoId: RepoId,
        val repoHttpCloneUrl: Url,
        val repoHttpProxyUrl: Url?,
        val repoCloneAuth: Secret?,
        val pineconeHybridIndex: String,
        val pineconeNamespace: String,
    ) {
        val identifier by lazy {
            topicIngestionId.toString()
        }
    }

    private fun generateS3Uri(
        orgId: OrgId,
        repoId: RepoId,
        bucket: String,
        vararg segments: String,
    ): Url {
        return URLBuilder(
            protocol = URLProtocol.createOrDefault("s3"),
            host = bucket,
            pathSegments = keyProvider.provide(orgId = orgId, repoId = repoId, *segments),
        ).build()
    }

    private fun generateTopicIngestion(
        processEnvironment: ProcessEnvironment,
        config: TopicIngestionInputPayloadConfig,
    ): TopicIngestion {
        return TopicIngestion(
            // Must be less than 63 characters
            processingJobName = "TopicIngestion-${config.identifier}",
            processOutput = generateS3Uri(
                orgId = config.orgId,
                repoId = config.repoId,
                bucket = config.topicS3Bucket,
                "topic-ingestion",
                "output",
                config.identifier,
            ).toString(),
            processEnvironment = processEnvironment,
        )
    }

    @Suppress("MagicNumber")
    private fun generateProcessEnvironment(
        config: TopicIngestionInputPayloadConfig,
    ): ProcessEnvironment {
        // HACK:
        //  AWS SageMaker has a hard constraint that vars must under 256 chars.
        //  The clone repo auth is a JWT token and some providers exceed this threshold,
        //  in order to avoid hitting the limit we will split the token into fragments,
        //  which we will re-join on the other side.

        val repoCloneAuthChunks = config.repoCloneAuth?.value
            ?.let(AmazonSageMakerUtils::chunk)
            ?.also {
                check(it.size <= 6) {
                    "repo clone auth is larger than expected"
                }
            }

        return ProcessEnvironment(
            orgId = config.orgId,
            repoId = config.repoId,
            repoHttpCloneUrl = config.repoHttpCloneUrl,
            repoHttpProxyUrl = config.repoHttpProxyUrl,
            repoCloneAuth0 = repoCloneAuthChunks?.getOrNull(0),
            repoCloneAuth1 = repoCloneAuthChunks?.getOrNull(1),
            repoCloneAuth2 = repoCloneAuthChunks?.getOrNull(2),
            repoCloneAuth3 = repoCloneAuthChunks?.getOrNull(3),
            repoCloneAuth4 = repoCloneAuthChunks?.getOrNull(4),
            repoCloneAuth5 = repoCloneAuthChunks?.getOrNull(5),
            pineconeHybridIndex = config.pineconeHybridIndex,
            pineconeNamespace = config.pineconeNamespace,
        )
    }

    fun generatePayload(
        config: TopicIngestionInputPayloadConfig,
    ): TopicIngestionInputPayload {
        val processEnvironment = generateProcessEnvironment(config = config)
        val topicIngestion = generateTopicIngestion(
            config = config,
            processEnvironment = processEnvironment,
        )

        return TopicIngestionInputPayload(
            orgId = config.orgId,
            repoId = config.repoId,
            topicIngestion = topicIngestion,
        )
    }
}
