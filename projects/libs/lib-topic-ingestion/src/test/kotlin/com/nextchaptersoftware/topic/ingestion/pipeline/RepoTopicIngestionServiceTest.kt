package com.nextchaptersoftware.topic.ingestion.pipeline

import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.UserEngagementModel
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Instant
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.deleteWhere
import org.jetbrains.exposed.sql.update
import org.junit.jupiter.api.Test

class RepoTopicIngestionServiceTest : DatabaseTestsBase() {
    private val repoTopicIngestionService = RepoTopicIngestionService(
        ingestionInterval = 2.days,
        maxReposPerTeam = 5,
        lastActiveAtThreshold = 720.days,
    )
    private val scmTeamStore = Stores.scmTeamStore

    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO

    private suspend fun setup() {
        org = ModelBuilders.makeOrg()
        scmTeam = ModelBuilders.makeScmTeam(org = org)
        ModelBuilders.makeUserEngagement(org = org)
    }

    @Test
    fun `selectRepoToIngest returns null when no repos`() = suspendingDatabaseTest {
        setup()
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest()).isEmpty()
    }

    @Test
    fun `selectRepoToIngest returns null when no repos to ingest`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        // ineligible, as not connected/selected
        makeRepo(scmTeam = scmTeam, isScmConnected = false)
        makeRepo(scmTeam = scmTeam, isUserSelected = false)

        // ineligible, as ingested recently
        makeRepo(scmTeam = scmTeam, topicIngestLastStartAt = now - 1.days)

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest()).isEmpty()
    }

    @Test
    @Suppress("LongMethod")
    fun `selectRepoToIngest returns only top 5 repos with latestAt`() = suspendingDatabaseTest {
        setup()
        val repo1 = makeRepo(
            scmTeam = scmTeam,
            createdAt = Instant.nowWithMicrosecondPrecision() - 1.days,
            lastActiveAt = Instant.nowWithMicrosecondPrecision() - 1.days,
        )
        val repo2 = makeRepo(
            scmTeam = scmTeam,
            createdAt = Instant.nowWithMicrosecondPrecision() - 2.days,
            lastActiveAt = Instant.nowWithMicrosecondPrecision() - 2.days,
        )
        val repo3 = makeRepo(
            scmTeam = scmTeam,
            createdAt = Instant.nowWithMicrosecondPrecision() - 3.days,
            lastActiveAt = Instant.nowWithMicrosecondPrecision() - 3.days,
        )
        val repo4 = makeRepo(
            scmTeam = scmTeam,
            createdAt = Instant.nowWithMicrosecondPrecision() - 4.days,
            lastActiveAt = Instant.nowWithMicrosecondPrecision() - 4.days,
        )
        val repo5 = makeRepo(
            scmTeam = scmTeam,
            createdAt = Instant.nowWithMicrosecondPrecision() - 5.days,
            lastActiveAt = Instant.nowWithMicrosecondPrecision() - 5.days,
        )
        makeRepo(
            scmTeam = scmTeam,
            createdAt = Instant.nowWithMicrosecondPrecision() - 100.days,
            lastActiveAt = Instant.nowWithMicrosecondPrecision() - 100.days,
        )

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo5.idValue)
        suspendedTransaction {
            RepoDAO.findById(repo5.id)?.topicIngestLastStartAt = Instant.nowWithMicrosecondPrecision()
        }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo4.idValue)
        suspendedTransaction {
            RepoDAO.findById(repo4.id)?.topicIngestLastStartAt = Instant.nowWithMicrosecondPrecision()
        }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo3.idValue)
        suspendedTransaction {
            RepoDAO.findById(repo3.id)?.topicIngestLastStartAt = Instant.nowWithMicrosecondPrecision()
        }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo2.idValue)
        suspendedTransaction {
            RepoDAO.findById(repo2.id)?.topicIngestLastStartAt = Instant.nowWithMicrosecondPrecision()
        }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo1.idValue)
        suspendedTransaction {
            RepoDAO.findById(repo1.id)?.topicIngestLastStartAt = Instant.nowWithMicrosecondPrecision()
        }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isNull()
    }

    @Test
    fun `selectRepoToIngest skips repos from soft deleted teams`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        val repo = makeRepo(scmTeam = scmTeam, createdAt = now - 8.days)
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo.idValue)

        scmTeamStore.deleteTeam(trx = null, scmTeam.idValue)
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isNull()
    }

    @Test
    fun `selectRepoToIngest skips repos from uninstalled teams`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        val repo = makeRepo(scmTeam = scmTeam, createdAt = now - 8.days)
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo.idValue)

        scmTeamStore.uninstallProvider(teamId = scmTeam.idValue, installationId = requireNotNull(scmTeam.providerExternalInstallationId))
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isNull()
    }

    @Test
    fun `selectRepoToIngest selects the oldest repo with higher lastActiveAt`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        val oldestRepo = makeRepo(scmTeam = scmTeam, createdAt = now - 10.days, lastActiveAt = now - 10.days)
        makeRepo(scmTeam = scmTeam, createdAt = now - 8.days, lastActiveAt = now - 8.days)
        makeRepo(scmTeam = scmTeam, createdAt = now - 9.days, lastActiveAt = now - 9.days)

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(oldestRepo.idValue)
    }

    @Test
    fun `selectRepoToIngest selects the least recently ingested`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        makeRepo(scmTeam = scmTeam, topicIngestLastStartAt = now - 8.days)
        val leastRecentlyIngestedRepo = makeRepo(
            scmTeam = scmTeam,
            topicIngestLastStartAt = now - 10.days,
        )
        makeRepo(scmTeam = scmTeam, topicIngestLastStartAt = now - 9.days)

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(leastRecentlyIngestedRepo.idValue)
    }

    @Test
    fun `selectRepoToIngest selects the newest repo when there are many candidates`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        makeRepo(scmTeam = scmTeam, isScmConnected = false) // ineligible, as not connected
        makeRepo(scmTeam = scmTeam, isUserSelected = false) // ineligible, as not selected
        makeRepo(scmTeam = scmTeam, topicIngestLastStartAt = now - 1.days) // ineligible, as ingested recently
        val expected = makeRepo(scmTeam = scmTeam, topicIngestLastStartAt = null) // eligible, as never ingested
        makeRepo(scmTeam = scmTeam, topicIngestLastStartAt = now - 10.days) // eligible, as not recently ingested

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(expected.idValue)
    }

    @Test
    fun `selectRepoToIngest skips repos from inactive teams`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        // active team
        val repo = makeRepo(scmTeam = scmTeam, createdAt = now - 100.days)
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo.idValue)

        // never active
        suspendedTransaction { UserEngagementModel.deleteWhere { orgId eq scmTeam.org.idValue } }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isNull()

        // last active more than DEFAULT_ACTIVE_PERIOD
        ModelBuilders.makeUserEngagement(org = org, createdAt = now - ScmTeamStore.DEFAULT_ACTIVE_PERIOD.plus(1.days))
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isNull()

        // last active less than DEFAULT_ACTIVE_PERIOD
        ModelBuilders.makeUserEngagement(org = org, createdAt = now - ScmTeamStore.DEFAULT_ACTIVE_PERIOD.minus(1.days))
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo.idValue)
    }

    @Test
    fun `selectRepoToIngest skips repos from personal teams`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        // company team
        val repo = makeRepo(scmTeam = scmTeam, createdAt = now - 100.days)
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isEqualTo(repo.idValue)

        // personal team
        suspendedTransaction {
            ScmTeamModel.update({ ScmTeamModel.id eq scmTeam.id.value }) {
                it[this.providerIsPersonalAccount] = true
            }
        }
        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest().firstOrNull()?.id).isNull()
    }

    @Test
    fun `selectRepoToIngest skips repos that are in backoff`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        makeRepo(scmTeam = scmTeam, createdAt = now, lastActiveAt = now, ingestionBackoffUntil = now + 1.days)

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest()).isEmpty()
    }

    @Test
    fun `selectRepoToIngest selects repos with an expired backoff`() = suspendingDatabaseTest {
        setup()
        val now = Instant.nowWithMicrosecondPrecision()

        makeRepo(scmTeam = scmTeam, createdAt = now, lastActiveAt = now, ingestionBackoffUntil = now - 1.minutes)

        assertThat(repoTopicIngestionService.selectRepoCandidatesToIngest()).hasSize(1)
    }
}
