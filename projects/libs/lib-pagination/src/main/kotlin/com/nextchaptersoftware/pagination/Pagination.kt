package com.nextchaptersoftware.pagination

import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.serialization.Serializable

object Pagination {

    /**
     * Streams all resources by paging sequentially through the entire collection.
     * Returns a [Batch] of items and the next URI to fetch.
     *
     * Batched streaming returns a list of items and then next cursor, to be used
     * by calling clients in cases where it is cost advantageous to cache the next
     * page API in the event that the long-running stream is interrupted.
     *
     * To use safely: the caller must process **all** of the items in the batch
     * before persisting the next page, otherwise items will be lost if there is an
     * interruption mid-batch.
     *
     * @param T the type of items emitted in the batches.
     * @param C the type of cursor used for pagination.
     * @param maxItems the maximum number of items to be retrieved, or null if no maximum is set.
     * @param initialCursor the initial cursor value to start pagination from, or null if pagination should start from the beginning.
     * @param execute a suspend function that takes a cursor and returns a batch.
     *                      This function is responsible for making the request to fetch data based on the provided cursor.
     * @return a flow emitting batches of items with associated cursors.
     */
    inline fun <reified T, C> batchStream(
        maxItems: Int? = null,
        initialCursor: C? = null,
        crossinline execute: suspend (C?, Int?) -> Batch<T, C>,
    ): Flow<Batch<T, C>> {
        return Paginated.paginate<Batch<T, C>, C>(
            maxItems = maxItems,
            initial = initialCursor,
            execute = { cursor: C?, quota: Int? ->
                val page = execute(cursor, quota)
                Paginated.Batch(
                    page = page,
                    cost = page.items.size,
                    next = page.nextCursor,
                )
            },
        )
    }

    @Serializable
    data class Batch<T, C>(
        val items: List<T>,
        val nextCursor: C?,
    )
}

/**
 * Transforms a flow of batch of items into a flow of items
 */
@OptIn(ExperimentalCoroutinesApi::class)
inline fun <reified T, C> Flow<Pagination.Batch<T, C>>.asFlatItemsFlow(): Flow<T> = flatMapConcat { batch ->
    batch.items.asFlow()
}
