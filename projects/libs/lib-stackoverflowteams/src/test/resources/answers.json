{"items": [{"owner": {"account_id": ********, "reputation": 1, "user_id": 1, "user_type": "team_admin", "profile_image": "https://www.gravatar.com/avatar/8d905e7c136dee6a22cdb5e168b069ba?s=256&d=identicon&r=PG&f=y&so-version=2", "display_name": "<PERSON>", "link": "https://stackoverflowteams.com/c/davidlam/users/1/david-lam"}, "is_accepted": false, "score": 0, "last_activity_date": **********, "last_edit_date": **********, "creation_date": **********, "answer_id": 12, "question_id": 11, "body": "<p>This is an answer EDITED AGAIN</p>\n"}, {"owner": {"account_id": -1, "reputation": 1, "user_id": -1, "user_type": "moderator", "display_name": "Community", "link": "https://stackoverflowteams.com/c/davidlam/users/-1/community"}, "is_accepted": false, "score": 0, "last_activity_date": **********, "creation_date": **********, "answer_id": 2, "question_id": 1, "body": "<p>Your reputation points are displayed in your profile and are an indication of how valuable your knowledge is to your team. You can earn points when your teammates think your question or answer was helpful. You also get points when the question asker feels that you’ve done a good job and accepts your answer.</p>\n"}, {"owner": {"account_id": -1, "reputation": 1, "user_id": -1, "user_type": "moderator", "display_name": "Community", "link": "https://stackoverflowteams.com/c/davidlam/users/-1/community"}, "is_accepted": false, "score": 0, "last_activity_date": **********, "creation_date": **********, "answer_id": 4, "question_id": 3, "body": "<p>Tags are keywords that you can add to a question or answer. Along with keywords in the question or answer itself, these tags help to organize content and make searching easier. Tags also power notifications - so if you’re interested in something, make sure to follow that tag to get the latest information.</p>\n<p>We added a few common tags to this question so you can see for yourself.</p>\n<p>Tags: <a href=\"/c/davidlam/questions/tagged/product\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;product&#39;\" aria-label=\"show questions tagged &#39;product&#39;\" rel=\"tag\" aria-labelledby=\"tag-product-tooltip-container\">product</a> <a href=\"/c/davidlam/questions/tagged/onboarding\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;onboarding&#39;\" aria-label=\"show questions tagged &#39;onboarding&#39;\" rel=\"tag\" aria-labelledby=\"tag-onboarding-tooltip-container\">onboarding</a> <a href=\"/c/davidlam/questions/tagged/training\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;training&#39;\" aria-label=\"show questions tagged &#39;training&#39;\" rel=\"tag\" aria-labelledby=\"tag-training-tooltip-container\">training</a> <a href=\"/c/davidlam/questions/tagged/development\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;development&#39;\" aria-label=\"show questions tagged &#39;development&#39;\" rel=\"tag\" aria-labelledby=\"tag-development-tooltip-container\">development</a> <a href=\"/c/davidlam/questions/tagged/scrum\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;scrum&#39;\" aria-label=\"show questions tagged &#39;scrum&#39;\" rel=\"tag\" aria-labelledby=\"tag-scrum-tooltip-container\">scrum</a> <a href=\"/c/davidlam/questions/tagged/hr\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;hr&#39;\" aria-label=\"show questions tagged &#39;hr&#39;\" rel=\"tag\" aria-labelledby=\"tag-hr-tooltip-container\">hr</a> <a href=\"/c/davidlam/questions/tagged/office-questions\" class=\"post-tag site132604 themed channel132604-tag\" title=\"show questions tagged &#39;office-questions&#39;\" aria-label=\"show questions tagged &#39;office-questions&#39;\" rel=\"tag\" aria-labelledby=\"tag-office-questions-tooltip-container\">office-questions</a></p>\n"}, {"owner": {"account_id": -1, "reputation": 1, "user_id": -1, "user_type": "moderator", "display_name": "Community", "link": "https://stackoverflowteams.com/c/davidlam/users/-1/community"}, "is_accepted": false, "score": 0, "last_activity_date": **********, "creation_date": **********, "answer_id": 6, "question_id": 5, "body": "<p>You can learn more about Stack Overflow for Teams in our <a href=\"https://www.stackoverflow.help\" rel=\"nofollow noreferrer\">help center</a>.</p>\n"}, {"owner": {"account_id": -1, "reputation": 1, "user_id": -1, "user_type": "moderator", "display_name": "Community", "link": "https://stackoverflowteams.com/c/davidlam/users/-1/community"}, "is_accepted": false, "score": 0, "last_activity_date": **********, "creation_date": **********, "answer_id": 8, "question_id": 7, "body": "<p>Stack Overflow for Teams is here for company-specific information. Questions regarding onboarding processes, key sales accounts, or what tools you have access to are all examples of topics to keep in your private Team. Simply put; if you have a question that you know others will benefit from, drop it in Stack Overflow for Teams.</p>\n<p>If you are one of those people that finds themselves answering the same questions on repeat, then you can actually ask and answer your own question. This allows you to simply point team members to the knowledge. If someone needs further clarification, they can add a comment to the answer and you’ll be notified. You can then respond when it works best for your workflow.</p>\n"}, {"owner": {"account_id": -1, "reputation": 1, "user_id": -1, "user_type": "moderator", "display_name": "Community", "link": "https://stackoverflowteams.com/c/davidlam/users/-1/community"}, "is_accepted": false, "score": 0, "last_activity_date": **********, "creation_date": **********, "answer_id": 10, "question_id": 9, "body": "<p>Stack Overflow for Teams is a platform designed to keep your team’s knowledge in one central location, and up to date. Ever tried searching for something in email or chat? The results can be less than accurate and oftentimes the sheer volume is overwhelming. Teams keeps a living account of wisdom that normally gets passed down through chats, emails, and face-to-face conversations.</p>\n<p>We structure knowledge the way that we naturally learn - through asking and answering questions.</p>\n"}], "has_more": false, "quota_max": 10000, "quota_remaining": 9999}