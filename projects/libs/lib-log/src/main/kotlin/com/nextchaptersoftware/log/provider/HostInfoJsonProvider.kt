package com.nextchaptersoftware.log.provider

import ch.qos.logback.classic.spi.ILoggingEvent
import com.fasterxml.jackson.core.JsonGenerator
import com.nextchaptersoftware.log.CustomFieldNames
import com.nextchaptersoftware.log.property.HostnamePropertyDefiner
import com.nextchaptersoftware.log.property.UserPropertyDefiner
import net.logstash.logback.composite.AbstractFieldJsonProvider
import net.logstash.logback.composite.FieldNamesAware
import net.logstash.logback.composite.JsonWritingUtils

class HostInfoJsonProvider : AbstractFieldJsonProvider<ILoggingEvent>(), FieldNamesAware<CustomFieldNames> {
    companion object {
        const val FIELD_ID = "hostInfo"
    }

    override fun writeTo(generator: JsonGenerator, event: ILoggingEvent) {
        JsonWritingUtils.writeMapStringFields(
            generator,
            FIELD_ID,
            mutableMapOf(
                "user" to UserPropertyDefiner().propertyValue,
                "hostname" to HostnamePropertyDefiner().propertyValue,
            ),
        )
    }

    override fun setFieldNames(fieldNames: CustomFieldNames) {
        fieldName = fieldNames.hostInfo
    }
}
