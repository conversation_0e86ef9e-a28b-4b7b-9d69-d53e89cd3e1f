package com.nextchaptersoftware.confluence.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.confluence.payloads.ConfluenceEvent
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler

class ConfluenceEventHandler(
    private val confluenceIngestPageEventHandler: TypedEventHandler<ConfluenceEvent.ConfluenceIngestPageEvent>,
    private val confluenceIngestBlogPostEventHandler: TypedEventHandler<ConfluenceEvent.ConfluenceIngestBlogPostEvent>,
    private val confluenceIngestSpaceEventHandler: TypedEventHandler<ConfluenceEvent.ConfluenceIngestSpaceEvent>,
    private val confluenceSyncSpaceEventHandler: TypedEventHandler<ConfluenceEvent.ConfluenceSyncSpaceEvent>,
    private val confluenceIngestSiteSpacesEventHandler: TypedEventHandler<ConfluenceEvent.ConfluenceIngestSiteSpacesEvent>,
    private val confluenceIngestSiteUsersAndGroupsEventHandler: TypedEventHandler<ConfluenceEvent.ConfluenceIngestSiteUsersAndGroupsEvent>,
) : EventHandler {
    override suspend fun handle(event: String): Boolean {
        return when (val confluenceEvent = event.decode<ConfluenceEvent>()) {
            is ConfluenceEvent.ConfluenceIngestPageEvent -> confluenceIngestPageEventHandler.handle(confluenceEvent)
            is ConfluenceEvent.ConfluenceIngestBlogPostEvent -> confluenceIngestBlogPostEventHandler.handle(confluenceEvent)
            is ConfluenceEvent.ConfluenceIngestSpaceEvent -> confluenceIngestSpaceEventHandler.handle(confluenceEvent)
            is ConfluenceEvent.ConfluenceSyncSpaceEvent -> confluenceSyncSpaceEventHandler.handle(confluenceEvent)
            is ConfluenceEvent.ConfluenceIngestSiteSpacesEvent -> confluenceIngestSiteSpacesEventHandler.handle(confluenceEvent)
            is ConfluenceEvent.ConfluenceIngestSiteUsersAndGroupsEvent -> confluenceIngestSiteUsersAndGroupsEventHandler.handle(confluenceEvent)
            is ConfluenceEvent.ConfluenceValidationEvent -> true
        }
    }
}
