2024-12-04T05:12:46.6486051Z Current runner version: '2.319.1'
2024-12-04T05:12:46.6491695Z Runner name: '12153441819-ip-192-168-7-194-ec2'
2024-12-04T05:12:46.6492477Z Runner group name: 'Default'
2024-12-04T05:12:46.6493267Z Machine name: 'ip-192-168-7-194'
2024-12-04T05:12:46.6509292Z Testing runner upgrade compatibility
2024-12-04T05:12:46.7237327Z ##[group]GITHUB_TOKEN Permissions
2024-12-04T05:12:46.7239158Z Actions: read
2024-12-04T05:12:46.7239704Z Checks: write
2024-12-04T05:12:46.7240210Z Contents: read
2024-12-04T05:12:46.7240688Z Metadata: read
2024-12-04T05:12:46.7241173Z PullRequests: write
2024-12-04T05:12:46.7241661Z ##[endgroup]
2024-12-04T05:12:46.7243920Z Secret source: Actions
2024-12-04T05:12:46.7244573Z Prepare workflow directory
2024-12-04T05:12:46.8393884Z Prepare all required actions
2024-12-04T05:12:46.8585276Z Getting action download info
2024-12-04T05:12:47.2833246Z Download action repository 'aws-actions/configure-aws-credentials@v4' (SHA:e3dd6a429d7300a6a4c196c26e071d42e0343502)
2024-12-04T05:13:00.4061970Z Download action repository 'actions/checkout@v4' (SHA:11bd71901bbe5b1630ceea73d27597364c9af683)
2024-12-04T05:13:03.6897789Z Download action repository 'dawidd6/action-ansible-playbook@v2' (SHA:c97d71562fcba83cc1ea0602d5a77013427f7571)
2024-12-04T05:13:20.5798416Z Download action repository 'KengoTODA/actions-setup-docker-compose@v1' (SHA:aa468051c6851848da9bfe114e7eac913c0bf59c)
2024-12-04T05:13:24.6719648Z Download action repository 'actions/setup-java@v4.2.2' (SHA:6a0805fcefea3d4657a47ac4c165951e33482018)
2024-12-04T05:13:35.0704095Z Download action repository 'gradle/actions@v3' (SHA:d9c87d481d55275bb5441eef3fe0e46805f9ef70)
2024-12-04T05:13:44.4165669Z Download action repository 'nick-fields/retry@v3' (SHA:7152eba30c6575329ac0576536151aca5a72780e)
2024-12-04T05:13:44.8991009Z Download action repository 'actions/cache@v4' (SHA:6849a6489940f00c2f30c0fb92c6274307ccb58a)
2024-12-04T05:13:45.9765450Z Download action repository '8398a7/action-slack@v3' (SHA:28ba43ae48961b90635b50953d216767a6bea486)
2024-12-04T05:13:46.4308599Z Download action repository 'mikepenz/action-junit-report@v5.0.0-a01' (SHA:5f36538697142e07cd2e6a7016f10d2be65be747)
2024-12-04T05:13:50.2233095Z Complete job name: services-build
2024-12-04T05:13:50.3819849Z ##[group]Run export GH_HOME=~
2024-12-04T05:13:50.3820438Z [36;1mexport GH_HOME=~[0m
2024-12-04T05:13:50.3820905Z [36;1mecho "GH_HOME=$GH_HOME" >> $GITHUB_ENV[0m
2024-12-04T05:13:50.3838356Z shell: /usr/bin/bash -e {0}
2024-12-04T05:13:50.3838784Z env:
2024-12-04T05:13:50.3839090Z   AWS_REGION: us-west-2
2024-12-04T05:13:50.3839524Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:13:50.3840027Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:13:50.3840502Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:13:50.3840994Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:13:50.3841840Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:13:50.3842395Z   CI: true
2024-12-04T05:13:50.3842730Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:13:50.3843179Z   CI_TITLE:
2024-12-04T05:13:50.3843479Z ##[endgroup]
2024-12-04T05:13:50.4596522Z ##[group]Run env
2024-12-04T05:13:50.4596879Z [36;1menv[0m
2024-12-04T05:13:50.4618707Z shell: /usr/bin/bash -e {0}
2024-12-04T05:13:50.4619122Z env:
2024-12-04T05:13:50.4619465Z   AWS_REGION: us-west-2
2024-12-04T05:13:50.4619900Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:13:50.4620409Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:13:50.4620890Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:13:50.4621381Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:13:50.4622130Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:13:50.4622675Z   CI: true
2024-12-04T05:13:50.4623010Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:13:50.4623453Z   CI_TITLE:
2024-12-04T05:13:50.4623754Z   GH_HOME: /root
2024-12-04T05:13:50.4624073Z ##[endgroup]
2024-12-04T05:13:50.4710929Z GITHUB_WORKSPACE=/actions-runner/_work/***/***
2024-12-04T05:13:50.4712120Z GITHUB_PATH=/actions-runner/_work/_temp/_runner_file_commands/add_path_4e35b5a7-958c-4673-a9c0-90cb7a89f555
2024-12-04T05:13:50.4713220Z GITHUB_ACTION=__run_2
2024-12-04T05:13:50.4713581Z GITHUB_RUN_NUMBER=57101
2024-12-04T05:13:50.4714045Z RUNNER_NAME=12153441819-ip-192-168-7-194-ec2
2024-12-04T05:13:50.4714550Z GITHUB_REPOSITORY_OWNER_ID=91906527
2024-12-04T05:13:50.4715076Z ACTIONS_RUNNER_HOOK_JOB_COMPLETED=/shutdown_script.sh
2024-12-04T05:13:50.4715574Z CI_TITLE=
2024-12-04T05:13:50.4715894Z GITHUB_TRIGGERING_ACTOR=pwerry
2024-12-04T05:13:50.4716291Z GITHUB_REF_TYPE=branch
2024-12-04T05:13:50.4716685Z TOUCH=Dummy value to force workflow rerun
2024-12-04T05:13:50.4717170Z AWS_REGION=us-west-2
2024-12-04T05:13:50.4717748Z ***
2024-12-04T05:13:50.4718062Z GITHUB_REPOSITORY_ID=436790478
2024-12-04T05:13:50.4718459Z GITHUB_ACTIONS=true
2024-12-04T05:13:50.4718903Z OUTPUT_ARTIFACT_NAME=built-artifacts-57101
2024-12-04T05:13:50.4719375Z SYSTEMD_EXEC_PID=1399
2024-12-04T05:13:50.4719802Z GITHUB_SHA=329fe673e7450852ec18fb305e7d212085f4c93b
2024-12-04T05:13:50.4720847Z GITHUB_WORKFLOW_REF=NextChapterSoftware/***/.github/workflows/ci-services.yml@refs/pull/17271/merge
2024-12-04T05:13:50.4721616Z _=/usr/bin/env
2024-12-04T05:13:50.4721991Z RUNNER_ENVIRONMENT=self-hosted
2024-12-04T05:13:50.4722404Z GITHUB_REF=refs/pull/17271/merge
2024-12-04T05:13:50.4722793Z RUNNER_OS=Linux
2024-12-04T05:13:50.4723125Z GITHUB_REF_PROTECTED=false
2024-12-04T05:13:50.4723555Z GITHUB_API_URL=https://api.github.com
2024-12-04T05:13:50.4723989Z GH_HOME=/root
2024-12-04T05:13:50.4724298Z LANG=C.UTF-8
2024-12-04T05:13:50.4724817Z RUNNER_TRACKING_ID=github_841dc991-5b80-4f1e-adac-bce573b72510
2024-12-04T05:13:50.4725368Z RUNNER_ARCH=X64
2024-12-04T05:13:50.4725763Z RUNNER_TEMP=/actions-runner/_work/_temp
2024-12-04T05:13:50.4726695Z GITHUB_STATE=/actions-runner/_work/_temp/_runner_file_commands/save_state_4e35b5a7-958c-4673-a9c0-90cb7a89f555
2024-12-04T05:13:50.4727973Z GITHUB_ENV=/actions-runner/_work/_temp/_runner_file_commands/set_env_4e35b5a7-958c-4673-a9c0-90cb7a89f555
2024-12-04T05:13:50.4729036Z GITHUB_EVENT_PATH=/actions-runner/_work/_temp/_github_workflow/event.json
2024-12-04T05:13:50.4729777Z INVOCATION_ID=94d87ac211c74cce88061b82ef8eae28
2024-12-04T05:13:50.4730279Z GITHUB_EVENT_NAME=pull_request
2024-12-04T05:13:50.4730677Z GITHUB_RUN_ID=12153441819
2024-12-04T05:13:50.4731598Z GITHUB_STEP_SUMMARY=/actions-runner/_work/_temp/_runner_file_commands/step_summary_4e35b5a7-958c-4673-a9c0-90cb7a89f555
2024-12-04T05:13:50.4732468Z GITHUB_ACTOR=pwerry
2024-12-04T05:13:50.4732818Z GITHUB_RUN_ATTEMPT=1
2024-12-04T05:13:50.4733250Z GITHUB_GRAPHQL_URL=https://api.github.com/graphql
2024-12-04T05:13:50.4733742Z TERM=vt220
2024-12-04T05:13:50.4734063Z SEGMENT_DOWNLOAD_TIMEOUT_MINS=3
2024-12-04T05:13:50.4734477Z RUNNER_ALLOW_RUNASROOT=1
2024-12-04T05:13:50.4734877Z GITHUB_SERVER_URL=https://github.com
2024-12-04T05:13:50.4735299Z SHLVL=4
2024-12-04T05:13:50.4735599Z GITHUB_ACTOR_ID=858772
2024-12-04T05:13:50.4736055Z RUNNER_TOOL_CACHE=/actions-runner/_work/_tool
2024-12-04T05:13:50.4736834Z GITHUB_WORKFLOW_SHA=329fe673e7450852ec18fb305e7d212085f4c93b
2024-12-04T05:13:50.4737395Z GITHUB_REF_NAME=17271/merge
2024-12-04T05:13:50.4737767Z GITHUB_JOB=build
2024-12-04T05:13:50.4738207Z GITHUB_REPOSITORY=NextChapterSoftware/***
2024-12-04T05:13:50.4738683Z GITHUB_RETENTION_DAYS=10
2024-12-04T05:13:50.4739065Z JOURNAL_STREAM=8:28888
2024-12-04T05:13:50.4739555Z RUNNER_WORKSPACE=/actions-runner/_work/***
2024-12-04T05:13:50.4740034Z GITHUB_ACTION_REPOSITORY=
2024-12-04T05:13:50.4740615Z CI_PR_TITLE=Include slack bot user id
2024-12-04T05:13:50.4741379Z PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/snap/bin
2024-12-04T05:13:50.4742136Z GITHUB_BASE_REF=main
2024-12-04T05:13:50.4742566Z CI=true
2024-12-04T05:13:50.4742998Z GITHUB_REPOSITORY_OWNER=NextChapterSoftware
2024-12-04T05:13:50.4743618Z BUILD_ARTIFACTS_DIR=./build/libs
2024-12-04T05:13:50.4744268Z GITHUB_HEAD_REF=peter/failing-test-example
2024-12-04T05:13:50.4744842Z GITHUB_ACTION_REF=
2024-12-04T05:13:50.4745390Z GITHUB_WORKFLOW=Services
2024-12-04T05:13:50.4746353Z GITHUB_OUTPUT=/actions-runner/_work/_temp/_runner_file_commands/set_output_4e35b5a7-958c-4673-a9c0-90cb7a89f555
2024-12-04T05:13:50.4747575Z GH_CACHE_BUCKET=***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:13:50.4748445Z OLDPWD=/
2024-12-04T05:13:50.4904323Z ##[group]Run aws-actions/configure-aws-credentials@v4
2024-12-04T05:13:50.4905029Z with:
2024-12-04T05:13:50.4905569Z   aws-access-key-id: ***
2024-12-04T05:13:50.4906312Z   aws-secret-access-key: ***
2024-12-04T05:13:50.4906814Z   aws-region: us-west-2
2024-12-04T05:13:50.4907623Z   role-to-assume: arn:aws:iam::877923746456:role/IAMS3AccessRole-***-gh-actions-s3-cache
2024-12-04T05:13:50.4908720Z   role-skip-session-tagging: true
2024-12-04T05:13:50.4909261Z   role-duration-seconds: 2400
2024-12-04T05:13:50.4909902Z   audience: sts.amazonaws.com
2024-12-04T05:13:50.4910382Z env:
2024-12-04T05:13:50.4910754Z   AWS_REGION: us-west-2
2024-12-04T05:13:50.4911285Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:13:50.4911899Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:13:50.4912471Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:13:50.4913085Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:13:50.4913908Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:13:50.4914640Z   CI: true
2024-12-04T05:13:50.4915068Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:13:50.4915626Z   CI_TITLE:
2024-12-04T05:13:50.4916015Z   GH_HOME: /root
2024-12-04T05:13:50.4916420Z ##[endgroup]
2024-12-04T05:13:50.7259644Z Assuming role with user credentials
2024-12-04T05:13:50.7524132Z Authenticated as assumedRoleId AROA4Y2CVIKMEPTETVYLH:GitHubActions
2024-12-04T05:13:50.7780310Z ##[group]Run actions/checkout@v4
2024-12-04T05:13:50.7780743Z with:
2024-12-04T05:13:50.7781068Z   submodules: recursive
2024-12-04T05:13:50.7781673Z   token: ***
2024-12-04T05:13:50.7782089Z   repository: NextChapterSoftware/***
2024-12-04T05:13:50.7782554Z   ssh-strict: true
2024-12-04T05:13:50.7782897Z   ssh-user: git
2024-12-04T05:13:50.7783260Z   persist-credentials: true
2024-12-04T05:13:50.7783650Z   clean: true
2024-12-04T05:13:50.7784006Z   sparse-checkout-cone-mode: true
2024-12-04T05:13:50.7784454Z   fetch-depth: 1
2024-12-04T05:13:50.7784786Z   fetch-tags: false
2024-12-04T05:13:50.7785131Z   show-progress: true
2024-12-04T05:13:50.7785489Z   lfs: false
2024-12-04T05:13:50.7785822Z   set-safe-directory: true
2024-12-04T05:13:50.7786186Z env:
2024-12-04T05:13:50.7786492Z   AWS_REGION: us-west-2
2024-12-04T05:13:50.7786930Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:13:50.7787459Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:13:50.7788281Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:13:50.7788838Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:13:50.7789537Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:13:50.7790163Z   CI: true
2024-12-04T05:13:50.7790514Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:13:50.7790988Z   CI_TITLE:
2024-12-04T05:13:50.7791297Z   GH_HOME: /root
2024-12-04T05:13:50.7791650Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:13:50.7792159Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:13:50.7792709Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:13:50.7798652Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:13:50.7799028Z ##[endgroup]
2024-12-04T05:13:50.9167560Z Syncing repository: NextChapterSoftware/***
2024-12-04T05:13:50.9170498Z ##[group]Getting Git version info
2024-12-04T05:13:50.9172031Z Working directory is '/actions-runner/_work/***/***'
2024-12-04T05:13:50.9173615Z [command]/usr/bin/git version
2024-12-04T05:13:51.5900193Z git version 2.34.1
2024-12-04T05:13:51.5925756Z ##[endgroup]
2024-12-04T05:13:51.5940802Z Temporarily overriding HOME='/actions-runner/_work/_temp/251f676c-c6ec-4230-9e82-23bde7356a08' before making global git config changes
2024-12-04T05:13:51.5942796Z Adding repository directory to the temporary git global config as a safe directory
2024-12-04T05:13:51.5946692Z [command]/usr/bin/git config --global --add safe.directory /actions-runner/_work/***/***
2024-12-04T05:13:51.5984947Z Deleting the contents of '/actions-runner/_work/***/***'
2024-12-04T05:13:51.5987676Z ##[group]Initializing the repository
2024-12-04T05:13:51.5991580Z [command]/usr/bin/git init /actions-runner/_work/***/***
2024-12-04T05:13:51.9020954Z hint: Using 'master' as the name for the initial branch. This default branch name
2024-12-04T05:13:51.9022015Z hint: is subject to change. To configure the initial branch name to use in all
2024-12-04T05:13:51.9022919Z hint: of your new repositories, which will suppress this warning, call:
2024-12-04T05:13:51.9023566Z hint:
2024-12-04T05:13:51.9024084Z hint: 	git config --global init.defaultBranch <name>
2024-12-04T05:13:51.9024596Z hint:
2024-12-04T05:13:51.9025174Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2024-12-04T05:13:51.9026107Z hint: 'development'. The just-created branch can be renamed via this command:
2024-12-04T05:13:51.9026778Z hint:
2024-12-04T05:13:51.9027123Z hint: 	git branch -m <name>
2024-12-04T05:13:51.9034962Z Initialized empty Git repository in /actions-runner/_work/***/***/.git/
2024-12-04T05:13:51.9047846Z [command]/usr/bin/git remote add origin https://github.com/NextChapterSoftware/***
2024-12-04T05:13:51.9086558Z ##[endgroup]
2024-12-04T05:13:51.9087206Z ##[group]Disabling automatic garbage collection
2024-12-04T05:13:51.9090057Z [command]/usr/bin/git config --local gc.auto 0
2024-12-04T05:13:51.9112320Z ##[endgroup]
2024-12-04T05:13:51.9112898Z ##[group]Setting up auth
2024-12-04T05:13:51.9118955Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
2024-12-04T05:13:51.9142528Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
2024-12-04T05:13:52.9200641Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
2024-12-04T05:13:52.9222901Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
2024-12-04T05:13:52.9398532Z [command]/usr/bin/git config --local http.https://github.com/.extraheader AUTHORIZATION: basic ***
2024-12-04T05:13:52.9440180Z ##[endgroup]
2024-12-04T05:13:52.9440821Z ##[group]Fetching the repository
2024-12-04T05:13:52.9448016Z [command]/usr/bin/git -c protocol.version=2 fetch --no-tags --prune --no-recurse-submodules --depth=1 origin +329fe673e7450852ec18fb305e7d212085f4c93b:refs/remotes/pull/17271/merge
2024-12-04T05:13:55.7020461Z From https://github.com/NextChapterSoftware/***
2024-12-04T05:13:55.7021534Z  * [new ref]         329fe673e7450852ec18fb305e7d212085f4c93b -> pull/17271/merge
2024-12-04T05:13:55.7037167Z ##[endgroup]
2024-12-04T05:13:55.7037807Z ##[group]Determining the checkout info
2024-12-04T05:13:55.7039358Z ##[endgroup]
2024-12-04T05:13:55.7043619Z [command]/usr/bin/git sparse-checkout disable
2024-12-04T05:13:55.7084290Z [command]/usr/bin/git config --local --unset-all extensions.worktreeConfig
2024-12-04T05:13:55.7104875Z ##[group]Checking out the ref
2024-12-04T05:13:55.7108221Z [command]/usr/bin/git checkout --progress --force refs/remotes/pull/17271/merge
2024-12-04T05:13:56.3342546Z Note: switching to 'refs/remotes/pull/17271/merge'.
2024-12-04T05:13:56.3343296Z
2024-12-04T05:13:56.3343773Z You are in 'detached HEAD' state. You can look around, make experimental
2024-12-04T05:13:56.3344689Z changes and commit them, and you can discard any commits you make in this
2024-12-04T05:13:56.3345575Z state without impacting any branches by switching back to a branch.
2024-12-04T05:13:56.3346077Z
2024-12-04T05:13:56.3346432Z If you want to create a new branch to retain commits you create, you may
2024-12-04T05:13:56.3347318Z do so (now or later) by using -c with the switch command. Example:
2024-12-04T05:13:56.3348145Z
2024-12-04T05:13:56.3348368Z   git switch -c <new-branch-name>
2024-12-04T05:13:56.3348672Z
2024-12-04T05:13:56.3348843Z Or undo this operation with:
2024-12-04T05:13:56.3349129Z
2024-12-04T05:13:56.3349269Z   git switch -
2024-12-04T05:13:56.3349467Z
2024-12-04T05:13:56.3349941Z Turn off this advice by setting config variable advice.detachedHead to false
2024-12-04T05:13:56.3350504Z
2024-12-04T05:13:56.3351122Z HEAD is now at 329fe67 Merge eedd3907c0295cbac9b71f83efbe7d3bc3692e30 into 6ff845d260770efe397057a3f41a4aea1f65e037
2024-12-04T05:13:56.3362756Z ##[endgroup]
2024-12-04T05:13:56.3363418Z ##[group]Setting up auth for fetching submodules
2024-12-04T05:13:56.3368371Z [command]/usr/bin/git config --global http.https://github.com/.extraheader AUTHORIZATION: basic ***
2024-12-04T05:13:56.3401056Z [command]/usr/bin/git config --global --unset-all url.https://github.com/.insteadOf
2024-12-04T05:13:56.3421417Z [command]/usr/bin/git config --global --add url.https://github.com/.insteadOf **************:
2024-12-04T05:13:56.3444908Z [command]/usr/bin/git config --global --add url.https://github.com/.insteadOf <EMAIL>:
2024-12-04T05:13:56.3462886Z ##[endgroup]
2024-12-04T05:13:56.3463465Z ##[group]Fetching submodules
2024-12-04T05:13:56.3465973Z [command]/usr/bin/git submodule sync --recursive
2024-12-04T05:13:56.3663943Z [command]/usr/bin/git -c protocol.version=2 submodule update --init --force --depth=1 --recursive
2024-12-04T05:13:56.3860774Z Submodule 'projects/clients/client-scm/src/main/resources/github-graphql/schema' (https://github.com/octokit/graphql-schema) registered for path 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:13:56.3880746Z Submodule 'shared/clientAssets' (https://github.com/NextChapterSoftware/***-client-assets) registered for path 'shared/clientAssets'
2024-12-04T05:13:56.3946108Z Cloning into '/actions-runner/_work/***/***/projects/clients/client-scm/src/main/resources/github-graphql/schema'...
2024-12-04T05:13:57.1917097Z Cloning into '/actions-runner/_work/***/***/shared/clientAssets'...
2024-12-04T05:14:19.7039369Z From https://github.com/octokit/graphql-schema
2024-12-04T05:14:19.7040243Z  * branch            ea27acb4c475ed2ad880490e8a68583f6856a0dd -> FETCH_HEAD
2024-12-04T05:14:19.7300115Z Submodule path 'projects/clients/client-scm/src/main/resources/github-graphql/schema': checked out 'ea27acb4c475ed2ad880490e8a68583f6856a0dd'
2024-12-04T05:14:21.6949864Z Submodule path 'shared/clientAssets': checked out '05472ba6459a382f669363a7bce027423296ca29'
2024-12-04T05:14:21.7014878Z [command]/usr/bin/git submodule foreach --recursive git config --local gc.auto 0
2024-12-04T05:14:21.7203354Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:14:21.7233000Z Entering 'shared/clientAssets'
2024-12-04T05:14:21.7269317Z ##[endgroup]
2024-12-04T05:14:21.7270134Z ##[group]Persisting credentials for submodules
2024-12-04T05:14:21.7275135Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'url\.https\:\/\/github\.com\/\.insteadOf' && git config --local --unset-all 'url.https://github.com/.insteadOf' || :"
2024-12-04T05:14:21.7457490Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:14:21.7490621Z Entering 'shared/clientAssets'
2024-12-04T05:14:21.7533476Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local 'http.https://github.com/.extraheader' 'AUTHORIZATION: basic ***' && git config --local --show-origin --name-only --get-regexp remote.origin.url"
2024-12-04T05:14:21.7715198Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:14:21.7745161Z file:/actions-runner/_work/***/***/.git/modules/projects/clients/client-scm/src/main/resources/github-graphql/schema/config	remote.origin.url
2024-12-04T05:14:21.7759629Z Entering 'shared/clientAssets'
2024-12-04T05:14:21.7789169Z file:/actions-runner/_work/***/***/.git/modules/shared/clientAssets/config	remote.origin.url
2024-12-04T05:14:21.7823469Z [command]/usr/bin/git submodule foreach --recursive git config --local --add 'url.https://github.com/.insteadOf' '**************:'
2024-12-04T05:14:21.8003793Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:14:21.8033905Z Entering 'shared/clientAssets'
2024-12-04T05:14:21.8072798Z [command]/usr/bin/git submodule foreach --recursive git config --local --add 'url.https://github.com/.insteadOf' '<EMAIL>:'
2024-12-04T05:14:21.8254788Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:14:21.8284049Z Entering 'shared/clientAssets'
2024-12-04T05:14:21.8319979Z ##[endgroup]
2024-12-04T05:14:21.8345832Z [command]/usr/bin/git log -1 --format=%H
2024-12-04T05:14:21.8367041Z 329fe673e7450852ec18fb305e7d212085f4c93b
2024-12-04T05:14:21.8486147Z ##[group]Run dawidd6/action-ansible-playbook@v2
2024-12-04T05:14:21.8486664Z with:
2024-12-04T05:14:21.8487068Z   playbook: local-env-decrypt-playbook.yml
2024-12-04T05:14:21.8487575Z   directory: ./secrets/local
2024-12-04T05:14:21.8488052Z   vault_password: ***
2024-12-04T05:14:21.8488417Z   options: --verbose
2024-12-04T05:14:21.8488774Z env:
2024-12-04T05:14:21.8489074Z   AWS_REGION: us-west-2
2024-12-04T05:14:21.8489529Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:14:21.8490049Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:14:21.8490552Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:14:21.8491075Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:14:21.8491729Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:14:21.8492292Z   CI: true
2024-12-04T05:14:21.8492640Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:14:21.8493100Z   CI_TITLE:
2024-12-04T05:14:21.8493413Z   GH_HOME: /root
2024-12-04T05:14:21.8493816Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:14:21.8494303Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:14:21.8494867Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:14:21.8500765Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:14:21.8501193Z ##[endgroup]
2024-12-04T05:14:21.9557264Z [command]/usr/bin/ansible-playbook local-env-decrypt-playbook.yml --verbose --vault-password-file .ansible_vault_password
2024-12-04T05:14:25.3322961Z [0;34mNo config file found; using defaults[0m
2024-12-04T05:14:25.3416795Z [1;35m[WARNING]: No inventory was parsed, only implicit localhost is available[0m
2024-12-04T05:14:25.3470010Z [1;35m[WARNING]: provided hosts list is empty, only localhost is available. Note that[0m
2024-12-04T05:14:25.3470896Z [1;35mthe implicit localhost does not match 'all'[0m
2024-12-04T05:14:26.0710377Z
2024-12-04T05:14:26.0710983Z PLAY [localhost] ***************************************************************
2024-12-04T05:14:26.0847487Z
2024-12-04T05:14:26.0848274Z TASK [Gathering Facts] *********************************************************
2024-12-04T05:14:28.6391611Z [0;32mok: [localhost][0m
2024-12-04T05:14:28.6495025Z
2024-12-04T05:14:28.6495408Z TASK [Create secrets directory] ************************************************
2024-12-04T05:14:28.9820656Z [0;33mchanged: [localhost] => {"changed": true, "gid": 0, "group": "root", "mode": "0755", "owner": "root", "path": "/root/.secrets/***/local", "size": 4096, "state": "directory", "uid": 0}[0m
2024-12-04T05:14:29.0000239Z
2024-12-04T05:14:29.0000713Z TASK [Decrypt secrets to ~/.secrets/***/local] ***************************
2024-12-04T05:14:29.4416977Z [0;33mchanged: [localhost] => (item=/actions-runner/_work/***/***/secrets/local/assets/refinery.env) => {"ansible_loop_var": "item", "changed": true, "checksum": "55b034c91fc2b5cc4782f3c9135d61f5425c04f0", "dest": "/root/.secrets/***/local/refinery.env", "gid": 0, "group": "root", "item": "/actions-runner/_work/***/***/secrets/local/assets/refinery.env", "md5sum": "f0cc1d29852740e7778c827910f6d1e3", "mode": "0644", "owner": "root", "size": 287, "src": "/root/.ansible/tmp/ansible-tmp-1733289269.016099-3674-166719045779227/source", "state": "file", "uid": 0}[0m
2024-12-04T05:14:29.7132482Z [0;33mchanged: [localhost] => (item=/actions-runner/_work/***/***/secrets/local/assets/secrets.conf) => {"ansible_loop_var": "item", "changed": true, "checksum": "c918d7c91a5ce423a6ebd8eae30b54f9612dbc85", "dest": "/root/.secrets/***/local/secrets.conf", "gid": 0, "group": "root", "item": "/actions-runner/_work/***/***/secrets/local/assets/secrets.conf", "md5sum": "6bc6808ea6c2d2032bebd4728e6d9b91", "mode": "0644", "owner": "root", "size": 28045, "src": "/root/.ansible/tmp/ansible-tmp-1733289269.4450095-3674-126043765747529/source", "state": "file", "uid": 0}[0m
2024-12-04T05:14:29.9824069Z [0;33mchanged: [localhost] => (item=/actions-runner/_work/***/***/secrets/local/assets/secrets.properties) => {"ansible_loop_var": "item", "changed": true, "checksum": "98d33246c629524ea201ca46bb6c02a5299feaea", "dest": "/root/.secrets/***/local/secrets.properties", "gid": 0, "group": "root", "item": "/actions-runner/_work/***/***/secrets/local/assets/secrets.properties", "md5sum": "6466b22b8bc6e2da37f160e9d0635a2d", "mode": "0644", "owner": "root", "size": 47, "src": "/root/.ansible/tmp/ansible-tmp-1733289269.716599-3674-47117265181848/source", "state": "file", "uid": 0}[0m
2024-12-04T05:14:29.9933408Z
2024-12-04T05:14:29.9933726Z PLAY RECAP *********************************************************************
2024-12-04T05:14:29.9934958Z [0;33mlocalhost[0m                  : [0;32mok=3   [0m [0;33mchanged=2   [0m unreachable=0    failed=0    skipped=0    rescued=0    ignored=0
2024-12-04T05:14:29.9935723Z
2024-12-04T05:14:30.0574690Z ##[group]Run KengoTODA/actions-setup-docker-compose@v1
2024-12-04T05:14:30.0575375Z with:
2024-12-04T05:14:30.0575805Z   version: 2.14.2
2024-12-04T05:14:30.0576238Z env:
2024-12-04T05:14:30.0576665Z   AWS_REGION: us-west-2
2024-12-04T05:14:30.0577224Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:14:30.0577834Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:14:30.0578458Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:14:30.0579098Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:14:30.0579898Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:14:30.0580574Z   CI: true
2024-12-04T05:14:30.0581057Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:14:30.0617403Z   CI_TITLE:
2024-12-04T05:14:30.0617809Z   GH_HOME: /root
2024-12-04T05:14:30.0618186Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:14:30.0618699Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:14:30.0619260Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:14:30.0625180Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:14:30.0625569Z ##[endgroup]
2024-12-04T05:14:30.1505045Z [command]/usr/bin/uname -s
2024-12-04T05:14:30.1534170Z [command]/usr/bin/uname -m
2024-12-04T05:14:30.1548269Z Linux
2024-12-04T05:14:30.1569010Z x86_64
2024-12-04T05:14:30.9121238Z [command]/usr/bin/chmod +x /actions-runner/_work/_temp/c8cfb121-e7b8-4c32-aadb-b14f94ecfbe0
2024-12-04T05:14:31.1336571Z ##[group]Run make start-ci-services
2024-12-04T05:14:31.1337133Z [36;1mmake start-ci-services[0m
2024-12-04T05:14:31.1353996Z shell: /usr/bin/bash -e {0}
2024-12-04T05:14:31.1354403Z env:
2024-12-04T05:14:31.1354711Z   AWS_REGION: us-west-2
2024-12-04T05:14:31.1355175Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:14:31.1355701Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:14:31.1356217Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:14:31.1356734Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:14:31.1357426Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:14:31.1357988Z   CI: true
2024-12-04T05:14:31.1358355Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:14:31.1358806Z   CI_TITLE:
2024-12-04T05:14:31.1359135Z   GH_HOME: /root
2024-12-04T05:14:31.1359492Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:14:31.1359978Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:14:31.1360535Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:14:31.1366494Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:14:31.1366875Z ##[endgroup]
2024-12-04T05:14:31.2520783Z docker-compose -f docker-compose-ci.yml up --force-recreate -d
2024-12-04T05:14:31.7623643Z #1 [internal] load build definition from Dockerfile
2024-12-04T05:14:31.7624353Z #1 transferring dockerfile: 180B done
2024-12-04T05:14:31.7625042Z #1 DONE 0.0s
2024-12-04T05:14:31.7625253Z
2024-12-04T05:14:31.7625498Z #2 [internal] load .dockerignore
2024-12-04T05:14:31.7626381Z #2 transferring context: 2B done
2024-12-04T05:14:31.7626851Z #2 DONE 0.0s
2024-12-04T05:14:31.7627037Z
2024-12-04T05:14:31.7627349Z #3 [internal] load metadata for docker.io/symptoma/activemq:latest
2024-12-04T05:14:32.6591990Z #3 DONE 1.0s
2024-12-04T05:14:32.7789510Z
2024-12-04T05:14:32.7790071Z #4 [internal] load build context
2024-12-04T05:14:32.7790615Z #4 transferring context: 8.08kB done
2024-12-04T05:14:32.7791076Z #4 DONE 0.0s
2024-12-04T05:14:32.7791282Z
2024-12-04T05:14:32.7791950Z #5 [1/2] FROM docker.io/symptoma/activemq:latest@sha256:a664920a36eab3c262b02e8cdc773e63d7820c5acf36749c703b4a6c510fd6ff
2024-12-04T05:14:32.7793460Z #5 resolve docker.io/symptoma/activemq:latest@sha256:a664920a36eab3c262b02e8cdc773e63d7820c5acf36749c703b4a6c510fd6ff 0.0s done
2024-12-04T05:14:32.7794924Z #5 sha256:4abcf20661432fb2d719aaf90656f55c287f8ca915dc1c92ec14ff61e67fbaf8 0B / 3.41MB 0.1s
2024-12-04T05:14:32.7796021Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 0B / 78.61MB 0.1s
2024-12-04T05:14:32.8801291Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 6.29MB / 78.61MB 0.2s
2024-12-04T05:14:32.8802470Z #5 sha256:40e3da3b40f9eb65fb85d08d17cc880759e849e510ee9c4ab298ec10d8ca6af6 1.62kB / 1.62kB done
2024-12-04T05:14:32.8803610Z #5 sha256:37ce65f8bda23c9e7823f6c48e4ae940775fdec384782df963843600a01ebf8b 17.93kB / 17.93kB done
2024-12-04T05:14:32.8804750Z #5 sha256:a664920a36eab3c262b02e8cdc773e63d7820c5acf36749c703b4a6c510fd6ff 1.61kB / 1.61kB done
2024-12-04T05:14:32.8805869Z #5 sha256:3beba684ccdbfc47e4567b6f88f849aee534398d8499571aa3a21a8eae61b891 0B / 3.55MB 0.2s
2024-12-04T05:14:32.9995013Z #5 sha256:4abcf20661432fb2d719aaf90656f55c287f8ca915dc1c92ec14ff61e67fbaf8 3.41MB / 3.41MB 0.3s
2024-12-04T05:14:32.9996213Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 18.87MB / 78.61MB 0.3s
2024-12-04T05:14:32.9997317Z #5 extracting sha256:4abcf20661432fb2d719aaf90656f55c287f8ca915dc1c92ec14ff61e67fbaf8
2024-12-04T05:14:33.1498427Z #5 sha256:4abcf20661432fb2d719aaf90656f55c287f8ca915dc1c92ec14ff61e67fbaf8 3.41MB / 3.41MB 0.3s done
2024-12-04T05:14:33.1499629Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 30.41MB / 78.61MB 0.4s
2024-12-04T05:14:33.1500801Z #5 sha256:3beba684ccdbfc47e4567b6f88f849aee534398d8499571aa3a21a8eae61b891 3.55MB / 3.55MB 0.4s
2024-12-04T05:14:33.1501936Z #5 extracting sha256:4abcf20661432fb2d719aaf90656f55c287f8ca915dc1c92ec14ff61e67fbaf8 0.1s done
2024-12-04T05:14:33.1503275Z #5 sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 0B / 51.88MB 0.4s
2024-12-04T05:14:33.2687102Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 44.04MB / 78.61MB 0.5s
2024-12-04T05:14:33.2688345Z #5 sha256:3beba684ccdbfc47e4567b6f88f849aee534398d8499571aa3a21a8eae61b891 3.55MB / 3.55MB 0.4s done
2024-12-04T05:14:33.2689530Z #5 sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 7.34MB / 51.88MB 0.5s
2024-12-04T05:14:33.2690647Z #5 sha256:617a8f466f39ce5546edaca54c983fdea2873f768c97e0cb8cabb9f727727158 0B / 996B 0.5s
2024-12-04T05:14:33.2691754Z #5 extracting sha256:3beba684ccdbfc47e4567b6f88f849aee534398d8499571aa3a21a8eae61b891 0.1s done
2024-12-04T05:14:33.3788600Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 69.21MB / 78.61MB 0.7s
2024-12-04T05:14:33.3789867Z #5 sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 27.26MB / 51.88MB 0.7s
2024-12-04T05:14:33.3791057Z #5 sha256:617a8f466f39ce5546edaca54c983fdea2873f768c97e0cb8cabb9f727727158 996B / 996B 0.5s done
2024-12-04T05:14:33.3792207Z #5 sha256:bf0ecfe16fb888344808caabee500fa98d73b3fc39ed14f2cc234a0b2695d7f7 997B / 997B 0.6s done
2024-12-04T05:14:33.3793549Z #5 sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1 0B / 32B 0.7s
2024-12-04T05:14:33.5295901Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 78.61MB / 78.61MB 0.8s
2024-12-04T05:14:33.5297077Z #5 sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 38.85MB / 51.88MB 0.8s
2024-12-04T05:14:33.5298224Z #5 sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1 32B / 32B 0.7s done
2024-12-04T05:14:33.6787501Z #5 sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 51.88MB / 51.88MB 1.0s
2024-12-04T05:14:33.9295325Z #5 sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 78.61MB / 78.61MB 1.1s done
2024-12-04T05:14:33.9296498Z #5 extracting sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150
2024-12-04T05:14:34.1731218Z #5 sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 51.88MB / 51.88MB 1.4s done
2024-12-04T05:14:34.4245155Z #5 extracting sha256:cfee378782f587dba10f7c632c4e611b394331613d3a6e5ac6b4ecbb00f98150 0.5s done
2024-12-04T05:14:34.5403125Z #5 extracting sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 0.1s
2024-12-04T05:14:34.7914200Z #5 extracting sha256:f220cba776bca134c64e3cba477caa612e44baa768a2f488300f2c14ce5ac1e3 0.3s done
2024-12-04T05:14:34.9220149Z #5 extracting sha256:617a8f466f39ce5546edaca54c983fdea2873f768c97e0cb8cabb9f727727158 done
2024-12-04T05:14:34.9221378Z #5 extracting sha256:bf0ecfe16fb888344808caabee500fa98d73b3fc39ed14f2cc234a0b2695d7f7 done
2024-12-04T05:14:34.9222472Z #5 extracting sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1
2024-12-04T05:14:35.0724421Z #5 extracting sha256:4f4fb700ef54461cfa02571ae0db9a0dc1e0cdb5577484a6d75e68dc38e8acc1 done
2024-12-04T05:14:35.1754376Z #5 DONE 2.5s
2024-12-04T05:14:35.3256779Z
2024-12-04T05:14:35.3257326Z #6 [2/2] COPY conf/activemq.xml /opt/activemq/conf/activemq.xml
2024-12-04T05:14:42.2118444Z #6 DONE 7.0s
2024-12-04T05:14:42.2664484Z
2024-12-04T05:14:42.2665129Z #7 exporting to image
2024-12-04T05:14:42.2665573Z #7 exporting layers 0.0s done
2024-12-04T05:14:42.2666404Z #7 writing image sha256:934c263aaf499bde38d51ee4db0dfdf5c2a27e5b7e9cd2381eab420e764a6aa8 done
2024-12-04T05:14:42.2667609Z #7 naming to docker.io/library/***-activemq done
2024-12-04T05:14:42.2668272Z #7 DONE 0.0s
2024-12-04T05:14:42.2671392Z Network ***_default  Creating
2024-12-04T05:14:42.3091094Z Network ***_default  Created
2024-12-04T05:14:42.3093962Z Volume "***_redis-data"  Creating
2024-12-04T05:14:42.3121633Z Volume "***_redis-data"  Created
2024-12-04T05:14:42.3123798Z Volume "***_activemq-data"  Creating
2024-12-04T05:14:42.3144643Z Volume "***_activemq-data"  Created
2024-12-04T05:14:42.3145776Z Container ***-redis-1  Creating
2024-12-04T05:14:42.3146507Z Container localstack_main  Creating
2024-12-04T05:14:42.3147106Z Container ***-activemq-1  Creating
2024-12-04T05:14:42.3919613Z Container ***-activemq-1  Created
2024-12-04T05:14:42.3927595Z Container ***-redis-1  Created
2024-12-04T05:14:42.6707812Z Container localstack_main  Created
2024-12-04T05:14:42.6715622Z Container ***-activemq-1  Starting
2024-12-04T05:14:42.6716306Z Container ***-redis-1  Starting
2024-12-04T05:14:42.6718739Z Container localstack_main  Starting
2024-12-04T05:14:45.2773731Z Container ***-activemq-1  Started
2024-12-04T05:14:45.5755599Z Container ***-redis-1  Started
2024-12-04T05:14:46.2360337Z Container localstack_main  Started
2024-12-04T05:14:46.2562757Z ##[group]Run actions/setup-java@v4.2.2
2024-12-04T05:14:46.2563577Z with:
2024-12-04T05:14:46.2564113Z   java-version: 21
2024-12-04T05:14:46.2564778Z   distribution: temurin
2024-12-04T05:14:46.2565417Z   cache: gradle
2024-12-04T05:14:46.2566034Z   java-package: jdk
2024-12-04T05:14:46.2566682Z   check-latest: false
2024-12-04T05:14:46.2567299Z   server-id: github
2024-12-04T05:14:46.2567980Z   server-username: GITHUB_ACTOR
2024-12-04T05:14:46.2569070Z   server-password: GITHUB_TOKEN
2024-12-04T05:14:46.2569897Z   overwrite-settings: true
2024-12-04T05:14:46.2570619Z   job-status: success
2024-12-04T05:14:46.2571542Z   token: ***
2024-12-04T05:14:46.2572089Z env:
2024-12-04T05:14:46.2572640Z   AWS_REGION: us-west-2
2024-12-04T05:14:46.2573416Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:14:46.2574395Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:14:46.2575309Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:14:46.2576331Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:14:46.2577547Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:14:46.2578581Z   CI: true
2024-12-04T05:14:46.2579230Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:14:46.2580084Z   CI_TITLE:
2024-12-04T05:14:46.2580628Z   GH_HOME: /root
2024-12-04T05:14:46.2581273Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:14:46.2582166Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:14:46.2583228Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:14:46.2594597Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:14:46.2595332Z ##[endgroup]
2024-12-04T05:14:46.4703058Z ##[group]Installed distributions
2024-12-04T05:14:46.4749693Z Trying to resolve the latest version from remote
2024-12-04T05:14:46.8151582Z Resolved latest version as 21.0.5+11.0.LTS
2024-12-04T05:14:46.8152139Z Trying to download...
2024-12-04T05:14:46.8153806Z Downloading Java 21.0.5+11.0.LTS (Temurin-Hotspot) from https://github.com/adoptium/temurin21-binaries/releases/download/jdk-21.0.5%2B11/OpenJDK21U-jdk_x64_linux_hotspot_21.0.5_11.tar.gz ...
2024-12-04T05:14:48.2208136Z Extracting Java archive...
2024-12-04T05:14:48.2284156Z [command]/usr/bin/tar xz --warning=no-unknown-keyword --overwrite -C /actions-runner/_work/_temp/fc15c279-b679-4c79-a603-807ddd068439 -f /actions-runner/_work/_temp/186b1543-37c6-44f9-98ba-25630352831e
2024-12-04T05:14:56.9725109Z Java 21.0.5+11.0.LTS was downloaded
2024-12-04T05:14:56.9725686Z Setting Java 21.0.5+11.0.LTS as the default
2024-12-04T05:14:56.9736352Z Creating toolchains.xml for JDK version 21 from temurin
2024-12-04T05:14:56.9807735Z Writing to /root/.m2/toolchains.xml
2024-12-04T05:14:56.9808261Z
2024-12-04T05:14:56.9808511Z Java configuration:
2024-12-04T05:14:56.9808892Z   Distribution: temurin
2024-12-04T05:14:56.9809291Z   Version: 21.0.5+11.0.LTS
2024-12-04T05:14:56.9810149Z   Path: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:14:56.9810716Z
2024-12-04T05:14:56.9811166Z ##[endgroup]
2024-12-04T05:14:56.9834673Z Creating settings.xml with server-id: github
2024-12-04T05:14:56.9835246Z Writing to /root/.m2/settings.xml
2024-12-04T05:15:00.2916350Z Received 0 of 1104432761 (0.0%), 0.0 MBs/sec
2024-12-04T05:15:01.2918658Z Received 79691776 of 1104432761 (7.2%), 38.0 MBs/sec
2024-12-04T05:15:02.2930456Z Received 176160768 of 1104432761 (16.0%), 56.0 MBs/sec
2024-12-04T05:15:03.2933873Z Received 281018368 of 1104432761 (25.4%), 66.9 MBs/sec
2024-12-04T05:15:04.2955172Z Received 369098752 of 1104432761 (33.4%), 70.3 MBs/sec
2024-12-04T05:15:05.2958448Z Received 473956352 of 1104432761 (42.9%), 75.3 MBs/sec
2024-12-04T05:15:06.2959194Z Received 574619648 of 1104432761 (52.0%), 78.2 MBs/sec
2024-12-04T05:15:07.2958312Z Received 675282944 of 1104432761 (61.1%), 80.4 MBs/sec
2024-12-04T05:15:08.2962167Z Received 767557632 of 1104432761 (69.5%), 81.3 MBs/sec
2024-12-04T05:15:09.2960969Z Received 872415232 of 1104432761 (79.0%), 83.2 MBs/sec
2024-12-04T05:15:10.2963516Z Received 964689920 of 1104432761 (87.3%), 83.6 MBs/sec
2024-12-04T05:15:11.2967065Z Received 1061158912 of 1104432761 (96.1%), 84.3 MBs/sec
2024-12-04T05:15:11.7446969Z Cache Size: ~1053 MB (1104432761 B)
2024-12-04T05:15:11.7505291Z [command]/usr/bin/tar -xf /actions-runner/_work/_temp/a3f06e74-4d7e-435d-8b94-daeccc9e33d3/cache.tzst -P -C /actions-runner/_work/***/*** --use-compress-program unzstd
2024-12-04T05:15:12.2978878Z Received 1104432761 of 1104432761 (100.0%), 81.0 MBs/sec
2024-12-04T05:15:17.4572244Z Cache restored successfully
2024-12-04T05:15:17.5824339Z Cache restored from key: setup-java-Linux-gradle-e8221ec95d8162758734506d0e704ac92e409742be2a6a60a90c9764e9b4081d
2024-12-04T05:15:17.6027326Z ##[group]Run export GRADLE_ARGS="--no-daemon build -x :common:generateProto -x :npmInstall"
2024-12-04T05:15:17.6028639Z [36;1mexport GRADLE_ARGS="--no-daemon build -x :common:generateProto -x :npmInstall"[0m
2024-12-04T05:15:17.6029392Z [36;1mecho $SKIP_TESTS[0m
2024-12-04T05:15:17.6029935Z [36;1mif [ ${SKIP_TESTS} == 'true' ]; then[0m
2024-12-04T05:15:17.6030494Z [36;1m  GRADLE_ARGS="$GRADLE_ARGS -x test"[0m
2024-12-04T05:15:17.6030993Z [36;1mfi[0m
2024-12-04T05:15:17.6031464Z [36;1mecho "GRADLE_ARGS=$(echo $GRADLE_ARGS)" >> $GITHUB_ENV[0m
2024-12-04T05:15:17.6045818Z shell: /usr/bin/bash -e {0}
2024-12-04T05:15:17.6046237Z env:
2024-12-04T05:15:17.6046549Z   AWS_REGION: us-west-2
2024-12-04T05:15:17.6047018Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:15:17.6047550Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:15:17.6048091Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:15:17.6048627Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:15:17.6049350Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:15:17.6049919Z   CI: true
2024-12-04T05:15:17.6050287Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:15:17.6050744Z   CI_TITLE:
2024-12-04T05:15:17.6051073Z   GH_HOME: /root
2024-12-04T05:15:17.6051441Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:15:17.6051922Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:15:17.6052485Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:15:17.6058396Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:15:17.6059046Z   JAVA_HOME: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:15:17.6060055Z   JAVA_HOME_21_X64: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:15:17.6060804Z   FAIL_SLOW: false
2024-12-04T05:15:17.6061149Z   SKIP_TESTS: false
2024-12-04T05:15:17.6061513Z ##[endgroup]
2024-12-04T05:15:17.6097223Z false
2024-12-04T05:15:17.6248687Z ##[group]Run gradle/actions/setup-gradle@v3
2024-12-04T05:15:17.6249173Z with:
2024-12-04T05:15:17.6249504Z   cache-disabled: true
2024-12-04T05:15:17.6249889Z   cache-read-only: true
2024-12-04T05:15:17.6250297Z   cache-write-only: false
2024-12-04T05:15:17.6250729Z   cache-overwrite-existing: false
2024-12-04T05:15:17.6251274Z   gradle-home-cache-includes: caches
notifications
2024-12-04T05:15:17.6251872Z   gradle-home-cache-cleanup: false
2024-12-04T05:15:17.6252327Z   add-job-summary: always
2024-12-04T05:15:17.6252777Z   add-job-summary-as-pr-comment: never
2024-12-04T05:15:17.6253271Z   dependency-graph: disabled
2024-12-04T05:15:17.6253772Z   dependency-graph-continue-on-failure: true
2024-12-04T05:15:17.6254323Z   build-scan-publish: false
2024-12-04T05:15:17.6254754Z   validate-wrappers: false
2024-12-04T05:15:17.6255188Z   generate-job-summary: true
2024-12-04T05:15:17.6255641Z   gradle-home-cache-strict-match: false
2024-12-04T05:15:17.6256210Z   workflow-job-context: null
2024-12-04T05:15:17.6256809Z   github-token: ***
2024-12-04T05:15:17.6257166Z env:
2024-12-04T05:15:17.6257465Z   AWS_REGION: us-west-2
2024-12-04T05:15:17.6257925Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:15:17.6258472Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:15:17.6258982Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:15:17.6259518Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:15:17.6260170Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:15:17.6260758Z   CI: true
2024-12-04T05:15:17.6261135Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:15:17.6261598Z   CI_TITLE:
2024-12-04T05:15:17.6261941Z   GH_HOME: /root
2024-12-04T05:15:17.6262310Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:15:17.6262807Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:15:17.6263385Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:15:17.6269591Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:15:17.6270274Z   JAVA_HOME: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:15:17.6271463Z   JAVA_HOME_21_X64: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:15:17.6272416Z   GRADLE_ARGS: --no-daemon build -x :common:generateProto -x :npmInstall
2024-12-04T05:15:17.6273066Z ##[endgroup]
2024-12-04T05:15:17.9804116Z Cache is disabled: will not restore state from previous builds.
2024-12-04T05:15:17.9815630Z Merged default JDK locations into /root/.m2/toolchains.xml
2024-12-04T05:15:17.9925789Z ##[group]Run nick-fields/retry@v3
2024-12-04T05:15:17.9926239Z with:
2024-12-04T05:15:17.9926549Z   timeout_minutes: 25
2024-12-04T05:15:17.9926924Z   max_attempts: 2
2024-12-04T05:15:17.9927265Z   retry_on: timeout
2024-12-04T05:15:17.9927862Z   command: ./gradlew --no-daemon build -x :common:generateProto -x :npmInstall
2024-12-04T05:15:17.9929327Z   on_retry_command: ps -ax
ls -al /tmp
rm -rf /tmp/embedded-pg
killall java || echo nojava
killall initdb || echo noinitdb
killall postgres || echo nopostgres
ps -ax
ls -al /tmp
2024-12-04T05:15:17.9930536Z   retry_wait_seconds: 10
2024-12-04T05:15:17.9930950Z   polling_interval_seconds: 1
2024-12-04T05:15:17.9931369Z   warning_on_retry: true
2024-12-04T05:15:17.9931766Z   continue_on_error: false
2024-12-04T05:15:17.9932149Z env:
2024-12-04T05:15:17.9932453Z   AWS_REGION: us-west-2
2024-12-04T05:15:17.9932913Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:15:17.9933443Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:15:17.9934010Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:15:17.9934527Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:15:17.9935216Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:15:17.9935793Z   CI: true
2024-12-04T05:15:17.9936148Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:15:17.9936619Z   CI_TITLE:
2024-12-04T05:15:17.9936942Z   GH_HOME: /root
2024-12-04T05:15:17.9937317Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:15:17.9937803Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:15:17.9938374Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:15:17.9944281Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:15:17.9944942Z   JAVA_HOME: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:15:17.9945938Z   JAVA_HOME_21_X64: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:15:17.9946878Z   GRADLE_ARGS: --no-daemon build -x :common:generateProto -x :npmInstall
2024-12-04T05:15:17.9947594Z   GRADLE_ACTION_ID: gradle/actions/setup-gradle
2024-12-04T05:15:17.9948393Z   GRADLE_BUILD_ACTION_SETUP_COMPLETED: true
2024-12-04T05:15:17.9948941Z   GRADLE_BUILD_ACTION_CACHE_RESTORED: true
2024-12-04T05:15:17.9949798Z   DEVELOCITY_INJECTION_INIT_SCRIPT_NAME: gradle-actions.inject-develocity.init.gradle
2024-12-04T05:15:17.9950683Z   DEVELOCITY_AUTO_INJECTION_CUSTOM_VALUE: gradle-actions
2024-12-04T05:15:17.9951281Z   GITHUB_DEPENDENCY_GRAPH_ENABLED: false
2024-12-04T05:15:17.9951804Z   GRADLE_CACHE_USERNAME: ***
2024-12-04T05:15:17.9952289Z   GRADLE_CACHE_PASSWORD: ***
2024-12-04T05:15:17.9952680Z ##[endgroup]
2024-12-04T05:15:18.6021316Z
2024-12-04T05:15:18.6023561Z Welcome to Gradle 8.10.2!
2024-12-04T05:15:18.6023892Z
2024-12-04T05:15:18.6024098Z Here are the highlights of this release:
2024-12-04T05:15:18.6024834Z  - Support for Java 23
2024-12-04T05:15:18.6025279Z  - Faster configuration cache
2024-12-04T05:15:18.6025786Z  - Better configuration cache reports
2024-12-04T05:15:18.6026119Z
2024-12-04T05:15:18.6026574Z For more details see https://docs.gradle.org/8.10.2/release-notes.html
2024-12-04T05:15:18.6027099Z
2024-12-04T05:15:18.7020186Z To honour the JVM settings for this build a single-use Daemon process will be forked. For more on this, please refer to https://docs.gradle.org/8.10.2/userguide/gradle_daemon.html#sec:disabling_the_daemon in the Gradle documentation.
2024-12-04T05:15:19.6015702Z Daemon will be stopped at the end of the build
2024-12-04T05:15:51.4020480Z
2024-12-04T05:15:51.4022484Z ################################################################################
2024-12-04T05:15:51.4023488Z # Thanks for using OpenAPI Generator.                                          #
2024-12-04T05:15:51.4025361Z # Please consider donation to help us maintain this project 🙏                 #
2024-12-04T05:15:51.4026882Z # https://opencollective.com/openapi_generator/donate                          #
2024-12-04T05:15:51.4027664Z #                                                                              #
2024-12-04T05:15:51.4029087Z # This generator's contributed by Jim Schubert (https://github.com/jimschubert)#
2024-12-04T05:15:51.4030886Z # Please support his work directly via https://patreon.com/jimschubert 🙏      #
2024-12-04T05:15:51.4032474Z ################################################################################
2024-12-04T05:15:51.4034000Z Successfully generated code to /actions-runner/_work/***/***/build/generated-client-api
2024-12-04T05:15:51.7010915Z
2024-12-04T05:16:08.4010886Z
2024-12-04T05:16:08.4012600Z
2024-12-04T05:16:08.4014831Z
2024-12-04T05:16:08.4017121Z
2024-12-04T05:16:08.4018805Z
2024-12-04T05:16:15.7010818Z
2024-12-04T05:16:15.7012081Z SlackBotQuestionTransformerTest > includes slack but user id() FAILED
2024-12-04T05:16:15.7013826Z     org.opentest4j.AssertionFailedError:
2024-12-04T05:16:15.7015073Z     expected: "Hello <@slackBotUserId>"
2024-12-04T05:16:15.7016123Z      but was: "Hello"
2024-12-04T05:16:15.7018213Z         at java.base@21.0.5/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
2024-12-04T05:16:15.7021476Z         at java.base@21.0.5/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
2024-12-04T05:16:15.7025276Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest$includes slack but user id$1.invokeSuspend(SlackBotQuestionTransformerTest.kt:54)
2024-12-04T05:16:15.7029169Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2024-12-04T05:16:15.7031602Z         at app//kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:266)
2024-12-04T05:16:15.7033704Z         at app//kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:100)
2024-12-04T05:16:15.7035926Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
2024-12-04T05:16:15.7037994Z         at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:101)
2024-12-04T05:16:15.7039926Z         at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
2024-12-04T05:16:15.7043993Z         at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
2024-12-04T05:16:15.7045844Z         at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
2024-12-04T05:16:15.7047579Z         at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
2024-12-04T05:16:15.7050260Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$lambda$3(DatabaseTestsBase.kt:231)
2024-12-04T05:16:15.7053277Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12$lambda$11$lambda$10(DatabaseTestsBase.kt:270)
2024-12-04T05:16:15.7056984Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$run(ThreadLocalTransactionManager.kt:345)
2024-12-04T05:16:15.7061645Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$lambda$11(ThreadLocalTransactionManager.kt:392)
2024-12-04T05:16:15.7066541Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:15.7071269Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction(ThreadLocalTransactionManager.kt:391)
2024-12-04T05:16:15.7075626Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$lambda$5(ThreadLocalTransactionManager.kt:300)
2024-12-04T05:16:15.7080304Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:15.7085038Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction(ThreadLocalTransactionManager.kt:249)
2024-12-04T05:16:15.7088807Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$default(ThreadLocalTransactionManager.kt:244)
2024-12-04T05:16:15.7091978Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12(DatabaseTestsBase.kt:268)
2024-12-04T05:16:15.7094198Z         at app//dev.failsafe.Functions.lambda$toCtxSupplier$11(Functions.java:243)
2024-12-04T05:16:15.7095872Z         at app//dev.failsafe.Functions.lambda$get$0(Functions.java:46)
2024-12-04T05:16:15.7097827Z         at app//dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:74)
2024-12-04T05:16:15.7100024Z         at app//dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:187)
2024-12-04T05:16:15.7101867Z         at app//dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:376)
2024-12-04T05:16:15.7103591Z         at app//dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:112)
2024-12-04T05:16:15.7105639Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb(DatabaseTestsBase.kt:264)
2024-12-04T05:16:15.7108845Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest(DatabaseTestsBase.kt:230)
2024-12-04T05:16:15.7112487Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$default(DatabaseTestsBase.kt:225)
2024-12-04T05:16:15.7116463Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest.includes slack but user id(SlackBotQuestionTransformerTest.kt:46)
2024-12-04T05:16:15.7119350Z
2024-12-04T05:16:23.5010256Z
2024-12-04T05:16:23.5011167Z SlackBotQuestionTransformerTest > includes slack but user id() FAILED
2024-12-04T05:16:23.5012049Z     org.opentest4j.AssertionFailedError:
2024-12-04T05:16:23.5012612Z     expected: "Hello <@slackBotUserId>"
2024-12-04T05:16:23.5013378Z      but was: "Hello"
2024-12-04T05:16:23.5014908Z         at java.base@21.0.5/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
2024-12-04T05:16:23.5016736Z         at java.base@21.0.5/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
2024-12-04T05:16:23.5018684Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest$includes slack but user id$1.invokeSuspend(SlackBotQuestionTransformerTest.kt:54)
2024-12-04T05:16:23.5020787Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2024-12-04T05:16:23.5022051Z         at app//kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:266)
2024-12-04T05:16:23.5023208Z         at app//kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:100)
2024-12-04T05:16:23.5024427Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
2024-12-04T05:16:23.5025558Z         at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:101)
2024-12-04T05:16:23.5026616Z         at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
2024-12-04T05:16:23.5027717Z         at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
2024-12-04T05:16:23.5028911Z         at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
2024-12-04T05:16:23.5029922Z         at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
2024-12-04T05:16:23.5031240Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$lambda$3(DatabaseTestsBase.kt:231)
2024-12-04T05:16:23.5032875Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12$lambda$11$lambda$10(DatabaseTestsBase.kt:270)
2024-12-04T05:16:23.5034755Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$run(ThreadLocalTransactionManager.kt:345)
2024-12-04T05:16:23.5036925Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$lambda$11(ThreadLocalTransactionManager.kt:392)
2024-12-04T05:16:23.5039207Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:23.5041449Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction(ThreadLocalTransactionManager.kt:391)
2024-12-04T05:16:23.5043475Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$lambda$5(ThreadLocalTransactionManager.kt:300)
2024-12-04T05:16:23.5045630Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:23.5047744Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction(ThreadLocalTransactionManager.kt:249)
2024-12-04T05:16:23.5049694Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$default(ThreadLocalTransactionManager.kt:244)
2024-12-04T05:16:23.5051361Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12(DatabaseTestsBase.kt:268)
2024-12-04T05:16:23.5052545Z         at app//dev.failsafe.Functions.lambda$toCtxSupplier$11(Functions.java:243)
2024-12-04T05:16:23.5053452Z         at app//dev.failsafe.Functions.lambda$get$0(Functions.java:46)
2024-12-04T05:16:23.5054484Z         at app//dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:74)
2024-12-04T05:16:23.5055648Z         at app//dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:187)
2024-12-04T05:16:23.5056636Z         at app//dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:376)
2024-12-04T05:16:23.5057559Z         at app//dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:112)
2024-12-04T05:16:23.5058786Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb(DatabaseTestsBase.kt:264)
2024-12-04T05:16:23.5060191Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest(DatabaseTestsBase.kt:230)
2024-12-04T05:16:23.5061809Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$default(DatabaseTestsBase.kt:225)
2024-12-04T05:16:23.5063970Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest.includes slack but user id(SlackBotQuestionTransformerTest.kt:46)
2024-12-04T05:16:31.1010587Z
2024-12-04T05:16:31.1011958Z SlackBotQuestionTransformerTest > includes slack but user id() FAILED
2024-12-04T05:16:31.1013042Z     org.opentest4j.AssertionFailedError:
2024-12-04T05:16:31.1013608Z     expected: "Hello <@slackBotUserId>"
2024-12-04T05:16:31.1014112Z      but was: "Hello"
2024-12-04T05:16:31.1015155Z         at java.base@21.0.5/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
2024-12-04T05:16:31.1016702Z         at java.base@21.0.5/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
2024-12-04T05:16:31.1018641Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest$includes slack but user id$1.invokeSuspend(SlackBotQuestionTransformerTest.kt:54)
2024-12-04T05:16:31.1020615Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2024-12-04T05:16:31.1021863Z         at app//kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:266)
2024-12-04T05:16:31.1023015Z         at app//kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:100)
2024-12-04T05:16:31.1024243Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
2024-12-04T05:16:31.1025365Z         at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:101)
2024-12-04T05:16:31.1026437Z         at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
2024-12-04T05:16:31.1027523Z         at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
2024-12-04T05:16:31.1028756Z         at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
2024-12-04T05:16:31.1029793Z         at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
2024-12-04T05:16:31.1031081Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$lambda$3(DatabaseTestsBase.kt:231)
2024-12-04T05:16:31.1032732Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12$lambda$11$lambda$10(DatabaseTestsBase.kt:270)
2024-12-04T05:16:31.1034616Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$run(ThreadLocalTransactionManager.kt:345)
2024-12-04T05:16:31.1036771Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$lambda$11(ThreadLocalTransactionManager.kt:392)
2024-12-04T05:16:31.1039081Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:31.1041327Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction(ThreadLocalTransactionManager.kt:391)
2024-12-04T05:16:31.1043365Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$lambda$5(ThreadLocalTransactionManager.kt:300)
2024-12-04T05:16:31.1045558Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:31.1047677Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction(ThreadLocalTransactionManager.kt:249)
2024-12-04T05:16:31.1051055Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$default(ThreadLocalTransactionManager.kt:244)
2024-12-04T05:16:31.1052740Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12(DatabaseTestsBase.kt:268)
2024-12-04T05:16:31.1053922Z         at app//dev.failsafe.Functions.lambda$toCtxSupplier$11(Functions.java:243)
2024-12-04T05:16:31.1054957Z         at app//dev.failsafe.Functions.lambda$get$0(Functions.java:46)
2024-12-04T05:16:31.1056002Z         at app//dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:74)
2024-12-04T05:16:31.1057163Z         at app//dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:187)
2024-12-04T05:16:31.1058174Z         at app//dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:376)
2024-12-04T05:16:31.1059089Z         at app//dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:112)
2024-12-04T05:16:31.1060203Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb(DatabaseTestsBase.kt:264)
2024-12-04T05:16:31.1061629Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest(DatabaseTestsBase.kt:230)
2024-12-04T05:16:31.1063223Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$default(DatabaseTestsBase.kt:225)
2024-12-04T05:16:31.1065312Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest.includes slack but user id(SlackBotQuestionTransformerTest.kt:46)
2024-12-04T05:16:39.0010766Z
2024-12-04T05:16:39.0011692Z SlackBotQuestionTransformerTest > includes slack but user id() FAILED
2024-12-04T05:16:39.0012587Z     org.opentest4j.AssertionFailedError:
2024-12-04T05:16:39.0013150Z     expected: "Hello <@slackBotUserId>"
2024-12-04T05:16:39.0013629Z      but was: "Hello"
2024-12-04T05:16:39.0014678Z         at java.base@21.0.5/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
2024-12-04T05:16:39.0016198Z         at java.base@21.0.5/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
2024-12-04T05:16:39.0018132Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest$includes slack but user id$1.invokeSuspend(SlackBotQuestionTransformerTest.kt:54)
2024-12-04T05:16:39.0020122Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2024-12-04T05:16:39.0021378Z         at app//kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:266)
2024-12-04T05:16:39.0022520Z         at app//kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:100)
2024-12-04T05:16:39.0023746Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
2024-12-04T05:16:39.0024857Z         at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:101)
2024-12-04T05:16:39.0025927Z         at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
2024-12-04T05:16:39.0027024Z         at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
2024-12-04T05:16:39.0028193Z         at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
2024-12-04T05:16:39.0029155Z         at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
2024-12-04T05:16:39.0030514Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$lambda$3(DatabaseTestsBase.kt:231)
2024-12-04T05:16:39.0032168Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12$lambda$11$lambda$10(DatabaseTestsBase.kt:270)
2024-12-04T05:16:39.0034049Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$run(ThreadLocalTransactionManager.kt:345)
2024-12-04T05:16:39.0036208Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$lambda$11(ThreadLocalTransactionManager.kt:392)
2024-12-04T05:16:39.0038766Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:39.0041116Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction(ThreadLocalTransactionManager.kt:391)
2024-12-04T05:16:39.0043357Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$lambda$5(ThreadLocalTransactionManager.kt:300)
2024-12-04T05:16:39.0045563Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:39.0047689Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction(ThreadLocalTransactionManager.kt:249)
2024-12-04T05:16:39.0049625Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$default(ThreadLocalTransactionManager.kt:244)
2024-12-04T05:16:39.0051314Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12(DatabaseTestsBase.kt:268)
2024-12-04T05:16:39.0052486Z         at app//dev.failsafe.Functions.lambda$toCtxSupplier$11(Functions.java:243)
2024-12-04T05:16:39.0053391Z         at app//dev.failsafe.Functions.lambda$get$0(Functions.java:46)
2024-12-04T05:16:39.0054441Z         at app//dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:74)
2024-12-04T05:16:39.0055583Z         at app//dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:187)
2024-12-04T05:16:39.0056584Z         at app//dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:376)
2024-12-04T05:16:39.0057495Z         at app//dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:112)
2024-12-04T05:16:39.0058589Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb(DatabaseTestsBase.kt:264)
2024-12-04T05:16:39.0060014Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest(DatabaseTestsBase.kt:230)
2024-12-04T05:16:39.0061616Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$default(DatabaseTestsBase.kt:225)
2024-12-04T05:16:39.0063695Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest.includes slack but user id(SlackBotQuestionTransformerTest.kt:46)
2024-12-04T05:16:46.3010153Z
2024-12-04T05:16:46.3011287Z SlackBotQuestionTransformerTest > includes slack but user id() FAILED
2024-12-04T05:16:46.3012528Z     org.opentest4j.AssertionFailedError:
2024-12-04T05:16:46.3013514Z     expected: "Hello <@slackBotUserId>"
2024-12-04T05:16:46.3014322Z      but was: "Hello"
2024-12-04T05:16:46.3016217Z         at java.base@21.0.5/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
2024-12-04T05:16:46.3019140Z         at java.base@21.0.5/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
2024-12-04T05:16:46.3022685Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest$includes slack but user id$1.invokeSuspend(SlackBotQuestionTransformerTest.kt:54)
2024-12-04T05:16:46.3026500Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2024-12-04T05:16:46.3029097Z         at app//kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:266)
2024-12-04T05:16:46.3031069Z         at app//kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:100)
2024-12-04T05:16:46.3033384Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
2024-12-04T05:16:46.3035510Z         at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:101)
2024-12-04T05:16:46.3037840Z         at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
2024-12-04T05:16:46.3039929Z         at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
2024-12-04T05:16:46.3041841Z         at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
2024-12-04T05:16:46.3043600Z         at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
2024-12-04T05:16:46.3046085Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$lambda$3(DatabaseTestsBase.kt:231)
2024-12-04T05:16:46.3049182Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12$lambda$11$lambda$10(DatabaseTestsBase.kt:270)
2024-12-04T05:16:46.3052742Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$run(ThreadLocalTransactionManager.kt:345)
2024-12-04T05:16:46.3056889Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$lambda$11(ThreadLocalTransactionManager.kt:392)
2024-12-04T05:16:46.3061136Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:46.3065498Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction(ThreadLocalTransactionManager.kt:391)
2024-12-04T05:16:46.3069689Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$lambda$5(ThreadLocalTransactionManager.kt:300)
2024-12-04T05:16:46.3073771Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:46.3077906Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction(ThreadLocalTransactionManager.kt:249)
2024-12-04T05:16:46.3081646Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$default(ThreadLocalTransactionManager.kt:244)
2024-12-04T05:16:46.3084867Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12(DatabaseTestsBase.kt:268)
2024-12-04T05:16:46.3087045Z         at app//dev.failsafe.Functions.lambda$toCtxSupplier$11(Functions.java:243)
2024-12-04T05:16:46.3088662Z         at app//dev.failsafe.Functions.lambda$get$0(Functions.java:46)
2024-12-04T05:16:46.3090594Z         at app//dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:74)
2024-12-04T05:16:46.3092718Z         at app//dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:187)
2024-12-04T05:16:46.3094529Z         at app//dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:376)
2024-12-04T05:16:46.3096267Z         at app//dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:112)
2024-12-04T05:16:46.3098285Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb(DatabaseTestsBase.kt:264)
2024-12-04T05:16:46.3100802Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest(DatabaseTestsBase.kt:230)
2024-12-04T05:16:46.3103806Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$default(DatabaseTestsBase.kt:225)
2024-12-04T05:16:46.3107575Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest.includes slack but user id(SlackBotQuestionTransformerTest.kt:46)
2024-12-04T05:16:53.6009815Z
2024-12-04T05:16:53.6010597Z SlackBotQuestionTransformerTest > includes slack but user id() FAILED
2024-12-04T05:16:53.6011920Z     org.opentest4j.AssertionFailedError:
2024-12-04T05:16:53.6012708Z     expected: "Hello <@slackBotUserId>"
2024-12-04T05:16:53.6013179Z      but was: "Hello"
2024-12-04T05:16:53.6014231Z         at java.base@21.0.5/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
2024-12-04T05:16:53.6015995Z         at java.base@21.0.5/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
2024-12-04T05:16:53.6017934Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest$includes slack but user id$1.invokeSuspend(SlackBotQuestionTransformerTest.kt:54)
2024-12-04T05:16:53.6019903Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
2024-12-04T05:16:53.6021300Z         at app//kotlinx.coroutines.UndispatchedCoroutine.afterResume(CoroutineContext.kt:266)
2024-12-04T05:16:53.6022444Z         at app//kotlinx.coroutines.AbstractCoroutine.resumeWith(AbstractCoroutine.kt:100)
2024-12-04T05:16:53.6023673Z         at app//kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)
2024-12-04T05:16:53.6024794Z         at app//kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:101)
2024-12-04T05:16:53.6025859Z         at app//kotlinx.coroutines.EventLoopImplBase.processNextEvent(EventLoop.common.kt:263)
2024-12-04T05:16:53.6026958Z         at app//kotlinx.coroutines.BlockingCoroutine.joinBlocking(Builders.kt:95)
2024-12-04T05:16:53.6028232Z         at app//kotlinx.coroutines.BuildersKt__BuildersKt.runBlocking(Builders.kt:69)
2024-12-04T05:16:53.6029196Z         at app//kotlinx.coroutines.BuildersKt.runBlocking(Unknown Source)
2024-12-04T05:16:53.6030545Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$lambda$3(DatabaseTestsBase.kt:231)
2024-12-04T05:16:53.6032198Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12$lambda$11$lambda$10(DatabaseTestsBase.kt:270)
2024-12-04T05:16:53.6034080Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$run(ThreadLocalTransactionManager.kt:345)
2024-12-04T05:16:53.6036237Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction$lambda$11(ThreadLocalTransactionManager.kt:392)
2024-12-04T05:16:53.6038536Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:53.6040775Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.inTopLevelTransaction(ThreadLocalTransactionManager.kt:391)
2024-12-04T05:16:53.6042802Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$lambda$5(ThreadLocalTransactionManager.kt:300)
2024-12-04T05:16:53.6044999Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.keepAndRestoreTransactionRefAfterRun(ThreadLocalTransactionManager.kt:400)
2024-12-04T05:16:53.6047122Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction(ThreadLocalTransactionManager.kt:249)
2024-12-04T05:16:53.6049043Z         at app//org.jetbrains.exposed.sql.transactions.ThreadLocalTransactionManagerKt.transaction$default(ThreadLocalTransactionManager.kt:244)
2024-12-04T05:16:53.6050729Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb$lambda$12(DatabaseTestsBase.kt:268)
2024-12-04T05:16:53.6051914Z         at app//dev.failsafe.Functions.lambda$toCtxSupplier$11(Functions.java:243)
2024-12-04T05:16:53.6052798Z         at app//dev.failsafe.Functions.lambda$get$0(Functions.java:46)
2024-12-04T05:16:53.6053849Z         at app//dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:74)
2024-12-04T05:16:53.6054999Z         at app//dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:187)
2024-12-04T05:16:53.6056000Z         at app//dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:376)
2024-12-04T05:16:53.6056921Z         at app//dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:112)
2024-12-04T05:16:53.6058005Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.withDb(DatabaseTestsBase.kt:264)
2024-12-04T05:16:53.6059563Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest(DatabaseTestsBase.kt:230)
2024-12-04T05:16:53.6061158Z         at app//com.nextchaptersoftware.db.utils.DatabaseTestsBase.suspendingDatabaseTest$default(DatabaseTestsBase.kt:225)
2024-12-04T05:16:53.6063350Z         at app//com.nextchaptersoftware.slack.bot.services.semantic.transform.SlackBotQuestionTransformerTest.includes slack but user id(SlackBotQuestionTransformerTest.kt:46)
2024-12-04T05:16:53.7011619Z
2024-12-04T05:16:53.7012714Z 11 tests completed, 6 failed, 1 skipped
2024-12-04T05:16:53.7013146Z
2024-12-04T05:16:53.8010300Z
2024-12-04T05:16:53.8011072Z FAILURE: Build failed with an exception.
2024-12-04T05:16:53.8011841Z
2024-12-04T05:16:53.8012048Z * What went wrong:
2024-12-04T05:16:53.8012789Z Execution failed for task ':projects:libs:lib-slack-bot:test'.
2024-12-04T05:16:53.8014407Z > There were failing tests. See the report at: file:///actions-runner/_work/***/***/projects/libs/lib-slack-bot/build/reports/tests/test/index.html
2024-12-04T05:16:53.8015332Z
2024-12-04T05:16:53.8015458Z * Try:
2024-12-04T05:16:53.8015847Z > Run with --scan to get full insights.
2024-12-04T05:16:53.8016197Z
2024-12-04T05:16:53.8016351Z BUILD FAILED in 1m 35s
2024-12-04T05:16:53.8016601Z
2024-12-04T05:16:53.8017091Z Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.
2024-12-04T05:16:53.8017730Z
2024-12-04T05:16:53.8018568Z You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.
2024-12-04T05:16:53.8019461Z
2024-12-04T05:16:53.8020332Z For more on this, please refer to https://docs.gradle.org/8.10.2/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.
2024-12-04T05:16:53.8021588Z 1554 actionable tasks: 373 executed, 1181 from cache
2024-12-04T05:17:05.1644493Z ##[error]Child_process exited with error code 1
2024-12-04T05:17:05.1775414Z ##[group]Run mikepenz/action-junit-report@v5.0.0-a01
2024-12-04T05:17:05.1775974Z with:
2024-12-04T05:17:05.1776287Z   check_retries: true
2024-12-04T05:17:05.1776674Z   detailed_summary: true
2024-12-04T05:17:05.1777056Z   flaky_summary: true
2024-12-04T05:17:05.1777527Z   report_paths: **/build/test-results/test/TEST-*.xml
2024-12-04T05:17:05.1778081Z   job_name: services-build
2024-12-04T05:17:05.1778475Z   update_check: true
2024-12-04T05:17:05.1778994Z   token: ***
2024-12-04T05:17:05.1779324Z   annotate_only: false
2024-12-04T05:17:05.1779725Z   check_annotations: true
2024-12-04T05:17:05.1780149Z   check_name: JUnit Test Report
2024-12-04T05:17:05.1780586Z   fail_on_failure: false
2024-12-04T05:17:05.1780980Z   require_tests: false
2024-12-04T05:17:05.1781363Z   require_passed_tests: false
2024-12-04T05:17:05.1781793Z   include_passed: false
2024-12-04T05:17:05.1782190Z   bread_crumb_delimiter: /
2024-12-04T05:17:05.1782614Z   transformers: []
2024-12-04T05:17:05.1783013Z   job_summary: true
2024-12-04T05:17:05.1783375Z   annotate_notice: false
2024-12-04T05:17:05.1783777Z   follow_symlink: false
2024-12-04T05:17:05.1784169Z   truncate_stack_traces: true
2024-12-04T05:17:05.1784572Z env:
2024-12-04T05:17:05.1784891Z   AWS_REGION: us-west-2
2024-12-04T05:17:05.1785338Z   OUTPUT_ARTIFACT_NAME: built-artifacts-57101
2024-12-04T05:17:05.1785880Z   BUILD_ARTIFACTS_DIR: ./build/libs
2024-12-04T05:17:05.1786384Z   TOUCH: Dummy value to force workflow rerun
2024-12-04T05:17:05.1786910Z   SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
2024-12-04T05:17:05.1787565Z   GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
2024-12-04T05:17:05.1788376Z   CI: true
2024-12-04T05:17:05.1788744Z   CI_PR_TITLE: Include slack bot user id
2024-12-04T05:17:05.1789201Z   CI_TITLE:
2024-12-04T05:17:05.1789602Z   GH_HOME: /root
2024-12-04T05:17:05.1789967Z   AWS_DEFAULT_REGION: us-west-2
2024-12-04T05:17:05.1790466Z   AWS_ACCESS_KEY_ID: ***
2024-12-04T05:17:05.1791208Z   AWS_SECRET_ACCESS_KEY: ***
2024-12-04T05:17:05.1797143Z   AWS_SESSION_TOKEN: ***
2024-12-04T05:17:05.1797791Z   JAVA_HOME: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:17:05.1798791Z   JAVA_HOME_21_X64: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/21.0.5-11.0.LTS/x64
2024-12-04T05:17:05.1799729Z   GRADLE_ARGS: --no-daemon build -x :common:generateProto -x :npmInstall
2024-12-04T05:17:05.1800445Z   GRADLE_ACTION_ID: gradle/actions/setup-gradle
2024-12-04T05:17:05.1801007Z   GRADLE_BUILD_ACTION_SETUP_COMPLETED: true
2024-12-04T05:17:05.1801535Z   GRADLE_BUILD_ACTION_CACHE_RESTORED: true
2024-12-04T05:17:05.1802318Z   DEVELOCITY_INJECTION_INIT_SCRIPT_NAME: gradle-actions.inject-develocity.init.gradle
2024-12-04T05:17:05.1803180Z   DEVELOCITY_AUTO_INJECTION_CUSTOM_VALUE: gradle-actions
2024-12-04T05:17:05.1803786Z   GITHUB_DEPENDENCY_GRAPH_ENABLED: false
2024-12-04T05:17:05.1804247Z ##[endgroup]
2024-12-04T05:17:05.2705486Z ##[group]📘 Reading input values
2024-12-04T05:17:05.2708242Z ##[endgroup]
2024-12-04T05:17:05.2708964Z ##[group]📦 Process test results
2024-12-04T05:17:05.2709507Z Preparing 1 report as configured.
2024-12-04T05:17:11.5181236Z projects/libs/lib-slack-bot/src/test/kotlin/com/nextchaptersoftware/slack/bot/services/semantic/transform/SlackBotQuestionTransformerTest.kt:46 | org.opentest4j.AssertionFailedError:  (7.834s)
2024-12-04T05:17:13.2350920Z ℹ️ Posting with conclusion 'failure' to https://github.com/NextChapterSoftware/***/pull/17271 (sha: eedd3907c0295cbac9b71f83efbe7d3bc3692e30)
2024-12-04T05:17:13.2352419Z ##[endgroup]
2024-12-04T05:17:13.2353194Z ##[group]🚀 Publish results
2024-12-04T05:17:13.2353949Z ℹ️ - JUnit Test Report - 4663 tests run, 4478 passed, 184 skipped, 1 failed.
2024-12-04T05:17:13.2355920Z    🧪 - projects/libs/lib-slack-bot/src/test/kotlin/com/nextchaptersoftware/slack/bot/services/semantic/transform/SlackBotQuestionTransformerTest.kt | org.opentest4j.AssertionFailedError:
2024-12-04T05:17:13.5454733Z ℹ️ - JUnit Test Report - Updating checks (Annotations: 1)
2024-12-04T05:17:13.5492231Z ##[endgroup]
2024-12-04T05:17:13.8697476Z Post job cleanup.
2024-12-04T05:17:14.1113406Z In post-action step
2024-12-04T05:17:14.1120289Z Cache is disabled: will not save state for later builds.
2024-12-04T05:17:14.1124030Z Generating Job Summary
2024-12-04T05:17:14.1141465Z Completed post-action step
2024-12-04T05:17:14.1273466Z Post job cleanup.
2024-12-04T05:17:14.2928476Z Post job cleanup.
2024-12-04T05:17:14.3936388Z Deleting ".ansible_vault_password" file
2024-12-04T05:17:14.4040839Z Post job cleanup.
2024-12-04T05:17:14.4961035Z [command]/usr/bin/git version
2024-12-04T05:17:14.4992938Z git version 2.34.1
2024-12-04T05:17:14.5032807Z Temporarily overriding HOME='/actions-runner/_work/_temp/7dec00da-08cc-4234-bacb-7eb1f111879f' before making global git config changes
2024-12-04T05:17:14.5034156Z Adding repository directory to the temporary git global config as a safe directory
2024-12-04T05:17:14.5043971Z [command]/usr/bin/git config --global --add safe.directory /actions-runner/_work/***/***
2024-12-04T05:17:14.5071122Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
2024-12-04T05:17:14.5094734Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
2024-12-04T05:17:14.5288379Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:17:14.5321791Z Entering 'shared/clientAssets'
2024-12-04T05:17:14.5368566Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
2024-12-04T05:17:14.5383774Z http.https://github.com/.extraheader
2024-12-04T05:17:14.5392501Z [command]/usr/bin/git config --local --unset-all http.https://github.com/.extraheader
2024-12-04T05:17:14.5415322Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
2024-12-04T05:17:14.5600886Z Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
2024-12-04T05:17:14.5620307Z http.https://github.com/.extraheader
2024-12-04T05:17:14.5646989Z Entering 'shared/clientAssets'
2024-12-04T05:17:14.5665809Z http.https://github.com/.extraheader
2024-12-04T05:17:14.5813645Z Post job cleanup.
2024-12-04T05:17:14.6970117Z A job completed hook has been configured by the self-hosted runner administrator
2024-12-04T05:17:14.6999845Z ##[group]Run '/shutdown_script.sh'
2024-12-04T05:17:14.7015131Z shell: /usr/bin/bash --noprofile --norc -e -o pipefail {0}
2024-12-04T05:17:14.7015733Z ##[endgroup]
2024-12-04T05:17:14.7081427Z Shutdown scheduled for Wed 2024-12-04 05:18:14 UTC, use 'shutdown -c' to cancel.
2024-12-04T05:17:14.7274949Z Evaluate and set job outputs
2024-12-04T05:17:14.7287588Z ##[warning]Skip output 'artifacts-cache-bucket' since it may contain secret.
2024-12-04T05:17:14.7289153Z Set output 'artifacts-cache-path'
2024-12-04T05:17:14.7290284Z Cleaning up orphan processes
