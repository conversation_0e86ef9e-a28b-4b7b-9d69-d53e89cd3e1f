package com.nextchaptersoftware.search.semantic.services.documentation

import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeSlackThread
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class DocumentFilterServiceTest : DatabaseTestsBase() {

    private val mockEmbeddingEventEnqueueService: EmbeddingEventEnqueueService = mock()
    private val mockIndexingAndEmbeddingService: IndexingAndEmbeddingService = mock()
    private val slackDocumentRetentionFilterService = SlackDocumentRetentionFilterService(
        slackThreadStore = Stores.slackThreadStore,
        embeddingEventEnqueueService = mockEmbeddingEventEnqueueService,
    )
    private val slackThreadRetentionFilterService = SlackThreadRetentionFilterService(
        indexingAndEmbeddingService = mockIndexingAndEmbeddingService,
    )
    private val documentFilterService = StandardDocumentFilterService(
        listOf(
            slackDocumentRetentionFilterService,
            slackThreadRetentionFilterService,
        ),
    )

    @Test
    fun `should keep non-Slack documents unchanged`() = suspendingDatabaseTest {
        val org = makeOrg()
        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Documentation,
                documentType = DocumentType.Documentation,
            ),
            createMLTypedDocument(
                sourceType = InsightType.PullRequest,
                documentType = DocumentType.Documentation,
            ),
        )

        val result = documentFilterService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(2)
        assertThat(result).containsExactlyElementsOf(documents)
    }

    @Test
    fun `should keep non-Documentation documents unchanged`() = suspendingDatabaseTest {
        val org = makeOrg()
        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.PullRequest,
                documentType = DocumentType.Thread,
            ),
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Code,
            ),
        )

        val result = documentFilterService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(2)
        assertThat(result).containsExactlyElementsOf(documents)
    }

    @Test
    fun `should keep Slack Documentation documents when SlackThread exists`() = suspendingDatabaseTest {
        val org = makeOrg()

        // Create a SlackThread in the database
        val slackThread = makeSlackThread(org = org)

        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = slackThread.id.value.value,
                groupId = slackThread.slackChannel.id.value.value,
            ),
        )

        val result = documentFilterService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(1)
        assertThat(result).containsExactlyElementsOf(documents)
    }

    @Test
    fun `should filter out Slack Documentation documents when SlackThread does not exist`() = suspendingDatabaseTest {
        val org = makeOrg()
        val nonExistentSlackThreadId = UUID.randomUUID()
        val existingSlackThread = makeSlackThread()

        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = nonExistentSlackThreadId,
            ),
            createMLTypedDocument(
                sourceType = InsightType.Documentation,
                documentType = DocumentType.Documentation,
                sourceDocumentId = existingSlackThread.id.value.value,
                groupId = existingSlackThread.slackChannel.id.value.value,
            ),
        )

        val result = documentFilterService.filterDocuments(org.idValue, documents)

        // Should filter out the Slack document since no SlackThread exists
        assertThat(result).hasSize(1)
        assertThat(result[0].sourceType).isEqualTo(InsightType.Documentation)
    }

    @Test
    fun `should keep Slack Documentation documents when sourceDocumentId is null`() = suspendingDatabaseTest {
        val org = makeOrg()
        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = null,
            ),
        )

        val result = documentFilterService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(1)
        assertThat(result).containsExactlyElementsOf(documents)
    }

    @Test
    fun `NoopDocumentFilterService should return all documents unchanged`() = suspendingDatabaseTest {
        val noopService = NoopDocumentFilterService()
        val org = makeOrg()
        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
            ),
        )

        val result = noopService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(1)
        assertThat(result).containsExactlyElementsOf(documents)
    }

    @Test
    fun `StandardDocumentFilterService should apply multiple filters in sequence`() = suspendingDatabaseTest {
        val org = makeOrg()
        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
            ),
            createMLTypedDocument(
                sourceType = InsightType.Documentation,
                documentType = DocumentType.Documentation,
            ),
        )

        // Create a filter that removes the first document
        val customFilter = object : DocumentFilterService {
            override suspend fun filterDocuments(orgId: OrgId, documents: List<MLTypedDocument>): List<MLTypedDocument> {
                return documents.drop(1) // Remove first document
            }
        }

        val compositeService = StandardDocumentFilterService(listOf(customFilter))
        val result = compositeService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(1)
        assertThat(result[0].sourceType).isEqualTo(InsightType.Documentation)
    }

    @Test
    fun `StandardDocumentFilterService with empty filter list should return all documents`() = suspendingDatabaseTest {
        val org = makeOrg()
        val documents = listOf(
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
            ),
        )

        val compositeService = StandardDocumentFilterService(emptyList())
        val result = compositeService.filterDocuments(org.idValue, documents)

        assertThat(result).hasSize(1)
        assertThat(result).containsExactlyElementsOf(documents)
    }

    @Test
    fun `should handle mixed scenarios with real database interactions`() = suspendingDatabaseTest {
        val org = makeOrg()

        // Create some SlackThreads in the database
        val existingSlackThread1 = makeSlackThread(org = org)
        val existingSlackThread2 = makeSlackThread(org = org)

        val documents = listOf(
            // Should be kept (SlackThread exists)
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = existingSlackThread1.id.value.value,
                groupId = existingSlackThread1.slackChannel.id.value.value,
            ),
            // Should be filtered out (SlackThread doesn't exist)
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
            ),
            // Should be kept (not Slack Documentation)
            createMLTypedDocument(
                sourceType = InsightType.Documentation,
                documentType = DocumentType.Documentation,
            ),
            // Should be kept (SlackThread exists)
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = existingSlackThread2.id.value.value,
                groupId = existingSlackThread2.slackChannel.id.value.value,
            ),
            // Should be kept (null sourceDocumentId)
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = null,
            ),
            // Should be filtered out (SlackChannel doesn't exist)
            createMLTypedDocument(
                sourceType = InsightType.Slack,
                documentType = DocumentType.Documentation,
                sourceDocumentId = existingSlackThread1.id.value.value,
                groupId = UUID.randomUUID(), // Channel doesn't exist
            ),
        )

        val result = documentFilterService.filterDocuments(org.idValue, documents)

        // Should keep 4 documents: 2 existing SlackThreads + 1 non-Slack + 1 null sourceDocumentId
        assertThat(result).hasSize(4)

        // Verify the correct documents are kept
        val keptSlackDocuments = result.filter {
            it.sourceType == InsightType.Slack && it.documentType == DocumentType.Documentation
        }
        assertThat(keptSlackDocuments).hasSize(3) // 2 existing + 1 null

        val keptSlackThreadIds = keptSlackDocuments.mapNotNull { it.sourceDocumentId }.toSet()
        assertThat(keptSlackThreadIds).containsExactlyInAnyOrder(
            existingSlackThread1.id.value.value,
            existingSlackThread2.id.value.value,
        )
    }

    private fun createMLTypedDocument(
        sourceType: InsightType,
        documentType: DocumentType,
        sourceDocumentId: UUID? = UUID.randomUUID(),
        groupId: UUID? = UUID.randomUUID(),
    ): MLTypedDocument {
        return MLTypedDocument(
            content = "Test content",
            source = "Test source",
            provider = Provider.Slack,
            documentType = documentType,
            sourceType = sourceType,
            sourceId = UUID.randomUUID(),
            documentId = UUID.randomUUID().toString(),
            sourceDocumentId = sourceDocumentId,
            groupId = groupId,
        )
    }
}
