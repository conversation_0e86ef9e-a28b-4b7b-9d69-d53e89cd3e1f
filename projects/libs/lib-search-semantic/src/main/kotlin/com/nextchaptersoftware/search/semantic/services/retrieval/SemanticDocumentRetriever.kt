package com.nextchaptersoftware.search.semantic.services.retrieval

import arrow.fx.coroutines.parMap
import com.nextchaptersoftware.data.preset.DataSourceInstallationPreset
import com.nextchaptersoftware.data.preset.DataSourcePresetConfiguration
import com.nextchaptersoftware.data.preset.DataSourcePresetGroups
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLRerankModel
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.stores.OrgMemberDecorator
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.mlSettingsStore
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.dsac.filter.DataSourceAccessControlFilterFactory
import com.nextchaptersoftware.kotlinx.coroutines.DOCUMENT_RETRIEVAL
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import com.nextchaptersoftware.ml.doc.rerank.services.DocumentRerankService
import com.nextchaptersoftware.ml.embedding.services.IEmbeddingService
import com.nextchaptersoftware.ml.query.context.DocumentContext
import com.nextchaptersoftware.ml.query.context.DocumentQueryContext
import com.nextchaptersoftware.ml.query.context.DocumentSet
import com.nextchaptersoftware.ml.query.context.GroupIdFilter
import com.nextchaptersoftware.ml.query.context.MLQuery
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.repo.RepoAccessResult
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.search.semantic.services.SemanticSearchDocumentService
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.PostRetrievalFilter
import com.nextchaptersoftware.search.semantic.services.retrieval.promotion.DocumentPromotionCondition
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.trace.coroutine.withOpenTelemetryContext
import com.nextchaptersoftware.types.MLReference
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class SemanticDocumentRetriever(
    private val dataSourceAccessControlFilterFactory: DataSourceAccessControlFilterFactory,
    private val documentRerankService: DocumentRerankService,
    private val embedder: IEmbeddingService,
    private val orgMemberDecorator: OrgMemberDecorator = Stores.orgMemberDecorator,
    private val planCapabilitiesService: PlanCapabilitiesService,
    private val repoAccessService: RepoAccessService,
    private val repoFocusService: RepoFocusService,
    private val semanticSearchDocumentService: SemanticSearchDocumentService,
    private val slackChannelAccessService: SlackChannelAccessService,
) {
    @Suppress("LongMethod", "CyclomaticComplexMethod")
    suspend fun doRetrieval(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        documentQuery: MLQuery,
        rerankQuery: MLQuery = documentQuery,
        template: MLInferenceTemplate,
        maxDocuments: Int = template.maxDocuments,
        maxRerankDocs: Int = template.maxRerankDocs,
        baseDocumentSet: DocumentSet = DocumentSet(
            documentTypes = template.documentTypes,
            insightTypes = template.insightTypes,
            sourceTypes = template.sourceTypes,
            kNearest = maxDocuments,
            groupIdFilter = GroupIdFilter.PublicGroups,
        ),
        useRRF: Boolean = template.useRRF,
        useCERR: Boolean = template.useCERR,
        sparseVectorWeight: Float = template.sparseVectorWeight,
        enableSparseVectorCodeSearch: Boolean = template.enableSparseVectorCodeSearch,
        documentTimeout: Duration,
        rerankModel: MLRerankModel = template.rerankModel,
        exclusiveRepoSet: Set<RepoId>? = null,
        nonExclusiveRepoSet: Set<RepoId>? = null,
        dsacContext: DsacContext,
        useShallowRetrievalWhenDsacOff: Boolean = false,
        dataSourcePresetConfiguration: DataSourcePresetConfiguration,
        wideSlackEmbeddingsRetrievalStyle: WideSlackEmbeddingsRetrievalStyle = WideSlackEmbeddingsRetrievalStyle.Always,
        postRetrievalFilters: List<PostRetrievalFilter> = emptyList(),
        documentPromotionConditions: List<DocumentPromotionCondition> = emptyList(),
    ): List<MLTypedDocument> = withLoggingContextAsync(
        "orgId" to orgId,
        "orgMemberId" to orgMemberId,
    ) {
        val exclusiveRepoSetNullIfEmpty = exclusiveRepoSet?.nullIfEmpty()
        val nonExclusiveRepoSetNullIfEmpty = nonExclusiveRepoSet?.nullIfEmpty()

        LOGGER.debugSensitiveAsync(
            "template" to template.name,
            "exclusiveRepos" to (exclusiveRepoSetNullIfEmpty?.joinToString(",") { it.toString() } ?: "none"),
            "nonExclusiveRepos" to (nonExclusiveRepoSetNullIfEmpty?.joinToString(",") { it.toString() } ?: "none"),
            sensitiveFields = mapOf(
                "documentQuery" to documentQuery.text,
            ),
        ) { "Starting document retrieval" }

        val repoAccess = when (dsacContext.isDsacOff) {
            true -> RepoAccessResult.All
            else -> repoAccessService.getRepoAccessForOrg(orgId = orgId, orgMemberId = orgMemberId)
        }
        LOGGER.debugAsync("repoAccess" to repoAccess) { "Document retrieval: repo access" }

        // FIXME: This is broken under the following scenario:
        // 1. OrgMember for query is OrgMember with account (person is not null), associated with GitHub integration
        // 2. User has an account in BitBucket, but has not signed in with BitBucket, although verified emails match GitHub
        // 3. Decorator will not find the BitBucket OrgMember, so no focused repos will be found
        val associatedOrgMemberIds = if (dsacContext.isDsacOff) {
            orgMemberDecorator.findAssociatedScmOrgMemberIds(orgId = orgId, orgMemberId = orgMemberId)
        } else {
            listOf(orgMemberId)
        }

        val repoFocusResult = repoFocusService.findRepoFocus(orgId = orgId, orgMemberIds = associatedOrgMemberIds)
        val repoFocus = repoFocusResult.map { it.repoId }.toSet()
        LOGGER.debugAsync("repoFocus" to repoFocus) { "Document retrieval: repo focus" }

        if (exclusiveRepoSetNullIfEmpty != null && repoAccess == RepoAccessResult.None) {
            LOGGER.debugAsync { "No access to exclusive repos" }
            return@withLoggingContextAsync emptyList()
        }

        val documentSets = if (useShallowRetrievalWhenDsacOff && dsacContext.isDsacOff) {
            listOf(baseDocumentSet)
        } else {
            buildDeepDocumentSets(
                orgId = orgId,
                orgMemberId = orgMemberId,
                documentSet = baseDocumentSet,
                exclusiveRepoSet = exclusiveRepoSetNullIfEmpty,
                nonExclusiveRepoSet = nonExclusiveRepoSetNullIfEmpty,
                repoAccess = repoAccess,
                repoFocus = repoFocus,
                dataSourcePresetConfiguration = dataSourcePresetConfiguration,
                wideSlackEmbeddingsRetrievalStyle = wideSlackEmbeddingsRetrievalStyle,
            )
        }

        val documentQueryVector: MLQuery.MLVectorQuery = when (documentQuery) {
            is MLQuery.MLTextQuery -> {
                val embeddingModel = mlSettingsStore.getReadEmbeddingModel(orgId = orgId)
                val embedding = embedder.getQueryEmbedding(documentQuery.text, embeddingModel)

                MLQuery.MLVectorQuery(documentQuery.text, embedding)
            }

            is MLQuery.MLVectorQuery -> {
                documentQuery
            }
        }

        // Create a data structure to carry both the document set and the sparse vector weight
        data class DocumentSetWithWeight(
            val documentSet: DocumentSet,
            val sparseVectorWeight: Float,
        )

        // Build the list of document sets with their respective sparse vector weights
        val documentSetsWithWeights = buildList {
            // Add all document sets with the original sparse vector weight
            documentSets.forEach { docSet ->
                add(DocumentSetWithWeight(docSet, sparseVectorWeight))
            }

            // Add additional SourceCode document sets with sparse vector weight 1.0f if enabled
            if (enableSparseVectorCodeSearch) {
                documentSets.filter { docSet ->
                    docSet.insightTypes.contains(InsightType.SourceCode)
                }.forEach { docSet ->
                    add(DocumentSetWithWeight(docSet, 1.0f))
                }
            }
        }

        val results = documentSetsWithWeights.parMap(
            context = Dispatchers.DOCUMENT_RETRIEVAL.withOpenTelemetryContext(currentCoroutineContext()),
        ) { (docSet, effectiveSparseVectorWeight) ->
            queryDocSet(
                orgId = orgId,
                docSet = docSet,
                useRRF = useRRF,
                sparseVectorWeight = effectiveSparseVectorWeight,
                dsacContext = dsacContext,
                repoAccess = repoAccess,
                orgMemberId = orgMemberId,
                documentQuery = documentQueryVector,
                useCERR = useCERR,
                documentTimeout = documentTimeout,
                rerankQuery = rerankQuery,
                template = template,
                rerankModel = rerankModel,
                maxRerankDocs = maxRerankDocs,
                postRetrievalFilters = postRetrievalFilters,
            )
        }

        results.flatten()
            .asSequence()
            .distinctBy {
                it.searchDocumentIdCompat
            }
            .map { doc ->
                when (documentPromotionConditions.any { condition -> condition.shouldPromote(doc) }) {
                    true -> doc.copy(score = 1.0f)
                    false -> doc
                }
            }
            .sortedByDescending {
                it.score ?: 0.0f
            }
            .take(baseDocumentSet.kNearest)
            .mapIndexed { index, doc ->
                doc.copy(
                    reference = MLReference(index),
                )
            }
            .toList()
    }

    @Suppress("LongMethod")
    private suspend fun queryDocSet(
        orgId: OrgId,
        docSet: DocumentSet,
        useRRF: Boolean,
        sparseVectorWeight: Float,
        dsacContext: DsacContext,
        repoAccess: RepoAccessResult,
        orgMemberId: OrgMemberId,
        documentQuery: MLQuery,
        useCERR: Boolean,
        documentTimeout: Duration,
        rerankQuery: MLQuery,
        template: MLInferenceTemplate,
        rerankModel: MLRerankModel,
        maxRerankDocs: Int,
        postRetrievalFilters: List<PostRetrievalFilter>,
    ): List<MLTypedDocument> {
        @Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
        val documents = runCatching {
            withTimeout(documentTimeout) {
                val semanticSearchDocs: List<MLTypedDocument> = semanticSearchDocumentService.queryDocumentSet(
                    orgId = orgId,
                    query = documentQuery,
                    documentSet = docSet,
                    useRRF = useRRF,
                    sparseVectorWeight = sparseVectorWeight,
                    dsacContext = dsacContext,
                )

                LOGGER.debugAsync(
                    "orgId" to orgId,
                    "orgMemberId" to orgMemberId,
                    "semanticSearchDocs" to semanticSearchDocs.size,
                ) {
                    "Document retrieval: semantic search results"
                }

                semanticSearchDocs
                    .let {
                        dataSourceAccessControlFilterFactory.get(
                            dsacContext = dsacContext,
                            repoAccess = repoAccess,
                            dataSourcePresetConfiguration = DataSourcePresetConfiguration.All, // REVIEW: why All?
                        ).filterDocuments(orgId = orgId, documents = it)
                    }
                    .sortedByDescending { it.score }
            }
        }.getOrElse { error ->
            LOGGER.errorSensitiveAsync(
                error,
                "insightTypes" to docSet.insightTypes.joinToString { it.name },
                sensitiveFields = mapOf(
                    "query" to documentQuery.text,
                ),
            ) {
                "Error querying document set"
            }

            emptyList()
        }.filter { doc ->
            postRetrievalFilters.none { filter -> !filter.filter(documentQuery.text, doc) }
        }

        @Suppress("TooGenericExceptionCaught") // Ignore: We need to catch TimeoutCancellationException
        return when (useCERR && documents.isNotEmpty()) {
            true -> try {
                documentRerankService.rerankDocuments(
                    orgId = orgId,
                    documentContext = DocumentContext(
                        documentQueryContext = DocumentQueryContext(
                            documentQueryText = rerankQuery.text,
                            documentQuery = rerankQuery,
                        ),
                        documents = documents,
                        mlFunctionContent = emptySet(),
                    ),
                    template = template.copy(
                        rerankModel = rerankModel,
                        maxRerankDocs = maxRerankDocs,
                    ),
                ).documents
            } catch (e: Exception) {
                LOGGER.errorAsync(
                    e,
                    "insightTypes" to docSet.insightTypes.joinToString { it.displayName },
                ) { "Failed to re-rank for document retrieval" }
                documents
            }

            else -> documents
        }
    }

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    suspend fun buildDeepDocumentSets(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        documentSet: DocumentSet,
        exclusiveRepoSet: Set<RepoId>?,
        nonExclusiveRepoSet: Set<RepoId>?,
        repoAccess: RepoAccessResult,
        repoFocus: Set<RepoId>,
        dataSourcePresetConfiguration: DataSourcePresetConfiguration,
        wideSlackEmbeddingsRetrievalStyle: WideSlackEmbeddingsRetrievalStyle,
    ): Set<DocumentSet> {
        // If an exclusive filter is present, then we will _only_ perform source code and PR queries against the exclusive repo
        LOGGER.debugAsync(
            "exclusiveRepoSet" to (exclusiveRepoSet?.joinToString(",") { it.toString() } ?: "none"),
            "nonExclusiveRepoSet" to (nonExclusiveRepoSet?.joinToString(",") { it.toString() } ?: "none"),
            "repoAccess" to repoAccess,
            "repoFocus" to repoFocus.joinToString(","),
        ) {
            "Building document sets"
        }

        val exclusiveRepoSetNullIfEmpty = exclusiveRepoSet?.nullIfEmpty()
        val nonExclusiveRepoSetNullIfEmpty = nonExclusiveRepoSet?.nullIfEmpty()

        val exclusiveRepos = exclusiveRepoSetNullIfEmpty?.let { repoIds ->
            when (repoAccess) {
                is RepoAccessResult.All -> repoIds
                is RepoAccessResult.None -> return emptySet()
                is RepoAccessResult.Some -> repoAccess.allowed.intersect(repoIds).nullIfEmpty() ?: return emptySet()
            }
        } ?: emptySet()

        LOGGER.debugAsync(
            "exclusiveRepos" to exclusiveRepos.joinToString(","),
        ) {
            "Building document sets: exclusive repos"
        }

        val nonExclusiveRepos = nonExclusiveRepoSetNullIfEmpty?.let { repoIds ->
            when (repoAccess) {
                is RepoAccessResult.All -> repoIds
                is RepoAccessResult.None -> null
                is RepoAccessResult.Some -> repoAccess.allowed.intersect(repoIds).nullIfEmpty()
            }
        }

        val focusedRepos = when (repoAccess) {
            is RepoAccessResult.All -> repoFocus
            is RepoAccessResult.None -> null
            is RepoAccessResult.Some -> repoAccess.allowed.intersect(repoFocus).nullIfEmpty()
        }

        LOGGER.debugAsync(
            "nonExclusiveRepos" to nonExclusiveRepos?.joinToString(","),
        ) {
            "Building document sets: non-exclusive repos"
        }

        // These are the repos allowed for the wide query
        val allowedWideRepos = when (repoAccess) {
            is RepoAccessResult.All -> emptySet()
            is RepoAccessResult.None -> null
            is RepoAccessResult.Some -> repoAccess.allowed
        }

        return if (exclusiveRepos.isNotEmpty()) {
            val exclusiveRepoBasedDocSet = createRepoSets(
                repoIds = exclusiveRepos,
                documentSet = documentSet,
                orgMemberId = orgMemberId,
                dataSourcePresetConfiguration = dataSourcePresetConfiguration,
            )

            LOGGER.debugAsync(
                "exclusiveRepoBasedDocSet" to exclusiveRepoBasedDocSet.joinToString(","),
            ) {
                "Building document sets: exclusive repo based document set"
            }

            exclusiveRepoBasedDocSet
        } else {
            // Compile the document sets for the focused repo queries
            val focusedRepoSets = focusedRepos?.nullIfEmpty()?.let { repoIds ->
                createRepoSets(
                    repoIds = repoIds,
                    documentSet = documentSet,
                    orgMemberId = orgMemberId,
                    dataSourcePresetConfiguration = dataSourcePresetConfiguration,
                )
            } ?: emptySet()

            LOGGER.debugAsync(
                "focusedRepoSets" to focusedRepoSets.joinToString(","),
            ) {
                "Building document sets: focused repo sets"
            }

            val nonExclusiveRepoSets = nonExclusiveRepos?.nullIfEmpty()?.let { repoIds ->
                createRepoSets(
                    repoIds = repoIds,
                    documentSet = documentSet,
                    orgMemberId = orgMemberId,
                    dataSourcePresetConfiguration = dataSourcePresetConfiguration,
                )
            } ?: emptySet()

            LOGGER.debugAsync(
                "nonExclusiveRepoSets" to nonExclusiveRepoSets.joinToString(","),
            ) {
                "Building document sets: non-exclusive repo sets"
            }

            val wideRepoSets = allowedWideRepos?.let { repoIds ->
                createRepoSets(
                    repoIds = repoIds,
                    documentSet = documentSet,
                    orgMemberId = orgMemberId,
                    dataSourcePresetConfiguration = dataSourcePresetConfiguration,
                )
            } ?: emptySet()

            val wideSlackSets = documentSet.insightTypes.mapNotNull { insightType ->
                when (insightType) {
                    InsightType.Slack -> {
                        val privateChannelIds = slackChannelAccessService.privateChannelIdsForOrgMember(
                            orgId = orgId,
                            orgMemberId = orgMemberId,
                        )
                        val groupIdFilter = if (privateChannelIds.isEmpty()) {
                            GroupIdFilter.PublicGroups
                        } else {
                            GroupIdFilter.PublicOrSpecificGroups(privateChannelIds.map { it.value }.toSet())
                        }
                        val slackPresetGroupIdFilter = dataSourcePresetConfiguration.intersectForSlackInstallations(
                            orgId = orgId,
                            orgMemberId = orgMemberId,
                            dsacGroupIdFilter = groupIdFilter,
                        )
                        documentSet
                            .withGroupIdFilter(slackPresetGroupIdFilter)
                            .withInsightTypesFilter(setOf(InsightType.Slack, InsightType.Documentation))
                    }

                    else -> null
                }
            }.toSet()

            val dataPresetGroupIdFilter = dataSourcePresetConfiguration.groupIdFilter()
            val wideDocumentSets = documentSet.insightTypes.mapNotNull { insightType ->
                when (insightType) {
                    InsightType.Documentation,
                    InsightType.Discussion, // FIXME richie *potentially* a mixture of repo-scoped and non-repo scoped insights
                    InsightType.Issue, // FIXME richie this is a mixture of repo-scoped and non-repo scoped insights
                    InsightType.Answer,
                        -> documentSet
                        .withGroupIdFilter(dataPresetGroupIdFilter)
                        .withInsightTypeFilter(insightType)

                    else -> null
                }
            }.toSet()

            LOGGER.debugAsync(
                "wideDocumentSets" to wideDocumentSets.joinToString(","),
            ) {
                "Building document sets: wide document sets"
            }

            val accessControlFilteredDocSets = focusedRepoSets + wideDocumentSets + nonExclusiveRepoSets + wideRepoSets + wideSlackSets

            val enableWideSlackEmbeddings = when (wideSlackEmbeddingsRetrievalStyle) {
                WideSlackEmbeddingsRetrievalStyle.Always -> true
                WideSlackEmbeddingsRetrievalStyle.Never -> false
            }

            val planHasWideSlackRetrievalCapability = planCapabilitiesService.hasCapability(
                orgId = orgId,
                planCapabilityType = PlanCapabilityType.WideSlackRetrieval,
            )

            if (enableWideSlackEmbeddings && planHasWideSlackRetrievalCapability) {
                accessControlFilteredDocSets
            } else {
                accessControlFilteredDocSets.mapNotNull {
                    if (it.insightTypes.contains(InsightType.Slack)) {
                        val filteredDocumentTypes = it.documentTypes.filterNot { docType -> docType == DocumentType.Documentation }.toSet()
                        if (filteredDocumentTypes.isEmpty()) {
                            null
                        } else {
                            it.copy(documentTypes = filteredDocumentTypes)
                        }
                    } else {
                        it
                    }
                }
            }.toSet()
        }
    }

    private suspend fun createRepoSets(
        repoIds: Set<RepoId>,
        documentSet: DocumentSet,
        orgMemberId: OrgMemberId,
        dataSourcePresetConfiguration: DataSourcePresetConfiguration,
    ): Set<DocumentSet> {
        return documentSet.insightTypes.mapNotNull { insightType ->
            val groupIds = repoIds.map { it.value }.toSet()
            val dsacGroupIdFilter = if (groupIds.isEmpty()) {
                GroupIdFilter.PublicGroups
            } else {
                GroupIdFilter.SpecificGroups(groupIds)
            }

            val presetGroupIdFilter = dataSourcePresetConfiguration.intersectForScmInstallations(
                dsacGroupIdFilter = dsacGroupIdFilter,
                orgMemberId = orgMemberId,
            )

            when (insightType) {
                InsightType.SourceCode,
                InsightType.PullRequest,
                InsightType.PullRequestComment,
                    -> documentSet.withGroupIdFilter(
                    groupIdFilter = presetGroupIdFilter,
                ).withInsightTypeFilter(
                    insightType = insightType,
                )

                else -> null
            }
        }.toSet()
    }

    private fun DocumentSet.withGroupIdFilter(
        groupIdFilter: GroupIdFilter,
    ): DocumentSet {
        return copy(
            groupIdFilter = groupIdFilter,
        )
    }

    private fun DocumentSet.withInsightTypeFilter(
        insightType: InsightType,
    ): DocumentSet {
        return copy(
            insightTypes = setOf(insightType),
        )
    }

    private fun DocumentSet.withInsightTypesFilter(
        insightTypes: Set<InsightType>,
    ): DocumentSet {
        return copy(
            insightTypes = insightTypes,
        )
    }

    // Note: This function is only used for SCM installations.
    // The need for this special SCM handling goes away once DSAC supports filtering by installationId.
    private suspend fun DataSourcePresetConfiguration.intersectForScmInstallations(
        dsacGroupIdFilter: GroupIdFilter,
        orgMemberId: OrgMemberId,
    ): GroupIdFilter = when (this) {
        // If all installations are allowed, then we can use the dsac group filter directly
        DataSourcePresetConfiguration.All -> {
            LOGGER.debugAsync(
                "orgMemberId" to orgMemberId,
            ) {
                "All data allowed for SCM, falling back to dsac group filter"
            }
            dsacGroupIdFilter
        }

        is DataSourcePresetConfiguration.Some -> {
            val scmPresets = allowed.filter { it.provider.isScmProvider }

            // Take the union of all groupIds from the scm presets
            val groupIds: Set<UUID> = scmPresets.flatMap { preset ->
                when (preset.groups) {
                    is DataSourcePresetGroups.All -> when (dsacGroupIdFilter) {
                        is GroupIdFilter.PublicGroups -> emptySet()

                        // If the dsac filter is specific, then we need to expand the groupIds from the installation.
                        is GroupIdFilter.SpecificGroups -> preset.expandGroupIdsFromInstallation(orgMemberId)

                        is GroupIdFilter.PublicOrSpecificGroups,
                        is GroupIdFilter.InstallationsOrSpecificGroups,
                            -> throw IllegalStateException("Invalid dsac group filter")
                    }

                    is DataSourcePresetGroups.Some -> (preset.groups as DataSourcePresetGroups.Some).allowed
                }
            }.toSet()

            val installationIds: Set<UUID> = scmPresets.mapNotNull { preset ->
                when (preset.groups) {
                    is DataSourcePresetGroups.All -> preset.installationId.value
                    is DataSourcePresetGroups.Some -> null
                }
            }.toSet()

            when (dsacGroupIdFilter) {
                // If the dsac filter is public, then we can defer to the preset filter.
                // Note that it's possible that both installationIds and groupIds are empty. This is valid, and means that
                // there are no scm presets configured for the data preset configuration. In this case, all scm data will be filtered
                is GroupIdFilter.PublicGroups -> {
                    LOGGER.debugAsync(
                        "orgMemberId" to orgMemberId,
                        "installationIds" to installationIds.joinToString(),
                        "groupIds" to groupIds.joinToString(),
                    ) {
                        "Specific Preset but Public DSAC, using InstallationOrSpecificGroups filter for SCM"
                    }
                    GroupIdFilter.InstallationsOrSpecificGroups(
                        installationIds = installationIds,
                        groupIds = groupIds,
                    )
                }

                // This assumes that the preset groupIds have been expanded from the installation id. So the only thing left is an intersection
                is GroupIdFilter.SpecificGroups -> {
                    LOGGER.debugAsync(
                        "orgMemberId" to orgMemberId,
                        "installationIds" to installationIds.joinToString(),
                        "groupIds" to groupIds.joinToString(),
                    ) {
                        "SpecificGroups filter for SCM"
                    }
                    GroupIdFilter.SpecificGroups(
                        groupIds = groupIds.intersect(dsacGroupIdFilter.groupIds),
                    )
                }

                // Not valid for SCM DSAC filters
                is GroupIdFilter.PublicOrSpecificGroups,
                is GroupIdFilter.InstallationsOrSpecificGroups,
                    -> throw IllegalStateException("Invalid scm dsac filter")
            }
        }
    }

    private suspend fun DataSourceInstallationPreset.expandGroupIdsFromInstallation(
        orgMemberId: OrgMemberId,
    ): Set<UUID> = repoAccessService.getAccessibleReposForInstallation(
        installationId = installationId,
        orgMemberId = orgMemberId,
    ).map { it.value }.toSet()

    // This function assumes that only a single Slack installation is possible within a data preset.
    // Expanding to multiple installations requires that DSAC supports filtering by installationId.
    private suspend fun DataSourcePresetConfiguration.intersectForSlackInstallations(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        dsacGroupIdFilter: GroupIdFilter,
    ): GroupIdFilter {
        return when (this) {
            // If all installations are allowed, then we can use the dsac group filter directly
            DataSourcePresetConfiguration.All -> {
                LOGGER.debugAsync(
                    "orgMemberId" to orgMemberId,
                ) {
                    "All data allowed for Slack, falling back to dsac group filter"
                }
                dsacGroupIdFilter
            }

            is DataSourcePresetConfiguration.Some -> {
                // Grab the first Slack preset. If none exists, it means that there are no Slack installations configured for the data preset
                // configuration, and we should filter all slack data.
                val slackPreset = allowed.firstOrNull { it.provider == Provider.Slack }
                    ?: run {
                        LOGGER.debugAsync(
                            "orgMemberId" to orgMemberId,
                        ) {
                            "No Slack installation found for data preset configuration"
                        }
                        return GroupIdFilter.SpecificGroups(emptySet())
                    }

                // Take the union of all groupIds from the slack presets
                when (slackPreset.groups) {
                    is DataSourcePresetGroups.All -> {
                        LOGGER.debugAsync(
                            "orgMemberId" to orgMemberId,
                        ) {
                            "All groups allowed for Slack, falling back to dsac group filter"
                        }
                        dsacGroupIdFilter
                    }

                    is DataSourcePresetGroups.Some -> {
                        val presetGroups = (slackPreset.groups as DataSourcePresetGroups.Some).allowed
                        when (dsacGroupIdFilter) {
                            is GroupIdFilter.PublicGroups,
                                -> {
                                LOGGER.debugAsync(
                                    "orgMemberId" to orgMemberId,
                                    "groupIds" to presetGroups.joinToString(),
                                ) {
                                    "Slack preset was Specific and dsac was Public. Using Specific"
                                }
                                GroupIdFilter.SpecificGroups(presetGroups)
                            }

                            is GroupIdFilter.PublicOrSpecificGroups,
                                -> {
                                LOGGER.debugAsync(
                                    "orgMemberId" to orgMemberId,
                                    "groupIds" to presetGroups.joinToString(),
                                ) {
                                    "Slack preset was Specific and dsac was PublicOrSpecific. Using Specific"
                                }
                                GroupIdFilter.SpecificGroups(
                                    groupIds = slackChannelAccessService.accessibleChannelIds(
                                        orgId = orgId,
                                        orgMemberId = orgMemberId,
                                        slackChannelIds = presetGroups.map(::SlackChannelId),
                                    ).map { it.value }.toSet(),
                                )
                            }

                            is GroupIdFilter.SpecificGroups,
                            is GroupIdFilter.InstallationsOrSpecificGroups,
                                -> throw IllegalStateException("Invalid slack dsac group filter")
                        }
                    }
                }
            }
        }
    }

    private fun DataSourcePresetConfiguration.groupIdFilter(): GroupIdFilter {
        return when (this) {
            DataSourcePresetConfiguration.All -> GroupIdFilter.PublicGroups

            is DataSourcePresetConfiguration.Some -> {
                val presets = allowed
                val groupIds = presets.flatMap { preset ->
                    when (preset.groups) {
                        is DataSourcePresetGroups.All -> emptySet()
                        is DataSourcePresetGroups.Some -> (preset.groups as DataSourcePresetGroups.Some).allowed
                    }
                }.toSet()

                val installationIds: Set<UUID> = presets.mapNotNull { preset ->
                    when (preset.groups) {
                        is DataSourcePresetGroups.All -> preset.installationId.value
                        is DataSourcePresetGroups.Some -> null
                    }
                }.toSet()

                GroupIdFilter.InstallationsOrSpecificGroups(
                    installationIds = installationIds,
                    groupIds = groupIds,
                )
            }
        }
    }
}
