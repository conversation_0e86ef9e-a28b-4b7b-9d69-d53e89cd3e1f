package com.nextchaptersoftware.search.semantic.services.mcp

import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.mcp.McpToolDefinition

class McpToolHistoricalContext(
    // Temporary - until we have a better way to handle this
    private val mcpToolAskUnblockedWhy: McpToolAskUnblockedWhy,
) {
    suspend fun run(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        parameters: McpToolDefinition.HistoricalContext,
        productAgent: ProductAgentType,
    ): MLInference {
        // For now, just call the ask unblocked why tool
        return mcpToolAskUnblockedWhy.run(
            orgId = orgId,
            orgMemberId = orgMemberId,
            parameters = McpToolDefinition.AskUnblockedWhy(
                query = parameters.query,
                repoId = parameters.repoId,
            ),
            productAgent = productAgent,
        )
    }
}
