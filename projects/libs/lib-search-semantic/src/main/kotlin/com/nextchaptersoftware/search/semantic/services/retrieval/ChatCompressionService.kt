package com.nextchaptersoftware.search.semantic.services.retrieval

import com.nextchaptersoftware.api.serialization.SerializationExtensions.lenientDecode
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MessageWithAuthor
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import com.nextchaptersoftware.log.sensitive.infoSensitiveAsync
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.input.asConversationMessageData
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ChatCompressionService(
    private val reducerCompletionService: CompletionService,
    private val promptCompilerService: PromptCompilerService,
    private val templateService: MLInferenceTemplateService,
    private val inferenceService: MLInferenceService,
) {

    suspend fun reduceForRetrieval(
        orgId: OrgId,
        botId: OrgMemberId?,
        messages: List<MessageWithAuthor>?,
        highlightedCode: String?,
        query: String,
        useHighlightedCodeCompressor: Boolean = false,
        chatCompressionTimeout: Duration,
    ): String? {
        val messageAuthors = "[${messages?.joinToString(",\n") { "Author: ${it.message.authorId}" } ?: ""}]"

        LOGGER.debugSensitiveAsync(
            "orgId" to orgId,
            "botId" to botId,
            "messageAuthors" to messageAuthors,
            sensitiveFields = mapOf(
                "query" to query,
                "highlightedCode" to highlightedCode,
            ),
        ) {
            "Reducing chat for retrieval"
        }

        if (messages.isNullOrEmpty() && highlightedCode == null) {
            return null
        }

        val isFirstMessageCompression = useHighlightedCodeCompressor && messages.isNullOrEmpty() && highlightedCode?.isNotEmpty() == true
        val template = when (isFirstMessageCompression) {
            true -> templateService.orgTemplate(orgId = orgId, kind = MLInferenceTemplateKind.HighlightedCodeCompressor)
            false -> templateService.orgTemplate(orgId = orgId, kind = MLInferenceTemplateKind.ChatCompressor)
        }

        val reducedMessages = messages?.let { msgs ->
            botId?.let { id ->
                reduceMessages(msgs, id)
            }
        } ?: messages.orEmpty()

        val prompt = promptCompilerService.compilePrompt(
            template = template,
            priorMessages = reducedMessages.mapNotNull { it.asConversationMessageData(botId) },
            query = query,
            highlightedCode = highlightedCode,
        ).prompt

        if (prompt.isBlank()) {
            return null
        }

        LOGGER.debugSensitiveAsync(
            "orgId" to orgId,
            "botId" to botId,
            sensitiveFields = mapOf(
                "prompt" to prompt,
            ),
        ) {
            "Reducing chat for retrieval with prompt"
        }

        @Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
        return runCatching {
            withTimeout(chatCompressionTimeout) {
                val rawResult = reducerCompletionService.query(
                    prompt = prompt,
                    template = template,
                )

                LOGGER.infoSensitiveAsync(
                    sensitiveFields = mapOf(
                        "prompt" to prompt,
                        "result" to rawResult,
                    ),
                ) { "Reduced historical chat" }

                rawResult.lenientDecode<ReduceResult>().reducedQuery
            }
        }.getOrElse {
            LOGGER.errorSensitiveAsync(
                t = it,
                sensitiveFields = mapOf(
                    "query" to query,
                    "orgId" to orgId,
                    "prompt" to prompt,
                ),
            ) { "Error reducing historical chat" }
            throw it
        }
    }

    suspend fun reduceMessages(
        messages: List<MessageWithAuthor>,
        botId: OrgMemberId?,
    ): List<MessageWithAuthor> {
        val lastBotMessageIndex = messages.indexOfLast { it.message.authorOrgMemberId == botId }
        if (lastBotMessageIndex <= 0) {
            return messages
        }

        val lastBotMessage = messages[lastBotMessageIndex]
        val messageBeforeLastBotMessage = messages[lastBotMessageIndex - 1]

        // Since the documentQuery is progressively reduced, we only need to take the last one here. The result is this:
        //
        //   [[USER]] <Progressively Reduced Document Query>
        //   [[BOT]] <Last Bot Answer>
        //   [[USER]] <Last User Query>
        return inferenceService.findByMessageId(lastBotMessage.message.id)?.documentQuery?.let { documentQuery ->
            listOf(
                messageBeforeLastBotMessage.copy(
                    message = messageBeforeLastBotMessage.message.copy(
                        content = documentQuery.asMessageBody().toByteArray(),
                    ),
                ),
            ) + messages.subList(lastBotMessageIndex, messages.size)
        } ?: messages
    }
}

@Serializable
private data class ReduceResult(
    @SerialName("reduced_query")
    val reducedQuery: String,
)
