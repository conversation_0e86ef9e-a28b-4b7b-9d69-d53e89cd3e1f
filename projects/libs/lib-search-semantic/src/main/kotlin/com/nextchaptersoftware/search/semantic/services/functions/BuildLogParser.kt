package com.nextchaptersoftware.search.semantic.services.functions

object BuildLogParser {
    private const val CONTEXT_LINES = 20
    private val contextStrings = listOf("error", "failed", "failure")

    fun filterLogLine(line: String): Boolean {
        return when {
            line.contains("> Task :") || line.contains("PASSED") || line.contains("SKIPPED") || line.isBlank() -> true
            else -> false
        }
    }

    fun contextLines(lines: List<String>): List<String> {
        val matchingIndices = lines.withIndex()
            .filter { (_, line) -> contextStrings.any { line.contains(it, ignoreCase = true) } }
            .map { it.index }

        val contextRanges = matchingIndices.map { index ->
            val start = maxOf(0, index - CONTEXT_LINES)
            val end = minOf(lines.lastIndex, index + CONTEXT_LINES)
            start..end
        }

        val mergedRanges = contextRanges
            .sortedBy { it.first }
            .fold(mutableListOf<IntRange>()) { acc, range ->
                if (acc.isEmpty() || acc.last().last < range.first - 1) {
                    acc.add(range)
                } else {
                    val lastRange = acc.removeAt(acc.lastIndex)
                    acc.add(lastRange.first..maxOf(lastRange.last, range.last))
                }
                acc
            }

        return mergedRanges
            .flatMap { range -> lines.subList(range.first, range.last + 1) }
            .distinct()
    }
}
