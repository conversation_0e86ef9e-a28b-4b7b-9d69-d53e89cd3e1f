package com.nextchaptersoftware.product.feedback

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.ProductFeedbackResponseDAO
import com.nextchaptersoftware.db.models.ProductFeedbackResponseType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.InstallationSuppressionStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.OrgSettingsStore
import com.nextchaptersoftware.db.stores.ProductFeedbackStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.ThreadStore
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant

class ProductFeedbackService(
    private val productFeedbackStore: ProductFeedbackStore = Stores.productFeedbackStore,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    private val orgSettingsStore: OrgSettingsStore = Stores.orgSettingsStore,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val identityStore: IdentityStore = Stores.identityStore,
    private val threadStore: ThreadStore = Stores.threadStore,
    private val installationSuppressionStore: InstallationSuppressionStore = Stores.installationSuppressionStore,
    private val slackNotifier: SlackNotifier,
) {

    suspend fun upsertProductFeedback(
        orgId: OrgId,
        personId: PersonId,
        productAgent: ProductAgentType,
        feedbackType: ProductFeedbackResponseType,
        feedbackDescription: String? = null,
    ): ProductFeedbackResponseDAO {
        val orgMember = orgMemberStore.findByOrgAndPerson(
            orgId = orgId,
            personId = personId,
            isCurrentPrimaryMember = false,
        )
        val suppressions = orgMember?.let {
            installationSuppressionStore.getAllByMember(
                orgMember = it.id,
            )
        } ?: emptySet()

        val providers = Provider.integrations(excludeScmProviders = false)
        val allInstallations = installationStore.getIntegrationInstallations(
            orgId = orgId,
            providers = providers,
        )

        val oauthInstallations = allInstallations.filter { it.provider.hasUserOAuth }
        val (suppressed, active) = oauthInstallations.partition { it.id in suppressions }
        val (unconnected, connected) = active.partition { installation ->
            val identity = identityStore.findAllByPersonAndInstallation(
                provider = installation.provider,
                personId = personId,
                installationId = installation.id,
            ).firstOrNull()
            (identity == null || identity.promptToReconnect)
        }

        return productFeedbackStore.insertFeedback(
            orgId = orgId,
            personId = personId,
            productAgent = productAgent,
            connectedInstallations = connected,
            unconnectedInstallations = unconnected,
            suppressedInstallations = suppressed,
            feedbackType = feedbackType,
            feedbackDescription = feedbackDescription,
        ).also {
            slackNotifier.announceProductFeedback(it.asDataModel())
        }
    }

    suspend fun hasProvidedFeedback(
        orgId: OrgId,
        personId: PersonId,
    ): Boolean {
        return productFeedbackStore.hasPersonProvidedFeedback(
            orgId = orgId,
            personId = personId,
        )
    }

    @Suppress("ReturnCount")
    suspend fun requiresProductFeedback(
        orgId: OrgId,
        personId: PersonId,
    ): Boolean {
        // First validate if the org has enabled product feedback
        val enabledProductFeedback = orgSettingsStore.getEnableProductFeedback(orgId)
        if (!enabledProductFeedback) {
            return false
        }

        // Check if the org member has provided feedback. We only require feedback once
        val hasMemberProvidedFeedback = productFeedbackStore.hasPersonProvidedFeedback(
            orgId = orgId,
            personId = personId,
        )
        if (hasMemberProvidedFeedback) {
            return false
        }

        val orgMember = orgMemberStore.findByOrgAndPerson(
            orgId = orgId,
            personId = personId,
            isCurrentPrimaryMember = false,
        ) ?: return false

        val firstQuestionForOrgMember = threadStore.findFirstUnblockedThreadForOrgMember(orgMember.id) ?: return false
        val dateThresholdCount = orgSettingsStore.getProductFeedbackDateThreshold(orgId)
        // If the last question asked + buffer time is still in the future, we don't require feedback
        if (firstQuestionForOrgMember.createdAt.plus(dateThresholdCount.days) > Instant.nowWithMicrosecondPrecision()) {
            return false
        }

        val questionThreshold = orgSettingsStore.getProductFeedbackQuestionThreshold(orgId)
        val questionsAsked = orgMember.questionsAskedPublic + orgMember.questionsAskedPrivate
        return questionsAsked >= questionThreshold
    }

    suspend fun downloadOrgProductFeedbackCSV(orgId: OrgId): String {
        val data = productFeedbackStore.getProductFeedbackResponseAggregateForOrg(orgId = orgId)
        val header = "Person,Email,Sentiment,Feedback"
        val rows = data.all.map {
            listOf(
                it.person.customDisplayName,
                it.person.primaryEmail,
                it.feedbackType.name,
                it.feedbackDescription ?: "-",
            ).joinToString(separator = ",")
        }
        val body = rows.joinToString("\n") { it }
        return "$header\n$body"
    }
}
