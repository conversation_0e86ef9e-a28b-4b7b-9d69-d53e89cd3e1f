package com.nextchaptersoftware.emoji.models.document

import com.nextchaptersoftware.emoji.models.Category
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject

@Serializable
data class EmojiDocument(
    @SerialName("added_in")
    val addedIn: String? = null,
    @SerialName("category")
    val category: Category? = null,
    @SerialName("has_img_apple")
    val hasImgApple: Boolean? = null,
    @SerialName("has_img_facebook")
    val hasImgFacebook: Boolean? = null,
    @SerialName("has_img_google")
    val hasImgGoogle: Boolean? = null,
    @SerialName("has_img_twitter")
    val hasImgTwitter: Boolean? = null,
    @SerialName("image")
    val image: String? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("non_qualified")
    val nonQualified: String? = null,
    @SerialName("obsoletes")
    val obsoletes: String? = null,
    @SerialName("obsoleted_by")
    val obsoletedBy: String? = null,
    @SerialName("sheet_x")
    val sheetX: Int? = null,
    @SerialName("sheet_y")
    val sheetY: Int? = null,
    @SerialName("short_name")
    val shortName: String? = null,
    @SerialName("short_names")
    val shortNames: List<String>? = null,
    @SerialName("skin_variations")
    val skinVariations: JsonObject? = null,
    @SerialName("sort_order")
    val sortOrder: Int? = null,
    @SerialName("subcategory")
    val subcategory: String? = null,
    @SerialName("unified")
    val unified: String? = null,
)
