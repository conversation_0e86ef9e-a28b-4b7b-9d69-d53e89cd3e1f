package com.nextchaptersoftware.slackpreferences

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.SlackChannelPreferencesMetricsDAO
import com.nextchaptersoftware.db.stores.SlackChannelPreferencesMetricsStore
import com.nextchaptersoftware.db.stores.SlackChannelPreferencesStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.nextchaptersoftware.utils.startOfDayPST
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.microseconds
import kotlinx.datetime.Instant

class SlackChannelPreferencesService(
    private val slackChannelPreferencesStore: SlackChannelPreferencesStore = Stores.slackChannelPreferencesStore,
    private val slackChannelPreferencesMetricsStore: SlackChannelPreferencesMetricsStore = Stores.slackChannelPreferencesMetricsStore,
    private val insiderService: InsiderServiceInterface = InsiderService(),
) {
    suspend fun calculateSlackChannelPreferencesMetrics() {
        val now = Instant.nowWithMicrosecondPrecision()

        val latest = slackChannelPreferencesMetricsStore.latest()
        if (latest == null || latest.ending.plus(1.days) < now) {
            val countChannels = slackChannelPreferencesStore.countAll(
                excludeIds = insiderService.insiderSlackChannelIds,
            )

            val countAutoAnswerEnabled = slackChannelPreferencesStore.countAutoResponseEnabled(
                excludeIds = insiderService.insiderSlackChannelIds,
            )

            suspendedTransaction {
                SlackChannelPreferencesMetricsDAO.new {
                    this.ending = now.startOfDayPST().minus(1.microseconds)
                    this.countChannels = countChannels
                    this.countAutoAnswerEnabled = countAutoAnswerEnabled
                }
            }
        }
    }
}
