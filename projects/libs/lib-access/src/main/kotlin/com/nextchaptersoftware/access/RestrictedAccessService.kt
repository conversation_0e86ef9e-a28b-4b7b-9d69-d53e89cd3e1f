package com.nextchaptersoftware.access

import com.nextchaptersoftware.db.models.Provider

class RestrictedAccessService(
    private val allowedAllHosts: Boolean,
    private val allowedAllOrgs: Boolean,
    private val allowedAllUsers: Boolean,
    private val allowedHosts: Set<String>,
    private val allowedOrgs: Map<Provider, Set<String>>,
    private val allowedUsers: Map<Provider, Set<String>>,
) : RestrictedAccessServiceInterface {

    override fun allowOrg(provider: Provider, externalId: String): <PERSON>olean {
        if (allowedAllOrgs) return true
        return allowedOrgs[provider].orEmpty().contains(externalId)
    }

    override fun allowUser(provider: Provider, externalId: String): <PERSON><PERSON><PERSON> {
        if (allowedAllUsers) return true
        return allowedUsers[provider].orEmpty().contains(externalId)
    }

    override fun allowHost(hostname: String): <PERSON><PERSON><PERSON> {
        if (allowedAllHosts) return true
        return allowedHosts.contains(hostname)
    }
}
