package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.api.models.EnterpriseProvider
import com.nextchaptersoftware.api.models.EnterpriseProviderOutcome
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.scm.apps.AppManifestNegotiation
import kotlinx.serialization.Serializable

/**
 * @see com.nextchaptersoftware.proxy.provider.rpc.handlers.ScmEnterpriseHandler
 */
interface ScmEnterpriseCalls {

    suspend fun scmEnterpriseCreateBitbucketDataCenterEnterpriseProvider(
        params: ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderParams,
    ): ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult

    @Serializable
    data class ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderParams(
        val orgId: OrgId?,
        val applicationId: String,
        val encryptedApplicationSecret: String,
        val displayName: String,
    )

    @Serializable
    data class ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult(
        val enterpriseProviderOutcome: EnterpriseProviderOutcome,
    )

    suspend fun scmEnterpriseCreateGitHubEnterpriseProvider(
        params: ScmEnterpriseCreateGitHubEnterpriseProviderParams,
    ): ScmEnterpriseCreateGitHubEnterpriseProviderResult

    @Serializable
    data class ScmEnterpriseCreateGitHubEnterpriseProviderParams(
        val orgId: OrgId?,
        val code: String,
        val negotiation: AppManifestNegotiation,
    )

    @Serializable
    data class ScmEnterpriseCreateGitHubEnterpriseProviderResult(
        val enterpriseProvider: EnterpriseProvider,
    )

    suspend fun scmEnterpriseCreateGitLabEnterpriseProvider(
        params: ScmEnterpriseCreateGitLabEnterpriseProviderParams,
    ): ScmEnterpriseCreateGitLabEnterpriseProviderResult

    @Serializable
    data class ScmEnterpriseCreateGitLabEnterpriseProviderParams(
        val orgId: OrgId?,
        val applicationId: String,
        val encryptedApplicationSecret: String,
        val displayName: String,
    )

    @Serializable
    data class ScmEnterpriseCreateGitLabEnterpriseProviderResult(
        val enterpriseProviderOutcome: EnterpriseProviderOutcome,
    )
}
