package com.nextchaptersoftware.rpc.proxy.provider

import com.nextchaptersoftware.rpc.calls.OAuthCalls
import com.nextchaptersoftware.rpc.calls.OAuthCalls.OAuthExchangeParams
import com.nextchaptersoftware.rpc.calls.OAuthCalls.OAuthExchangeResult
import com.nextchaptersoftware.rpc.calls.ProviderIdentityCalls
import com.nextchaptersoftware.rpc.calls.RepoComputeCalls
import com.nextchaptersoftware.rpc.calls.RepoComputeCalls.ComputeRepoAccessParams
import com.nextchaptersoftware.rpc.calls.RepoComputeCalls.ComputeRepoAccessResult
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderParams
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitHubEnterpriseProviderParams
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitHubEnterpriseProviderResult
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitLabEnterpriseProviderParams
import com.nextchaptersoftware.rpc.calls.ScmEnterpriseCalls.ScmEnterpriseCreateGitLabEnterpriseProviderResult
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls.ScmExchangeAuthCodeForIdentityResult
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls.ScmIdentityExchangeAuthCodeForIdentityParams
import kotlinx.rpc.RemoteService
import kotlinx.rpc.annotations.Rpc

@Rpc
interface ForAuthServiceRpc :
    RemoteService,
    OAuthCalls,
    RepoComputeCalls,
    ScmEnterpriseCalls,
    ScmIdentityCalls,
    ProviderIdentityCalls {

    override suspend fun oauthExchange(
        params: OAuthExchangeParams,
    ): OAuthExchangeResult

    override suspend fun repoAccessComputeRepoAccess(
        params: ComputeRepoAccessParams,
    ): ComputeRepoAccessResult

    override suspend fun scmEnterpriseCreateBitbucketDataCenterEnterpriseProvider(
        params: ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderParams,
    ): ScmEnterpriseCreateBitbucketDataCenterEnterpriseProviderResult

    override suspend fun scmEnterpriseCreateGitHubEnterpriseProvider(
        params: ScmEnterpriseCreateGitHubEnterpriseProviderParams,
    ): ScmEnterpriseCreateGitHubEnterpriseProviderResult

    override suspend fun scmEnterpriseCreateGitLabEnterpriseProvider(
        params: ScmEnterpriseCreateGitLabEnterpriseProviderParams,
    ): ScmEnterpriseCreateGitLabEnterpriseProviderResult

    override suspend fun scmIdentityExchangeAuthCodeForIdentity(
        params: ScmIdentityExchangeAuthCodeForIdentityParams,
    ): ScmExchangeAuthCodeForIdentityResult

    override suspend fun providerIdentityExchangeAuthCodeForIdentity(
        params: ProviderIdentityCalls.ProviderIdentityExchangeAuthCodeForIdentityParams,
    ): ProviderIdentityCalls.ProviderExchangeAuthCodeForIdentityResult
}
