package com.nextchaptersoftware.rpc.ktor

import io.ktor.server.application.Application
import io.ktor.server.application.install
import kotlinx.rpc.krpc.KrpcConfigBuilder
import kotlinx.rpc.krpc.ktor.server.Krpc
import kotlinx.rpc.krpc.serialization.json.json
import org.openapitools.server.infrastructure.Serializer

fun Application.installRPC(
    configure: KrpcConfigBuilder.Server.() -> Unit = {},
) {
    install(Krpc) {
        serialization {
            json {
                serializersModule = Serializer.kotlinSerializationAdapters
            }
        }
        configure.invoke(this)
    }
}
