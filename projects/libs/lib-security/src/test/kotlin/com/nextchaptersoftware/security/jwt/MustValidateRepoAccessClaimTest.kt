package com.nextchaptersoftware.security.jwt

import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class MustValidateRepoAccessClaimTest {
    private val team1 = UUID.randomUUID()
    private val team2 = UUID.randomUUID()
    private val team3 = UUID.randomUUID()

    @Test
    fun toClaim() {
        val teamIds = setOf(team1, team2, team3)

        MustValidateRepoAccessClaim.toClaim(teamIds = teamIds, teamIdsMvra = emptySet()).also {
            assertThat(it).isNull()
        }
        MustValidateRepoAccessClaim.toClaim(teamIds = teamIds, teamIdsMvra = setOf(team1)).also {
            assertThat(it).isEqualTo(arrayListOf(0).toTypedArray())
        }
        MustValidateRepoAccessClaim.toClaim(teamIds = teamIds, teamIdsMvra = setOf(team3)).also {
            assertThat(it).isEqualTo(arrayListOf(2).toTypedArray())
        }
        MustValidateRepoAccessClaim.toClaim(teamIds = teamIds, teamIdsMvra = setOf(team1, team2, team3)).also {
            assertThat(it).isEqualTo(arrayListOf(0, 1, 2).toTypedArray())
        }
    }

    @Test
    fun fromClaim() {
        MustValidateRepoAccessClaim.fromClaim(orgIds = emptyList(), mustValidateRepoAccessClaim = emptyArray<Int>()).also {
            assertThat(it).isEmpty()
        }

        MustValidateRepoAccessClaim.fromClaim(orgIds = listOf(team1), mustValidateRepoAccessClaim = arrayListOf(0).toTypedArray()).also {
            assertThat(it).containsExactly(team1)
        }
        MustValidateRepoAccessClaim.fromClaim(orgIds = listOf(team1, team2, team3), mustValidateRepoAccessClaim = arrayListOf(0, 2).toTypedArray())
            .also {
                assertThat(it).containsExactly(team1, team3)
            }
        MustValidateRepoAccessClaim.fromClaim(orgIds = listOf(team1, team2, team3), mustValidateRepoAccessClaim = emptyArray<Int>())
            .also {
                assertThat(it).isEmpty()
            }

        // leniently ignores invalid indexes
        MustValidateRepoAccessClaim.fromClaim(orgIds = emptyList(), mustValidateRepoAccessClaim = arrayListOf(0).toTypedArray()).also {
            assertThat(it).isEmpty()
        }
        MustValidateRepoAccessClaim.fromClaim(orgIds = emptyList(), mustValidateRepoAccessClaim = arrayListOf(100).toTypedArray()).also {
            assertThat(it).isEmpty()
        }
        MustValidateRepoAccessClaim.fromClaim(orgIds = listOf(team1), mustValidateRepoAccessClaim = arrayListOf(100).toTypedArray()).also {
            assertThat(it).isEmpty()
        }
    }
}
