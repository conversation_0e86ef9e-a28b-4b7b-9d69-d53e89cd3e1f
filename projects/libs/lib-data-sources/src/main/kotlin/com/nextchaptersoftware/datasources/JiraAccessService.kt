package com.nextchaptersoftware.datasources

import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.IssueStatus
import com.nextchaptersoftware.db.models.JiraBoard
import com.nextchaptersoftware.db.models.JiraEpic
import com.nextchaptersoftware.db.models.JiraIssue
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.models.JiraSite
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.JiraBoardStore
import com.nextchaptersoftware.db.stores.JiraEpicStore
import com.nextchaptersoftware.db.stores.JiraIssueStore
import com.nextchaptersoftware.db.stores.JiraSiteStore
import com.nextchaptersoftware.db.stores.JiraSprintStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.api.JiraIssuesApi
import com.nextchaptersoftware.jira.models.IssueWithoutFields
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import io.ktor.http.Url
import kotlin.time.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class JiraAccessService(
    private val atlassianTokenProvider: AtlassianTokenProvider,
    private val jiraApiProvider: JiraApiProvider,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val jiraSiteStore: JiraSiteStore = Stores.jiraSiteStore,
    private val jiraBoardStore: JiraBoardStore = Stores.jiraBoardStore,
    private val jiraSprintStore: JiraSprintStore = Stores.jiraSprintStore,
    private val jiraEpicStore: JiraEpicStore = Stores.jiraEpicStore,
    private val jiraIssueStore: JiraIssueStore = Stores.jiraIssueStore,
) {

    suspend fun getJiraIssueByKey(
        orgId: OrgId,
        dsacContext: DsacContext,
        issueKey: String,
    ): JiraIssue? {
        val jiraIssue = jiraIssueStore.findByKey(orgId, issueKey)
            ?: return null

        if (!dsacContext.isDsacEnforced(installationId = jiraIssue.installationId)) {
            return jiraIssue
        }

        return jiraSiteStore.findForInstallation(installationId = jiraIssue.installationId)?.let { site ->
            val url = JiraIssuesApi.urlForIssue(baseUrl = site.userAuthBaseUrl, issueKey = jiraIssue.issueKey)

            dsacContext.getMemberIdentityPair(installationId = jiraIssue.installationId)?.identity?.let { identity ->
                runSuspendCatching {
                    val tokens = atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)
                    tokens?.let {
                        jiraApiProvider.jiraIssuesApi.getIssue(orgIds = setOf(orgId), url = url, tokens = tokens)?.let {
                            jiraIssue
                        }
                    }
                }.getOrElse {
                    LOGGER.warnAsync { "DSAC: Jira identity does not have access to issue" }
                    null
                }
            } ?: run {
                LOGGER.debugAsync { "DSAC: User does not have connected Jira account" }
                null
            }
        }
    }

    suspend fun getJiraIssueIdsByBoardName(
        orgId: OrgId,
        dsacContext: DsacContext,
        boardName: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            val board = jiraBoardStore.getBoardByNameMatch(installationId = site.installationId, name = boardName) ?: run {
                LOGGER.debugAsync { "No board found for boardName" }
                return@firstNotNullOfOrNull null
            }

            getJiraIssuesByBoard(orgId = orgId, dsacContext = dsacContext, site = site, board = board)
        } ?: emptyList()
    }

    // TODO currently unused
    suspend fun getJiraIssueIdsByBoardId(
        orgId: OrgId,
        dsacContext: DsacContext,
        boardId: String,
    ) = withLoggingContextAsync(
        "boardId" to boardId,
    ) {
        jiraSites(orgId).firstNotNullOfOrNull { site ->
            val board = jiraBoardStore.getBoardByBoardId(installationId = site.installationId, boardId = boardId) ?: run {
                LOGGER.debugAsync { "No board found for boardId" }
                return@firstNotNullOfOrNull null
            }

            getJiraIssuesByBoard(orgId = orgId, dsacContext = dsacContext, site = site, board = board)
        } ?: emptyList()
    }

    private suspend fun getJiraIssuesByBoard(
        orgId: OrgId,
        dsacContext: DsacContext,
        site: JiraSite,
        board: JiraBoard,
    ): List<JiraIssue> = withLoggingContextAsync(
        "externalBoardId" to board.id,
        "boardId" to board.boardId,
        "boardName" to board.name,
    ) {
        val issuesWithoutFields = when (dsacContext.isDsacEnforced(site.installationId)) {
            false -> {
                LOGGER.debugAsync { "Getting Jira board issues" }
                jiraApiProvider.jiraBoardsApi.getAllBoardIssuesWithoutFields(
                    orgIds = setOf(orgId),
                    baseUrl = site.userAuthBaseUrl,
                    boardId = board.boardId,
                ) { scope ->
                    atlassianTokenProvider.getTokens(installationId = site.installationId, withScope = scope).accessToken
                }
            }

            else -> {
                dsacContext.getMemberIdentityPair(installationId = site.installationId)?.identity?.let { identity ->
                    LOGGER.debugAsync("identityId" to identity.id) { "Getting Jira board issues for identity" }
                    jiraApiProvider.jiraBoardsApi.getAllBoardIssuesWithoutFields(
                        orgIds = setOf(orgId),
                        baseUrl = site.userAuthBaseUrl,
                        boardId = board.boardId,
                    ) {
                        atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)?.accessToken
                    }
                } ?: run {
                    LOGGER.debugAsync { "Member does not have access to Jira" }
                    emptyList()
                }
            }
        }

        jiraIssueStore.listByIssueIds(installationId = site.installationId, issueIds = issuesWithoutFields.map { it.id })
    }

    suspend fun getJiraIssueIdsBySprint(
        orgId: OrgId,
        dsacContext: DsacContext,
        sprintName: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            val sprint = jiraSprintStore.getSprintByNameMatch(installationId = site.installationId, name = sprintName) ?: run {
                LOGGER.debugAsync { "No sprint found for sprintName" }
                return@firstNotNullOfOrNull null
            }

            val issuesWithoutFields = when (dsacContext.isDsacEnforced(site.installationId)) {
                false -> {
                    LOGGER.debugAsync { "Getting Jira sprint issues for org" }
                    jiraApiProvider.jiraSprintsApi.getAllSprintIssuesWithoutFields(
                        orgIds = setOf(orgId),
                        baseUrl = site.userAuthBaseUrl,
                        sprintId = sprint.sprintId,
                    ) { scope ->
                        atlassianTokenProvider.getTokens(installationId = site.installationId, withScope = scope)
                    }
                }

                else -> dsacContext.getMemberIdentityPair(site.installationId)?.identity?.let { identity ->
                    LOGGER.debugAsync("identityId" to identity.id) { "Getting Jira sprint issues for identity" }
                    jiraApiProvider.jiraSprintsApi.getAllSprintIssuesWithoutFields(
                        orgIds = setOf(orgId),
                        baseUrl = site.userAuthBaseUrl,
                        sprintId = sprint.sprintId,
                    ) {
                        atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)
                    }
                } ?: run {
                    LOGGER.debugAsync { "No authed Jira identity for orgMemberId" }
                    emptyList()
                }
            }

            jiraIssueStore.listByIssueIds(installationId = site.installationId, issueIds = issuesWithoutFields.map { it.id })
        } ?: emptyList()
    }

    suspend fun getJiraIssueIdsForCurrentSprint(
        orgId: OrgId,
        dsacContext: DsacContext,
        boardName: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            val board = jiraBoardStore.getBoardByNameMatch(installationId = site.installationId, name = boardName) ?: run {
                LOGGER.debugAsync { "No board found for boardName" }
                return@firstNotNullOfOrNull null
            }

            val activeSprints = jiraSprintStore.getSprintsForBoard(installationId = site.installationId, jiraBoardId = board.id).filter {
                it.state.lowercase() == "active"
            }

            val issuesWithoutFields = when (dsacContext.isDsacEnforced(site.installationId)) {
                false -> {
                    LOGGER.debugAsync { "Getting current Jira sprint issues for org" }
                    activeSprints.flatMap { sprint ->
                        jiraApiProvider.jiraSprintsApi.getAllSprintIssuesWithoutFields(
                            orgIds = setOf(orgId),
                            baseUrl = site.userAuthBaseUrl,
                            sprintId = sprint.sprintId,
                        ) { scope ->
                            atlassianTokenProvider.getTokens(installationId = site.installationId, withScope = scope)
                        }
                    }
                }

                else -> dsacContext.getMemberIdentityPair(site.installationId)?.identity?.let { identity ->
                    LOGGER.debugAsync("identityId" to identity.id) { "Getting current Jira sprint issues for identity" }
                    val tokens = atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)
                    tokens?.let {
                        activeSprints.flatMap { sprint ->
                            jiraApiProvider.jiraSprintsApi.getAllSprintIssuesWithoutFields(
                                orgIds = setOf(orgId),
                                baseUrl = site.userAuthBaseUrl,
                                sprintId = sprint.sprintId,
                            ) {
                                atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)
                            }
                        }
                    }
                } ?: run {
                    LOGGER.debugAsync { "No authed Jira identity for orgMemberId" }
                    emptyList()
                }
            }

            jiraIssueStore.listByIssueIds(installationId = site.installationId, issueIds = issuesWithoutFields.map { it.id })
        } ?: emptyList()
    }

    // TODO currently unused
    suspend fun getJiraIssueIdsByEpicName(
        orgId: OrgId,
        dsacContext: DsacContext,
        epicName: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            val epic = jiraEpicStore.getEpicByNameMatch(installationId = site.installationId, name = epicName) ?: run {
                LOGGER.debugAsync { "No epic found for epicName" }
                return@firstNotNullOfOrNull null
            }

            fetchJiraIssuesByEpic(orgId = orgId, dsacContext = dsacContext, site = site, epic = epic)
        } ?: emptyList()
    }

    suspend fun getJiraIssuesByEpicKey(
        orgId: OrgId,
        dsacContext: DsacContext,
        epicKey: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            val epic = jiraEpicStore.getEpicByIssueKey(installationId = site.installationId, epicKey = epicKey) ?: run {
                LOGGER.debugAsync { "No epic found for epicKey" }
                return@firstNotNullOfOrNull null
            }

            fetchJiraIssuesByEpic(orgId = orgId, dsacContext = dsacContext, site = site, epic = epic)
        } ?: emptyList()
    }

    // TODO currently unused
    suspend fun getJiraIssueIdsByEpicId(
        orgId: OrgId,
        dsacContext: DsacContext,
        epicId: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            val epic = jiraEpicStore.getEpicByIssueId(installationId = site.installationId, epicId = epicId) ?: run {
                LOGGER.debugAsync { "No epic found for epicId" }
                return@firstNotNullOfOrNull null
            }

            fetchJiraIssuesByEpic(orgId = orgId, dsacContext = dsacContext, site = site, epic = epic)
        } ?: emptyList()
    }

    private suspend fun fetchJiraIssuesByEpic(
        orgId: OrgId,
        dsacContext: DsacContext,
        site: JiraSite,
        epic: JiraEpic,
    ): List<JiraIssue> = withLoggingContextAsync(
        "externalEpicId" to epic.id,
        "epicKey" to epic.issueKey,
        "epicId" to epic.issueId,
    ) {
        val issuesWithoutFields = when (dsacContext.isDsacEnforced(site.installationId)) {
            false -> {
                LOGGER.debugAsync { "Getting Jira epic issues for org" }
                val tokens = atlassianTokenProvider.getTokens(installationId = site.installationId)
                jiraApiProvider.jiraIssuesApi.getAllEpicIssuesWithoutFields(
                    orgIds = setOf(orgId),
                    tokens = tokens,
                    baseUrl = site.userAuthBaseUrl,
                    epicIdOrKey = epic.issueKey,
                )
            }

            else -> dsacContext.getMemberIdentityPair(site.installationId)?.identity?.let { identity ->
                LOGGER.debugAsync("identityId" to identity.id) { "Getting Jira epic issues for identity" }
                val tokens = atlassianTokenProvider.getTokens(identityId = identity.id, provider = Provider.Jira)
                tokens?.let {
                    jiraApiProvider.jiraIssuesApi.getAllEpicIssuesWithoutFields(
                        orgIds = setOf(orgId),
                        tokens = tokens,
                        baseUrl = site.userAuthBaseUrl,
                        epicIdOrKey = epic.issueKey,
                    )
                }
            } ?: run {
                LOGGER.debugAsync { "No authed Jira identity for orgMemberId" }
                emptyList()
            }
        }

        jiraIssueStore.listByIssueIds(installationId = site.installationId, issueIds = issuesWithoutFields.map { it.id })
    }

    private suspend fun jiraSites(orgId: OrgId): List<JiraSite> {
        return installationStore.getIntegrationInstallations(
            orgId = orgId,
            providers = listOf(Provider.Jira),
        ).mapNotNull {
            jiraSiteStore.findForInstallation(installationId = it.id)
        }
    }

    suspend fun getJiraIssuesByFilterId(
        orgId: OrgId,
        dsacContext: DsacContext,
        filterId: String,
    ): List<JiraIssue> {
        return jiraSites(orgId).firstNotNullOfOrNull { site ->
            getJiraIssuesByFilterId(
                orgId = orgId,
                dsacContext = dsacContext,
                site = site,
                filterId = filterId,
            )
        } ?: emptyList()
    }

    suspend fun getJiraIssuesByFilterId(
        orgId: OrgId,
        dsacContext: DsacContext,
        site: JiraSite,
        filterId: String,
    ): List<JiraIssue> = withLoggingContextAsync(
        "filterId" to filterId,
    ) {
        val issuesWithoutFields = when (dsacContext.isDsacEnforced(site.installationId)) {
            false -> {
                LOGGER.debugAsync { "Getting Jira filter issues for org" }
                getAllIssuesByFilter(
                    orgId = orgId,
                    baseUrl = site.userAuthBaseUrl,
                    installationId = site.installationId,
                    filterId = filterId,
                )
            }

            else -> dsacContext.getMemberIdentityPair(site.installationId)?.identity?.let { identity ->
                LOGGER.debugAsync("identityId" to identity.id) { "Getting Jira filter issues for identity" }
                getAllIssuesByFilter(
                    orgId = orgId,
                    baseUrl = site.userAuthBaseUrl,
                    identityId = identity.id,
                    filterId = filterId,
                )
            } ?: run {
                LOGGER.debugAsync { "No authed Jira identity for orgMemberId" }
                emptyList()
            }
        }

        jiraIssueStore.listByIssueIds(installationId = site.installationId, issueIds = issuesWithoutFields.map { it.id })
    }

    private suspend fun getAllIssuesByFilter(
        orgId: OrgId,
        baseUrl: Url,
        installationId: InstallationId,
        filterId: String,
    ): List<IssueWithoutFields> {
        val tokens = atlassianTokenProvider.getTokens(installationId = installationId)
        return jiraApiProvider.jiraIssuesApi.getAllIssuesByFilter(
            orgIds = setOf(orgId),
            tokens = tokens,
            baseUrl = baseUrl,
            filterId = filterId,
        )
    }

    private suspend fun getAllIssuesByFilter(
        orgId: OrgId,
        baseUrl: Url,
        identityId: IdentityId,
        filterId: String,
    ): List<IssueWithoutFields> {
        val tokens = atlassianTokenProvider.getTokens(identityId = identityId, provider = Provider.Jira)
            ?: return emptyList()
        return jiraApiProvider.jiraIssuesApi.getAllIssuesByFilter(
            orgIds = setOf(orgId),
            tokens = tokens,
            baseUrl = baseUrl,
            filterId = filterId,
        )
    }

    suspend fun findJiraIssuesWithFilters(
        orgId: OrgId,
        projectId: JiraProjectId?,
        issueStatus: IssueStatus?,
        assigneeId: OrgMemberId?,
        creatorId: OrgMemberId?,
        createdAfter: Instant?,
        createdBefore: Instant?,
        updatedAfter: Instant?,
        updatedBefore: Instant?,
        resolvedAfter: Instant?,
        resolvedBefore: Instant?,
    ): List<JiraIssue> {
        return jiraIssueStore.findJiraIssuesWithFilters(
            orgId = orgId,
            projectId = projectId,
            issueStatus = issueStatus,
            assigneeId = assigneeId,
            creatorId = creatorId,
            createdAfter = createdAfter,
            createdBefore = createdBefore,
            updatedAfter = updatedAfter,
            updatedBefore = updatedBefore,
            resolvedAfter = resolvedAfter,
            resolvedBefore = resolvedBefore,
        )
    }
}
