package com.nextchaptersoftware.insider

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.SlackChannelModel
import com.nextchaptersoftware.db.models.SlackTeamModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AnyOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.insider.models.ResolvedAdminWebUser
import com.nextchaptersoftware.insider.models.ResolvedInsideMember
import com.nextchaptersoftware.insider.models.ResolvedInsideOrg
import com.nextchaptersoftware.insider.models.ResolvedInsideOrgMember
import com.nextchaptersoftware.insider.models.ResolvedInsideSlackChannel
import com.nextchaptersoftware.insider.models.ResolvedInsideTeam
import com.nextchaptersoftware.insider.models.ResolvedInsideUser
import com.nextchaptersoftware.insider.models.ResolvedInsiders
import com.nextchaptersoftware.insider.models.config.InsideConfigIds
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import kotlinx.coroutines.runBlocking
import mu.KotlinLogging
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.and

private val LOGGER = KotlinLogging.logger {}

/**
 * Provides an efficient way to determine is a team or user is an insider (internal).
 *
 * User and teams IDs are looked up and memoized during object initialization,
 * which means the database must available by the time this object is first accessed.
 */
class InsiderService(
    private val config: InsiderConfig = InsiderConfig.instance,
    private val enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
) : InsiderServiceInterface {

    private val insiders by lazy {
        runBlocking {
            runSuspendCatching {
                resolveInsiders()
            }.getOrElse {
                LOGGER.errorAsync(it) { "Failed to resolve insiders" }
                ResolvedInsiders(
                    resolvedInsideTeams = emptySet(),
                    resolvedInsideOrgs = emptySet(),
                    resolvedInsideMembers = emptySet(),
                    resolvedInsideUsers = emptyList(),
                    resolvedAdminWebUsers = emptyList(),
                    resolvedInsideOrgMembers = emptySet(),
                    resolvedInsideSlackChannels = emptySet(),
                )
            }
        }
    }

    private suspend fun resolveInsiders(): ResolvedInsiders {
        val resolvedInsideTeams = findResolvedInsideTeams()

        return ResolvedInsiders(
            resolvedInsideTeams = resolvedInsideTeams,
            resolvedInsideOrgs = findResolvedInsideOrgs(resolvedInsideTeams),
            resolvedInsideMembers = findResolvedInsideMembers(resolvedInsideTeams),
            resolvedInsideUsers = findResolvedUsers(config.insideUsers),
            resolvedAdminWebUsers = findResolvedAdminWebUsers(),
            resolvedInsideOrgMembers = findResolvedInsideOrgMembers(resolvedInsideTeams),
            resolvedInsideSlackChannels = findResolvedInsideSlackChannels(resolvedInsideTeams),
        )
    }

    private suspend fun findResolvedEnterprises(): List<EnterpriseAppConfigId> {
        return listOf(
            enterpriseAppConfigStore.findAllByHostAndPort(config.insideEnterpriseHosts.githubEnterprise, Provider.GitHubEnterprise),
            enterpriseAppConfigStore.findAllByHostAndPort(config.insideEnterpriseHosts.gitlabSelfHosted, Provider.GitLabSelfHosted),
        ).flatten()
    }

    private suspend fun findResolvedInsideTeams(): Set<ResolvedInsideTeam> {
        val enterpriseIds = findResolvedEnterprises()
        return suspendedTransaction {
            ScmTeamModel
                .select(ScmTeamModel.id)
                .where {
                    with(ScmTeamModel) {
                        AnyOp(
                            (provider eq Provider.Bitbucket) and (
                                    providerExternalId inList config.insideTeams.bitbucket.plus(config.insideUsers.bitbucket).distinct()
                                    ),
                            (provider eq Provider.GitHub) and (
                                    providerExternalId inList config.insideTeams.github.plus(config.insideUsers.github).distinct()
                                    ),
                            (provider eq Provider.GitLab) and (
                                    providerExternalId inList config.insideTeams.gitlab.plus(config.insideUsers.gitlab).distinct()
                                    ),
                            (providerEnterprise inList enterpriseIds),
                        )
                    }
                }
                .map {
                    ResolvedInsideTeam(
                        teamId = it[ScmTeamModel.id].value,
                    )
                }
                .toSet()
        }
    }

    private suspend fun findResolvedInsideOrgs(insideTeams: Set<ResolvedInsideTeam>): Set<ResolvedInsideOrg> {
        return suspendedTransaction {
            ScmTeamModel
                .select(ScmTeamModel.org)
                .where { ScmTeamModel.id inList insideTeams.map(ResolvedInsideTeam::teamId) }
                .map { ResolvedInsideOrg(it[ScmTeamModel.org].value) }
                .toSet()
        }
    }

    private suspend fun findResolvedInsideMembers(insideTeams: Set<ResolvedInsideTeam>): Set<ResolvedInsideMember> {
        return suspendedTransaction {
            MemberModel
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.installation,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                )
                .select(MemberModel.id)
                .where { ScmTeamModel.id inList insideTeams.map(ResolvedInsideTeam::teamId) }
                .map {
                    ResolvedInsideMember(
                        memberId = it[MemberModel.id].value,
                    )
                }
                .toSet()
        }
    }

    private suspend fun findResolvedInsideOrgMembers(insideTeams: Set<ResolvedInsideTeam>): Set<ResolvedInsideOrgMember> {
        return suspendedTransaction {
            OrgMemberModel
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.org,
                    onColumn = OrgMemberModel.org,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = ScmTeamModel.installation,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = MemberModel,
                    otherColumn = MemberModel.installation,
                    onColumn = InstallationModel.id,
                    joinType = JoinType.INNER,
                )
                .select(OrgMemberModel.id)
                .where { ScmTeamModel.id inList insideTeams.map(ResolvedInsideTeam::teamId) }
                .map {
                    ResolvedInsideOrgMember(
                        orgMemberId = it[OrgMemberModel.id].value,
                    )
                }
                .toSet()
        }
    }

    private suspend fun findResolvedInsideSlackChannels(insideTeams: Set<ResolvedInsideTeam>): Set<ResolvedInsideSlackChannel> {
        return suspendedTransaction {
            SlackChannelModel
                .join(
                    otherTable = SlackTeamModel,
                    otherColumn = SlackTeamModel.id,
                    onColumn = SlackChannelModel.slackTeam,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = SlackTeamModel.installation,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.org,
                    onColumn = InstallationModel.org,
                    joinType = JoinType.INNER,
                ) {
                    ScmTeamModel.id inList insideTeams.map(ResolvedInsideTeam::teamId)
                }
                .select(SlackChannelModel.id)
                .map {
                    ResolvedInsideSlackChannel(
                        slackChannelId = it[SlackChannelModel.id].value,
                    )
                }.toSet()
        }
    }

    private suspend fun findResolvedUsers(userIds: InsideConfigIds): List<ResolvedInsideUser> {
        val enterpriseIds = findResolvedEnterprises()
        return suspendedTransaction {
            IdentityModel
                .join(
                    otherTable = PersonModel,
                    otherColumn = PersonModel.id,
                    onColumn = IdentityModel.person,
                    joinType = JoinType.INNER,
                )
                .select(IdentityModel.id, PersonModel.id)
                .where {
                    with(IdentityModel) {
                        AnyOp(
                            (provider eq Provider.Bitbucket) and (externalId inList userIds.bitbucket),
                            (provider eq Provider.GitHub) and (externalId inList userIds.github),
                            (provider eq Provider.GitLab) and (externalId inList userIds.gitlab),
                            (externalTeamId inList enterpriseIds.map(EnterpriseAppConfigId::toString)),
                        )
                    }
                }
                .map {
                    ResolvedInsideUser(
                        personId = it[PersonModel.id].value,
                        identityId = it[IdentityModel.id].value,
                    )
                }
        }
    }

    private suspend fun findResolvedAdminWebUsers(): List<ResolvedAdminWebUser> {
        return suspendedTransaction {
            config.adminWebUsers.mapNotNull { userConfig ->
                IdentityModel
                    .join(
                        otherTable = PersonModel,
                        otherColumn = PersonModel.id,
                        onColumn = IdentityModel.person,
                        joinType = JoinType.INNER,
                    )
                    .select(IdentityModel.id, PersonModel.id)
                    .whereAll(
                        IdentityModel.provider inList listOf(Provider.Unblocked, Provider.GitHub),
                        IdentityModel.externalId eq userConfig.externalId,
                    )
                    .limit(1)
                    .firstOrNull()
                    ?.let {
                        ResolvedAdminWebUser(
                            personId = it[PersonModel.id].value,
                            identityId = it[IdentityModel.id].value,
                            email = EmailAddress.of(userConfig.email),
                        )
                    }
            }
        }
    }

    override val insiderOrgs: List<OrgId>
        get() = insiders.resolvedInsideOrgs.map(ResolvedInsideOrg::orgId)

    override val insiderScmTeamIds: List<ScmTeamId>
        get() = insiders.resolvedInsideTeams.map(ResolvedInsideTeam::teamId)

    override val insiderPersonIds: List<PersonId>
        get() = insiders.resolvedInsideUsers.map(ResolvedInsideUser::personId)

    override val insiderIdentityIds: List<IdentityId>
        get() = insiders.resolvedInsideUsers.map(ResolvedInsideUser::identityId)

    override val insiderMemberIds: List<MemberId>
        get() = insiders.resolvedInsideMembers.map(ResolvedInsideMember::memberId)

    override val insiderOrgMemberIds: List<OrgMemberId>
        get() = insiders.resolvedInsideOrgMembers.map(ResolvedInsideOrgMember::orgMemberId)

    override val insiderSlackChannelIds: List<SlackChannelId>
        get() = insiders.resolvedInsideSlackChannels.map(ResolvedInsideSlackChannel::slackChannelId)

    override fun isInsiderOrg(orgId: OrgId) =
        insiders.resolvedInsideOrgs.map(ResolvedInsideOrg::orgId).contains(orgId)

    override fun isInsiderTeam(teamId: ScmTeamId) =
        insiders.resolvedInsideTeams.map(ResolvedInsideTeam::teamId).contains(teamId)

    override fun isInsiderPerson(personId: PersonId) =
        insiders.resolvedInsideUsers.map(ResolvedInsideUser::personId).contains(personId)

    override fun isInsiderIdentity(identityId: IdentityId) =
        insiders.resolvedInsideUsers.map(ResolvedInsideUser::identityId).contains(identityId)

    override fun isInsiderMember(memberId: MemberId) =
        insiders.resolvedInsideMembers.map(ResolvedInsideMember::memberId).contains(memberId)

    override fun isInsiderOrgMember(orgMemberId: OrgMemberId) =
        insiders.resolvedInsideOrgMembers.map(ResolvedInsideOrgMember::orgMemberId).contains(orgMemberId)

    override fun isInsiderSlackChannel(slackChannelId: SlackChannelId) =
        insiders.resolvedInsideSlackChannels.map(ResolvedInsideSlackChannel::slackChannelId).contains(slackChannelId)

    override fun isInsider(
        orgId: OrgId?,
        teamId: ScmTeamId?,
        identityId: IdentityId?,
        personId: PersonId?,
        orgMemberId: OrgMemberId?,
    ): Boolean? {
        return listOfNotNull(
            orgId?.let { isInsiderOrg(orgId) },
            teamId?.let { isInsiderTeam(teamId) },
            identityId?.let { isInsiderIdentity(identityId) },
            personId?.let { isInsiderPerson(personId) },
            orgMemberId?.let { isInsiderOrgMember(orgMemberId) },
        ).nullIfEmpty()?.any { it }
    }

    override fun getAdminIdentityByEmail(email: EmailAddress): IdentityId? =
        insiders.resolvedAdminWebUsers.firstOrNull { it.email == email }?.identityId

    override suspend fun refresh() {
        runSuspendCatching {
            insiders.resolvedInsideTeams = findResolvedInsideTeams()
            insiders.resolvedAdminWebUsers = findResolvedAdminWebUsers()
        }.onFailure {
            LOGGER.errorAsync(it) { "Failed to update insider teams" }
        }
    }
}
