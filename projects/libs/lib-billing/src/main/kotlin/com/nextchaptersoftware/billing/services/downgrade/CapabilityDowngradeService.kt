package com.nextchaptersoftware.billing.services.downgrade

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.utils.KotlinUtils.required
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

/**
 * Responsible for removing capabilities from an organization.
 */
class CapabilityDowngradeService(
    private val planCapabilitiesService: PlanCapabilitiesService,
    private val ciCapabilityDowngradeHandler: CiCapabilityDowngradeHandler,
    private val multiScmCapabilityDowngradeHandler: MultiScmCapabilityDowngradeHand<PERSON>,
    private val noOpCapabilityDowngradeHandler: NoOpCapabilityDowngrade<PERSON>andler,
    private val preferencesCapabilityDowngradeHandler: PreferencesCapabilityDowngradeHandler,
    private val providerCapabilityDowngradeHandler: ProviderCapabilityDowngradeHandler,
    private val securityCapabilityDowngradeHandler: SecurityCapabilityDowngradeHandler,
) : CapabilityDowngradeHandler {

    /**
     * Ensures that capabilities not allowed by this orgs plan are removed from the organization.
     * This is idempotent, so it can be called multiple times without issue.
     * It always does the right thing based on the current plan.
     *
     * @param orgId The organization to downgrade.
     */
    suspend fun downgradeOrg(orgId: OrgId) {
        val deniedCapabilities = getDeniedCapabilities(orgId).toMutableList()

        // Ensure that Multi-SCM is removed last, if it is present in the denied capabilities
        if (deniedCapabilities.remove(PlanCapabilityType.MultiSCM)) {
            deniedCapabilities.add(PlanCapabilityType.MultiSCM)
        }

        deniedCapabilities.forEach { capability ->
            withLoggingContextAsync(
                "orgId" to orgId,
                "capability" to capability.name,
            ) {
                runSuspendCatching {
                    handleCapabilityDowngrade(orgId, capability)
                }.onFailure {
                    LOGGER.errorAsync(it) { "Failed to downgrade capability" }
                }
            }
        }
    }

    override suspend fun handleCapabilityDowngrade(orgId: OrgId, capability: PlanCapabilityType) {
        LOGGER.infoAsync { "Removing capability from org" }
        when (capability) {
            PlanCapabilityType.AzureDevOps,
            PlanCapabilityType.BitbucketDataCenter,
            PlanCapabilityType.Coda,
            PlanCapabilityType.Confluence,
            PlanCapabilityType.ConfluenceDataCenter,
            PlanCapabilityType.GitHubEnterprise,
            PlanCapabilityType.GitLabDedicated,
            PlanCapabilityType.GoogleDrive,
            PlanCapabilityType.Jira,
            PlanCapabilityType.JiraDataCenter,
            PlanCapabilityType.Notion,
            PlanCapabilityType.Slack,
            PlanCapabilityType.StackOverflowForTeams,
            PlanCapabilityType.UnblockedApi,
            PlanCapabilityType.Websites,
                -> providerCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)

            PlanCapabilityType.MultiSCM,
                -> multiScmCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)

            PlanCapabilityType.AnswerPreferences,
                -> preferencesCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)

            PlanCapabilityType.CI,
                -> ciCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)

            PlanCapabilityType.DSAC,
            PlanCapabilityType.RBAC,
            PlanCapabilityType.SSO,
                -> securityCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)

            // TODO not clear what should be done here. For now nothing is done.
            PlanCapabilityType.Experts,
            PlanCapabilityType.IncognitoMode,
            PlanCapabilityType.InvoicedBilling,
            PlanCapabilityType.UnblockedInSlack,
            PlanCapabilityType.WideSlackRetrieval,
            PlanCapabilityType.SlackPrivateScopes,
            PlanCapabilityType.SlackAutoResponse,
            PlanCapabilityType.DataSourcePresets,
            PlanCapabilityType.MCP,
                -> noOpCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)

            PlanCapabilityType.AccountManager,
            PlanCapabilityType.CustomTerms,
            PlanCapabilityType.PremiumSupport,
            PlanCapabilityType.ReferenceObsoletion,
            PlanCapabilityType.SecurityReview,
            PlanCapabilityType.SlackSupportChannel,
                -> noOpCapabilityDowngradeHandler.handleCapabilityDowngrade(orgId, capability)
        }
    }

    private suspend fun getDeniedCapabilities(orgId: OrgId): List<PlanCapabilityType> {
        return PlanCapabilityType.entries.minus(getAllowedCapabilities(orgId))
    }

    private suspend fun getAllowedCapabilities(orgId: OrgId): Set<PlanCapabilityType> {
        return planCapabilitiesService.getAllowedCapabilities(orgId)
            // Not safe to proceed if we cannot determine the allowed capabilities
            .required { "Can't downgrade org that has no billing model" }
    }
}
