package com.nextchaptersoftware.billing.services

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.CardType
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanRate
import com.nextchaptersoftware.db.models.PlanTier
import com.nextchaptersoftware.db.stores.OrgBillingSeatStore
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.PlanAndPrices
import com.nextchaptersoftware.db.stores.PlanStore
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.stripe.services.StripeService
import com.nextchaptersoftware.test.mokito.MockitoExtensions.eqValue
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.stripe.model.Customer
import com.stripe.model.PaymentMethod
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds
import kotlin.time.Instant
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions
import org.mockito.kotlin.verifyNoMoreInteractions

class BillingServiceTest {
    private val orgId = OrgId.random()

    private val orgBilling = MockDataClasses.orgBilling(orgId = orgId)

    private val orgBillingStore = mock<OrgBillingStore>().also {
        runBlocking { `when`(it.findByOrg(orgId = orgId)).thenReturn(orgBilling) }
    }

    private val orgBillingSeatStore = mock<OrgBillingSeatStore>()

    private val orgStore = mock<OrgStore>()

    private val stripeService = mock<StripeService>()

    private val planCapabilitiesService = mock<PlanCapabilitiesService>()

    private val planStore = mock<PlanStore>()

    private val service = BillingService(
        orgStore = orgStore,
        orgBillingStore = orgBillingStore,
        orgBillingSeatStore = orgBillingSeatStore,
        stripeService = stripeService,
        planCapabilitiesService = planCapabilitiesService,
        planStore = planStore,
        enableTestBillingActions = true,
        config = GlobalConfig.INSTANCE.billing,
    )

    @Test
    fun isLegacyPlanEligible() = runTest {
        val trialPlan = MockDataClasses.plan(isTrialPlan = true)
        val notTrialPlan = MockDataClasses.plan()

        `when`(planStore.find(planId = eqValue(trialPlan.id))).thenReturn(trialPlan)
        `when`(planStore.find(planId = eqValue(notTrialPlan.id))).thenReturn(notTrialPlan)

        val orgA = MockDataClasses.org(createdAt = BillingService.LEGACY_PLAN_CUTOFF_DATE.minus(1.seconds))
        val orgB = MockDataClasses.org(createdAt = BillingService.LEGACY_PLAN_CUTOFF_DATE.plus(1.seconds))
        val orgC = MockDataClasses.org(createdAt = BillingService.LEGACY_PLAN_CUTOFF_DATE.minus(1.seconds))

        `when`(orgStore.findById(trx = anyOrNull(), orgId = eqValue(orgA.id), includeDeleted = anyOrNull())).thenReturn(orgA)
        `when`(orgStore.findById(trx = anyOrNull(), orgId = eqValue(orgB.id), includeDeleted = anyOrNull())).thenReturn(orgB)
        `when`(orgStore.findById(trx = anyOrNull(), orgId = eqValue(orgC.id), includeDeleted = anyOrNull())).thenReturn(orgC)

        val orgBillingA = MockDataClasses.orgBilling(orgId = orgA.id, planId = trialPlan.id)
        val orgBillingB = MockDataClasses.orgBilling(orgId = orgB.id, planId = trialPlan.id)
        val orgBillingC = MockDataClasses.orgBilling(orgId = orgC.id, planId = notTrialPlan.id)

        `when`(orgBillingStore.findByOrg(orgId = eqValue(orgA.id))).thenReturn(orgBillingA)
        `when`(orgBillingStore.findByOrg(orgId = eqValue(orgB.id))).thenReturn(orgBillingB)
        `when`(orgBillingStore.findByOrg(orgId = eqValue(orgC.id))).thenReturn(orgBillingC)

        assertThat(service.isLegacyPlanEligible(orgA.id)).isTrue() // Because they're on trial plan and before cutoff date
        assertThat(service.isLegacyPlanEligible(orgB.id)).isFalse() // Because they're on trial plan but after cutoff date
        assertThat(service.isLegacyPlanEligible(orgC.id)).isFalse() // Because they're on non-trial plan even though they are after cutoff date

        `when`(orgBillingStore.findByOrg(orgId = eqValue(orgA.id))).thenReturn(orgBillingA.copy(planId = notTrialPlan.id))
        assertThat(service.isLegacyPlanEligible(orgA.id)).isFalse() // Should not be eligible now that the team has picked a plan
    }

    @Test
    fun setDefaultPaymentMethod() = runTest {
        val card = mock<PaymentMethod.Card>().also {
            `when`(it.brand).thenReturn("visa")
            `when`(it.last4).thenReturn("1234")
            `when`(it.expMonth).thenReturn(1)
            `when`(it.expYear).thenReturn(2023)
        }

        val paymentMethod = mock<PaymentMethod>().also {
            `when`(it.id).thenReturn("pm_123")

            `when`(it.card).thenReturn(card)
        }

        `when`(stripeService.getPaymentMethods(customerId = orgBilling.stripeCustomerId))
            .thenReturn(listOf(paymentMethod))

        service.setDefaultPaymentMethod(orgId = orgBilling.orgId)

        verify(stripeService, times(1)).setDefaultPaymentMethod(
            customerId = orgBilling.stripeCustomerId,
            paymentMethod = paymentMethod,
        )

        verify(orgBillingStore, times(1)).setCardInfo(
            stripeCustomerId = orgBilling.stripeCustomerId,
            cardType = CardType.Visa,
            cardLastFour = "1234",
            cardExpMonth = 1,
            cardExpYear = 2023,
        )
    }

    @Test
    fun maybeScheduleSeatUpdateForNextCycle() = runTest {
        val plan = MockDataClasses.plan(tier = PlanTier.Business)
        val price = MockDataClasses.planPrice(planId = plan.id, type = PlanRate.Monthly, cost = 2300)
        `when`(planStore.findWithPrices(planId = plan.id)).thenReturn(PlanAndPrices(plan, listOf(price)))

        val now = Instant.nowWithMicrosecondPrecision()
        val seats = 5
        val orgBilling = MockDataClasses.orgBilling(
            orgId = orgBilling.orgId,
            planId = plan.id,
            rate = PlanRate.Monthly,
            currentBillingCycleEnd = now.plus(14.days),
            seats = seats,
        )

        // 1. First try when the number of assigned seats is less than current seats
        `when`(orgBillingSeatStore.getSeatsFilled(orgBillingId = orgBilling.id)).thenReturn(
            (1..(seats - 1)).map { MockDataClasses.orgBillingSeat(orgBillingId = orgBilling.id) },
        )

        assertThat(service.maybeScheduleSeatUpdateForNextCycle(orgBilling = orgBilling)).isEqualTo(
            SeatUpdateResult.NotUpdated,
        )

        verifyNoInteractions(stripeService)
        verifyNoInteractions(orgBillingStore)

        // 2. Then try when the number of assigned seats is greater than current seats
        `when`(orgBillingSeatStore.getSeatsFilled(orgBillingId = orgBilling.id)).thenReturn(
            (1..(seats + 1)).map { MockDataClasses.orgBillingSeat(orgBillingId = orgBilling.id) },
        )

        assertThat(service.maybeScheduleSeatUpdateForNextCycle(orgBilling = orgBilling)).isEqualTo(
            SeatUpdateResult.Updated(updatedSeats = seats + 1),
        )

        verify(stripeService, times(1)).scheduleSubscriptionChange(
            customerId = orgBilling.stripeCustomerId,
            newPriceId = null,
            newSeats = seats + 1,
        )
        verify(orgBillingStore, times(1)).setNextPlan(
            orgId = orgBilling.orgId,
            nextPlanId = orgBilling.planId,
            nextRate = orgBilling.rate,
            nextSeats = seats + 1,
        )

        // 3. Finally try when the number of assigned seats is equal to the number of seats
        `when`(orgBillingSeatStore.getSeatsFilled(orgBillingId = orgBilling.id)).thenReturn(
            (1..seats).map { MockDataClasses.orgBillingSeat(orgBillingId = orgBilling.id) },
        )

        assertThat(service.maybeScheduleSeatUpdateForNextCycle(orgBilling = orgBilling)).isEqualTo(
            SeatUpdateResult.ExistingUpdateCancelled,
        )

        verify(stripeService, times(1)).cancelScheduledSubscriptionUpdate(
            customerId = orgBilling.stripeCustomerId,
        )
        verify(orgBillingStore, times(1)).setNextPlan(
            orgId = orgBilling.orgId,
            nextPlanId = null,
            nextRate = null,
            nextSeats = null,
        )
        verifyNoMoreInteractions(stripeService)
        verifyNoMoreInteractions(orgBillingStore)
    }

    @Test
    fun maybeChargeProratedAmountForSeats() = runTest {
        val plan = MockDataClasses.plan(tier = PlanTier.Business)
        val price = MockDataClasses.planPrice(planId = plan.id, type = PlanRate.Annual, cost = 22800)
        `when`(planStore.findWithPrices(planId = plan.id)).thenReturn(PlanAndPrices(plan, listOf(price)))

        val now = Instant.nowWithMicrosecondPrecision()
        val orgBilling = MockDataClasses.orgBilling(
            orgId = orgBilling.orgId,
            planId = plan.id,
            rate = PlanRate.Annual,
            currentBillingCycleEnd = now.plus(80.days),
        )
        val seats = 3

        service.maybeChargeProratedAmountForSeats(orgBilling = orgBilling, seats = seats)

        verify(
            stripeService, times(1),
        ).chargeCustomer(
            customerId = orgBilling.stripeCustomerId,
            description = "Unblocked Business Plan (prorated for 2 months)",
            quantity = seats.toLong(),
            unitAmount = 3800,
        )
    }

    @Test
    fun `getOrCreate -- billing enabled`() = runTest {
        val trialPlan = MockDataClasses.plan(tier = null, isTrialPlan = true)
        val customer = mock<Customer>().also { `when`(it.id).thenReturn("cus_123") }

        `when`(orgBillingStore.findByOrg(orgId)).thenReturn(null)
        `when`(planStore.trialPlan()).thenReturn(trialPlan)
        `when`(stripeService.createCustomer(orgId)).thenReturn(customer)

        service.getOrCreate(orgId)

        verify(
            orgBillingStore, times(1),
        ).create(
            orgId = eqValue(orgId),
            planId = eqValue(trialPlan.id),
            stripeCustomerId = eq("cus_123"),
            trialEnd = any(),
        )
    }

    @Test
    fun `getOrCreate -- billing disabled`() = runTest {
        val enterprisePlan = MockDataClasses.plan(tier = PlanTier.Enterprise)

        `when`(orgBillingStore.findByOrg(orgId)).thenReturn(null)
        `when`(planStore.enterprisePlan()).thenReturn(enterprisePlan)

        val serviceWithBillingDisabled = BillingService(
            orgStore = orgStore,
            orgBillingStore = orgBillingStore,
            orgBillingSeatStore = orgBillingSeatStore,
            stripeService = stripeService,
            planCapabilitiesService = planCapabilitiesService,
            planStore = planStore,
            enableTestBillingActions = true,
            config = GlobalConfig.INSTANCE.billing.copy(enabled = false),
        )

        serviceWithBillingDisabled.getOrCreate(orgId = orgId)

        verifyNoInteractions(stripeService)

        verify(
            orgBillingStore, times(1),
        ).create(
            orgId = eqValue(orgId),
            planId = eqValue(enterprisePlan.id),
            stripeCustomerId = eq("org_${orgId.value}"),
            trialEnd = any(),
        )
    }
}
