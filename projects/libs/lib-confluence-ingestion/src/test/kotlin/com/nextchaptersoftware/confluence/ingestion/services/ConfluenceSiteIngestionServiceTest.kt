package com.nextchaptersoftware.confluence.ingestion.services

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.ConfluenceSite
import com.nextchaptersoftware.db.models.ConfluenceSiteId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.ConfluenceSiteStore
import com.nextchaptersoftware.db.stores.ConfluenceSpaceStore
import com.nextchaptersoftware.db.stores.IngestionStore
import com.nextchaptersoftware.ingestion.services.IntegrationIngestionCompletionService
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.RedisLock
import com.nextchaptersoftware.test.mokito.MockitoExtensions.eqValue
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.eq
import org.mockito.kotlin.verify

class ConfluenceSiteIngestionServiceTest {
    private val ingestionStore: IngestionStore = mock()
    private val confluenceSiteStore: ConfluenceSiteStore = mock()
    private val confluenceSpaceStore: ConfluenceSpaceStore = mock()
    private val integrationIngestionCompletionService: IntegrationIngestionCompletionService = mock()
    private val confluenceSpaceIngestionService: ConfluenceSpaceIngestionService = mock()
    private val confluencePageIngestionService: ConfluencePageIngestionService = mock()
    private val confluenceBlogPostsIngestionService: ConfluenceBlogPostsIngestionService = mock()
    private val confluenceSpaceProviderService = ConfluenceSpaceProviderService(confluenceSpaceStore)
    private val lockProvider: LockProvider = mock()

    private val service = ConfluenceSiteIngestionService(
        ingestionStore = ingestionStore,
        confluenceSiteStore = confluenceSiteStore,
        confluenceSpaceProviderService = confluenceSpaceProviderService,
        integrationIngestionCompletionService = integrationIngestionCompletionService,
        confluenceSpaceIngestionService = confluenceSpaceIngestionService,
        confluencePageIngestionService = confluencePageIngestionService,
        confluenceBlogPostsIngestionService = confluenceBlogPostsIngestionService,
        lockProvider = lockProvider,
    )

    private val installation = MockDataClasses.baseInstallation(provider = Provider.Confluence)
    private val site = ConfluenceSite(
        id = ConfluenceSiteId.random(),
        installationId = installation.id,
        siteId = "abc",
        baseUrl = Url("https://example.com"),
        avatarUrl = Url("https://example.com/avatar"),
        name = "Example",
        ingestPersonalSpaces = false,
    )
    private val lock = mock<RedisLock>()

    @Test
    fun `ingest -- initial ingestion`() = runTest {
        val ingestion = MockDataClasses.confluenceIngestion()
        `when`(
            ingestionStore.upsert(
                installationId = installation.id,
                provider = installation.provider,
            ),
        ).thenReturn(ingestion)

        `when`(lock.renew()).thenReturn(true)
        `when`(confluencePageIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceBlogPostsIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceSpaceStore.list(eqValue(site.id), eq(null), eq(null))).thenReturn(emptyList())

        service.ingest(installation = installation, site = site, lock = lock)

        verify(ingestionStore, times(1)).setLastSynced(
            installationId = installation.id,
            lastSynced = ingestion.createdAt,
        )
    }

    @Test
    fun `ingest -- initial ingestion interrupted`() = runTest {
        val ingestion = MockDataClasses.confluenceIngestion(
            lastSynced = null,
            pagesNextUrl = null,
            blogPostsNextUrl = Url("google.com"),
        )
        `when`(
            ingestionStore.upsert(
                installationId = installation.id,
                provider = installation.provider,
            ),
        ).thenReturn(ingestion)

        `when`(lock.renew()).thenReturn(true)
        `when`(confluencePageIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceBlogPostsIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceSpaceStore.list(eqValue(site.id), eq(null), eq(null))).thenReturn(emptyList())

        service.ingest(installation = installation, site = site, lock = lock)

        verify(ingestionStore, times(1)).setLastSynced(
            installationId = installation.id,
            lastSynced = ingestion.createdAt,
        )
    }

    @Test
    fun `ingest -- sync`() = runTest {
        val ingestion = MockDataClasses.confluenceIngestion(
            lastSynced = Instant.nowWithMicrosecondPrecision(),
            pagesNextUrl = null,
            blogPostsNextUrl = null,
        )
        `when`(
            ingestionStore.upsert(
                installationId = installation.id,
                provider = installation.provider,
            ),
        ).thenReturn(ingestion)

        `when`(lock.renew()).thenReturn(true)
        `when`(confluencePageIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceBlogPostsIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceSpaceStore.list(eqValue(site.id), eq(null), eq(null))).thenReturn(emptyList())

        service.ingest(installation = installation, site = site, lock = lock)

        val captor = argumentCaptor<Instant>()
        verify(ingestionStore, times(1)).setLastSynced(
            installationId = eqValue(installation.id),
            lastSynced = captor.capture(),
        )
        assertThat(captor.firstValue).isGreaterThan(ingestion.lastSynced)
    }

    @Test
    fun `ingest -- sync from interrupting`() = runTest {
        val ingestion = MockDataClasses.confluenceIngestion(
            lastSynced = Instant.nowWithMicrosecondPrecision(),
            pagesNextUrl = Url("google.com"),
            blogPostsNextUrl = null,
        )
        `when`(
            ingestionStore.upsert(
                installationId = installation.id,
                provider = installation.provider,
            ),
        ).thenReturn(ingestion)

        `when`(lock.renew()).thenReturn(true)
        `when`(confluencePageIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceBlogPostsIngestionService.ingest(eq(installation), eq(site), eq(ingestion), any(), eq(lock))).thenReturn(true)
        `when`(confluenceSpaceStore.list(eqValue(site.id), eq(null), eq(null))).thenReturn(emptyList())

        service.ingest(installation = installation, site = site, lock = lock)

        verify(ingestionStore, times(0)).setLastSynced(
            installationId = any(),
            lastSynced = any(),
        )
    }
}
