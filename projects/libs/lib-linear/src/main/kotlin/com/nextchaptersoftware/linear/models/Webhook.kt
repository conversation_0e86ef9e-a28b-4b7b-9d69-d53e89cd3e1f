package com.nextchaptersoftware.linear.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Suppress("ConstructorParameterNaming")
@Serializable
data class Webhook(
    @SerialName("action") val _action: String,
    val url: String? = null,
    @SerialName("type") val _type: String,
    val organizationId: String,
    val webhookTimestamp: Long,
) {
    enum class Action(val str: String) {
        Create("create"),
        Update("update"),
        Remove("remove"),
        Restore("restore"),
        Unknown("unknown"),
    }

    enum class Type(val str: String) {
        Comment("comment"),
        Issue("issue"),
        Unknown("unknown"),
    }

    val action: Action
        get() = Action.entries.firstOrNull { it.str.lowercase() == _action.lowercase() } ?: Action.Unknown

    val type: Type
        get() = Type.entries.firstOrNull { it.str.lowercase() == _type.lowercase() } ?: Type.Unknown
}
