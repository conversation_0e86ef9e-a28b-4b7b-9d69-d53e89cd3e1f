plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:models", "default"))
    implementation(project(":projects:libs:lib-event-queue", "default"))
    implementation(project(":projects:libs:lib-service", "default"))
    implementation(project(":projects:libs:lib-bot", "default"))
    implementation(project(":projects:libs:lib-insight", "default"))
    implementation(project(":projects:libs:lib-ml-completion", "default"))
    implementation(project(":projects:libs:lib-ml-embedding", "default"))
    implementation(project(":projects:libs:lib-ml-embedding-core", "default"))
    implementation(project(":projects:libs:lib-ml-inference", "default"))

    implementation("org.apache.commons:commons-math3:3.6.1")
    implementation("org.ejml:ejml-kotlin:0.43.1")
    implementation("org.ejml:ejml-all:0.43.1")

    testImplementation(kotlin("test"))
}
