To change the Anthropic model being used, you need to:

1. Update the `AnthropicCompletionsRequest.model` field when making requests to the Anthropic Completions API. This field specifies which model to use for generating completions. The available models are:

- `Claude1`
- `Claude1100K`
- `ClaudeInstant1`
- `ClaudeInstant1100K`

2. Update the `AnthropicApiConfiguration.model` field which is used to construct the `AnthropicApiProvider`. This specifies the default model to use if one is not provided on a per-request basis.

So for example, to switch from `Claude1` to `ClaudeInstant1` as the default model, you would:

1. Update `AnthropicApiConfiguration`:
```kotlin
val anthropicApiConfiguration = AnthropicApiConfiguration(
   // ...
   model = AnthropicCompletionsModel.ClaudeInstant1
)
```

2. Any requests made through the `anthropicApiProvider` would now use the `ClaudeInstant1` model by default.

3. You can still override the model on a per-request basis by setting `AnthropicCompletionsRequest.model`.

So in summary, there are two ways to specify the Anthropic model - as a default on the `AnthropicApiProvider`, or on a per-request basis. Updating either of these will change the model being used.
