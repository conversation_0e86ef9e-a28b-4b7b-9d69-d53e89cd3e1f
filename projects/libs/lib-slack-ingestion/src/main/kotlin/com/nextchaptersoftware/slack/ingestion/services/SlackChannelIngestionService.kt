package com.nextchaptersoftware.slack.ingestion.services

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.SlackChannel
import com.nextchaptersoftware.db.models.SlackChannelIngestion
import com.nextchaptersoftware.db.models.SlackChannelIngestionStatus
import com.nextchaptersoftware.db.models.SlackIngestion
import com.nextchaptersoftware.db.models.SlackTeam
import com.nextchaptersoftware.db.stores.EmbeddingDeleteStore
import com.nextchaptersoftware.db.stores.SlackChannelIngestionStore
import com.nextchaptersoftware.db.stores.SlackChannelStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.redis.lock.RedisLock
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.slack.search.SlackMessageBinarySearcher
import com.nextchaptersoftware.slack.services.SlackChannelFilterService
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.slack.utils.SlackMessageExtensions.isTombstone
import com.nextchaptersoftware.slack.utils.resolvedName
import com.slack.api.model.Conversation
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

@Suppress("UnusedPrivateMember")
class SlackChannelIngestionService(
    private val slackTokenService: SlackTokenService,
    private val slackMessageBinarySearcher: SlackMessageBinarySearcher,
    private val embeddingDeleteStore: EmbeddingDeleteStore = Stores.embeddingDeleteStore,
    private val slackChannelStore: SlackChannelStore = Stores.slackChannelStore,
    private val slackChannelIngestionStore: SlackChannelIngestionStore = Stores.slackChannelIngestionStore,
    private val slackChannelMessagesIngestionService: SlackChannelMessagesIngestionService,
    private val slackChannelFilterService: SlackChannelFilterService,
) {
    suspend fun ingest(
        lock: RedisLock,
        org: Org,
        installation: Installation,
        slackTeam: SlackTeam,
        slackIngestion: SlackIngestion,
        slackConversation: Conversation,
    ) = withLoggingContextAsync(
        "installationId" to installation.id.value,
        "slackTeamId" to slackTeam.id.value,
        "slackIngestionId" to slackIngestion.id,
        "slackConversationName" to slackConversation.resolvedName(),
    ) {
        assert(slackConversation.isChannel)

        val firstMessageTs: String? = fetchFirstMessageTs(
            slackTeam = slackTeam,
            slackConversation = slackConversation,
        )

        val slackChannel = Database.suspendedTransaction {
            slackChannelStore.upsert(
                trx = this,
                slackTeamId = slackIngestion.slackTeam,
                slackExternalChannelId = slackConversation.id,
                name = slackConversation.resolvedName(),
                isChannel = slackConversation.isChannel,
                isShared = slackConversation.isShared,
                isExtShared = slackConversation.isExtShared,
                isOrgShared = slackConversation.isOrgShared,
                isPrivate = slackConversation.isPrivate,
                isArchived = slackConversation.isArchived,
                isIm = slackConversation.isIm,
                isMpim = slackConversation.isMpim,
                isMember = slackConversation.isMember,
                memberCount = slackConversation.numOfMembers,
                conversationHostId = slackConversation.conversationHostId,
                creator = slackConversation.creator,
                user = slackConversation.user,
                firstMessageTs = firstMessageTs,
            ).asDataModel()
        }

        val slackChannelIngestion = Database.suspendedTransaction {
            slackChannelIngestionStore.upsert(
                trx = this,
                slackTeamId = slackTeam.id,
                slackChannelId = slackChannel.id,
                slackIngestionId = slackIngestion.id,
            ).asDataModel()
        }

        conditionallyIngestChannel(
            lock = lock,
            org = org,
            installation = installation,
            slackTeam = slackTeam,
            slackIngestion = slackIngestion,
            slackChannelIngestion = slackChannelIngestion,
            slackChannel = slackChannel,
            slackConversation = slackConversation,
        )
    }

    @Suppress("LongMethod")
    private suspend fun conditionallyIngestChannel(
        lock: RedisLock,
        org: Org,
        installation: Installation,
        slackTeam: SlackTeam,
        slackIngestion: SlackIngestion,
        slackChannelIngestion: SlackChannelIngestion,
        slackChannel: SlackChannel,
        slackConversation: Conversation,
    ) {
        val shouldIngestChannel = slackChannelFilterService.isValid(
            orgId = org.id,
            slackTeamId = slackTeam.id,
            slackChannelId = slackChannel.id,
            orgMemberId = null,
        )

        var status = SlackChannelIngestionStatus.Busy
        when (shouldIngestChannel) {
            true -> {
                runSuspendCatching {
                    LOGGER.traceAsync { "Beginning slack channel ingestion" }

                    embeddingDeleteStore.unmarkGroupForDeletion(
                        namespaceId = org.id,
                        installationId = installation.id,
                        groupId = slackChannel.id.value,
                    )

                    slackChannelIngestionStore.upsert(
                        slackTeamId = slackTeam.id,
                        slackIngestionId = slackIngestion.id,
                        slackChannelId = slackChannel.id,
                        status = status,
                    )

                    slackChannelMessagesIngestionService.ingestChannelMessages(
                        lock = lock,
                        org = org,
                        installation = installation,
                        slackTeam = slackTeam,
                        slackIngestion = slackIngestion,
                        slackChannelIngestion = slackChannelIngestion,
                        slackChannel = slackChannel,
                        slackConversation = slackConversation,
                    )

                    // TODO: Needs to be re-enabled once doc-mapping endpoint is fixed
//                    slackChannelFilesIngestionService.ingestChannelFiles(
//                        lock = lock,
//                        org = org,
//                        installation = installation,
//                        slackTeam = slackTeam,
//                        slackIngestion = slackIngestion,
//                        slackChannelIngestion = slackChannelIngestion,
//                        slackChannel = slackChannel,
//                        slackConversation = slackConversation,
//                    )
                }.onFailure {
                    LOGGER.errorAsync(it) { "Failed to ingest slack channel" }
                    status = SlackChannelIngestionStatus.Error
                }.onSuccess {
                    LOGGER.traceAsync { "Finished slack channel ingestion" }
                    status = SlackChannelIngestionStatus.Finished
                }.also {
                    slackChannelIngestionStore.upsert(
                        slackTeamId = slackTeam.id,
                        slackIngestionId = slackIngestion.id,
                        slackChannelId = slackChannel.id,
                        status = status,
                    )
                }.getOrThrow()
            }

            false -> {
                LOGGER.traceAsync { "Skipping channel ingestion since it is not configured for ingestion" }
                slackChannelIngestionStore.upsert(
                    slackTeamId = slackTeam.id,
                    slackIngestionId = slackIngestion.id,
                    slackChannelId = slackChannel.id,
                    status = SlackChannelIngestionStatus.Skipped,
                )
            }
        }
    }

    private suspend fun fetchFirstMessageTs(
        slackTeam: SlackTeam,
        slackConversation: Conversation,
    ): String? = runSuspendCatching {
        val firstMessageTs = slackChannelStore.getFirstMessageTsBySlackExternalChannelId(
            slackExternalChannelId = slackConversation.id,
        )

        slackMessageBinarySearcher.findEarliest(
            token = slackTokenService.getSlackToken(
                slackTeamId = slackTeam.id,
                permissions = listOf(SlackPermission.CHANNELS_HISTORY),
            ).value,
            channelId = slackConversation.id,
            startTs = firstMessageTs ?: slackConversation.created.toString(),
        ) { message ->
            !message.isTombstone()
        }?.ts ?: firstMessageTs
    }.getOrNull()
}
