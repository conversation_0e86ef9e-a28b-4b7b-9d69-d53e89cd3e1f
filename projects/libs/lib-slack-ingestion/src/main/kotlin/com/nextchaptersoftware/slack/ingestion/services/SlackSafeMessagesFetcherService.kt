package com.nextchaptersoftware.slack.ingestion.services

import com.google.gson.JsonSyntaxException
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.slack.serialization.SlackSerializer
import com.slack.api.methods.MethodsCompletionException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.jsonObject
import mu.KotlinLogging

/**
 * Executes a Slack Web-API request and falls back to re-parsing the raw JSON after
 * **stripping the `attachments` arrays** whenever Slack emits malformed JSON.
 *
 * Works for any endpoint that returns `messages` blocks—e.g. `conversations.history`,
 * `conversations.replies`, or `search.messages`.
 *
 * @param R Slack SDK *response* type (`ConversationsHistoryResponse`, `ConversationsRepliesResponse`, …)
 * @param token OAuth bearer token for the raw-JSON retry.
 * @param call Primary suspending call to the Slack SDK client (may throw).
 * @param fetchRaw Suspend function that fetches the same page as raw JSON text.
 */
object SlackSafeMessagesFetcherService {
    val LOGGER = KotlinLogging.logger { }

    suspend inline fun <reified R> fetch(
        crossinline call: suspend () -> R,
        crossinline fetchRaw: suspend () -> String,
    ): R = runSuspendCatching { call() }.getOrElse { throwable ->
        if (!shouldAttemptAttachmentStrip(throwable)) throw throwable

        LOGGER.warnAsync(throwable) { "Slack JSON parse failed; retrying after stripping attachments" }

        val raw = fetchRaw()
        val cleaned = stripAttachments(raw)

        SlackSerializer.SERIALIZATION_FACTORY.fromJson(cleaned, R::class.java)
    }

    fun shouldAttemptAttachmentStrip(t: Throwable): Boolean = when (t) {
        is MethodsCompletionException -> {
            val cause = t.otherException
            cause is JsonSyntaxException ||
                    (cause is IllegalStateException && isArrayObjectMismatch(cause))
        }

        is JsonSyntaxException -> true

        is IllegalStateException -> isArrayObjectMismatch(t)

        else -> false
    }

    private fun isArrayObjectMismatch(ex: IllegalStateException): Boolean =
        ex.message?.run { contains("BEGIN_OBJECT") || contains("BEGIN_ARRAY") } == true

    /**
     * Removes `attachments` from every message in a Slack JSON payload.
     */
    @Suppress("ktlint:nextchaptersoftware:no-standard-json-serialization-operators-rule")
    fun stripAttachments(raw: String): String {
        val root: JsonElement = Json.parseToJsonElement(raw)
        val cleaned = buildJsonObject {
            root.jsonObject.forEach { (key, value) ->
                if (key == "messages" && value is JsonArray) {
                    val msgs = buildJsonArray {
                        value.forEach { msg ->
                            add(
                                buildJsonObject {
                                    msg.jsonObject.forEach { (mk, mv) ->
                                        if (mk != "attachments") put(mk, mv)
                                    }
                                },
                            )
                        }
                    }
                    put("messages", msgs)
                } else {
                    put(key, value)
                }
            }
        }
        return Json.encodeToString(cleaned)
    }
}
