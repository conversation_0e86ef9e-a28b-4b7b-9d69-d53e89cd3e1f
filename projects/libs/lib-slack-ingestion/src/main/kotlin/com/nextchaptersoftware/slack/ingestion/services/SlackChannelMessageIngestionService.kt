package com.nextchaptersoftware.slack.ingestion.services

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.SlackChannel
import com.nextchaptersoftware.db.models.SlackTeam
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.stores.SlackChannelStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.integration.data.events.queue.enqueue.DataEventEnqueueService
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.client.org.HttpClientFactory
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.redis.lock.RedisLock
import com.nextchaptersoftware.slack.api.SlackApiProvider
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.slack.ingestion.utils.SlackMessageExtensions.isTopLevelMessage
import com.nextchaptersoftware.slack.serialization.SlackSerializer
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.summarization.events.queue.enqueue.SummarizationEventEnqueueService
import com.nextchaptersoftware.summarization.events.queue.payloads.SummarizationEvent
import com.slack.api.methods.response.conversations.ConversationsRepliesResponse
import com.slack.api.model.Conversation
import com.slack.api.model.Message
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.http.appendPathSegments
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class SlackChannelMessageIngestionService(
    private val slackChannelStore: SlackChannelStore = Stores.slackChannelStore,
    private val slackMessageModelService: SlackMessageModelService,
    private val slackThreadModelService: SlackThreadModelService,
    private val slackThreadFinalizationService: SlackThreadFinalizationService,
    private val slackApiProvider: SlackApiProvider,
    private val slackTokenService: SlackTokenService,
    private val slackChannelMessageAnalysisService: SlackChannelMessageAnalysisService,
    private val dataEventEnqueueService: DataEventEnqueueService,
    private val summarizationEventEnqueueService: SummarizationEventEnqueueService,
    private val slackWideThreadIngestionEnablementService: SlackWideThreadIngestionEnablementService,
) {
    suspend fun ingest(
        org: Org,
        installation: Installation,
        slackTeam: SlackTeam,
        slackChannel: SlackChannel,
        slackConversation: Conversation,
        slackMessage: Message,
        lock: RedisLock? = null,
        isPrivate: Boolean? = null,
    ): Thread? = withLoggingContextAsync(
        "slackMessageTs" to slackMessage.ts,
    ) {
        LOGGER.traceAsync { "Beginning slack channel message ingestion" }

        val slackMessages = ingestMessages(
            slackTeam = slackTeam,
            slackConversation = slackConversation,
            slackMessage = slackMessage,
            lock = lock,
        )

        val analysisDecision = slackChannelMessageAnalysisService.analyze(
            org = org,
            slackTeam = slackTeam,
            slackMessage = slackMessage,
            slackMessages = slackMessages,
        )

        when (analysisDecision) {
            is SlackChannelMessageAnalysisDecision.SlackMessageRelevancyFailed,
            SlackChannelMessageAnalysisDecision.SlackThreadRelevancyFailed,
                -> null

            is SlackChannelMessageAnalysisDecision.SlackChannelMessagesWithoutPRsApproved -> {
                enqueueSummarizationFilterAndDocumentIngestionEvent(
                    org = org,
                    slackChannel = slackChannel,
                    parentMessage = slackMessage,
                    slackMessages = analysisDecision.slackMessages,
                )
                null
            }

            is SlackChannelMessageAnalysisDecision.SlackChannelMessageWithPRsApproved -> {
                triggerMessageDataIngestion(
                    org = org,
                    slackTeam = slackTeam,
                    slackChannel = slackChannel,
                    slackMessage = slackMessage,
                    slackMessages = analysisDecision.slackMessages,
                )

                ingest(
                    org = org,
                    installation = installation,
                    slackTeam = slackTeam,
                    slackChannel = slackChannel,
                    pullRequests = analysisDecision.pullRequests,
                    slackMessage = slackMessage,
                    slackMessages = analysisDecision.slackMessages,
                    isPrivate = isPrivate,
                )
            }
        }
    }

    @Suppress("UnusedPrivateMember")
    private suspend fun enqueueSummarizationFilterAndDocumentIngestionEvent(
        org: Org,
        slackChannel: SlackChannel,
        parentMessage: Message,
        slackMessages: List<Message>,
    ) {
        // Skip threads authored by bots
        if (parentMessage.botId != null) {
            return
        }

        runSuspendCatching {
            if (slackWideThreadIngestionEnablementService.isWideSlackThreadIngestionEnabled(orgId = org.id)) {
                SlackSerializer.toJson(slackMessages)?.let { slackMessagesJson ->
                    summarizationEventEnqueueService.enqueueEvent(
                        event = SummarizationEvent.GenerateSlackThreadSummary(
                            slackChannelId = slackChannel.id,
                            slackMessagesJson = slackMessagesJson,
                        ),
                    )
                }
            }
        }.onFailure {
            LOGGER.errorAsync(it) { "Failed to enqueue summarization event for Slack." }
        }
    }

    suspend fun ingest(
        slackTeamId: SlackTeamId,
        pullRequests: List<ParsedPullRequest>,
        slackExternalChannelId: String,
        slackThreadTs: String,
        isPrivate: Boolean? = null,
    ): Thread? = withLoggingContextAsync(
        "slackExternalChannelId" to slackExternalChannelId,
        "slackThreadTs" to slackThreadTs,
    ) {
        val slackChannelInfo = suspendedTransaction {
            slackChannelStore.findBySlackExternalChannelId(
                trx = this,
                slackExternalChannelId = slackExternalChannelId,
                slackTeamId = slackTeamId,
            )
        }

        if (slackChannelInfo == null) {
            LOGGER.warnAsync { "Did not create thread for slack message because there was no associated slack channel" }
            return@withLoggingContextAsync null
        }

        val slackMessages = ingestMessages(
            slackTeam = slackChannelInfo.slackTeam.asDataModel(),
            slackExternalChannelId = slackExternalChannelId,
            slackThreadTs = slackThreadTs,
        )

        if (slackMessages.isEmpty()) {
            LOGGER.warnAsync { "Did not create thread for slack message because there was no associated slack messages" }
            return@withLoggingContextAsync null
        }

        val slackMessage = slackMessages.firstOrNull { it.isTopLevelMessage }
        if (slackMessage == null) {
            LOGGER.warnAsync(
                "firstMessageTs" to slackMessages.first().ts,
                "firstMessageThreadTs" to slackMessages.first().threadTs,
            ) {
                "Did not create thread for slack message because there could not retrieve first slack message"
            }
            return@withLoggingContextAsync null
        }

        ingest(
            org = slackChannelInfo.org.asDataModel(),
            installation = slackChannelInfo.installation.asDataModel(),
            slackTeam = slackChannelInfo.slackTeam.asDataModel(),
            slackChannel = slackChannelInfo.slackChannel.asDataModel(),
            pullRequests = pullRequests,
            slackMessage = slackMessage,
            slackMessages = slackMessages,
            isPrivate = isPrivate,
        )
    }

    suspend fun ingest(
        org: Org,
        installation: Installation,
        slackTeam: SlackTeam,
        slackChannel: SlackChannel,
        pullRequests: List<ParsedPullRequest>,
        slackMessage: Message,
        slackMessages: List<Message>,
        isPrivate: Boolean? = null,
    ): Thread? = withLoggingContextAsync(
        "slackMessageTs" to slackMessage.ts,
    ) {
        LOGGER.traceAsync { "Beginning slack channel message ingestion" }
        val thread = suspendedTransaction {
            slackThreadModelService.upsertThread(
                trx = this,
                org = org,
                installation = installation,
                slackTeam = slackTeam,
                pullRequests = pullRequests,
                slackChannel = slackChannel,
                slackMessage = slackMessage,
                isPrivate = isPrivate,
            )?.asDataModel()
        }

        if (thread == null) {
            LOGGER.warnAsync { "Did not create thread for slack message because it was not associated with a team member" }
            return@withLoggingContextAsync null
        }

        slackMessages.forEach { slackMessageReply ->
            if (!slackMessageReply.isTopLevelMessage) {
                suspendedTransaction {
                    slackMessageModelService.upsertMessage(
                        trx = this,
                        org = org,
                        thread = thread,
                        slackTeam = slackTeam,
                        slackChannel = slackChannel,
                        slackMessage = slackMessageReply,
                    )
                }
            }
        }

        slackThreadFinalizationService.finalizeThreadCreation(orgId = org.id, slackTeamId = slackTeam.id, threadId = thread.id)
        LOGGER.debugAsync { "Finished slack channel message ingestion" }
        thread
    }

    private suspend fun ingestMessages(
        slackTeam: SlackTeam,
        slackConversation: Conversation,
        slackMessage: Message,
        lock: RedisLock? = null,
    ): List<Message> = when (slackMessage.replyCount) {
        null -> listOf(slackMessage)

        else -> ingestMessages(
            slackTeam = slackTeam,
            slackExternalChannelId = slackConversation.id,
            slackThreadTs = slackMessage.threadTs,
            lock = lock,
        )
    }

    private suspend fun ingestMessages(
        slackTeam: SlackTeam,
        slackExternalChannelId: String,
        slackThreadTs: String,
        lock: RedisLock? = null,
    ): List<Message> {
        val slackMessages = mutableListOf<Message>()

        var cursor: String? = null
        do {
            val response = safelyGetConversationsReplies(
                slackTeam = slackTeam,
                slackExternalChannelId = slackExternalChannelId,
                slackThreadTs = slackThreadTs,
                cursor = cursor,
            )

            LOGGER.traceAsync(
                "slack.cursor" to cursor,
                "slack.response.isOk" to response.isOk,
                "slack.response.error" to response.error,
                "slack.response.isHasMore" to response.isHasMore,
                "slack.response.messages.size" to response.messages?.size,
            ) { "Received slack response for message paging" }

            response.messages?.let {
                slackMessages.addAll(it)
            }

            cursor = if (response.isHasMore) {
                response.responseMetadata.nextCursor
            } else {
                null
            }

            lock?.renew()
        } while (!cursor.isNullOrEmpty())

        return slackMessages
    }

    private fun triggerMessageDataIngestion(
        org: Org,
        slackTeam: SlackTeam,
        slackChannel: SlackChannel,
        slackMessage: Message,
        slackMessages: List<Message>,
    ) {
        runSuspendCatching {
            dataEventEnqueueService.enqueueSlackDataEvent(
                orgId = org.id,
                slackExternalTeamId = slackTeam.slackExternalTeamId,
                slackExternalChannelId = slackChannel.slackExternalChannelId,
                slackMessageTs = slackMessage.ts,
                slackMessages = slackMessages,
            )
        }.onFailure {
            LOGGER.errorSync(it) { "Failed to enqueue slack message data event " }
        }
    }

    /**
     * Performs a resilient `conversations.replies` request that automatically retries with the
     * attachment‑stripping fallback when Slack returns malformed JSON.
     *
     * Mirrors [SlackChannelMessagesIngestionService.safelyGetConversationsHistory] for thread
     * pagination.
     */
    private suspend fun safelyGetConversationsReplies(
        slackTeam: SlackTeam,
        slackExternalChannelId: String,
        slackThreadTs: String,
        cursor: String?,
        limit: Int = 200,
    ): ConversationsRepliesResponse {
        val token = slackTokenService.getSlackToken(
            slackTeamId = slackTeam.id,
            permissions = listOf(SlackPermission.CHANNELS_HISTORY), // add GROUPS_HISTORY dynamically if required upstream
        ).value

        // Primary + fallback handled centrally by SlackSafeMessagesFetcherService
        return SlackSafeMessagesFetcherService.fetch(
            call = {
                slackApiProvider.slackConversationApi.getConversationsReplies(
                    token = token,
                    channelId = slackExternalChannelId,
                    parentTs = slackThreadTs,
                    cursor = cursor,
                    limit = limit,
                )
            },
            fetchRaw = {
                fetchRawReplies(
                    token = token,
                    channelId = slackExternalChannelId,
                    parentTs = slackThreadTs,
                    cursor = cursor,
                    limit = limit,
                )
            },
        )
    }

    /**
     * Downloads the raw JSON payload for a single page of `conversations.replies`
     * **without** attempting to parse it.  Used by `SlackSafeMessagesFetcherService`
     * when the primary Slack-SDK call fails with malformed attachments.
     *
     * @param token     Slack OAuth bearer token.
     * @param channelId Channel ID that contains the thread.
     * @param parentTs  Thread’s root message timestamp (`ts`).
     * @param cursor    Pagination cursor if continuing a previous request.
     * @param limit     Max messages to request (Slack cap is 200).
     * @return The raw JSON string returned by Slack.
     */
    internal suspend fun fetchRawReplies(
        token: String,
        channelId: String,
        parentTs: String,
        cursor: String?,
        limit: Int,
    ): String {
        return HttpClientFactory.createHttpClient(orgIds = null) {
            expectSuccess = false // we want the raw body even on non-2xx
        }.use { client ->
            client.get("https://slack.com/api") {
                url { appendPathSegments("conversations.replies") }
                parameter("channel", channelId)
                parameter("ts", parentTs)
                parameter("limit", limit)
                cursor?.let { parameter("cursor", it) }
                headers.append("Authorization", "Bearer $token")
            }.body()
        }
    }
}
