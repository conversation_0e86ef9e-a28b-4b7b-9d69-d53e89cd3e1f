@file:Suppress("ktlint:nextchaptersoftware:no-nano-datetime")

package com.nextchaptersoftware.auth.ci

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.ci.config.CISecretsConfig
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.security.RSAKeyLoader
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.utils.date
import io.ktor.http.Url
import io.ktor.util.decodeBase64String
import java.util.UUID
import kotlin.time.Clock
import kotlin.time.Duration.Companion.days
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class CIJwtSigner(
    private val ciConfig: CIConfig,
    private val ciSecretsConfig: CISecretsConfig,
) {
    private val signingAlgorithm by lazy {
        runSuspendCatching {
            Algorithm.RSA256(
                null,
                RSAKeyLoader.createPrivateKey(ciSecretsConfig.ci.authTokenPrivateKey.value.decodeBase64String()),
            )
        }.onFailure {
            LOGGER.errorSync(it) { "Failed to create RSA256 algorithm" }
        }.getOrNull()
    }

    fun generateCIContextToken(
        identityId: UUID,
        orgId: UUID,
        username: String,
        avatarUrl: Url,
        repoFullName: String,
        providerName: String,
    ): String {
        val now = Clock.System.now()
        val expiry = now + 30.days
        val builder = JWT.create()
            .withAudience(Jwt.Audience.CIContext.value)
            .withIssuer(ciConfig.auth.tokenIssuer)
            .withSubject(identityId.toString())
            .withIssuedAt(now.date)
            .withNotBefore(now.date)
            .withExpiresAt(expiry.date)

        builder.withArrayClaim(
            Jwt.Claim.Orgs.value,
            listOf(orgId).map { it.toString() }.toTypedArray(),
        )

        // SCM specific claims
        builder.withClaim(Jwt.Claim.ScmProviderDisplayName.value, providerName)
        builder.withClaim(Jwt.Claim.ScmAvatarUrl.value, avatarUrl.asString)
        builder.withClaim(Jwt.Claim.ScmRepoFullName.value, repoFullName)
        builder.withClaim(Jwt.Claim.ScmUsername.value, username)

        return builder.sign(signingAlgorithm)
    }
}
