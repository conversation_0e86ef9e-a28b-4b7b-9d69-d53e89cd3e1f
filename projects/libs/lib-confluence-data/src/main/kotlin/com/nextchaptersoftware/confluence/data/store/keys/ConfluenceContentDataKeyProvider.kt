package com.nextchaptersoftware.confluence.data.store.keys

import com.nextchaptersoftware.db.models.ConfluenceContentId
import com.nextchaptersoftware.db.models.ConfluenceSpaceId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.integration.data.store.keys.IntegrationDataKeyProvider

class ConfluenceContentDataKeyProvider : IntegrationDataKeyProvider.ConfluenceDataKeyProvider() {
    fun provide(
        orgId: OrgId,
        installationId: InstallationId,
        confluenceSpaceId: ConfluenceSpaceId,
        confluenceContentId: ConfluenceContentId,
    ): String {
        val pathList = listOfNotNull(
            orgId,
            installationId,
            confluenceSpaceId,
            confluenceContentId,
        )
            .map { it.toString() }

        return provide(*pathList.toTypedArray())
    }
}
