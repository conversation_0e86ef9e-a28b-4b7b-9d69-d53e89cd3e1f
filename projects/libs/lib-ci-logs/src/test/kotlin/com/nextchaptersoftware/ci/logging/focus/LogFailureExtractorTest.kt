package com.nextchaptersoftware.ci.logging.focus

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class LogFailureExtractorTest {

    @Test
    fun `extractFailedChunks -- context lines`() {
        val originalLines = """
            |line 0
            |line 1
            |line 2
            |line 3
            |line 4 Some test FAILED
            |line 5
            """.trimMargin().lines()

        LogFailureExtractor(contextLines = 0).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(4..4, setOf(" FAILED$")),
            )
        }

        LogFailureExtractor(contextLines = 2).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(2..5, setOf(" FAILED$")),
            )
        }

        LogFailureExtractor(contextLines = 100).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(0..5, setOf(" FAILED$")),
            )
        }
    }

    @Test
    fun `extractFailedChunks -- various failures`() {
        val originalLines = """
            |line 0
            |line 1
            |line 2 .............Failed
            |line 3
            |FAIL: //services/zerp/backend:zerp-backend.pyright_3_12
            |line 5
            |=================================== FAILURES ===================================
            |line 7
            |FAILED services//test.py::Tester::test_handle - AttributeError: 'NoneType' object has no attribute 'lease_token'"
            |line 9
            |line 10
            |=== FAIL: services/db TestWithTx/golden_pathIDs (0.00s)
            |line 12
            |Failure/Error:
            |line 14
            |java/com/tecton/configuration_check/ConfigurationCheck.kt:3:51: error: unresolved reference: CheckId
            |line 16
            |#25 7.248 [ ERROR ]  TypeScript: app/javascript/components/custo/bc-custo-themes/bc-custo-themes.spec.ts:136:69
            |line 18
            """.trimMargin().lines()

        LogFailureExtractor(contextLines = 0, mergeThreshold = 0).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(2..2, setOf("\\.{5,}Failed$")),
                FailureChunk(4..4, setOf("^FAIL: ")),
                FailureChunk(6..6, setOf("^==+ FAILURES ==+$")),
                FailureChunk(8..8, setOf("^FAILED ")),
                FailureChunk(11..11, setOf("^=== FAIL: ")),
                FailureChunk(13..13, setOf("^Failure/Error:")),
                FailureChunk(15..15, setOf(":\\d+:\\d+:\\s+error:\\s+")),
                FailureChunk(17..17, setOf(""" \[ ERROR \] """)),
            )
        }
    }

    @Test
    fun `extractFailedChunks -- merge failure chunks`() {
        val originalLines = """
            |line 0
            |FAIL some test
            |line 2
            |line 3
            |Some test FAILED
            |line 5
            """.trimMargin().lines()

        LogFailureExtractor(contextLines = 0, mergeThreshold = 1).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(1..1, setOf("^\\s*FAIL ")),
                FailureChunk(4..4, setOf(" FAILED$")),
            )
        }

        LogFailureExtractor(contextLines = 0, mergeThreshold = 2).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(1..1, setOf("^\\s*FAIL ")),
                FailureChunk(4..4, setOf(" FAILED$")),
            )
        }

        LogFailureExtractor(contextLines = 0, mergeThreshold = 3).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(1..4, setOf("^\\s*FAIL ", " FAILED$")),
            )
        }

        LogFailureExtractor(contextLines = 1, mergeThreshold = 0).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(0..2, setOf("^\\s*FAIL ")),
                FailureChunk(3..5, setOf(" FAILED$")),
            )
        }

        LogFailureExtractor(contextLines = 1, mergeThreshold = 1).extractFailedChunks(originalLines).also { chunks ->
            assertThat(chunks).containsExactly(
                FailureChunk(0..5, setOf("^\\s*FAIL ", " FAILED$")),
            )
        }
    }

    @Test
    fun `extractFailedSnippets -- merge failure chunks`() {
        val originalLines = """
            |line 1
            |FAIL some test
            |line 3
            |line 4
            |Some test FAILED
            |line 6
            """.trimMargin().lines()

        LogFailureExtractor(contextLines = 0, mergeThreshold = 1).extractFailedSnippets(originalLines).also { snippetLines ->
            assertThat(snippetLines).containsExactly(
                "=== LOG SNIPPET (lines 2–2) ===",
                "FAIL some test",
                "=== END OF LOG SNIPPET ===",
                "=== LOG SNIPPET (lines 5–5) ===",
                "Some test FAILED",
                "=== END OF LOG SNIPPET ===",
            )
        }

        LogFailureExtractor(contextLines = 0, mergeThreshold = 3).extractFailedSnippets(originalLines).also { snippetLines ->
            assertThat(snippetLines).containsExactly(
                "=== LOG SNIPPET (lines 2–5) ===",
                "FAIL some test",
                "line 3",
                "line 4",
                "Some test FAILED",
                "=== END OF LOG SNIPPET ===",
            )
        }

        LogFailureExtractor(contextLines = 1, mergeThreshold = 0).extractFailedSnippets(originalLines).also { snippetLines ->
            assertThat(snippetLines).containsExactly(
                "=== LOG SNIPPET (lines 1–3) ===",
                "line 1",
                "FAIL some test",
                "line 3",
                "=== END OF LOG SNIPPET ===",
                "=== LOG SNIPPET (lines 4–6) ===",
                "line 4",
                "Some test FAILED",
                "line 6",
                "=== END OF LOG SNIPPET ===",
            )
        }

        LogFailureExtractor(contextLines = 1, mergeThreshold = 1).extractFailedSnippets(originalLines).also { snippetLines ->
            assertThat(snippetLines).containsExactly(
                "=== LOG SNIPPET (lines 1–6) ===",
                "line 1",
                "FAIL some test",
                "line 3",
                "line 4",
                "Some test FAILED",
                "line 6",
                "=== END OF LOG SNIPPET ===",
            )
        }
    }
}
