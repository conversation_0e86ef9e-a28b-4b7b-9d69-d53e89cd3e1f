=== LOG SNI<PERSON>ET (lines 2394–2501) ===
> Task :projects:libs:lib-intercom:compileTestKotlin NO-SOURCE
> Task :projects:libs:lib-intercom:compileTestJava NO-SOURCE
> Task :projects:libs:lib-intercom:testClasses UP-TO-DATE
> Task :projects:libs:lib-intercom:test NO-SOURCE
> Task :projects:libs:lib-intercom:check UP-TO-DATE
> Task :projects:libs:lib-intercom:build
> Task :projects:libs:lib-event-queue:jar
> Task :projects:libs:lib-event-queue:assemble
> Task :projects:clients:client-cohere:compileTestKotlin FROM-CACHE
> Task :projects:clients:client-cohere:compileTestJava NO-SOURCE
> Task :projects:clients:client-cohere:testClasses UP-TO-DATE
> Task :projects:libs:lib-event-queue:compileTestKotlin FROM-CACHE
> Task :projects:libs:lib-event-queue:compileTestJava NO-SOURCE
> Task :projects:libs:lib-event-queue:testClasses UP-TO-DATE
> Task :projects:libs:lib-ktor:compileTestKotlin FROM-CACHE
> Task :projects:clients:client-slack:compileTestKotlin FROM-CACHE
> Task :projects:libs:lib-emoji:test FROM-CACHE
> Task :projects:clients:client-slack:compileTestJava NO-SOURCE
> Task :projects:clients:client-slack:processTestResources NO-SOURCE
> Task :projects:clients:client-slack:testClasses UP-TO-DATE
> Task :projects:libs:lib-emoji:check UP-TO-DATE
> Task :projects:libs:lib-emoji:build
> Task :projects:libs:lib-ktor:compileTestJava NO-SOURCE
> Task :projects:libs:lib-ktor:processTestResources NO-SOURCE
> Task :projects:libs:lib-ktor:testClasses UP-TO-DATE
> Task :projects:libs:lib-job:compileKotlin FROM-CACHE
> Task :projects:libs:lib-job:compileJava NO-SOURCE
> Task :projects:libs:lib-job:classes UP-TO-DATE
> Task :projects:libs:lib-job:jar
> Task :projects:libs:lib-job:assemble
> Task :projects:libs:lib-job:compileTestKotlin NO-SOURCE
> Task :projects:libs:lib-job:compileTestJava NO-SOURCE
> Task :projects:libs:lib-job:testClasses UP-TO-DATE
> Task :projects:libs:lib-job:test NO-SOURCE
> Task :projects:libs:lib-job:check UP-TO-DATE
> Task :projects:libs:lib-job:build
> Task :projects:libs:lib-pdf:compileTestJava NO-SOURCE
> Task :projects:libs:lib-pdf:processTestResources
> Task :projects:libs:lib-pdf:testClasses
> Task :projects:clients:client-pinecone:compileKotlin FROM-CACHE
> Task :projects:clients:client-pinecone:compileJava NO-SOURCE
> Task :projects:clients:client-pinecone:classes UP-TO-DATE
> Task :projects:clients:client-pinecone:jar
> Task :projects:clients:client-pinecone:assemble
> Task :projects:clients:client-pinecone:compileTestKotlin FROM-CACHE
> Task :projects:clients:client-pinecone:compileTestJava NO-SOURCE
> Task :projects:clients:client-pinecone:testClasses UP-TO-DATE
> Task :projects:libs:lib-api-auth:detekt
> Task :projects:libs:lib-api-auth:lintKotlinMain FROM-CACHE

> Task :projects:libs:lib-api-integration-extension:detekt FAILED
/actions-runner/_work/***/***/projects/libs/lib-api-integration-extension/src/main/kotlin/com/nextchaptersoftware/api/integration/extension/services/web/WebIngestionConfigurationService.kt:66:9: Function parameter `orgMemberId` is unused. [UnusedPrivateMember]

> Task :projects:models:compileKotlin FROM-CACHE
> Task :projects:models:compileJava NO-SOURCE
> Task :projects:models:classes UP-TO-DATE

> Task :projects:libs:lib-api:detekt FAILED
/actions-runner/_work/***/***/projects/libs/lib-api/src/main/kotlin/com/nextchaptersoftware/api/services/PersonUpsertService.kt:119:85: Function parameter `provider` is unused. [UnusedPrivateMember]

> Task :custom-ktlint-rules:test FROM-CACHE
> Task :projects:models:jar

> Task :projects:libs:lib-compress:test

CompressionBase64Test > test compression and decompression is commutative() PASSED

CompressionBase64Test > test compression() PASSED

CompressionTest > compression and decompression is commutative() PASSED

CompressionTest > compression() PASSED

> Task :projects:libs:lib-common:test

HostnameTest > invalid() PASSED

HostnameTest > sanitizes() PASSED

UnblockedPRCommentSignatureTest > withSignatureStripped() PASSED

HostnameTest > valid() PASSED

RoundRobinIterableTest > should handle concurrent access correctly() PASSED

HostnameTest > invalid characters() PASSED

RoundRobinIterableTest > test single element cycle() PASSED

UnblockedPRCommentSignatureTest > withSignatureStripped for a message with no signature() PASSED

RoundRobinIterableTest > should return elements in round-robin order() PASSED

RoundRobinIterableTest > peek should return the current element without advancing() PASSED

UnblockedPRCommentSignatureTest > withSignatureStripped for a message with no signature -- legacy() PASSED

UnblockedPRCommentSignatureTest > withEditSignature() PASSED

UnblockedPRCommentSignatureTest > withEditSignature + withSignatureStripped() PASSED

UnblockedPRCommentSignatureTest > withCreateSignature + withSignatureStripped() PASSED

UnblockedPRCommentSignatureTest > withCreateSignature() PASSED

FlowExtensionsTest > takeOnNotNull() PASSED
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended

=== END OF LOG SNIPPET ===
=== LOG SNIPPET (lines 3628–3765) ===
PineconeUpsertApiTest > test upsert() > test upsert() PASSED

> Task :projects:libs:lib-cas:test

RedisCASTest > store and retrieve() PASSED

RedisCASTest > retrieve fails when expired() PASSED

> Task :projects:clients:client-activemq:test

ActiveMQPriorityTest > test priority() PASSED

ActiveMQGroupTest > test message group affinity() PASSED

> Task :projects:clients:client-openai:test

OpenAICompletionsApiTest > test completion api with omni() PASSED

OpenAICompletionsApiTest > test chatCompletions flow with o1() PASSED

AzureOpenAICompletionsApiTest > test chat completion api for gpt4 omni() PASSED

OpenAICompletionsApiTest > test completion api with o1() PASSED

> Task :projects:clients:client-activemq:test

ActiveMQPriorityStressTest > stress test priority with selector() PASSED

ActiveMQPriorityStressTest > stress test priority without selector() PASSED

> Task :projects:clients:client-pinecone:test

PineconeQueryApiTest > test query with metadata retrieval() > test query with metadata retrieval() PASSED

PineconeServerlessApiTest > test upsert and list() > test upsert and list() PASSED

PineconeQueryApiTest > test basic query() > test basic query() PASSED

PineconeDeleteApiTest > test deletion() > test deletion() PASSED

PineconeQueryApiTest > test query with values retrieval() > test query with values retrieval() PASSED

PineconeQueryApiTest > test query with empty sparse vector() > test query with empty sparse vector() PASSED

PineconeQueryApiTest > test query for undefined and boolean values() > test query for undefined and boolean values() PASSED

PineconeQueryApiTest > test query against elements with null metadata fields() > test query against elements with null metadata fields() PASSED

PineconeQueryApiTest > test filtered query() > test filtered query() PASSED

FAILURE: Build completed with 2 failures.

1: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':projects:libs:lib-api-integration-extension:detekt'.
> Analysis failed with 1 weighted issues.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.
==============================================================================

2: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':projects:libs:lib-api:detekt'.
> Analysis failed with 1 weighted issues.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.
==============================================================================

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.10.2/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.
665 actionable tasks: 174 executed, 491 from cache

BUILD FAILED in 3m 5s
##[endgroup]
##[error]Child_process exited with error code 1
##[group]Run mikepenz/action-junit-report@v5.2.0
with:
  check_retries: true
  detailed_summary: true
  flaky_summary: true
  report_paths: **/build/test-results/test/TEST-*.xml
  job_name: services-build
  update_check: true
  token: ***
  annotate_only: false
  check_annotations: true
  check_name: JUnit Test Report
  fail_on_failure: false
  fail_on_parse_error: false
  require_tests: false
  require_passed_tests: false
  include_passed: false
  bread_crumb_delimiter: /
  transformers: []
  job_summary: true
  group_suite: false
  comment: false
  updateComment: true
  annotate_notice: false
  follow_symlink: false
  truncate_stack_traces: true
  resolve_ignore_classname: false
env:
  AWS_REGION: us-west-2
  OUTPUT_ARTIFACT_NAME: built-artifacts-62060
  BUILD_ARTIFACTS_DIR: ./build/libs
  TOUCH: Dummy value to force workflow rerun
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
  GH_CACHE_BUCKET: ***-gh-actions-s3-cache-sec-ops-us-west-2
  CI: true
  CI_PR_TITLE: Remove segment
  CI_TITLE: 
  GH_HOME: /root
  AWS_DEFAULT_REGION: us-west-2
  AWS_ACCESS_KEY_ID: ***
  AWS_SECRET_ACCESS_KEY: ***
  AWS_SESSION_TOKEN: ***
  JAVA_HOME: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/23.0.2-7/x64
  JAVA_HOME_23_X64: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/23.0.2-7/x64
  GRADLE_ARGS: --no-daemon build -x :common:generateProto -x :npmInstall
  GRADLE_ACTION_ID: gradle/actions/setup-gradle
  GRADLE_BUILD_ACTION_SETUP_COMPLETED: true
  GRADLE_BUILD_ACTION_CACHE_RESTORED: true
  DEVELOCITY_INJECTION_INIT_SCRIPT_NAME: gradle-actions.inject-develocity.init.gradle
  DEVELOCITY_AUTO_INJECTION_CUSTOM_VALUE: gradle-actions
=== END OF LOG SNIPPET ===
