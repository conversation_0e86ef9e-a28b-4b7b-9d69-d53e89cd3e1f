=== LOG SNIPPET (lines 17–68) ===
Actions: write
Contents: read
Metadata: read
##[endgroup]
Secret source: Actions
Prepare workflow directory
Prepare all required actions
Getting action download info
Download action repository 'ksivamuthu/actions-setup-gh-cli@v3' (SHA:c78dbed4be2f8d6133a14a9a597ee12fd4ed5c93)
Complete job name: delete-offline-runners
##[group]Run ksivamuthu/actions-setup-gh-cli@v3
with:
  version: 2.24.3
##[endgroup]
Installing gh cli in self hosted runner
Downloading gh cli from https://github.com/cli/cli/releases/download/v2.24.3/gh_2.24.3_linux_amd64.tar.gz
[command]/usr/bin/tar xz --warning=no-unknown-keyword --overwrite -C /home/<USER>/work/_temp/10ac98d1-8d75-4e6f-bbff-db1dfa9613f7 -f gh_tar
gh cli installed successfully
##[group]Run sudo apt-get install jq
sudo apt-get install jq
shell: /usr/bin/bash -e {0}
##[endgroup]
Reading package lists...
Building dependency tree...
Reading state information...
jq is already the newest version (1.7.1-3build1).
0 upgraded, 0 newly installed, 0 to remove and 12 not upgraded.
##[group]Run echo "Fetching runners for repository: $REPO_NAME..."
echo "Fetching runners for repository: $REPO_NAME..."
RUNNERS_JSON=$(gh api --paginate -H "Accept: application/vnd.github.v3+json" /repos/$REPO_NAME/actions/runners)

RUNNER_IDS=$(echo "$RUNNERS_JSON" | jq -r '.runners[] | select((.status=="offline" or .status=="online") and .busy==false) | .id')

if [[ -z "$RUNNER_IDS" ]]; then
    echo "No offline runners found in $REPO_NAME."
    exit 0
fi

for ID in $RUNNER_IDS; do
    echo "Deleting offline runner with ID: $ID from repository: $REPO_NAME..."
    gh api -X DELETE -H "Accept: application/vnd.github.v3+json" /repos/$REPO_NAME/actions/runners/$ID
    echo "Deleted runner ID: $ID"
done
shell: /usr/bin/bash -e {0}
env:
  REPO_NAME: NextChapterSoftware/unblocked
  GH_TOKEN: ***
##[endgroup]
Fetching runners for repository: NextChapterSoftware/unblocked...
gh: Resource not accessible by integration (HTTP 403)
##[error]Process completed with exit code 1.
Cleaning up orphan processes
=== END OF LOG SNIPPET ===
