=== LOG SNIPPET (lines 452–571) ===
C<PERSON> restored successfully
Restored dependencies with key gradle-dependencies-v1-c90fbeb2a26416bdd8f81474ac5f50ef to /root/.gradle/caches/modules-*/files-*/*/*/*/*
<PERSON><PERSON> restored successfully
Restored transforms with key gradle-transforms-v1-7104623353fe76c3e198759f17350743 to /root/.gradle/caches/transforms-4/*/
/root/.gradle/caches/*/transforms/*/
##[endgroup]
##[group]Run npm run build:prod --product_number=22262 --product_version=0.0.22262
npm run build:prod --product_number=22262 --product_version=0.0.22262
shell: /usr/bin/bash -e {0}
env:
  AWS_REGION: us-west-2
  OUTPUT_ARTIFACT_NAME: built-artifacts-22262
  BUILD_ARTIFACTS_DIR: ./web/dist
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
  GH_CACHE_BUCKET: unblocked-gh-actions-s3-cache-sec-ops-us-west-2
  AWS_DEFAULT_REGION: us-west-2
  AWS_ACCESS_KEY_ID: ***
  AWS_SECRET_ACCESS_KEY: ***
  AWS_SESSION_TOKEN: ***
  JAVA_HOME: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/23.0.2-7/x64
  JAVA_HOME_23_X64: /actions-runner/_work/_tool/Java_Temurin-Hotspot_jdk/23.0.2-7/x64
  GRADLE_ACTION_ID: gradle/actions/setup-gradle
  GRADLE_BUILD_ACTION_SETUP_COMPLETED: true
  GRADLE_BUILD_ACTION_CACHE_RESTORED: true
  DEVELOCITY_INJECTION_INIT_SCRIPT_NAME: gradle-actions.inject-develocity.init.gradle
  DEVELOCITY_AUTO_INJECTION_CUSTOM_VALUE: gradle-actions
  GITHUB_DEPENDENCY_GRAPH_ENABLED: false
##[endgroup]

> build:prod
> npm run generate-api && webpack --config webpack.prod.js --env PRODUCT_NUMBER=$npm_config_product_number --env PRODUCT_VERSION=$npm_config_product_version

> generate-api
> make -C ../api/. generate-api

make: Entering directory '/actions-runner/_work/unblocked/unblocked/api'
cd /actions-runner/_work/unblocked/unblocked/api/.. && ./gradlew :api:openApiGenerateShared :api:openApiExtraGenerateShare :common:generateProto
Starting a Gradle Daemon (subsequent builds will be faster)
> Task :buildSrc:checkKotlinGradlePluginConfigurationErrors
> Task :buildSrc:pluginDescriptors
> Task :buildSrc:processResources

> Task :buildSrc:compileKotlin
Could not load entry 357e1c6e98963884ed8040f34fff63ec from remote build cache: Loading entry from 'http://gradlecache.secops.getunblocked.com/cache/357e1c6e98963884ed8040f34fff63ec' response status 403: Forbidden

> Task :buildSrc:compileJava NO-SOURCE
> Task :buildSrc:compileGroovy NO-SOURCE
> Task :buildSrc:classes
> Task :buildSrc:jar

FAILURE: Build failed with an exception.

* Where:
Build file '/actions-runner/_work/unblocked/unblocked/projects/libs/lib-api-auth/build.gradle.kts' line: 22

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

* What went wrong:
Project with path ':projects:libs:lib-segment' could not be found in project ':projects:libs:lib-api-auth'.

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
For more on this, please refer to https://docs.gradle.org/8.10.2/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.
> Get more help at https://help.gradle.org.

BUILD FAILED in 29s
5 actionable tasks: 5 executed
The remote build cache was disabled during the build due to errors.
make: *** [Makefile:12: generate-api] Error 1
make: Leaving directory '/actions-runner/_work/unblocked/unblocked/api'
npm error Lifecycle script `generate-api` failed with error:
npm error code 2
npm error path /actions-runner/_work/unblocked/unblocked/web
npm error workspace web-prototype
npm error location /actions-runner/_work/unblocked/unblocked/web
npm error command failed
npm error command sh -c make -C ../api/. generate-api
npm error Lifecycle script `build:prod` failed with error:
npm error code 2
npm error path /actions-runner/_work/unblocked/unblocked/web
npm error workspace web-prototype
npm error location /actions-runner/_work/unblocked/unblocked/web
npm error command failed
npm error command sh -c npm run generate-api && webpack --config webpack.prod.js --env PRODUCT_NUMBER=$npm_config_product_number --env PRODUCT_VERSION=$npm_config_product_version
##[error]Process completed with exit code 2.
Post job cleanup.
In post-action step
Cache is read-only: will not save state for use in subsequent builds.
Generating Job Summary
Completed post-action step
Post job cleanup.
[command]/usr/bin/git version
git version 2.34.1
Temporarily overriding HOME='/actions-runner/_work/_temp/7047c277-3bf8-41e9-8024-238d17260dad' before making global git config changes
Adding repository directory to the temporary git global config as a safe directory
[command]/usr/bin/git config --global --add safe.directory /actions-runner/_work/unblocked/unblocked
[command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
[command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
Entering 'shared/clientAssets'
[command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
http.https://github.com/.extraheader
[command]/usr/bin/git config --local --unset-all http.https://github.com/.extraheader
[command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
Entering 'projects/clients/client-scm/src/main/resources/github-graphql/schema'
http.https://github.com/.extraheader
Entering 'shared/clientAssets'
http.https://github.com/.extraheader
A job completed hook has been configured by the self-hosted runner administrator
##[group]Run '/shutdown_script.sh'
shell: /usr/bin/bash --noprofile --norc -e -o pipefail {0}
##[endgroup]
Shutdown scheduled for Thu 2025-02-27 23:55:02 UTC, use 'shutdown -c' to cancel.
Cleaning up orphan processes
Terminate orphan process: pid (3544) (java)
Terminate orphan process: pid (3668) (java)
=== END OF LOG SNIPPET ===
