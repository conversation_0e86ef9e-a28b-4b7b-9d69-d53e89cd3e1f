=== LOG SNIPPET (lines 419–519) ===
##[group]Run # Not all versions support setting CARGO_REGISTRIES_CRATES_IO_PROTOCOL
# Not all versions support setting CARGO_REGISTRIES_CRATES_IO_PROTOCOL
# On versions 1.66, 1.67, and 1.68.0-nightly the value "sparse" is still unstable.
# https://github.com/dtolnay/rust-toolchain/pull/69#discussion_r1107268108
# If we detect an incompatible value, set it to "git" which is always supported.
if [[ "rustc 1.86.0 (05f9846f8 2025-03-31)" =~ ^rustc\ (1\.6[67]\.|1\.68\.0-nightly) && "${CARGO_REGISTRIES_CRATES_IO_PROTOCOL}" == "sparse" ]]; then
  echo "Downgrade cargo registry protocol to git"
  echo "CARGO_REGISTRIES_CRATES_IO_PROTOCOL=git" >> $GITHUB_ENV
fi
shell: /usr/bin/bash --noprofile --norc -e -o pipefail {0}
env:
  pythonLocation: /opt/hostedtoolcache/Python/3.8.18/x64
  PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib/pkgconfig
  Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib
  CARGO_INCREMENTAL: 0
  CARGO_PROFILE_DEV_DEBUG: 0
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: short
  RUSTFLAGS: -D warnings
  CARGO_UNSTABLE_SPARSE_REGISTRY: true
  CARGO_REGISTRIES_CRATES_IO_PROTOCOL: sparse
##[endgroup]
##[group]Run Swatinem/rust-cache@v2
with:
  cache-on-failure: true
  cache-bin: true
  prefix-key: v0-rust
  cache-targets: true
  cache-all-crates: false
  save-if: true
  cache-provider: github
  lookup-only: false
env:
  pythonLocation: /opt/hostedtoolcache/Python/3.8.18/x64
  PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib/pkgconfig
  Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib
  CARGO_INCREMENTAL: 0
  CARGO_PROFILE_DEV_DEBUG: 0
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: short
  RUSTFLAGS: -D warnings
  CARGO_UNSTABLE_SPARSE_REGISTRY: true
  CARGO_REGISTRIES_CRATES_IO_PROTOCOL: sparse
##[endgroup]
Error: The process '/home/<USER>/.cargo/bin/cargo' failed with exit code 101
    at ExecState._setResult (/home/<USER>/work/_actions/Swatinem/rust-cache/v2/dist/restore/index.js:9728:25)
    at ExecState.CheckComplete (/home/<USER>/work/_actions/Swatinem/rust-cache/v2/dist/restore/index.js:9711:18)
    at ChildProcess.<anonymous> (/home/<USER>/work/_actions/Swatinem/rust-cache/v2/dist/restore/index.js:9605:27)
    at ChildProcess.emit (node:events:524:28)
    at maybeClose (node:internal/child_process:1104:16)
    at ChildProcess._handle.onexit (node:internal/child_process:304:5) {
  commandFailed: {
    command: 'cargo metadata --all-features --format-version 1 --no-deps',
    stderr: '\x1B[1m\x1B[31merror\x1B[0m\x1B[1m:\x1B[0m could not find `Cargo.toml` in `/home/<USER>/work/cloud_infra_stacks/cloud_infra_stacks` or any parent directory\n'
  }
}
##[group]Cache Configuration
Cache Provider:
    github
Workspaces:
    /home/<USER>/work/cloud_infra_stacks/cloud_infra_stacks
Cache Paths:
    /home/<USER>/.cargo/bin
    /home/<USER>/.cargo/.crates.toml
    /home/<USER>/.cargo/.crates2.json
    /home/<USER>/.cargo/registry
    /home/<USER>/.cargo/git
    /home/<USER>/work/cloud_infra_stacks/cloud_infra_stacks/target
Restore Key:
    v0-rust-pre-commit-Linux-x64-a4d893e0
Cache Key:
    v0-rust-pre-commit-Linux-x64-a4d893e0-da39a3ee
.. Prefix:
  - v0-rust-pre-commit-Linux-x64
.. Environment considered:
  - Rust Version: 1.86.0 x86_64-unknown-linux-gnu (05f9846f893b09a1be1fc8560e33fc3c815cfecb)
  - CARGO_INCREMENTAL
  - CARGO_PROFILE_DEV_DEBUG
  - CARGO_REGISTRIES_CRATES_IO_PROTOCOL
  - CARGO_TERM_COLOR
  - CARGO_UNSTABLE_SPARSE_REGISTRY
  - RUST_BACKTRACE
  - RUSTFLAGS
.. Lockfiles considered:
##[endgroup]

... Restoring cache ...
Cache hit for: v0-rust-pre-commit-Linux-x64-a4d893e0-da39a3ee
Received 529 of 529 (100.0%), 0.0 MBs/sec
Cache Size: ~0 MB (529 B)
[command]/usr/bin/tar -xf /home/<USER>/work/_temp/66834355-70b1-441f-941f-1b90ef8d5277/cache.tzst -P -C /home/<USER>/work/cloud_infra_stacks/cloud_infra_stacks --use-compress-program unzstd
Cache restored successfully
Restored from cache key "v0-rust-pre-commit-Linux-x64-a4d893e0-da39a3ee" full match: true.
##[group]Run pre-commit/action@v3.0.1
with:
=== END OF LOG SNIPPET ===
=== LOG SNIPPET (lines 626–726) ===
  LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib
  CARGO_INCREMENTAL: 0
  CARGO_PROFILE_DEV_DEBUG: 0
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: short
  RUSTFLAGS: -D warnings
  CARGO_UNSTABLE_SPARSE_REGISTRY: true
  CARGO_REGISTRIES_CRATES_IO_PROTOCOL: sparse
  CACHE_ON_FAILURE: true
  SKIP: no-commit-to-branch
  PIP_INDEX_URL: ***artifactory.platform.flyzipline.com/artifactory/api/pypi/pypi/simple
##[endgroup]
Cache hit for: pre-commit-3|/opt/hostedtoolcache/Python/3.8.18/x64|e9727170b9106e7330fdac4a9717c9b673f43b34bf0f1e10cd6462626f7a00e7
Received 32554388 of 32554388 (100.0%), 47.2 MBs/sec
Cache Size: ~31 MB (32554388 B)
[command]/usr/bin/tar -xf /home/<USER>/work/_temp/598a0644-6a50-42bd-b2b8-a2343c44e8f4/cache.tzst -P -C /home/<USER>/work/cloud_infra_stacks/cloud_infra_stacks --use-compress-program unzstd
Cache restored successfully
Cache restored from key: pre-commit-3|/opt/hostedtoolcache/Python/3.8.18/x64|e9727170b9106e7330fdac4a9717c9b673f43b34bf0f1e10cd6462626f7a00e7
##[group]Run pre-commit run --show-diff-on-failure --color=always --all-files
pre-commit run --show-diff-on-failure --color=always --all-files
shell: /usr/bin/bash --noprofile --norc -e -o pipefail {0}
env:
  pythonLocation: /opt/hostedtoolcache/Python/3.8.18/x64
  PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib/pkgconfig
  Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.8.18/x64
  LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.8.18/x64/lib
  CARGO_INCREMENTAL: 0
  CARGO_PROFILE_DEV_DEBUG: 0
  CARGO_TERM_COLOR: always
  RUST_BACKTRACE: short
  RUSTFLAGS: -D warnings
  CARGO_UNSTABLE_SPARSE_REGISTRY: true
  CARGO_REGISTRIES_CRATES_IO_PROTOCOL: sparse
  CACHE_ON_FAILURE: true
  SKIP: no-commit-to-branch
  PIP_INDEX_URL: ***artifactory.platform.flyzipline.com/artifactory/api/pypi/pypi/simple
##[endgroup]
autoflake................................................................Passed
Lint Dockerfiles.........................................................Passed
check for added large files..............................................Passed
check yaml...............................................................Passed
fix end of files.........................................................Passed
trim trailing whitespace.................................................Passed
black....................................................................Passed
isort....................................................................Passed
yamllint.................................................................Passed
Detect secrets...........................................................Passed
go-fmt...................................................................Passed
go-mod-tidy..............................................................Failed
- hook id: go-mod-tidy
- files were modified by this hook

go: downloading k8s.io/api v0.30.3
go: downloading k8s.io/apimachinery v0.30.3
go: downloading k8s.io/klog/v2 v2.130.1
go: downloading github.com/prometheus/client_golang v1.19.1
go: downloading github.com/gogo/protobuf v1.3.2
go: downloading github.com/google/gofuzz v1.2.0
go: downloading sigs.k8s.io/yaml v1.3.0
go: downloading github.com/google/go-cmp v0.6.0
go: downloading github.com/spf13/pflag v1.0.5
go: downloading sigs.k8s.io/structured-merge-diff/v4 v4.4.1
go: downloading github.com/stretchr/testify v1.8.4
go: downloading github.com/go-logr/logr v1.4.1
go: downloading github.com/prometheus/client_model v0.5.0
go: downloading github.com/prometheus/common v0.48.0
go: downloading google.golang.org/protobuf v1.33.0
go: downloading gopkg.in/inf.v0 v0.9.1
go: downloading k8s.io/utils v0.0.0-20230726121419-3b25d923346b
go: downloading sigs.k8s.io/json v0.0.0-20221116044647-bc3834ca7abd
go: downloading gopkg.in/yaml.v2 v2.4.0
go: downloading github.com/davecgh/go-spew v1.1.1
go: downloading github.com/json-iterator/go v1.1.12
go: downloading github.com/pmezard/go-difflib v1.0.0
go: downloading gopkg.in/yaml.v3 v3.0.1
go: downloading github.com/beorn7/perks v1.0.1
go: downloading github.com/cespare/xxhash/v2 v2.2.0
go: downloading github.com/prometheus/procfs v0.12.0
go: downloading golang.org/x/sys v0.30.0
go: downloading golang.org/x/net v0.36.0
go: downloading gopkg.in/check.v1 v1.0.0-20201130134442-10cb98267c6c
go: downloading github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd
go: downloading github.com/modern-go/reflect2 v1.0.2
go: downloading github.com/kr/pretty v0.3.1
go: downloading golang.org/x/text v0.22.0
go: downloading github.com/kr/text v0.2.0
go: downloading github.com/rogpeppe/go-internal v1.10.0
go: downloading k8s.io/api v0.27.4
go: downloading k8s.io/apimachinery v0.27.4
go: downloading k8s.io/client-go v0.27.4
go: downloading github.com/google/gofuzz v1.1.0
go: downloading k8s.io/utils v0.0.0-20230209194617-a36077c30491
go: downloading github.com/google/go-cmp v0.5.9
go: downloading golang.org/x/net v0.33.0
go: downloading k8s.io/klog/v2 v2.90.1
go: downloading github.com/stretchr/testify v1.8.1
go: downloading sigs.k8s.io/structured-merge-diff/v4 v4.2.3
go: downloading golang.org/x/term v0.27.0
go: downloading golang.org/x/oauth2 v0.0.0-20220223155221-ee480838109b
=== END OF LOG SNIPPET ===
=== LOG SNIPPET (lines 763–863) ===
go: downloading github.com/prometheus/common v0.44.0
go: downloading github.com/prometheus/procfs v0.11.1
go: downloading github.com/stretchr/objx v0.5.0
go: downloading github.com/BurntSushi/toml v1.1.0
go: downloading github.com/cenkalti/backoff/v4 v4.1.3
go: downloading github.com/go-jose/go-jose/v3 v3.0.4
go: downloading github.com/kelseyhightower/envconfig v1.4.0
go: downloading github.com/patrickmn/go-cache v0.0.0-20180815053127-5633e0862627
go: downloading github.com/matttproud/golang_protobuf_extensions v1.0.4
go: downloading golang.org/x/term v0.29.0
go: downloading golang.org/x/oauth2 v0.8.0
go: downloading golang.org/x/crypto v0.35.0
go: downloading go1.24.1 (linux/amd64)
go: downloading k8s.io/apimachinery v0.30.2
go: downloading k8s.io/api v0.30.2
go: downloading k8s.io/client-go v0.30.2
go: downloading k8s.io/klog v1.0.0
go: downloading github.com/stretchr/testify v1.10.0
go: downloading golang.org/x/net v0.38.0
go: downloading golang.org/x/sys v0.31.0
go: downloading github.com/golang/protobuf v1.5.4
go: downloading github.com/google/gnostic-models v0.6.8
go: downloading k8s.io/kube-openapi v0.0.0-20240228011516-70dd3763d340
go: downloading golang.org/x/time v0.3.0
go: downloading golang.org/x/term v0.30.0
go: downloading golang.org/x/oauth2 v0.16.0
go: downloading github.com/evanphx/json-patch v4.12.0+incompatible
go: downloading golang.org/x/text v0.23.0
go: downloading github.com/go-openapi/jsonreference v0.20.2
go: downloading github.com/emicklei/go-restful/v3 v3.11.0
go: downloading github.com/pkg/errors v0.9.1
go: downloading github.com/onsi/ginkgo/v2 v2.15.0
go: downloading github.com/onsi/gomega v1.31.0
go: downloading github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572
go: downloading k8s.io/apimachinery v0.29.0
go: downloading k8s.io/api v0.29.0
go: downloading k8s.io/client-go v0.29.0
go: downloading k8s.io/klog/v2 v2.120.1
go: downloading github.com/robfig/cron/v3 v3.0.0
go: downloading github.com/stretchr/objx v0.5.2
go: downloading k8s.io/kube-openapi v0.0.0-20231010175941-2dd684a91f00
go: downloading golang.org/x/time v0.5.0
go: downloading github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da
go: downloading golang.org/x/oauth2 v0.10.0
go: downloading github.com/google/uuid v1.5.0
go: downloading github.com/onsi/ginkgo/v2 v2.13.0
go: downloading github.com/onsi/gomega v1.29.0

go-test-mod..............................................................Passed
Generate ZipApp CRD & Go Structs.........................................Passed
Terraform Format.........................................................Failed
- hook id: terraform-fmt
- files were modified by this hook

Unable to find image 'ghcr.io/opentofu/opentofu:latest' locally
latest: Pulling from opentofu/opentofu

66a3d608f3fa: Pulling fs layer 

7c8581932a32: Pulling fs layer 

52e2b5b86328: Pulling fs layer 

7c8581932a32: Downloading   98.3kB/9.077MB

52e2b5b86328: Downloading  261.2kB/25.63MB

66a3d608f3fa: Downloading  37.05kB/3.626MB

7c8581932a32: Verifying Checksum 

7c8581932a32: Download complete 

66a3d608f3fa: Verifying Checksum 

66a3d608f3fa: Download complete 

52e2b5b86328: Downloading   22.3MB/25.63MB

66a3d608f3fa: Extracting  65.54kB/3.626MB

52e2b5b86328: Verifying Checksum 

52e2b5b86328: Download complete 

66a3d608f3fa: Extracting  3.626MB/3.626MB

66a3d608f3fa: Pull complete 

7c8581932a32: Extracting   98.3kB/9.077MB

7c8581932a32: Extracting  2.851MB/9.077MB

7c8581932a32: Extracting  8.749MB/9.077MB

7c8581932a32: Extracting  9.077MB/9.077MB

7c8581932a32: Pull complete 

52e2b5b86328: Extracting  262.1kB/25.63MB

=== END OF LOG SNIPPET ===
=== LOG SNIPPET (lines 986–1057) ===
+github.com/onsi/ginkgo/v2 v2.9.1/go.mod h1:FEcmzVcCHl+4o9bQZVab+4dC9+j+91t2FHSzmGAPfuo=
 github.com/onsi/gomega v1.27.4 h1:Z2AnStgsdSayCMDiCU42qIz+HLqEPcgiOCXjAU/w+8E=
+github.com/onsi/gomega v1.27.4/go.mod h1:riYq/GJKh8hhoM01HN6Vmuy93AarCXCBGpvFDK3q3fQ=
 github.com/patrickmn/go-cache v0.0.0-20180815053127-5633e0862627 h1:pSCLCl6joCFRnjpeojzOpEYs4q7Vditq8fySFG5ap3Y=
 github.com/patrickmn/go-cache v0.0.0-20180815053127-5633e0862627/go.mod h1:3Qf8kWWT7OJRJbdiICTKqZju1ZixQ/KpMGzzAfe6+WQ=
 github.com/pmezard/go-difflib v1.0.0 h1:4DBwDE0NGyQoBHbLQYPwSUPoCMWR5BEzIk/f1lZbAQM=
@@ -109,7 +114,9 @@ github.com/prometheus/common v0.44.0/go.mod h1:ofAIvZbQ1e/nugmZGz4/qCb9Ap1VoSTIO
 github.com/prometheus/procfs v0.11.1 h1:xRC8Iq1yyca5ypa9n1EZnWZkt7dwcoRPQwX/5gwaUuI=
 github.com/prometheus/procfs v0.11.1/go.mod h1:eesXgaPo1q7lBpVMoMy0ZOFTth9hBn4W/y0/p/ScXhY=
 github.com/rogpeppe/go-internal v1.11.0 h1:cWPaGQEPrBb5/AsnsZesgZZ9yb1OQ+GOISoDNXVBh4M=
+github.com/rogpeppe/go-internal v1.11.0/go.mod h1:ddIwULY96R17DhadqLgMfk9H9tvdUzkipdSkR5nkCZA=
 github.com/spf13/pflag v1.0.5 h1:iy+VFUOCP1a+8yFto/drg2CJ5u0yRoB7fZw3DKv/JXA=
+github.com/spf13/pflag v1.0.5/go.mod h1:McXfInJRrz4CZXVZOBLb0bTZqETkiAhM9Iw0y3An2Bg=
 github.com/stoewer/go-strcase v1.2.0/go.mod h1:IBiWB2sKIp3wVVQ3Y035++gc+knqhUQag1KpM8ahLw8=
 github.com/stretchr/objx v0.1.0/go.mod h1:HFkY916IF+rwdDfMAkV7OtwuqBVzrE8GR6GFx+wExME=
 github.com/stretchr/objx v0.4.0/go.mod h1:YvHI0jy2hoMjB+UWwv71VJQ9isScKT/TqJzVSSt89Yw=
@@ -210,6 +217,7 @@ golang.org/x/tools v0.0.0-20210106214847-113979e3529a/go.mod h1:emZCQorbCU4vsT4f
 golang.org/x/tools v0.1.12/go.mod h1:hNGJHUnrk76NpqgfD5Aqm5Crs+Hm0VOH/i9J2+nxYbc=
 golang.org/x/tools v0.6.0/go.mod h1:Xwgl3UAJ/d3gWutnCtw505GrjyAbvKui8lOU390QaIU=
 golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d h1:vU5i/LfpvrRCpgM/VPfJLg5KjxD3E+hfT1SH+d9zLwg=
+golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d/go.mod h1:aiJjzUbINMkxbQROHiO6hDPo2LHcIPhhQsa9DLh0yGk=
 golang.org/x/xerrors v0.0.0-20190717185122-a985d3407aa7/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
 golang.org/x/xerrors v0.0.0-20191011141410-1b5146add898/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
 golang.org/x/xerrors v0.0.0-20191204190536-9bdfabe68543/go.mod h1:I/5z698sn9Ka8TeJc9MKroUUfqBBauWjQqLJ2OPfmY0=
diff --git a/apps/vpa-operator/go.mod b/apps/vpa-operator/go.mod
index e9078a6..c2c233a 100644
--- a/apps/vpa-operator/go.mod
+++ b/apps/vpa-operator/go.mod
@@ -1,6 +1,7 @@
 module flyzipline.com/cloudinfra/vpa/operator
 
-go 1.22.0
+go 1.23.0
+
 toolchain go1.24.1
 
 require (
diff --git a/terrafu/modules/aspect-workflows/workflows.tf b/terrafu/modules/aspect-workflows/workflows.tf
index 12d716f..6dadaaa 100755
--- a/terrafu/modules/aspect-workflows/workflows.tf
+++ b/terrafu/modules/aspect-workflows/workflows.tf
@@ -175,7 +175,7 @@ module "aspect_workflows" {
     default = {
       agent_idle_timeout_min    = 120
       gh_repo                   = "ZiplineTeam/FlightSystems" # `org/repo` of the GitHub repository under test
-      gha_workflow_ids          = []                         # WORKFLOWS_TEMPLATE: to reduce GitHub API call frequency and prevent rate limiting, add the workflow ID of the Aspect Workflows main GitHub Action
+      gha_workflow_ids          = []                          # WORKFLOWS_TEMPLATE: to reduce GitHub API call frequency and prevent rate limiting, add the workflow ID of the Aspect Workflows main GitHub Action
       max_runners               = 50
       min_runners               = 0
       queue                     = "aspect-default"
##[error]Process completed with exit code 1.
Post job cleanup.
Cache up-to-date.
Post job cleanup.
[command]/usr/bin/git version
git version 2.49.0
Temporarily overriding HOME='/home/<USER>/work/_temp/9acdba1e-09e7-4aa5-bbf3-abd5a268d69f' before making global git config changes
Adding repository directory to the temporary git global config as a safe directory
[command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/cloud_infra_stacks/cloud_infra_stacks
[command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
[command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
[command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
http.https://github.com/.extraheader
[command]/usr/bin/git config --local --unset-all http.https://github.com/.extraheader
[command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
Post job cleanup.
[command]/usr/bin/docker logout 
Removing login credentials for https://index.docker.io/v1/
##[group]Post cache
State not set
##[endgroup]
Cleaning up orphan processes
=== END OF LOG SNIPPET ===
