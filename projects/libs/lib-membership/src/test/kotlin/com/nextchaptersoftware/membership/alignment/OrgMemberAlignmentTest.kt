package com.nextchaptersoftware.membership.alignment

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.minutes
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class OrgMemberAlignmentTest : DatabaseTestsBase() {
    private val memberStore: MemberStore = Stores.memberStore
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore
    private val personStore: PersonStore = Stores.personStore
    private val identityStore: IdentityStore = Stores.identityStore

    private val service: OrgMemberAlignment = OrgMemberAlignment(
        memberStore = memberStore,
        orgMemberStore = orgMemberStore,
        personStore = personStore,
        identityStore = identityStore,
    )

    private lateinit var person: PersonDAO
    private lateinit var personIdentity: IdentityDAO
    private lateinit var scmTeam1: ScmTeamDAO
    private lateinit var scmTeam2: ScmTeamDAO
    private lateinit var installation1: InstallationDAO
    private lateinit var installation2: InstallationDAO
    private lateinit var org1: OrgDAO
    private lateinit var org2: OrgDAO

    private suspend fun setup() {
        person = makePerson()
        org1 = makeOrg()
        org2 = makeOrg()
        installation1 = makeInstallation(org = org1)
        installation2 = makeInstallation(org = org2)
        scmTeam1 = makeScmTeam(installation = installation1, org = org1)
        scmTeam2 = makeScmTeam(installation = installation2, org = org2)
        personIdentity = makeIdentity(person = person)
    }

    @Test
    fun `alignOrgMembersForIdentity returns null when no identities exist`() = suspendingDatabaseTest {
        setup()

        val result = service.alignOrgMembersForIdentity(orgId = org1.idValue, identityId = personIdentity.id.value)

        assertThat(result).isNull()
    }

    @Test
    fun `alignOrgMembersForIdentity returns null when no members exist for identities`() = suspendingDatabaseTest {
        setup()

        makeIdentity(person = person, provider = Provider.GitHub)

        val result = service.alignOrgMembersForIdentity(orgId = org1.idValue, identityId = personIdentity.id.value)

        assertThat(result).isNull()
    }

    @Test
    fun `alignOrgMembersForIdentity returns the correct primary OrgMember and matched OrgMembers`() = suspendingDatabaseTest {
        setup()

        val identity1 = makeIdentity(person = person, provider = Provider.GitHub)
        val identity2 = makeIdentity(person = person, provider = Provider.Slack)
        val org1Member1 = makeOrgMember(org = org1)
        val org1Member2 = makeOrgMember(org = org1)
        val primaryMember = makeMember(
            scmTeam = scmTeam1,
            installation = installation1,
            identity = identity1,
            orgMember = org1Member1,
            isCurrentMember = true,
            isPrimaryMember = true,
        )
        makeMember(
            scmTeam = scmTeam1,
            installation = installation1,
            identity = identity2,
            orgMember = org1Member2,
            isCurrentMember = true,
            isPrimaryMember = false,
        )

        val result = service.alignOrgMembersForIdentity(orgId = org1.idValue, identityId = personIdentity.id.value)

        assertThat(result).isNotNull
        assertThat(result?.selectedOrgMember?.id).isEqualTo(primaryMember.orgMember.id.value)
        assertThat(result?.alignOrgMembers)
            .flatMap({ it.id })
            .containsExactlyInAnyOrder(org1Member2.idValue)
    }

    @Test
    fun `alignOrgMembersForIdentity does not cross org boundaries for alignment`() = suspendingDatabaseTest {
        setup()

        // Identities linked to the same person but different orgs
        val identity1 = makeIdentity(person = person, provider = Provider.GitHub)
        val identity2 = makeIdentity(person = person, provider = Provider.Slack)

        val org1Member1 = makeOrgMember(org = org1)
        val org1Member2 = makeOrgMember(org = org1)
        val org2Member2 = makeOrgMember(org = org2)

        val primaryMemberOrg1 = makeMember(
            scmTeam = scmTeam1,
            installation = installation1,
            identity = identity1,
            orgMember = org1Member1,
            isCurrentMember = true,
            isPrimaryMember = true,
        )
        makeMember(
            scmTeam = scmTeam1,
            installation = installation1,
            identity = identity2,
            orgMember = org1Member2,
            isCurrentMember = true,
            isPrimaryMember = false,
        )
        makeMember(
            scmTeam = scmTeam2,
            installation = installation2,
            identity = identity2,
            orgMember = org2Member2,
            isCurrentMember = true,
            isPrimaryMember = false,
        )

        // Perform alignment within org1
        val result = service.alignOrgMembersForIdentity(
            orgId = org1.idValue,
            identityId = personIdentity.id.value,
        )

        // Validate that the alignment stays within org1 boundaries
        assertThat(result).isNotNull
        assertThat(result?.selectedOrgMember?.id).isEqualTo(primaryMemberOrg1.orgMember.id.value)
        assertThat(result?.alignOrgMembers?.map { it.id }).containsExactlyInAnyOrder(org1Member2.idValue)
    }

    @Test
    fun `alignOrgMembersForIdentity selects the OrgMember with an associated person when primary member does not have a person`() =
        suspendingDatabaseTest {
            setup()

            // Create identities
            val identity1 = makeIdentity(person = person, provider = Provider.GitHub) // Primary member identity with no associated person
            val identity2 = makeIdentity(person = person, provider = Provider.Slack) // Secondary member identity with an associated person

            // Create OrgMembers
            val org1Member1 = makeOrgMember(org = org1, person = null, createPerson = false) // OrgMember without a person
            val org1Member2 = makeOrgMember(org = org1, person = person) // OrgMember with a person

            // Create Members
            val primaryMember = makeMember(
                scmTeam = scmTeam1,
                installation = installation1,
                identity = identity1,
                orgMember = org1Member1,
                isCurrentMember = true,
                isPrimaryMember = true, // This member is marked as primary but has no associated person
            )
            val secondaryMember = makeMember(
                scmTeam = scmTeam1,
                installation = installation1,
                identity = identity2,
                orgMember = org1Member2,
                isCurrentMember = false,
                isPrimaryMember = false, // This member is secondary but has an associated person
            )

            // Perform alignment within org1
            val result = service.alignOrgMembersForIdentity(
                orgId = org1.idValue,
                identityId = personIdentity.id.value,
            )

            // Validate that the secondaryMember's OrgMember is selected as the primary OrgMember
            assertThat(result).isNotNull
            assertThat(result?.selectedOrgMember?.id).isEqualTo(secondaryMember.orgMember.idValue)
            assertThat(result?.alignOrgMembers?.map { it.id }).containsExactlyInAnyOrder(primaryMember.orgMember.idValue)
        }

    @Test
    fun `alignOrgMembersForIdentity prioritizes an existing orphaned orgMember with person`() = suspendingDatabaseTest {
        setup()

        // Create identities
        val identity1 = makeIdentity(person = person, provider = Provider.GitHub) // Primary member identity with no associated person

        // Create OrgMembers
        val org1Member1 = makeOrgMember(org = org1, person = null, createPerson = false) // OrgMember without a person

        // Orphaned OrgMember
        val orphanedOrgMemberWithPerson = makeOrgMember(org = org1, person = person) // OrgMember with a person

        // Create Member
        makeMember(
            scmTeam = scmTeam1,
            installation = installation1,
            identity = identity1,
            orgMember = org1Member1,
            isCurrentMember = true,
            isPrimaryMember = true, // This member is marked as primary but has no associated person
        )

        // Perform alignment within org1
        val result = service.alignOrgMembersForIdentity(
            orgId = org1.idValue,
            identityId = personIdentity.id.value,
        )

        // Validate that the secondaryMember's OrgMember is selected as the orphaned orgMember
        assertThat(result).isNotNull
        assertThat(result?.selectedOrgMember?.id).isEqualTo(orphanedOrgMemberWithPerson.id.value)
        assertThat(result?.alignOrgMembers?.map { it.id }).containsExactlyInAnyOrder(org1Member1.id.value)
    }

    @Test
    fun `alignOrgMembersForIdentity sorts members by primary and current status`() = suspendingDatabaseTest {
        // Arrange: Create an organization, person, and identities
        val org = makeOrg()
        val person = makePerson()
        val orgMember1 = makeOrgMember(org = org, createPerson = false)
        val orgMember2 = makeOrgMember(org = org, createPerson = false)
        val orgMember3 = makeOrgMember(org = org, createPerson = false)
        val orgMember4 = makeOrgMember(org = org, createPerson = false)
        val identity1 = makeIdentity(person = person, provider = Provider.GitHub)
        val identity2 = makeIdentity(person = person, provider = Provider.GitLab)

        // Arrange: Create members with different statuses
        val nonPrimaryCurrentMember = makeMember(
            orgMember = orgMember3,
            identity = identity1,
            isPrimaryMember = false,
            isCurrentMember = true,
        )

        val nonPrimaryNonCurrentMember = makeMember(
            orgMember = orgMember4,
            identity = identity2,
            isPrimaryMember = false,
            isCurrentMember = false,
        )

        val primaryCurrentMember = makeMember(
            orgMember = orgMember1,
            identity = identity1,
            isPrimaryMember = true,
            isCurrentMember = true,
        )

        val primaryNonCurrentMember = makeMember(
            orgMember = orgMember2,
            identity = identity2,
            isPrimaryMember = true,
            isCurrentMember = false,
        )

        val result = service.alignOrgMembersForIdentity(
            orgId = org.idValue,
            identityId = identity1.id.value,
        )

        // Assert: Ensure the primary current member is selected
        assertThat(result).isNotNull
        assertThat(result?.selectedOrgMember?.id).isEqualTo(primaryCurrentMember.orgMember.id.value)

        // Assert: Ensure that the remaining OrgMembers are correctly aligned
        assertThat(result?.alignOrgMembers).hasSize(3)
        assertThat(result?.alignOrgMembers?.map { it.id })
            .containsExactlyInAnyOrder(
                primaryNonCurrentMember.orgMember.id.value,
                nonPrimaryCurrentMember.orgMember.id.value,
                nonPrimaryNonCurrentMember.orgMember.id.value,
            )
    }

    @Test
    fun `alignOrgMembersForIdentity sorts members by createdAt`() = suspendingDatabaseTest {
        // Arrange: Create an organization, person, and identities
        val org = makeOrg()
        val person = makePerson()
        val orgMember1 = makeOrgMember(org = org, createPerson = false)
        val orgMember2 = makeOrgMember(org = org, createPerson = false)
        val identity1 = makeIdentity(person = person, provider = Provider.GitHub)
        val identity2 = makeIdentity(person = person, provider = Provider.GitLab)

        val now = Instant.nowWithMicrosecondPrecision()

        // Arrange: Create members with different statuses
        val nonPrimaryOldestMember = makeMember(
            createdAt = now.minus(5.minutes),
            orgMember = orgMember1,
            identity = identity1,
            isPrimaryMember = false,
            isCurrentMember = false,
        )

        val nonPrimaryNewestMember = makeMember(
            createdAt = now.minus(1.minutes),
            orgMember = orgMember2,
            identity = identity2,
            isPrimaryMember = false,
            isCurrentMember = false,
        )

        val result = service.alignOrgMembersForIdentity(
            orgId = org.idValue,
            identityId = identity1.id.value,
        )

        // Assert: Ensure the primary current member is selected
        assertThat(result).isNotNull
        assertThat(result?.selectedOrgMember?.id).isEqualTo(nonPrimaryOldestMember.orgMember.id.value)

        // Assert: Ensure that the remaining OrgMembers are correctly aligned
        assertThat(result?.alignOrgMembers).hasSize(1)
        assertThat(result?.alignOrgMembers?.map { it.id })
            .containsExactlyInAnyOrder(
                nonPrimaryNewestMember.orgMember.id.value,
            )
    }

    @Test
    fun `alignOrgMembersForIdentity aligns correctly when identity is shared across two members without a person`() = suspendingDatabaseTest {
        val org = makeOrg()
        val installation1 = makeInstallation(org = org)
        val installation2 = makeInstallation(org = org)
        val identity = makeIdentity(person = null) // Identity not linked to a person
        val orgMember1 = makeOrgMember(org = org, createPerson = false)
        val orgMember2 = makeOrgMember(org = org, createPerson = false)

        makeMember(
            installation = installation1,
            identity = identity,
            orgMember = orgMember1,
            isCurrentMember = true,
            isPrimaryMember = true,
        )

        makeMember(
            installation = installation2,
            identity = identity,
            orgMember = orgMember2,
            isCurrentMember = true,
            isPrimaryMember = false,
        )

        // Execute: Call the service to align org members for the identity.
        val result = service.alignOrgMembersForIdentity(orgId = org.idValue, identityId = identity.id.value)

        // Validate: Ensure the alignment result is as expected.
        assertThat(result).isNotNull
        assertThat(result?.selectedOrgMember?.id).isEqualTo(orgMember1.id.value)
        assertThat(result?.alignOrgMembers).hasSize(1)
        assertThat(result?.alignOrgMembers?.first()?.id).isEqualTo(orgMember2.id.value)
    }
}
