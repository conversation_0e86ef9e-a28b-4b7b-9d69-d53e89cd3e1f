package com.nextchaptersoftware.environment

import com.nextchaptersoftware.config.GlobalConfig

class LandingPageUrlBuilder(
    config: GlobalConfig = GlobalConfig.INSTANCE,
) : BaseUrlBuilder(
    hostName = config.landingPage.hostName,
    port = config.landingPage.port,
    enableTls = config.landingPage.enableTLS,
    basePathSegments = config.landingPage.basePath?.let { listOf(it) },
) {
    fun withDownload() = apply {
        addPathSegments("download")
    }

    fun withPricing() = apply {
        addPathSegments("pricing")
    }

    fun withSecurity() = apply {
        addPathSegments("security")
    }

    companion object {
        fun download() = LandingPageUrlBuilder().withDownload().build()
    }
}
