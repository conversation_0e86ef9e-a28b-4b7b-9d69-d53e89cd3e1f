package com.nextchaptersoftware.ktor.client

import com.nextchaptersoftware.pagination.Pagination
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow

typealias HttpClientBatch<T> = Pagination.Batch<T, Url>

object HttpClientPagination {

    /**
     * Streams all resources by paging sequentially through the entire collection.
     * Returns a [Batch] of items and the next URI to fetch.
     *
     * Batched streaming returns a list of items and then next page URL, to be used
     * by calling clients in cases where it is cost advantageous to cache the next
     * page API in the event that the long-running stream is interrupted.
     *
     * To use safely: the caller must process **all** of the items in the batch
     * before persisting the next page, otherwise items will be lost if there is an
     * interruption mid-batch.
     *
     * @param T the type of items emitted in the batches.
     * @param initialUri the initial URI to start fetching data from.
     * @param maxItems the maximum number of items to be retrieved, or null if no maximum is set.
     * @param dataProvider a suspend function that takes the HTTP response and returns a list of items of type T.
     *                     This function extracts the list of items from the response body.
     * @param nextUriProvider a suspend function that takes the HTTP response and returns the next URI for pagination.
     *                        This function retrieves the URI for the next page of results from the response.
     * @param block a lambda that configures the HTTP request builder before making the request.
     * @return a flow emitting batches of items obtained from HTTP responses.
     */
    inline fun <reified T> HttpClient.batchStream(
        initialUri: String,
        maxItems: Int? = null,
        noinline dataProvider: suspend (HttpResponse) -> List<T> = { it.body() },
        noinline nextUriProvider: suspend (HttpResponse) -> Url?,
        crossinline block: HttpRequestBuilder.() -> Unit = {},
    ): Flow<HttpClientBatch<T>> {
        return Pagination.batchStream<T, Url>(
            maxItems = maxItems,
            initialCursor = null,
            execute = { cursor, quota ->
                // FIXME: handle quota
                val response = when (cursor) {
                    null -> get(initialUri, block)
                    else -> get(cursor)
                }
                val items = dataProvider(response)
                val nextUri = nextUriProvider(response)

                Pagination.Batch(
                    items = items,
                    nextCursor = nextUri,
                )
            },
        )
    }
}
