package com.nextchaptersoftware.api.integration.factory

import com.nextchaptersoftware.api.integration.models.IntegrationModel
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import io.ktor.http.Url

interface IntegrationFactory {
    suspend fun generate(
        orgId: OrgId,
        identityId: IdentityId,
        personId: PersonId,
        provider: Provider,
        overrideAuthRedirectUrl: Url?,
        dataIntegrationRedirectUrl: Url? = null,
        clientState: String? = null,
    ): IntegrationModel?
}
