package com.nextchaptersoftware.api.integration.factory

import com.nextchaptersoftware.api.auth.services.State.newState
import com.nextchaptersoftware.api.auth.services.url.redirect.ScmRedirectUrlService
import com.nextchaptersoftware.api.integration.models.IntegrationModel
import com.nextchaptersoftware.db.models.AuthenticationStateDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.Scm
import io.ktor.http.Url

class ScmIntegrationFactory(
    private val scmRedirectUrlService: ScmRedirectUrlService,
) : IntegrationFactory {

    override suspend fun generate(
        orgId: OrgId,
        identityId: IdentityId,
        personId: PersonId,
        provider: Provider,
        overrideAuthRedirectUrl: Url?,
        dataIntegrationRedirectUrl: Url?,
        clientState: String?,
    ): IntegrationModel {
        val scm = Scm.fromProvider(provider = provider, enterpriseId = null) // FIXME enterpriseId

        val state = AuthenticationStateDAO.newState()

        val installUrl = scmRedirectUrlService.buildRedirectUrl(
            oAuthApiType = scm,
            nonce = state.nonce,
            authRedirectOverrideUrl = overrideAuthRedirectUrl,
            clientState = clientState,
            state = null,
        )

        return IntegrationModel(
            displayName = provider.displayName,
            installUrl = installUrl.asString,
            provider = provider,
        )
    }
}
