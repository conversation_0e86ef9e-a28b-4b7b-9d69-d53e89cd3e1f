package com.nextchaptersoftware.api.integration.installation.services

import com.nextchaptersoftware.activation.ProviderActivation
import com.nextchaptersoftware.api.integration.installation.models.IntegrationInstallationModel
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import io.ktor.http.Url
import kotlinx.datetime.Instant

class InstallationService(
    private val installationProviderService: InstallationProviderService,
    private val providerActivation: ProviderActivation,
) {
    suspend fun getInstallation(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        identityId: IdentityId,
        personId: PersonId,
        installationId: InstallationId,
        dataIntegrationRedirectUrl: Url?,
        overrideRedirectUrl: Url?,
        clientState: String?,
    ): IntegrationInstallationModel? {
        return installationProviderService.getIntegrationInstallation(
            orgId = orgId,
            orgMemberId = orgMemberId,
            identityId = identityId,
            personId = personId,
            installationId = installationId,
            dataIntegrationRedirectUrl = dataIntegrationRedirectUrl,
            overrideRedirectUrl = overrideRedirectUrl,
            clientState = clientState,
        )
    }

    suspend fun listInstallations(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        provider: Provider?,
        excludeScmProviders: Boolean,
        identityId: IdentityId,
        personId: PersonId,
        dataIntegrationRedirectUrl: Url?,
        overrideRedirectUrl: Url?,
        clientState: String?,
        modifiedSince: Instant?,
    ): List<IntegrationInstallationModel> = withLoggingContextAsync(
        "orgId" to orgId,
        "provider" to provider,
        "personId" to personId,
    ) {
        val integrationProviders = provider?.let(::listOf)
            ?: Provider.integrations(
                excludeScmProviders = excludeScmProviders,
                includeIf = providerActivation::isActive,
            )

        installationProviderService.listIntegrationInstallations(
            orgId = orgId,
            orgMemberId = orgMemberId,
            providers = integrationProviders,
            identityId = identityId,
            personId = personId,
            dataIntegrationRedirectUrl = dataIntegrationRedirectUrl,
            overrideRedirectUrl = overrideRedirectUrl,
            modifiedSince = modifiedSince,
            clientState = clientState,
        )
    }

    suspend fun listScmInstallations(orgId: OrgId): Set<InstallationId> {
        return installationStore.findByProvider(orgId, Provider.scmProviders).map { it.id }.toSet()
    }
}
