package com.nextchaptersoftware.api.integration.factory

import com.nextchaptersoftware.api.integration.models.IntegrationModel
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import io.ktor.http.Url

class FeatureFlaggedIntegrationFactory(
    private val integrationFactory: IntegrationFactory,
    private val clientConfigService: ClientConfigService,
    private val type: ClientCapabilityType,
) : IntegrationFactory {

    override suspend fun generate(
        orgId: OrgId,
        identityId: IdentityId,
        personId: PersonId,
        provider: Provider,
        overrideAuthRedirectUrl: Url?,
        dataIntegrationRedirectUrl: Url?,
        clientState: String?,
    ): IntegrationModel? = when (clientConfigService.computeMergedCapabilityForType(personId = personId, orgId = orgId, type = type)) {
        true -> {
            integrationFactory.generate(
                orgId = orgId,
                identityId = identityId,
                personId = personId,
                provider = provider,
                overrideAuthRedirectUrl = overrideAuthRedirectUrl,
                dataIntegrationRedirectUrl = dataIntegrationRedirectUrl,
                clientState = clientState,
            )
        }

        else -> {
            null
        }
    }
}
