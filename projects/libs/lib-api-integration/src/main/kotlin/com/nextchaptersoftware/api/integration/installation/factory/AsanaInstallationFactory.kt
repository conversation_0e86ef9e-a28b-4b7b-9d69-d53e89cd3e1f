package com.nextchaptersoftware.api.integration.installation.factory

import com.nextchaptersoftware.api.integration.connection.services.ConnectionService
import com.nextchaptersoftware.api.integration.installation.errors.AsanaInstallationErrorProvider
import com.nextchaptersoftware.api.integration.installation.errors.MissingTokenErrorProvider
import com.nextchaptersoftware.api.integration.installation.models.IntegrationInstallationError
import com.nextchaptersoftware.api.integration.installation.models.IntegrationInstallationModel
import com.nextchaptersoftware.api.integration.installation.progress.IngestionInstallationProgress
import com.nextchaptersoftware.api.integration.oauth.AsanaUserOauthUrlProvider
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import io.ktor.http.Url

class AsanaInstallationFactory(
    private val connectionService: ConnectionService,
    private val asanaUserOauthUrlProvider: AsanaUserOauthUrlProvider,
    private val ingestionInstallationProgress: IngestionInstallationProgress = IngestionInstallationProgress(),
    private val missingTokenErrorProvider: MissingTokenErrorProvider = MissingTokenErrorProvider(),
    private val asanaInstallationErrorProvider: AsanaInstallationErrorProvider = AsanaInstallationErrorProvider(),
) : InstallationFactory {

    override suspend fun generate(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        installation: Installation,
        identityId: IdentityId,
        personId: PersonId,
        redirectUrl: Url?,
        clientState: String?,
    ): IntegrationInstallationModel {
        val installUrl = asanaUserOauthUrlProvider.get(
            orgId = orgId,
            personId = personId,
            provider = Provider.Asana,
            clientState = clientState,
            redirectUrl = redirectUrl,
        )

        val connectionIdentity = connectionService.getInstallationConnection(
            installation = installation,
            personId = personId,
        )

        val progress = ingestionInstallationProgress.getProgress(installation)

        return IntegrationInstallationModel(
            id = installation.id,
            orgId = installation.orgId,
            displayName = installation.displayName,
            installationDsacEnabled = installation.dsacEnabled,
            provider = installation.provider,
            avatarUrl = installation.avatarUrl,
            htmlUrl = installation.htmlUrl,
            userOauthUrl = installUrl,
            connectionIdentity = connectionIdentity,
            modifiedAt = installation.modifiedAt,
            progress = progress,
            isSetupIncomplete = installation.isSetupIncomplete,
            error = when {
                missingTokenErrorProvider.isMissingValidToken(installation = installation) -> IntegrationInstallationError(
                    message = "${installation.provider.displayName} account has been disconnected.",
                    provider = installation.provider,
                    errorType = IntegrationInstallationError.ErrorType.InvalidToken,
                )

                asanaInstallationErrorProvider.isMissingConfiguration(installation = installation) -> IntegrationInstallationError(
                    message = "No ${if (installation.provider == Provider.Confluence) "spaces" else "projects"} have been selected",
                    provider = installation.provider,
                    errorType = IntegrationInstallationError.ErrorType.EmptyConfiguration,
                )

                else -> null
            },
        )
    }
}
