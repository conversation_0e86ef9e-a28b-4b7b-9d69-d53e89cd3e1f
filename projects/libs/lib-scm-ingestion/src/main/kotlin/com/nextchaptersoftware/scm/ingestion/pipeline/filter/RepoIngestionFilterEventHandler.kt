package com.nextchaptersoftware.scm.ingestion.pipeline.filter

import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.types.Hash

interface RepoIngestionFilterEventHandler {
    suspend fun onEmptyHeadCommit(scmTeam: ScmTeam, repo: Repo): <PERSON><PERSON><PERSON>

    suspend fun onHeadCommit(scmTeam: ScmTeam, repo: Repo, headCommit: Hash): Boolean
}
