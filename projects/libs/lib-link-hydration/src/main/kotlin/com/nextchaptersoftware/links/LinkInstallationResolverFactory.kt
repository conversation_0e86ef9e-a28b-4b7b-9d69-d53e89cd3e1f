package com.nextchaptersoftware.links

import com.nextchaptersoftware.datasources.ConfluenceAccessService
import com.nextchaptersoftware.datasources.JiraAccessService
import com.nextchaptersoftware.datasources.ThreadAccessService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ConfluenceSiteModel
import com.nextchaptersoftware.db.models.EnterpriseAppConfigModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.JiraSiteModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.SlackTeamModel
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.links.hydration.LinkHydrator
import com.nextchaptersoftware.links.hydration.installation.CodaLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.ConfluenceLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.GoogleDriveLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.JiraLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.LinearLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.NotionLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.SlackLinkHydrator
import com.nextchaptersoftware.links.hydration.installation.StackOverflowLinkHydrator
import com.nextchaptersoftware.links.hydration.scm.AzureDevOpsLinkHydrator
import com.nextchaptersoftware.links.hydration.scm.BitbucketDataCenterLinkHydrator
import com.nextchaptersoftware.links.hydration.scm.BitbucketLinkHydrator
import com.nextchaptersoftware.links.hydration.scm.GitHubLinkHydrator
import com.nextchaptersoftware.links.hydration.scm.GitLabLinkHydrator
import com.nextchaptersoftware.ml.doc.converter.ConfluenceDocConverter
import com.nextchaptersoftware.ml.doc.converter.JiraDocConverter
import com.nextchaptersoftware.ml.doc.converter.PullRequestDocConverter
import com.nextchaptersoftware.ml.doc.converter.ThreadDocConverter
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.types.Hostname
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq

/**
 * Responsible for resolving a URL to an installed integration.
 */
class LinkInstallationResolverFactory(
    private val confluenceAccessService: ConfluenceAccessService?,
    private val confluenceDocConverter: ConfluenceDocConverter,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val jiraAccessService: JiraAccessService?,
    private val jiraDocConverter: JiraDocConverter,
    private val pullRequestDocConverter: PullRequestDocConverter,
    private val repoAccessService: RepoAccessService,
    private val scmRepoApiFactory: ScmRepoApiFactory,
    private val threadAccessService: ThreadAccessService,
    private val threadDocConverter: ThreadDocConverter,
) {
    suspend fun getLinkResolver(orgId: OrgId): LinkInstallationResolver {
        return LinkInstallationResolver(discoverLinkHydrators(orgId))
    }

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    private suspend fun discoverLinkHydrators(orgId: OrgId): Set<LinkHydrator> {
        val scmEnterpriseHostnames = getEnterpriseScmHostnames(orgId)

        return installationStore.getIntegrationInstallations(orgId = orgId).mapNotNull { installation ->
            when (installation.provider) {
                Provider.AzureDevOps,
                    -> AzureDevOpsLinkHydrator(
                    hostname = Hostname.parse(Scm.AzureDevOps.hostname),
                    installation = installation,
                    pullRequestDocConverter = pullRequestDocConverter,
                    repoAccessService = repoAccessService,
                    scmRepoApiFactory = scmRepoApiFactory,
                )

                Provider.Bitbucket,
                    -> BitbucketLinkHydrator(
                    hostname = Hostname.parse(Scm.Bitbucket.hostname),
                    installation = installation,
                    pullRequestDocConverter = pullRequestDocConverter,
                    repoAccessService = repoAccessService,
                    scmRepoApiFactory = scmRepoApiFactory,
                )

                Provider.Coda,
                    -> CodaLinkHydrator(Hostname.parse("coda.io"), installation)

                Provider.GitHub,
                    -> GitHubLinkHydrator(
                    hostname = Hostname.parse(Scm.GitHub.hostname),
                    installation = installation,
                    pullRequestDocConverter = pullRequestDocConverter,
                    repoAccessService = repoAccessService,
                    scmRepoApiFactory = scmRepoApiFactory,
                    threadAccessService = threadAccessService,
                    threadDocConverter = threadDocConverter,
                )

                Provider.GitLab,
                    -> GitLabLinkHydrator(
                    hostname = Hostname.parse(Scm.GitLab.hostname),
                    installation = installation,
                    pullRequestDocConverter = pullRequestDocConverter,
                    repoAccessService = repoAccessService,
                    scmRepoApiFactory = scmRepoApiFactory,
                )

                Provider.GoogleDrive,
                Provider.GoogleDriveWorkspace,
                    -> GoogleDriveLinkHydrator(Hostname.parse("docs.google.com"), installation)

                Provider.Linear,
                    -> LinearLinkHydrator(
                    hostname = Hostname.parse("linear.app"),
                    installation = installation,
                    threadAccessService = threadAccessService,
                    threadDocConverter = threadDocConverter,
                )

                Provider.Notion,
                    -> NotionLinkHydrator(Hostname.parse("www.notion.so"), installation)

                Provider.StackOverflowTeams,
                    -> StackOverflowLinkHydrator(Hostname.parse("stackoverflowteams.com"), installation)

                Provider.BitbucketDataCenter,
                    -> scmEnterpriseHostnames[installation.id]?.let {
                    BitbucketDataCenterLinkHydrator(
                        hostname = it,
                        installation = installation,
                        pullRequestDocConverter = pullRequestDocConverter,
                        repoAccessService = repoAccessService,
                        scmRepoApiFactory = scmRepoApiFactory,
                    )
                }

                Provider.GitHubEnterprise,
                    -> scmEnterpriseHostnames[installation.id]?.let {
                    GitHubLinkHydrator(
                        hostname = it, // FIXME multiple
                        installation = installation,
                        pullRequestDocConverter = pullRequestDocConverter,
                        repoAccessService = repoAccessService,
                        scmRepoApiFactory = scmRepoApiFactory,
                        threadAccessService = threadAccessService,
                        threadDocConverter = threadDocConverter,
                    )
                }

                Provider.GitLabSelfHosted,
                    -> scmEnterpriseHostnames[installation.id]?.let {
                    GitLabLinkHydrator(
                        hostname = it,
                        installation = installation,
                        pullRequestDocConverter = pullRequestDocConverter,
                        repoAccessService = repoAccessService,
                        scmRepoApiFactory = scmRepoApiFactory,
                    )
                }

                Provider.Confluence,
                Provider.ConfluenceDataCenter,
                    -> getConfluenceHostname(installation.id)?.let { hostname ->
                    confluenceAccessService?.let {
                        ConfluenceLinkHydrator(
                            hostname = hostname,
                            installation = installation,
                            confluenceAccessService = confluenceAccessService,
                            confluenceDocConverter = confluenceDocConverter,
                        )
                    }
                }

                Provider.Jira,
                Provider.JiraDataCenter,
                    -> getJiraHostname(installation.id)?.let { hostname ->
                    jiraAccessService?.let {
                        JiraLinkHydrator(
                            hostname = hostname,
                            installation = installation,
                            jiraAccessService = jiraAccessService,
                            jiraDocConverter = jiraDocConverter,
                        )
                    }
                }

                Provider.Slack,
                    -> getSlackHostname(installation.id)?.let { SlackLinkHydrator(it, installation) }

                Provider.Asana, // TODO ASANA implement
                    -> null

                /*
                 * These providers do not support content links
                 */
                Provider.Aws,
                Provider.AwsIdentityCenter,
                Provider.BitbucketPipelines,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHubActions,
                Provider.GitLabPipelines,
                Provider.GoogleWorkspace,
                Provider.MicrosoftEntra,
                Provider.Okta,
                Provider.PingOne,
                Provider.Unblocked,
                Provider.Web,
                    -> null
            }
        }.toSet()
    }

    private suspend fun getConfluenceHostname(installationId: InstallationId): Hostname? {
        return suspendedTransaction {
            ConfluenceSiteModel
                .select(ConfluenceSiteModel.baseUrl)
                .where { ConfluenceSiteModel.installation eq installationId }
                .map { it[ConfluenceSiteModel.baseUrl].asUrl.host }
                .firstNotNullOfOrNull { Hostname.parseOrNull(it) }
        }
    }

    private suspend fun getJiraHostname(installationId: InstallationId): Hostname? {
        return suspendedTransaction {
            JiraSiteModel
                .select(JiraSiteModel.baseUrl)
                .where { JiraSiteModel.installation eq installationId }
                .map { it[JiraSiteModel.baseUrl].asUrl.host }
                .firstNotNullOfOrNull { Hostname.parseOrNull(it) }
        }
    }

    private suspend fun getSlackHostname(installationId: InstallationId): Hostname? {
        return suspendedTransaction {
            SlackTeamModel
                .select(SlackTeamModel.htmlUrl)
                .where { SlackTeamModel.installation eq installationId }
                .map { it[SlackTeamModel.htmlUrl].asUrl.host }
                .firstNotNullOfOrNull { Hostname.parseOrNull(it) }
        }
    }

    private suspend fun getEnterpriseScmHostnames(orgId: OrgId): Map<InstallationId, Hostname> {
        val hostnamesByInstallation = suspendedTransaction {
            InstallationModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.installation,
                    onColumn = InstallationModel.id,
                ) {
                    ScmTeamStore.SCM_TEAM_EXISTS_CLAUSE
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = EnterpriseAppConfigModel,
                    otherColumn = EnterpriseAppConfigModel.id,
                    onColumn = ScmTeamModel.providerEnterprise,
                )
                .select(InstallationModel.id, EnterpriseAppConfigModel.hostAndPort)
                .whereAll(
                    InstallationModel.org eq orgId,
                    InstallationStore.INSTALLATION_EXISTS,
                )
                .mapNotNull { row ->
                    val hostname = row[EnterpriseAppConfigModel.hostAndPort]
                        .substringBefore(':')
                        .let { Hostname.parseOrNull(it) }
                    hostname?.let { row[InstallationModel.id].value to hostname }
                }
        }.toMap()

        // Special case hack for Plaid GitHub Enterprise
        val extra = hostnamesByInstallation
            .filter { it.value == Hostname.parse("github-ext.plaid.com") }
            .mapValues { Hostname.parse("github.plaid.com") }

        return hostnamesByInstallation + extra
    }
}
