package com.nextchaptersoftware.links.hydration.scm

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.types.Hostname
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock

class BitbucketDataCenterLinkHydratorTest {
    private val hydrator = BitbucketDataCenterLinkHydrator(
        hostname = Hostname.parse("atlassian-test.com"),
        installation = MockDataClasses.baseInstallation(),
        pullRequestDocConverter = mock(),
        repoAccessService = mock(),
        scmRepoApiFactory = mock(),
    )

    @Test
    fun `parsePullRequestSegments valid`() {
        val expected = RepoSegments.PullRequest(
            ownerName = "APP",
            repoName = "ios-app",
            pullRequestNumber = 13,
            isDiffRelated = false,
        )

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app/pull-requests/13".asUrl),
        ).isEqualTo(expected)

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app/pull-requests/13/overview".asUrl),
        ).isEqualTo(expected.copy(isDiffRelated = false))

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app/pull-requests/13/commits".asUrl),
        ).isEqualTo(expected.copy(isDiffRelated = true))

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app/pull-requests/13/diff".asUrl),
        ).isEqualTo(expected.copy(isDiffRelated = true))
    }

    @Test
    fun `parsePullRequestSegments invalid`() {
        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990//APP/repos/ios-app/pull-requests/13".asUrl),
        ).isNull()

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects//repos/ios-app/pull-requests/13".asUrl),
        ).isNull()

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP//ios-app/pull-requests/13".asUrl),
        ).isNull()

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos//pull-requests/13".asUrl),
        ).isNull()

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app//13".asUrl),
        ).isNull()

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app/pull-requests/".asUrl),
        ).isNull()

        assertThat(
            hydrator.parsePullRequestSegments("https://atlassian-test.com:7990/projects/APP/repos/ios-app/pull-requests/f00".asUrl),
        ).isNull()
    }
}
