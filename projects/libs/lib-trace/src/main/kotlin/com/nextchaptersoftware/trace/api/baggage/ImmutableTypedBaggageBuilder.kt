package com.nextchaptersoftware.trace.api.baggage

import io.opentelemetry.api.baggage.Baggage
import io.opentelemetry.api.baggage.BaggageBuilder
import io.opentelemetry.api.baggage.BaggageEntryMetadata
import io.opentelemetry.api.common.AttributeKey
import io.opentelemetry.api.common.AttributeType
import io.opentelemetry.api.common.AttributeType.BOOLEAN
import io.opentelemetry.api.common.AttributeType.DOUBLE
import io.opentelemetry.api.common.AttributeType.LONG
import io.opentelemetry.api.common.AttributeType.STRING
import io.opentelemetry.api.common.Attributes

@Suppress("TooManyFunctions")
class ImmutableTypedBaggageBuilder : TypedBaggageBuilder {
    private val baggageBuilder: BaggageBuilder = Baggage.builder()

    override fun build(): Baggage {
        return baggageBuilder.build()
    }

    override fun put(key: String, value: String, entryMetadata: BaggageEntryMetadata): ImmutableTypedBaggageBuilder {
        baggageBuilder.put(key, value, entryMetadata)
        return this
    }

    override fun put(key: String, value: String): ImmutableTypedBaggageBuilder {
        return put(key, value, BaggageEntryMetadata.create(AttributeType.STRING.name))
    }

    override fun <T> put(key: AttributeKey<T>, value: T): TypedBaggageBuilder {
        if (key.key.isEmpty() || value == null) {
            return this
        }
        return putAllAttributes(mapOf(key to value))
    }

    override fun remove(key: String): ImmutableTypedBaggageBuilder {
        return this
    }

    override fun put(key: String, value: Int): ImmutableTypedBaggageBuilder {
        return put(key, value.toString(), BaggageEntryMetadata.create(AttributeType.LONG.name))
    }

    override fun put(key: String, value: Long): ImmutableTypedBaggageBuilder {
        return put(key, value.toString(), BaggageEntryMetadata.create(AttributeType.LONG.name))
    }

    override fun put(key: String, value: Double): ImmutableTypedBaggageBuilder {
        return put(key, value.toString(), BaggageEntryMetadata.create(AttributeType.DOUBLE.name))
    }

    override fun put(key: String, value: Boolean): ImmutableTypedBaggageBuilder {
        return put(key, value.toString(), BaggageEntryMetadata.create(AttributeType.BOOLEAN.name))
    }

    override fun putAll(baggage: Baggage): ImmutableTypedBaggageBuilder {
        baggage.forEach { k, v -> baggageBuilder.put(k, v.value, v.metadata) }
        return this
    }

    override fun putAll(fields: Map<String, *>): ImmutableTypedBaggageBuilder {
        for ((key, value) in fields.entries) {
            value?.let {
                when (value) {
                    is Byte, is Short, is Int, is Long -> put(key, (value as Number).toLong())
                    is Float, is Double -> put(key, (value as Number).toDouble())
                    is Boolean -> put(key, value)
                    else -> put(key, value.toString())
                }
            }
        }
        return this
    }

    override fun putAllAttributes(attributes: Map<AttributeKey<*>, *>): ImmutableTypedBaggageBuilder {
        for ((key, value) in attributes.entries) {
            value?.let {
                when (key.type) {
                    STRING -> put(key.key, value.toString())
                    DOUBLE -> put(key.key, (value as Number).toDouble())
                    BOOLEAN -> put(key.key, value as Boolean)
                    LONG -> put(key.key, value as Long)
                    else -> put(key.key, value.toString())
                }
            }
        }
        return this
    }

    override fun putAllAttributes(attributes: Attributes): ImmutableTypedBaggageBuilder {
        return putAllAttributes(attributes.asMap())
    }
}
