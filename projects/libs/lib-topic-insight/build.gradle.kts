plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:libs:lib-config", configuration = "default"))
    implementation(project(":projects:libs:lib-insight", "default"))
    implementation(project(":projects:libs:lib-log", "default"))
    implementation(project(":projects:libs:lib-ml-inference", "default"))
    implementation(project(":projects:libs:lib-ml-prompt", "default"))
    implementation(project(":projects:libs:lib-recommendation", "default"))
    implementation(project(":projects:libs:lib-topic", "default"))
    implementation(project(":projects:libs:lib-trace-ktor", "default"))
    implementation(project(":projects:models", "default"))

    implementation(libs.bundles.ktor.client)

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-aws", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))
}
