package com.nextchaptersoftware.cache

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import io.github.reactivecircus.cache4k.Cache
import kotlin.time.Duration
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

/**
 * In-memory local write-through cache.
 */
class LocalWriteThroughCache<K : Any, V : Any>(
    private val writeTimeout: Duration? = null,
    private val maximumCacheSize: Long? = null,
) : WriteThroughCache<K, V> {
    private val cache = Cache.Builder<K, V>()
        .apply {
            writeTimeout?.let {
                expireAfterWrite(it)
            }
            maximumCacheSize?.let {
                maximumCacheSize(it)
            } ?: maximumCacheSize(MAX_ENTRIES)
        }.build()

    override suspend fun set(key: K, newValue: V, invalidationPredicate: (V?) -> <PERSON><PERSON><PERSON>, store: suspend (K, V) -> V) {
        cache.get(key).also { previousValue ->
            if (invalidationPredicate(previousValue)) {
                runSuspendCatching {
                    store(key, newValue).also { persistedValue ->
                        cache.put(key, persistedValue)
                    }
                }.onFailure {
                    LOGGER.errorAsync(it) { "store failed" }
                }
            }
        }
    }

    override fun clear() {
        cache.invalidateAll()
    }

    companion object {
        private const val MAX_ENTRIES = 10_000L
    }
}
