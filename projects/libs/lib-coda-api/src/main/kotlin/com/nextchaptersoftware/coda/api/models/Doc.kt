package com.nextchaptersoftware.coda.api.models

import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Doc(
    @SerialName("id") val id: String,
    @SerialName("type") val type: String,
    @SerialName("name") val name: String,
    @SerialName("folderId") val folderId: String,
    @SerialName("workspaceId") val workspaceId: String,
    @SerialName("owner") val owner: String, // email of the creator of this doc
    @SerialName("ownerName") val ownerName: String,
    @SerialName("browserLink") val browserLink: String,
    @SerialName("createdAt") val createdAt: Instant,
    @SerialName("updatedAt") val updatedAt: Instant,
    @SerialName("acl") val acl: List<Permission>,
)
