package com.nextchaptersoftware.stripe.webhook.services

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.stripe.events.config.StripeEventsConfig
import com.nextchaptersoftware.stripe.events.queue.enqueue.StripeEventEnqueueService
import com.stripe.exception.SignatureVerificationException
import com.stripe.net.Webhook
import kotlin.getValue
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class StripeWebhookEventService(
    private val stripeEventEnqueueService: StripeEventEnqueueService,
) {
    private val config by lazy {
        StripeEventsConfig.INSTANCE.stripe
    }

    suspend fun process(
        stripeSignature: String,
        body: String,
    ) {
        val event = runSuspendCatching {
            Webhook.constructEvent(body, stripeSignature, config.signingSecret.value)
        }.getOrElse {
            when (it) {
                is SignatureVerificationException -> {
                    LOGGER.errorAsync(it) { "Error verifying signature" }
                    throw BadRequestException("The request has an invalid signature")
                }

                else -> {
                    LOGGER.errorAsync(it) { "Error processing Stripe webhook event" }
                    throw BadRequestException("Error processing Stripe webhook event")
                }
            }
        }

        return withLoggingContextAsync(
            "eventId" to event.id,
            "eventLivemode" to event.livemode,
        ) {
            LOGGER.debugAsync { "Enqueuing Stripe event for processing" }
            stripeEventEnqueueService.enqueueEvent(body = body, signature = stripeSignature)
        }
    }
}
