package com.nextchaptersoftware.slack.extractor.utils

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.slack.utils.SlackUrlBuilder
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackConversationSummaryPromptExtractorTest {
    private val slackChannelDocumentLoader = SlackChannelDocumentLoader(
        token = GlobalConfig.INSTANCE.providers.slack.botAuthToken.value,
    )

    private val slackConversationSummaryPromptExtractor = SlackConversationSummaryPromptExtractor()

    @Test
    fun `test document loading`() = runTest {
        val slackChannelExternalId = "C07ME5MSY7M"
        val model = slackChannelDocumentLoader.loadChannelDocument(
            channelId = slackChannelExternalId,
            includeReplies = true,
            threadTs = null,
            limit = 100,
        ) { true }

        assertThat(model).isNotNull

        val slackUrlBuilder = SlackUrlBuilder(
            baseHost = "richie.slack.com",
        )

        model?.also {
            val prompt = slackConversationSummaryPromptExtractor.generatePrompt(
                slackChannelDocument = model,
                userQuery = "What is the summary",
                slackUrlBuilder = slackUrlBuilder,
            )
            assertThat(prompt).isNotEmpty()
        }
    }
}
