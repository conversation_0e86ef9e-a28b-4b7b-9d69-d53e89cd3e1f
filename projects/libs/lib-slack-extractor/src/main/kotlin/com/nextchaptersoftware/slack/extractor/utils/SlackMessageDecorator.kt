package com.nextchaptersoftware.slack.extractor.utils

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.slack.extractor.models.SlackMessageModel
import com.nextchaptersoftware.slack.extractor.models.SlackUserModel
import com.nextchaptersoftware.slack.services.SlackMemberResolutionService
import com.slack.api.model.Attachment
import com.slack.api.model.Message
import com.slack.api.model.block.ContextBlock
import com.slack.api.model.block.SectionBlock
import com.slack.api.model.block.composition.TextObject
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

// --------------------------------------------------------------------------
// New helper: extract all human-visible text from a single Attachment.
// --------------------------------------------------------------------------
@Suppress("NestedBlockDepth")
internal fun Attachment.effectiveText(): String {
    val texts = mutableListOf<String>()

    // 1. Top-level text on the attachment
    if (!text.isNullOrBlank()) {
        texts.add(text.trim())
    }

    // 2. Walk every block & pull whatever text is relevant for that type
    blocks?.forEach { block ->
        when (block) {
            is SectionBlock -> {
                // main section text
                block.text?.text?.let { if (it.isNotBlank()) texts.add(it.trim()) }
                // any "fields" within a section block
                block.fields?.forEach { f -> f.text?.let { if (it.isNotBlank()) texts.add(it.trim()) } }
            }

            is ContextBlock -> {
                // context blocks contain TextObjects in their elements list
                block.elements?.forEach { elem ->
                    if (elem is TextObject && !elem.text.isNullOrBlank()) {
                        texts.add(elem.text.trim())
                    }
                }
            }

            // add more block-type cases here as needed (e.g., HeaderBlock)
            else -> {}
        }
    }

    return texts.joinToString(separator = "\n").trim()
}

// --------------------------------------------------------------------------
// Effective text for a Message now delegates to Attachment.effectiveText()
// --------------------------------------------------------------------------
internal fun Message.effectiveText(): String {
    val primaryText = this.text.orEmpty().trim()

    val attachmentsText = attachments
        ?.map { it.effectiveText() }
        ?.filter { it.isNotEmpty() }
        ?.joinToString(separator = "\n")
        .orEmpty()

    return listOf(primaryText, attachmentsText)
        .filter { it.isNotEmpty() }
        .joinToString(separator = "\n")
}

// ------------------- Remainder of file is unchanged logic -----------------

fun Flow<Message>.decorate(
    slackUserCache: SlackUserCache,
): Flow<SlackMessageModel> = map { it.decorate(slackUserCache) }

suspend fun Message.decorate(
    slackUserCache: SlackUserCache,
): SlackMessageModel {
    val user = user?.let { userId ->
        slackUserCache.getUserById(userId = userId)?.let { user ->
            SlackUserModel(
                name = user.name,
                realName = user.realName,
                id = user.id,
                tz = user.tz,
                tzLabel = user.tzLabel,
                tzOffset = user.tzOffset,
            )
        }
    }

    return SlackMessageModel(
        replyCount = replyCount ?: 0,
        text = SlackUserMentionExpander.expandMentions(
            messageText = this.effectiveText(),
        ) { slackUserCache.getUserById(it)?.name },
        type = type,
        ts = ts,
        parentTs = threadTs,
        user = user,
        channelId = channel,
    )
}

/**
 * Lightweight decorate: resolve only the name via SlackMemberResolutionService.
 */
suspend fun Message.decorate(
    orgId: OrgId,
    slackTeamId: SlackTeamId,
    slackMemberResolutionService: SlackMemberResolutionService,
): SlackMessageModel {
    val user = user?.let { userId ->
        slackMemberResolutionService.resolveMemberByUserId(
            orgId = orgId,
            slackTeamId = slackTeamId,
            userId = userId,
        )?.identity?.let { identity ->
            SlackUserModel(
                name = identity.username,
                realName = identity.displayName,
                id = userId,
            )
        }
    }

    return SlackMessageModel(
        replyCount = replyCount ?: 0,
        text = SlackUserMentionExpander.expandMentions(
            messageText = this.effectiveText(),
        ) {
            slackMemberResolutionService.resolveMemberByUserId(
                orgId = orgId,
                slackTeamId = slackTeamId,
                userId = it,
            )?.identity?.let { identity ->
                identity.displayNameOrUsername
            }
        },
        type = type,
        ts = ts,
        parentTs = threadTs,
        user = user,
        channelId = channel,
    )
}
