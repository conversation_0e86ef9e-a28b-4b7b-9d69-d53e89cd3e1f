package com.nextchaptersoftware.slack.extractor.utils

import com.slack.api.methods.Methods.CONVERSATIONS_LIST
import com.slack.api.methods.Methods.CONVERSATIONS_REPLIES
import com.slack.api.methods.MethodsCustomRateLimitResolver
import com.slack.api.methods.MethodsRateLimitTier
import com.slack.api.methods.MethodsRateLimits
import java.util.Optional

class SlackExtractorRateLimiter(
    private val rateLimitMultiplier: Int = 2,
) : MethodsCustomRateLimitResolver {
    override fun getCustomAllowedRequestsPerMinute(teamId: String, methodName: String): Optional<Int> {
        val allowedRequests = when (methodName) {
            CONVERSATIONS_REPLIES, CONVERSATIONS_LIST -> {
                getAllowedRequestsPerMinute(methodName = methodName)?.let {
                    it * rateLimitMultiplier
                }
            }

            else -> null
        }

        return allowedRequests?.let {
            Optional.of(it)
        } ?: Optional.empty()
    }

    override fun getCustomAllowedRequestsForChatPostMessagePerMinute(teamId: String, channel: String): Optional<Int> {
        return Optional.empty()
    }

    override fun getCustomAllowedRequestsForAssistantThreadsSetStatusPerMinute(p0: String?, p1: String?): Optional<Int> {
        return Optional.empty()
    }

    private fun getAllowedRequestsPerMinute(methodName: String): Int? {
        return MethodsRateLimits.lookupRateLimitTier(methodName)?.let {
            MethodsRateLimitTier.getAllowedRequestsPerMinute(it)
        }
    }
}
