package com.nextchaptersoftware.ml.functions

import com.nextchaptersoftware.slack.extractor.models.SlackMessageModel
import com.nextchaptersoftware.slack.extractor.models.SlackUserModel
import com.nextchaptersoftware.slack.utils.SlackUrlBuilder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackThreadRendererTest {
    private val slackUrlBuilder = SlackUrlBuilder(
        baseHost = "ncs.slack.com",
    )

    private val parent = SlackMessageModel(
        replyCount = 0,
        text = "This is the parent message",
        type = "message",
        ts = "1234567890.123456",
        parentTs = "1234567890.123456",
        user = SlackUserModel(
            name = "user",
            realName = "User Name",
            id = "U12345678",
            tz = "America/New_York",
            tzLabel = "Eastern Daylight Time",
            tzOffset = -14400,
        ),
        channelId = "C12345678",
    )

    private val replyMessage1 = SlackMessageModel(
        replyCount = 0,
        text = "This is the first reply",
        type = "message",
        ts = "1234567890.123457",
        parentTs = "1234567890.123456",
        user = SlackUserModel(
            name = "bot",
            realName = "Bot Name",
            id = "U987654321",
            tz = "America/New_York",
            tzLabel = "Eastern Daylight Time",
            tzOffset = -14400,
        ),
        channelId = "C12345678",
    )

    private val replyMessage2 = SlackMessageModel(
        replyCount = 0,
        text = "This is the second reply",
        type = "message",
        ts = "1234567890.123458",
        parentTs = "1234567890.123456",
        user = SlackUserModel(
            name = "user",
            realName = "User Name",
            id = "U12345678",
            tz = "America/New_York",
            tzLabel = "Eastern Daylight Time",
            tzOffset = -14400,
        ),
        channelId = "C12345678",
    )

    private val replyMessage3 = SlackMessageModel(
        replyCount = 0,
        text = "This is the third reply",
        type = "message",
        ts = "1234567890.123459",
        parentTs = "1234567890.123456",
        user = SlackUserModel(
            name = "bot",
            realName = "Bot Name",
            id = "U987654321",
            tz = "America/New_York",
            tzLabel = "Eastern Daylight Time",
            tzOffset = -14400,
        ),
        channelId = "C12345678",
    )

    private val replies = listOf(
        replyMessage1,
        replyMessage2,
        replyMessage3,
    )

    @Test
    fun `render with links`() {
        val rendered = SlackThreadRenderer.render(
            slackUrlBuilder = slackUrlBuilder,
            parent = parent,
            replies = replies,
            renderLinks = true,
        )

        assertThat(rendered).isEqualTo(
            """
            |### [2009-02-13T23:31:30Z] [User Name] [https://ncs.slack.com/archives/C12345678/p1234567890123456?thread_ts=1234567890.123456&cid=C12345678]
            |This is the parent message
            |    #### [2009-02-13T23:31:30Z] [Bot Name] [https://ncs.slack.com/archives/C12345678/p1234567890123457?thread_ts=1234567890.123456&cid=C12345678]
            |    This is the first reply
            |    #### [2009-02-13T23:31:30Z] [User Name] [https://ncs.slack.com/archives/C12345678/p1234567890123458?thread_ts=1234567890.123456&cid=C12345678]
            |    This is the second reply
            |    #### [2009-02-13T23:31:30Z] [Bot Name] [https://ncs.slack.com/archives/C12345678/p1234567890123459?thread_ts=1234567890.123456&cid=C12345678]
            |    This is the third reply
        """.trimMargin(),
        )
    }

    @Test
    fun `render without links`() {
        val rendered = SlackThreadRenderer.render(
            slackUrlBuilder = slackUrlBuilder,
            parent = parent,
            replies = replies,
            renderLinks = false,
        )

        assertThat(rendered).isEqualTo(
            """
            |### [2009-02-13T23:31:30Z] [User Name]
            |This is the parent message
            |    #### [2009-02-13T23:31:30Z] [Bot Name]
            |    This is the first reply
            |    #### [2009-02-13T23:31:30Z] [User Name]
            |    This is the second reply
            |    #### [2009-02-13T23:31:30Z] [Bot Name]
            |    This is the third reply
        """.trimMargin(),
        )
    }
}
