package com.nextchaptersoftware.ml.functions.core

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encodePretty
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.dsac.DsacContext
import com.nextchaptersoftware.test.utils.TestUtils.assertNotNull
import kotlin.reflect.full.isSubclassOf
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.assertThrows

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MLFunctionTest {

    private val orgId by lazy { OrgId.random() }
    private val orgMemberId by lazy { OrgMemberId.random() }
    private val dsacContext by lazy { DsacContext.disabled(orgId = orgId) }

    @BeforeEach
    fun setup() {
        MLFunction.dumpRegistry()
        MLFunction.registerInstance(TestMLFunction)
        MLFunction.registerInstance(TestRAGFunction)
    }

    @Test
    fun `functionDefinitions should extract correct information for value types`() {
        val definitions = MLFunction.functionDefinitions().filter { it.className == "TestMLFunction" }

        // Assuming there is a known subclass with known functions and parameters
        val testFunctionDefinition = assertNotNull(definitions.find { it.name == "testFunctionWithOrgMemberId" })
        assertThat(testFunctionDefinition.className).isEqualTo("TestMLFunction")
        assertThat(testFunctionDefinition.description).isEqualTo(
            """
            This is a test function that takes 1 argument and returns an OrgMemberId.
            """.trimIndent(),
        )
        assertThat(testFunctionDefinition.parameters).hasSize(1)
        assertThat(testFunctionDefinition.parameters[0].name).isEqualTo("arg1")
        assertThat(testFunctionDefinition.parameters[0].description).isEqualTo(
            """
            This is the first argument.
            """.trimIndent(),
        )
        assertThat(testFunctionDefinition.parameters[0].type).isEqualTo("OrgMemberId")
        assertThat(testFunctionDefinition.returnType).isEqualTo("OrgMemberId")
    }

    @Test
    fun `functionDefinitions should extract correct information`() {
        val definitions = MLFunction.functionDefinitions().filter { it.className == "TestMLFunction" }

        // Assuming there is a known subclass with known functions and parameters
        val testFunctionDefinition = assertNotNull(definitions.find { it.name == "testFunction" })
        assertThat(testFunctionDefinition.className).isEqualTo("TestMLFunction")
        assertThat(testFunctionDefinition.description).isEqualTo(
            """
            This is a test function that takes 3 arguments and returns a string.
            """.trimIndent(),
        )
        assertThat(testFunctionDefinition.parameters).hasSize(3)
        assertThat(testFunctionDefinition.parameters[0].name).isEqualTo("arg1")
        assertThat(testFunctionDefinition.parameters[0].description).isEqualTo(
            """
            This is the first argument.
            """.trimIndent(),
        )
        assertThat(testFunctionDefinition.parameters[0].type).isEqualTo("String")
        assertThat(testFunctionDefinition.parameters[1].name).isEqualTo("arg2")
        assertThat(testFunctionDefinition.parameters[1].description).isEqualTo(
            """
            This is the second argument.
            """.trimIndent(),
        )
        assertThat(testFunctionDefinition.parameters[1].type).isEqualTo("Boolean")
        assertThat(testFunctionDefinition.parameters[2].name).isEqualTo("arg3")
        assertThat(testFunctionDefinition.parameters[2].description).isEqualTo(
            """
            This is the third argument.
            """.trimIndent(),
        )
        assertThat(testFunctionDefinition.parameters[2].type).isEqualTo("Int")
        assertThat(testFunctionDefinition.returnType).isEqualTo("String")
    }

    @Test
    fun `functionDefinitionsByClass should have commonFunctions under each class`() {
        val definitionsByClass = MLFunction.functionDefinitionsByClass()
        assertThat(definitionsByClass).hasSize(2)
        assertThat(definitionsByClass.all { clazz -> clazz.any { it.name == "commonFunction" } })
    }

    @Test
    fun `invokeFunction should correctly invoke a function`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        val result = MLFunction.invokeFunction(
            functionInvocation = MLFunctionInvocation(
                className = "TestMLFunction",
                functionName = "testFunction",
                parameters = mapOf(
                    "arg1" to "value1",
                    "arg2" to "true",
                    "arg3" to "3",
                ),
            ),
            executionContext = executionContext,
        )

        assertThat(result).isEqualTo("value1 true 3")
    }

    @Test
    fun `invokeFunction should correctly invoke a function with value type parameters`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        val orgMemberId = OrgMemberId.random()
        val result = MLFunction.invokeFunction(
            functionInvocation = MLFunctionInvocation(
                className = "TestMLFunction",
                functionName = "testFunctionWithOrgMemberId",
                parameters = mapOf(
                    "arg1" to orgMemberId.toString(),
                ),
            ),
            executionContext = executionContext,
        )

        assertThat(result).isEqualTo(orgMemberId)
    }

    @Test
    fun `invokeFunction should throw for missing parameter`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        assertThrows<IllegalArgumentException> {
            MLFunction.invokeFunction(
                functionInvocation = MLFunctionInvocation(
                    "TestMLFunction",
                    "testFunction",
                    mapOf("param1" to "value1"),
                ),
                executionContext = executionContext,
            )
        }
    }

    @Test
    fun `invokeFunction should throw for parameters of wrong type`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        assertThrows<IllegalArgumentException> {
            MLFunction.invokeFunction(
                functionInvocation = MLFunctionInvocation(
                    className = "TestMLFunction",
                    functionName = "testFunction",
                    parameters = mapOf(
                        "arg1" to "value1",
                        "arg2" to "true",
                        "arg3" to "monkey",
                    ),
                ),
                executionContext = executionContext,
            )
        }
    }

    @Test
    fun `invokeFunction for RAG functions should work`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        val result = MLFunction.invokeFunction(
            functionInvocation = MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testFunctionReturningSingleDoc",
                parameters = mapOf(),
            ),
            executionContext = executionContext,
        )

        assertThat(result).isInstanceOf(String::class.java)
        val doc = result as String
        assertThat(doc).isEqualTo("hello")
    }

    @Test
    fun `json function serialization should work`() {
        val json = MLFunction.functionDefinitions().filter { it.className == "TestMLFunction" }.encodePretty()
        assertThat(json).isEqualTo(
            """
            [
                {
                    "className": "TestMLFunction",
                    "name": "testFunction",
                    "description": "This is a test function that takes 3 arguments and returns a string.",
                    "parameters": [
                        {
                            "name": "arg1",
                            "description": "This is the first argument.",
                            "type": "String",
                            "nullable": false
                        },
                        {
                            "name": "arg2",
                            "description": "This is the second argument.",
                            "type": "Boolean",
                            "nullable": false
                        },
                        {
                            "name": "arg3",
                            "description": "This is the third argument.",
                            "type": "Int",
                            "nullable": true
                        }
                    ],
                    "returnType": "String"
                },
                {
                    "className": "TestMLFunction",
                    "name": "testFunctionWithOrgMemberId",
                    "description": "This is a test function that takes 1 argument and returns an OrgMemberId.",
                    "parameters": [
                        {
                            "name": "arg1",
                            "description": "This is the first argument.",
                            "type": "OrgMemberId",
                            "nullable": false
                        }
                    ],
                    "returnType": "OrgMemberId"
                },
                {
                    "className": "TestMLFunction",
                    "name": "commonFunction",
                    "description": "A common function.",
                    "parameters": [],
                    "returnType": "String"
                }
            ]
            """.trimIndent(),
        )
    }

    @Test
    fun `compileToKotlinFormat should format basic function definition correctly`() {
        val definition = MLFunctionDefinition(
            className = "TestClass",
            name = "testFunction",
            description = null,
            parameters = listOf(),
            returnType = "Unit",
            experimental = false,
        )

        val expected = "TestClass::testFunction(): Unit"
        assertThat(definition.compileToKotlinFormat()).isEqualTo(expected)
    }

    @Test
    fun `compileToKotlinFormat should handle multiple parameters correctly`() {
        val definition = MLFunctionDefinition(
            className = "TestClass",
            name = "testFunction",
            description = null,
            parameters = listOf(
                MLFunctionParameter("param1", "Description 1", "Int", nullable = false),
                MLFunctionParameter("param2", "Description 2\nWith multiple lines", "String", nullable = false),
                MLFunctionParameter("param3", "Description 3", "Boolean", nullable = true),
            ),
            returnType = "Boolean",
            experimental = false,
        )

        val expected = """
            TestClass::testFunction(
                /**
                 * Description 1
                 */
                param1: Int,
                /**
                 * Description 2
                 * With multiple lines
                 */
                param2: String,
                /**
                 * Description 3
                 */
                param3: Boolean?,
            ): Boolean
        """.trimIndent()
        assertThat(definition.compileToKotlinFormat()).isEqualTo(expected)
    }

    @Test
    fun `compileToKotlinFormat should format descriptions correctly`() {
        val definition = MLFunctionDefinition(
            className = "",
            name = "testFunction",
            description = "This is a test function.\nWith multiple lines.",
            parameters = listOf(),
            returnType = "Int",
            experimental = false,
        )

        val expected = """
            /**
             * This is a test function.
             * With multiple lines.
             */
            testFunction(): Int
        """.trimIndent()
        assertThat(definition.compileToKotlinFormat()).isEqualTo(expected)
    }

    @Test
    fun `compileToKotlinFormat should handle empty class names correctly`() {
        val definition = MLFunctionDefinition(
            className = "",
            name = "testFunction",
            description = null,
            parameters = listOf(),
            returnType = "String",
            experimental = false,
        )

        val expected = "testFunction(): String"
        assertThat(definition.compileToKotlinFormat()).isEqualTo(expected)
    }

    @Test
    fun `compileToKotlinFormat should omit context parameter`() {
        val menu = MLFunction.functionDefinitions().joinToString("\n") { it.compileToKotlinFormat() }
        assertThat(menu).isEqualTo(
            """
            /**
             * This is a test function that takes 3 arguments and returns a string.
             */
            TestMLFunction::testFunction(
                /**
                 * This is the first argument.
                 */
                arg1: String,
                /**
                 * This is the second argument.
                 */
                arg2: Boolean,
                /**
                 * This is the third argument.
                 */
                arg3: Int?,
            ): String
            /**
             * This is a test function that takes 1 argument and returns an OrgMemberId.
             */
            TestMLFunction::testFunctionWithOrgMemberId(
                /**
                 * This is the first argument.
                 */
                arg1: OrgMemberId,
            ): OrgMemberId
            /**
             * A common function.
             */
            TestMLFunction::commonFunction(): String
            /**
             * ContentForAuthor
             */
            TestRAGFunction::contentForAuthor(
                person: UUID,
            ): String
            /**
             * TestCyclicalFunctionOrgId
             */
            TestRAGFunction::testCyclicalFunctionOrgId(): OrgId
            /**
             * TestCyclicalFunctionPersonId
             */
            TestRAGFunction::testCyclicalFunctionPersonId(
                personId: UUID,
            ): UUID
            /**
             * TestFunctionPersonsForOrg
             */
            TestRAGFunction::testFunctionPersonsForOrg(): List<UUID>
            /**
             * TestFunctionReturningHelloWorld
             */
            TestRAGFunction::testFunctionReturningHelloWorld(): String
            /**
             * TestFunctionReturningMultiDocs
             */
            TestRAGFunction::testFunctionReturningMultiDocs(): List<String>
            /**
             * TestFunctionReturningSingleDoc
             */
            TestRAGFunction::testFunctionReturningSingleDoc(): String
            /**
             * TestFunctionTakingContentAndReturningDoc
             */
            TestRAGFunction::testFunctionTakingContentAndReturningDoc(
                string: String,
            ): String
            /**
             * TestFunctionTakingContextReturningOrgId
             */
            TestRAGFunction::testFunctionTakingContextReturningOrgId(): OrgId
            /**
             * TestGetOrgId
             */
            TestRAGFunction::testGetOrgId(): OrgId
            /**
             * TestGetThreadsForOrg
             */
            TestRAGFunction::testGetThreadsForOrg(): List<UUID>
            /**
             * TestMostProlificAuthor
             */
            TestRAGFunction::testMostProlificAuthor(
                persons: List<UUID>,
                threads: List<UUID>,
            ): UUID
            """.trimIndent(),
        )
    }

    @Test
    fun `test rag function`() {
        val definitions = MLFunction.classes().filter { it.isSubclassOf(TestRAGFunction::class) }.flatMap { it.functionDefinitions() }

        // Assuming there is a known subclass with known functions and parameters
        val testFunctionDefinition = assertNotNull(definitions.find { it.name == "testFunctionReturningSingleDoc" })
        assertThat(testFunctionDefinition.className).isEqualTo("TestRAGFunction")
        assertThat(testFunctionDefinition.description).isNotNull()
        assertThat(testFunctionDefinition.parameters).hasSize(0)
        assertThat(testFunctionDefinition.returnType).isEqualTo("String")
    }

    @Test
    fun `test serialization of function returning multiple documents`() {
        val definitions = MLFunction.classes().filter { it.isSubclassOf(TestRAGFunction::class) }.flatMap { it.functionDefinitions() }

        // Assuming there is a known subclass with known functions and parameters
        val testFunctionDefinition = assertNotNull(definitions.find { it.name == "testFunctionReturningMultiDocs" })
        assertThat(testFunctionDefinition.className).isEqualTo("TestRAGFunction")
        assertThat(testFunctionDefinition.description).isNotNull()
        assertThat(testFunctionDefinition.parameters).hasSize(0)
        assertThat(testFunctionDefinition.returnType).isEqualTo("List<String>")
    }

    @Test
    fun `throws exception when function takes teamId but execution context is not supplied`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        assertThrows<IllegalArgumentException> {
            MLFunction.invokeFunction(
                functionInvocation = MLFunctionInvocation(
                    className = "TestRAGFunction",
                    functionName = "testFunctionTakingTeamId",
                    parameters = mapOf(),
                ),
                executionContext = executionContext,
            )
        }
    }

    @Test
    fun `uses supplied execution context for teamId instead of supplied parameter`() = runTest {
        val result = MLFunction.invokeFunction(
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testFunctionTakingContextReturningOrgId",
                parameters = emptyMap(),
            ),
            MLFunctionExecutionContext(
                orgId = orgId,
                orgMemberId = orgMemberId,
                dsacContext = dsacContext,
            ),
        )

        assertThat(result).isEqualTo(orgId)
    }

    @Test
    fun `execution context can execute chains`() = runTest {
        val executionContext = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        MLFunction.invokeFunction(
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testFunctionReturningHelloWorld",
                resultName = "HelloWorldResult",
                parameters = mapOf(),
            ),
            executionContext,
        )

        val secondResult = MLFunction.invokeFunction(
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testFunctionTakingContentAndReturningDoc",
                parameters = mapOf("string" to "[HelloWorldResult]"),
            ),
            executionContext,
        )

        assertThat(secondResult).isInstanceOf(String::class.java)
        assertThat(secondResult as String).isEqualTo("hello world")
    }

    @Test
    fun `test graph detects cycles`() = runTest {
        val functions = listOf(
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testCyclicalFunctionTeamId",
                resultName = "teamId",
                parameters = mapOf("teamId" to "[personId]"),
            ),
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testCyclicalFunctionPersonId",
                resultName = "personId",
                parameters = mapOf("personId" to "[teamId]"),
            ),
        )

        assertThrows<IllegalArgumentException> {
            MLFunctionExecutionGraph(functions)
        }
    }

    @Test
    fun `test graph execution`() = runTest {
        val functions = listOf(
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testFunctionPersonsForOrg",
                resultName = "persons",
                parameters = emptyMap(),
            ),
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testGetThreadsForOrg",
                resultName = "threads",
                parameters = emptyMap(),
            ),
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testMostProlificAuthor",
                resultName = "author",
                parameters = mapOf(
                    "persons" to "[persons]",
                    "threads" to "[threads]",
                ),
            ),
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "contentForAuthor",
                resultName = "content",
                parameters = mapOf("person" to "[author]"),
            ),
            MLFunctionInvocation(
                className = "TestRAGFunction",
                functionName = "testFunctionTakingContentAndReturningDoc",
                parameters = mapOf("string" to "[content]"),
            ),
        )

        val graph = MLFunctionExecutionGraph(functions)
        val context = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        val result = graph.execute(context).firstNotNullOf {
            when (it is String) {
                true -> it
                false -> null
            }
        }

        assertThat(result).isEqualTo(orgId.toString())
    }

    @Test
    fun `test class with property`() = runTest {
        val ragFunctionClassWithProperty = TestRAGFunctionWithClassProperty("hello world")
        MLFunction.registerInstance(ragFunctionClassWithProperty)

        val functions = listOf(
            MLFunctionInvocation(
                className = "TestRAGFunctionWithClassProperty",
                functionName = "testFunctionReturningSingleDoc",
                parameters = emptyMap(),
            ),
        )

        val graph = MLFunctionExecutionGraph(functions)
        val context = MLFunctionExecutionContext(
            orgId = orgId,
            orgMemberId = orgMemberId,
            dsacContext = dsacContext,
        )
        val result = graph.execute(context).firstNotNullOf {
            when (it is String) {
                true -> it
                false -> null
            }
        }

        assertThat(result).isEqualTo("hello world")
    }

    @Test
    fun `functionDefinitionsByProviders should filter and return correct definitions`() {
        val enabledProviders = setOf(Provider.Linear, Provider.GitHub, Provider.Jira)
        val definitions = MLFunction.functionDefinitionsForProviders(enabledProviders)
        assertThat(definitions).isNotEmpty
        assertThat(definitions.all { it.className == "TestMLFunction" }).isTrue()
    }

    @Test
    fun `functionDefinitionsByClassAndProviders should filter and return correct definitions by class`() {
        val enabledProviders = setOf(Provider.GitHub, Provider.GitLab, Provider.Linear)
        val definitionsByClass = MLFunction.functionDefinitionsByClassForProviders(enabledProviders)
        assertThat(definitionsByClass).isNotEmpty
        assertThat(definitionsByClass.all { clazz -> clazz.any { it.className == "TestMLFunction" } }).isTrue()
    }
}
