package com.nextchaptersoftware.notification.services

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSocialNetwork
import com.nextchaptersoftware.db.ModelBuilders.makeTeamInvitee
import com.nextchaptersoftware.db.ModelBuilders.makeUserEngagement
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration.Companion.days
import kotlin.time.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class PeerInviteSuggestionServiceTest : DatabaseTestsBase() {

    private val inviteSuggestionService = PeerInviteSuggestionService(notificationEventEnqueueService = mock())
    private lateinit var org: OrgDAO
    private lateinit var installation: InstallationDAO
    private lateinit var scmTeam: ScmTeamDAO

    private suspend fun setup() {
        org = makeOrg()
        installation = makeInstallation(org = org)
        scmTeam = makeScmTeam(installation = installation)
    }

    private suspend fun makeEngagedMember() = makeMember(installation = installation).also { member ->
        val now = Instant.nowWithMicrosecondPrecision()
        repeat(5) {
            makeUserEngagement(member = member, createdAt = now.minus(it.days))
        }
    }

    @Test
    fun `getInviteSuggestions returns peers that do not have accounts`() = suspendingDatabaseTest {
        setup()
        val currentMember = makeMember(installation = installation)

        val nonPeer = makeMember(installation = installation).asDataModel()
        val peerWithAccount = makePerson().let { person ->
            makeIdentity(person = person).let { identity ->
                makeMember(installation = installation, identity = identity).also { member ->
                    makeSocialNetwork(installation = installation, reviewer = currentMember.orgMember, author = member.orgMember, weight = 0.9F)
                }
            }
        }.asDataModel()
        val peerWithoutAccount = makeMember(installation = installation).also { member ->
            makeSocialNetwork(installation = installation, reviewer = currentMember.orgMember, author = member.orgMember, weight = 0.9F)
        }.asDataModel()

        inviteSuggestionService.getInviteSuggestions(
            orgId = org.idValue,
            orgMemberId = currentMember.orgMemberId,
            sociallyConnected = true,
        ).also { result ->
            assertThat(result.map { it.member.id }).doesNotContain(nonPeer.id, peerWithAccount.id)
            assertThat(result.map { it.member.id }).containsExactlyInAnyOrder(peerWithoutAccount.id)
        }
    }

    @Test
    fun `getInviteSuggestions exclude peers that have been invited by current user`() = suspendingDatabaseTest {
        setup()
        val currentMember = makeOrgMember(org = installation.org)

        val peerWithoutAccount = makeOrgMember(org = installation.org).also { orgMember ->
            makeMember(installation = installation, orgMember = orgMember)
            makeSocialNetwork(installation = installation, reviewer = currentMember, author = orgMember, weight = 0.9F)
        }

        inviteSuggestionService.getInviteSuggestions(
            orgId = org.idValue,
            orgMemberId = currentMember.idValue,
            sociallyConnected = true,
        ).also { result ->
            assertThat(result).hasSize(1)
            assertThat(result.map { it.orgMember.id }).containsExactlyInAnyOrder(peerWithoutAccount.idValue)
        }

        // now invite the peer
        makeTeamInvitee(org = scmTeam.org, sender = currentMember, invitee = peerWithoutAccount)
        inviteSuggestionService.getInviteSuggestions(
            orgId = org.idValue,
            orgMemberId = currentMember.idValue,
            sociallyConnected = true,
        ).also { result ->
            assertThat(result).isEmpty()
        }
    }

    @Test
    fun `suggestPeersToInvite skips non-engaged members`() = suspendingDatabaseTest {
        setup()
        val currentMember = makeMember(installation = installation)
        val person = makePerson()

        makeMember(installation = installation).also { member ->
            makeSocialNetwork(installation = installation, reviewer = currentMember.orgMember, author = member.orgMember, weight = 0.9F)
        }

        inviteSuggestionService.suggestPeersToInvite(
            orgId = org.idValue,
            orgMemberId = currentMember.orgMemberId,
            personId = person.idValue,
        ).also { result ->
            assertThat(result).isEmpty()
        }
    }

    @Test
    fun `suggestPeersToInvite makes suggestions for engaged members`() = suspendingDatabaseTest {
        setup()
        val currentMember = makeEngagedMember()
        val person = makePerson()

        val peerWithoutAccount = makeMember(installation = installation).also { member ->
            makeSocialNetwork(installation = installation, reviewer = currentMember.orgMember, author = member.orgMember, weight = 0.9F)
        }

        inviteSuggestionService.suggestPeersToInvite(
            orgId = org.idValue,
            orgMemberId = currentMember.orgMemberId,
            personId = person.idValue,
        ).also { result ->
            assertThat(result.map { it.member.id }).containsExactlyInAnyOrder(peerWithoutAccount.idValue)
        }
    }

    @Test
    fun `getBestEmailForInvitees gets most popular email`() = suspendingDatabaseTest {
        setup()
        val primaryMember = makeMember(
            scmTeam = scmTeam,
            identity = makeIdentity(provider = Provider.GitHub, emails = listOf(EmailAddress.of("<EMAIL>"))),
        )
        val otherMember1 = makeMember(
            scmTeam = scmTeam,
            isPrimaryMember = false,
            association = primaryMember.idValue,
            identity = makeIdentity(
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
                provider = Provider.Confluence,
            ),
        ).asDataModel().id
        val otherMember2 = makeMember(
            scmTeam = scmTeam,
            isPrimaryMember = false,
            association = primaryMember.idValue,
            identity = makeIdentity(
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
                provider = Provider.Slack,
            ),
        ).asDataModel().id

        inviteSuggestionService.getBestEmailForInvitees(primaryMemberIds = listOf(primaryMember.idValue)).also { result ->
            assertThat(result).hasSize(1)
            assertThat(result[primaryMember.id.value]).isEqualTo("<EMAIL>")
        }
        inviteSuggestionService.getBestEmailForInvitees(primaryMemberIds = listOf(otherMember1, otherMember2)).also { result ->
            assertThat(result).isEmpty()
        }
    }
}
