package com.nextchaptersoftware.crypto

import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray
import com.nextchaptersoftware.utils.Base64.base64Encode
import com.sksamuel.hoplite.Secret
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class AESCryptoSystemTest {

    companion object {
        private val KEY = AESCryptoSystem.generateKey()
        private val SECRET = Secret("Hello, world!")
    }

    @Test
    fun `key generation`() {
        assertThat(KEY.algorithm).isEqualTo("AES")
        assertThat(KEY.encoded).hasSize(AESCryptoSystem.KEY_BIT_LENGTH / 8)
    }

    @Test
    fun `imported key is same as original exported key`() {
        val exportedKey = AESCryptoSystem.exportKey(KEY)
        val importedKey = AESCryptoSystem.importKey(exportedKey)

        assertThat(importedKey.algorithm).isEqualTo(KEY.algorithm)
        assertThat(importedKey.encoded).isEqualTo(KEY.encoded)
        assertThat(importedKey.format).isEqualTo(KEY.format)
    }

    @Test
    fun `encrypts data`() {
        AESCryptoSystem.AESEncryption(KEY).encrypt(SECRET).also {
            assertThat(it.value).hasSizeGreaterThan(1)
        }
    }

    @Test
    fun `decrypting encrypted data should return original data -- as string`() {
        val encryptedData = AESCryptoSystem.AESEncryption(KEY).encrypt(SECRET)
        val decryptedData = AESCryptoSystem.AESDecryption(KEY).decrypt(encryptedData)
        assertThat(decryptedData).isEqualTo(SECRET)
    }

    @Test
    fun `decrypting encrypted data should return original data -- as bytes`() {
        val secretAsBytes = SECRET.value.toByteArray()

        val encryptedData = AESCryptoSystem.AESEncryption(KEY).encryptBytes(secretAsBytes)
        val decryptedData = AESCryptoSystem.AESDecryption(KEY).decryptAsByteArray(encryptedData)
        assertThat(decryptedData).isNotSameAs(secretAsBytes)
        assertThat(decryptedData).isEqualTo(secretAsBytes)
    }

    @Test
    fun `decrypting encrypted data should return original data -- empty string`() {
        val original = Secret("")
        AESCryptoSystem.AESEncryption(KEY).encrypt(original).also { encryptedData ->
            AESCryptoSystem.AESDecryption(KEY).decrypt(encryptedData).also { decryptedData ->
                assertThat(decryptedData).isEqualTo(original)
            }
        }
    }

    @Test
    fun `decrypting encrypted data should return original data -- special characters`() {
        val original = Secret("🔒✅")
        AESCryptoSystem.AESEncryption(KEY).encrypt(original).also { encryptedData ->
            AESCryptoSystem.AESDecryption(KEY).decrypt(encryptedData).also { decryptedData ->
                assertThat(decryptedData).isEqualTo(original)
            }
        }
    }

    @Test
    fun `decrypting encrypted data should return original data -- long input`() {
        val original = Secret(SECRET.value.repeat(1000))
        AESCryptoSystem.AESEncryption(KEY).encrypt(original).also { encryptedData ->
            AESCryptoSystem.AESDecryption(KEY).decrypt(encryptedData).also { decryptedData ->
                assertThat(decryptedData).isEqualTo(original)
            }
        }
    }

    @Test
    fun `decrypting encrypted data should return original data -- using imported key`() {
        val exportedKey = AESCryptoSystem.exportKey(KEY)
        val importedKey = AESCryptoSystem.importKey(exportedKey)

        val encryptedData = AESCryptoSystem.AESEncryption(KEY).encrypt(SECRET)
        val decryptedData = AESCryptoSystem.AESDecryption(importedKey).decrypt(encryptedData)
        assertThat(decryptedData).isEqualTo(SECRET)
    }

    @Test
    fun `decrypting with incorrect key should throw an exception`() {
        val encryptedData = AESCryptoSystem.AESEncryption(KEY).encrypt(SECRET)
        val wrongKey = AESCryptoSystem.generateKey()
        assertThrows<CryptoException> {
            AESCryptoSystem.AESDecryption(wrongKey).decrypt(encryptedData)
        }
    }

    @Nested
    inner class ChannelTests {
        private val secretKey = AESCryptoSystem.importKey("gSDNNuhCRKgFrVi4jAyRB1yXUuO2dijIdO+XHXmFg5Q=")

        @Test
        fun `test channel decryption from Python`() {
            val decryption = AESCryptoSystem.AESDecryption(secretKey)
            val base64EncodedCiphertext = "y3M4P/UBQeCmVy4AHsjP0X6CL6fv9bg/WzOZTedI8Aj7Vn/jBiUrsqEsUhLHYczD7GaiNyU="
            val ciphertext = Ciphertext(base64EncodedCiphertext.base64DecodeAsByteArray())
            decryption.decrypt(ciphertext).also {
                assertThat(it.value).isEqualTo("Hello, world from Python!")
            }
        }

        @Test
        fun `test channel encryption`() {
            val encryption = AESCryptoSystem.AESEncryption(secretKey)
            encryption.encrypt(Secret("Hello, world from Kotlin!"))
                .value
                .base64Encode()
                .also { println(it) }
        }
    }
}
