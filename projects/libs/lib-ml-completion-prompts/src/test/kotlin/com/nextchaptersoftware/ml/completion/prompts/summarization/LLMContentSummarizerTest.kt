package com.nextchaptersoftware.ml.completion.prompts.summarization

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.embedding.models.EmbeddingDocumentKey
import com.nextchaptersoftware.insight.index.model.InsightIndexContent
import com.nextchaptersoftware.insight.index.model.ThreadInsightIndexContentModel
import com.nextchaptersoftware.ml.api.MachineLearningApiConfiguration
import com.nextchaptersoftware.ml.api.MachineLearningApiProvider
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import java.util.UUID
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class LLMContentSummarizerTest {
    private val contents = """
Is there anyway to point our “local” services to dev db? Specifically Admin console + Notificaiton service?\nEverytime I try running our local stack services, my DB schema is always out of date. So I drop it and spend an hour trying to fix things.\n\n\n\n\n\n\n\nMahdi\n  3 hours ago\nI have it on my list but didn’t get around to it\n\n\nMatt Adam\n  3 hours ago\nYeah this would be nice\n\n\nRichie\n  3 hours ago\nthat sounds like chaos\n\n\nRichie\n  3 hours ago\nMahdi, you mean local stack can talk to DEV AWS compute, not DEV DBs.\n\n\nRichie\n  3 hours ago\nIf local stacks had access to DEV dbs, then all hell would break loose\n\n\nMatt Adam\n  3 hours ago\nI'm totally open to alternatives, but at this point any time I want to a small change to the admin UI, it eats up half a day trying to cobble a local stack into the right shape to actually run it.\n\n\nMahdi\n  3 hours ago\nHow about the ability to import dev db into local stack ?\n:+1:\n1\n\n\n\nRichie\n  3 hours ago\nI’m sure there are alternatives. definitely do not share DEV DBs though.\n\n\nRichie\n  3 hours ago\ndump and load might work\n\n\nMahdi\n  3 hours ago\nWhat I was talking about was using Dev services not direct DB access.\n\n\nJeff\n  3 hours ago\nCould we take a snapshot of the Dev DB and use that as a stub / launching point for local?\n\n\nJeff\n  3 hours ago\nThe largest pain point for me is starting from scratch everytime.\n\n\nMahdi\n  3 hours ago\nIf the db is small enough we should be able to. Exporting the data is already being done via backups  (edited)\n\n\nMahdi\n  3 hours ago\nIt wouldn’t address the issues with ml pipelines or ability to send test emails though\n\n\nJeff\n  3 hours ago\nI don’t mind running the services locally to test emails (I don’t know about ml pipeline)\n\n\nMatt Adam\n  3 hours ago\nIt would go out of date pretty quickly whenever we change db schema too\n\n\nMatt Adam\n  3 hours ago\n(though hooking into a backup system is maybe the right way to handle this?)\n\n\nRichie\n  3 hours ago\ndump and load would likely work. it’d take a minute, so you’d just redo if schema drifted.\n\n\nMahdi\n  3 hours ago\nBackups are stored on S3. So it’s just a file download. I am looking to see how big are they\n\n\nRichie\n  3 hours ago\nThe local DB and DEV db names are different (not sure why), so that needs to be solved too\n\n\nMahdi\n  3 hours ago\nDev DB is about 1GB\n\n\nMahdi\n  3 hours ago\nHow about we standardize one of your local DB exports and commit it to GitLFS. Then on every startup we can load that data ?\n\n\nRichie\n  3 hours ago\nfails when schema changes\n\n\nMahdi\n  3 hours ago\nI hate myself for saying this…how about an actual (small) clone of the Dev DB running in Dev ? I can have a lambda function refresh it from latest Dev backup every few hours. Then you can point your localstacks at it\n\n\nRichie\n  3 hours ago\noverengineering :slightly_smiling_face:\n:+1:\n1\n\n\n\nMahdi\n  3 hours ago\nLast idea, I promise….\nWe publish a docker image built by our CI pre-loaded with Dev DB. Refreshed nightly!\nIt integrates seamlessly to your existing workflow (edited)\n\n\nRashin Arab\n  1 hour ago\nFrom what I'm gathering the real problem is not any of this, is tht migrtion of schemas fails on localstack. (edited)\n\n\nRashin Arab\n  1 hour ago\nIn particular with hard migrations that force nully columns into non-null columns. Besides that, anything is possible. :slightly_smiling_face: (edited)\n\n\nRichie\n  32 minutes ago\nTypically you’d define a sequential upgrade path of schema migrations so that very old instances could self upgrade; total overkill imo since each of those need tests.\nTwo low cost solutions:\nDump and load DEV DB when you want to play with real data instead of booting a personal stack.\nBring back the fixtures for local stack, so that when you drop the local DB it boots up some dummy team members so that you can log in.\nReply don’t think we should spend much time on this though because there are much much bigger problems
    """.trimIndent()

    private val slackThreadHistory = """
        ## SUMMARIZE THIS SLACK HISTORY
        Do not just quote the messages; summarize them together in your own words.

        For each item in your summary, include the date and time when the item occurred.
        Even though the date and time is presented to YOU in ISO 8601, you should format the date in natural language for you summary:
            "on Monday at 3:02 PM".
        Dates presented to you for each message are in UTC, but you must convert them to Pacific Daylight Time in your response.

        The messages below, which you will summarize, are formatted as follows:
        '''
        ### [ISO 8601 Date/Time] [User Name] [Link to Message]
        [Message Text]
            #### [ISO 8601 Date/Time] [User Name] [Link to Reply Message]
            [Threaded Reply Text]
        '''

        SUMMARIZE the following message history in your own words. Message history is enclosed in triple single quotes:

        '''
        ### [2024-09-25T02:19:21Z] [Peter Werry]
        <@U045NBEQDMF> are you ok now?
            #### [2024-09-25T02:19:24Z] [Unblocked] [
            >>>are you ok now? Sorry, I can't answer your question right now. The Unblocked team has been notified and is working on a solution.
            #### [2024-09-25T02:29:06Z] [Rashin Arab]
            at lesat it's not repeating messages now
            #### [2024-09-25T02:29:17Z] [Rashin Arab]
            Changes are delploying soon
            #### [2024-09-25T02:34:01Z] [Peter Werry]
            :+1:
            #### [2024-09-25T03:12:37Z] [Rashin Arab]
            <@U045NBEQDMF> Who is peter?
            #### [2024-09-25T03:12:39Z] [Unblocked]
            >>>Who is peter? Peter Werry is a member of the Next Chapter Software Org team. He has been involved in various discussions and contributions, as seen in several Slack conversations and pull requests. For example, he has participated in discussions about fixing bugs and deploying changes, such as in <null|this Slack conversation> and <null|this pull request>.

            Is there something specific you need to know about Peter or his contributions? *References:* <https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ccdfb12b-aad2-4858-b42e-4fcc32a78758|https://github.com/NextChapterSoftware/unblocke...> | <https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/ecd0c020-f6b2-4e16-9a08-5f2759396294|https://github.com/NextChapterSoftware/unblocke...> *Suggested follow-up questions:* How long has Peter been with the Next Chapter Software Org? Ask button Your feedback improves your answers. This was: Great button Okay button Wrong button
            #### [2024-09-25T03:16:47Z] [Unblocked]
            >>>How long has Peter been with the Next Chapter Software Org? Peter Werry has been involved with Next Chapter Software Org at least since April 2022. He has contributed to various projects and discussions, as evidenced by his participation in multiple pull requests and Slack conversations. For example, he authored <https://github.com/NextChapterSoftware/unblocked/pull/15119|PR #15119> on August 30, 2024, and was active in Slack discussions as early as April 6, 2022, regarding the macOS video app setup. *References:* <https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/pullRequest/7d2780b4-2e9a-461e-8160-28913738b46f|Add OrgMemberAndPerson> | <https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/36f224af-46c4-4453-935c-c60575869960|https://github.com/NextChapterSoftware/unblocke...> | <https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/03b4c150-c904-42dc-8f2b-5dd95078b7aa|https://github.com/NextChapterSoftware/unblocke...> *Suggested follow-up questions:* What roles or responsibilities has Peter taken on during his time at Next Chapter Software Org? Ask button Could you elaborate on the nature of the discussions Peter participated in on Slack? Ask button Your feedback improves your answers. This was: Great button Okay button Wrong button
            #### [2024-09-25T03:41:03Z] [Peter Werry]
            <@U045NBEQDMF> summarize this thread
            #### [2024-09-25T03:41:06Z] [Unblocked]
            &gt;&gt;&gt;summarize this thread Unblocked is thinking...
        '''
    """.trimIndent()

    private val threadInsightIndexContentModel = ThreadInsightIndexContentModel(
        id = UUID.randomUUID(),
        orgId = OrgId.random(),
        authorId = OrgMemberId.random(),
        documentId = EmbeddingDocumentKey.BLANK_KEY.asKey,
        provider = Provider.Unblocked,
        messages = listOf(
            InsightIndexContent(
                id = UUID.randomUUID(),
                content = listOf(contents),
                authorId = MemberId.random(),
                authorOrgMemberId = OrgMemberId.random(),
                authorDisplayName = null,
                createdAt = Instant.nowWithMicrosecondPrecision(),
                modifiedAt = Instant.nowWithMicrosecondPrecision(),
            ),
        ),
        pullRequestContent = null,
        thread = mock(),
        createdAt = Instant.nowWithMicrosecondPrecision(),
        authorDisplayName = "blah",
        repoFullName = null,
        installationId = InstallationId.random(),
        groupId = null,
        insightType = InsightType.Answer,
        title = "blah",
        summary = null,
        repo = null,
    )

    private val contentSummarizer = LLMContentSummarizer(
        completionService = MachineLearningCompletionService(
            machineLearningApiProvider = MachineLearningApiProvider(
                config = MachineLearningApiConfiguration(
                    baseApiUri = Url(GlobalConfig.INSTANCE.machineLearning.defaultDeployment.baseApiUri),
                ),
            ),
        ),
    )

    private val threadSummarizer = LLMThreadContentSummarizer(contentSummarizer)

    @Disabled
    @Test
    fun `test summarization`() = runTest {
        val summarization = threadSummarizer.summarize(context = threadInsightIndexContentModel)
        println(summarization)
        assertThat(summarization).isNotEmpty()
    }

    @Disabled
    @Test
    fun `raw summarization`() = runTest {
        val summarization = contentSummarizer.summarize(slackThreadHistory)
        println(summarization)
        assertThat(summarization).isNotEmpty()
    }
}
