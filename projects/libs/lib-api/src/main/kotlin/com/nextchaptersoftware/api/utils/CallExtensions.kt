package com.nextchaptersoftware.api.utils

import com.nextchaptersoftware.api.models.ApiError
import com.nextchaptersoftware.auth.ci.CIJwt
import com.nextchaptersoftware.ktor.CustomHeaders
import com.nextchaptersoftware.utils.epoch
import io.ktor.http.ContentType
import io.ktor.http.Headers
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.principal
import io.ktor.server.response.header
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import kotlinx.datetime.Instant

object CallExtensions {

    val ApplicationCall.isCIContextAudience: Boolean
        get() = CIJwt.isCIContextAudience(principal<JWTPrincipal>()?.audience)

    suspend fun ApplicationCall.respondJson(
        status: HttpStatusCode? = null,
        provider: suspend () -> String,
    ) {
        respondText(
            status = status,
            contentType = ContentType.Application.Json,
            provider = provider,
        )
    }

    /*
     * Error responses
     */
    suspend inline fun ApplicationCall.respondUnauthorized() =
        respondWithError(status = HttpStatusCode.Unauthorized)

    suspend inline fun ApplicationCall.respondBadRequest() =
        respondWithError(status = HttpStatusCode.BadRequest)

    suspend inline fun ApplicationCall.respondForbidden() =
        respondWithError(status = HttpStatusCode.Forbidden)

    suspend inline fun ApplicationCall.respondNotImplemented() =
        respondWithError(status = HttpStatusCode.NotImplemented)

    suspend inline fun ApplicationCall.respondNotFound() =
        respondWithError(status = HttpStatusCode.NotFound)

    suspend inline fun ApplicationCall.respondWithError(status: HttpStatusCode) {
        val error = ApiError(status = status.value, detail = status.description)
        respondWithStatus(status = status, message = error)
    }

    /*
     * Successful responses
     */
    suspend inline fun ApplicationCall.respondAccepted() =
        respondWithStatus(HttpStatusCode.Accepted)

    suspend inline fun ApplicationCall.respondNoContent() =
        respondWithStatus(HttpStatusCode.NoContent)

    suspend inline fun <reified T : Any> ApplicationCall.respondCreated(message: T) {
        respondWithStatus(
            status = HttpStatusCode.Created,
            message = message,
        )
    }

    suspend inline fun ApplicationCall.respondWithStatus(status: HttpStatusCode) {
        respond(status = status, message = status.description)
    }

    suspend inline fun <reified T : Any> ApplicationCall.respondWithStatus(status: HttpStatusCode, message: T) {
        respond(status = status, message = message)
    }

    /**
     * Append the given headers to the HTTP response
     */
    fun ApplicationCall.addResponseHeader(headers: Headers) {
        headers.forEach { name, values ->
            response.headers.append(name, values.joinToString(", "))
        }
    }

    fun ApplicationCall.addLatestModifiedToResponseHeader(latestModifieds: List<Instant>, from: Instant?) {
        val latestModified = latestModifieds.fold(from ?: Instant.epoch) { latest, instant ->
            when {
                instant > latest -> instant
                else -> latest
            }
        }
        response.header(CustomHeaders.LAST_MODIFIED, latestModified.toString())
    }
}
