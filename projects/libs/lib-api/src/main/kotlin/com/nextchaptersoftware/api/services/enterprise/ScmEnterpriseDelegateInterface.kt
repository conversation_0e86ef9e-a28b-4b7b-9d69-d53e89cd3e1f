package com.nextchaptersoftware.api.services.enterprise

import com.nextchaptersoftware.api.models.EnterpriseProvider
import com.nextchaptersoftware.api.models.EnterpriseProviderOutcome
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.scm.apps.AppManifestNegotiation

interface ScmEnterpriseDelegateInterface {

    suspend fun createBitbucketDataCenterEnterpriseProvider(
        orgId: OrgId?,
        applicationId: String,
        displayName: String,
        encryptedApplicationSecret: String,
    ): EnterpriseProviderOutcome

    suspend fun createGitHubEnterpriseProvider(
        orgId: OrgId?,
        code: String,
        negotiation: AppManifestNegotiation,
    ): EnterpriseProvider

    suspend fun createGitLabEnterpriseProvider(
        orgId: OrgId?,
        applicationId: String,
        displayName: String,
        encryptedApplicationSecret: String,
    ): EnterpriseProviderOutcome
}
