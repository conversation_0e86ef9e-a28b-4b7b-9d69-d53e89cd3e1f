package com.nextchaptersoftware.api.services

import com.nextchaptersoftware.activemq.ActiveMQProducer.ActiveMQProducerSession
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.models.Provider.GitHub
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.event.queue.payloads.EventPayloadCompressor
import com.nextchaptersoftware.membership.MemberService
import com.nextchaptersoftware.notification.events.email.models.EmailTrigger
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationEvent
import com.nextchaptersoftware.notification.events.queue.payloads.NotificationOrgEvent
import com.nextchaptersoftware.types.EmailAddress
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

class EmailServiceTest : DatabaseTestsBase() {
    private val eventPayloadCompressor = EventPayloadCompressor()

    private fun setup(): Pair<EmailService, ActiveMQProducerSession> {
        val messageProducer = mock<ActiveMQProducerSession>()
        val eventEnqueueService = StandardEventEnqueueService(messageProducer = messageProducer)

        return EmailService(
            personEmailPreferencesService = PersonEmailPreferencesService(
                personStore = Stores.personStore,
                personEmailPreferencesStore = Stores.personEmailPreferencesStore,
            ),
            notificationEventEnqueueService = NotificationEventEnqueueService(eventEnqueueService = eventEnqueueService),
            memberService = MemberService(),
        ) to messageProducer
    }

    @Test
    fun `validate invite emails are being sent for participants with accounts`() = suspendingDatabaseTest {
        val (service, sqsProvider) = setup()
        val author = ModelBuilders.makePerson()
        val org = ModelBuilders.makeOrg()
        val scmTeam = ModelBuilders.makeScmTeam(org = org)
        val authorIdentity = ModelBuilders.makeIdentity(
            person = author,
            provider = GitHub,
        )
        val thread = ModelBuilders.makeThread(org = org)
        ModelBuilders.makeMember(identity = authorIdentity, scmTeam = scmTeam)

        val person1 = ModelBuilders.makePerson()
        val identity1 = ModelBuilders.makeIdentity(person = person1)
        val member1 = ModelBuilders.makeMember(identity = identity1, scmTeam = scmTeam, isPrimaryMember = true, isCurrentMember = true)
        val threadParticipant1 = ModelBuilders.makeThreadParticipant(thread = thread, member = member1)

        val person2 = ModelBuilders.makePerson()
        val identity2 = ModelBuilders.makeIdentity(person = person2)
        val member2 = ModelBuilders.makeMember(identity = identity2, scmTeam = scmTeam, isPrimaryMember = true, isCurrentMember = true)
        val threadParticipant2 = ModelBuilders.makeThreadParticipant(thread = thread, member = member2)

        service.sendThreadInviteEmails(
            sender = authorIdentity.id.value,
            orgId = org.idValue,
            threadId = thread.idValue,
            orgMemberAndEmails = listOf(
                threadParticipant1.asDataModel().let { OrgMemberAndEmail(it.orgMemberId, null) },
                threadParticipant2.asDataModel().let { OrgMemberAndEmail(it.orgMemberId, null) },
            ),
            trigger = EmailTrigger.API_CALL,
        )

        val captor = argumentCaptor<String>()
        verify(sqsProvider, Mockito.times(1)).sendMessage(captor.capture(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull())
        val threadInviteEmailPayload = eventPayloadCompressor.decompressBody(captor.firstValue)
            .decode<NotificationEvent>() as NotificationOrgEvent.ThreadInviteEmailEvent
        assertThat(threadInviteEmailPayload.recipients).containsExactlyInAnyOrder(
            threadParticipant1.member?.identity?.primaryEmail?.let(EmailAddress::of),
            threadParticipant2.member?.identity?.primaryEmail?.let(EmailAddress::of),
        )
    }

    @Test
    fun `validate invite join emails are being sent for participants who are members but no account`() = suspendingDatabaseTest {
        val (service, sqsProvider) = setup()
        val author = ModelBuilders.makePerson()
        val org = ModelBuilders.makeOrg()
        val scmTeam = ModelBuilders.makeScmTeam(org = org)
        val authorIdentity = ModelBuilders.makeIdentity(
            person = author,
            provider = GitHub,
        )
        val thread = ModelBuilders.makeThread(org = org)
        ModelBuilders.makeMember(identity = authorIdentity, scmTeam = scmTeam)

        val identity1 = ModelBuilders.makeIdentity()
        val member1 = ModelBuilders.makeMember(identity = identity1, scmTeam = scmTeam, isCurrentMember = true)
        val threadParticipant1 = ModelBuilders.makeThreadParticipant(thread = thread, member = member1)
        val orgMemberAndEmail = OrgMemberAndEmail(
            orgMemberId = threadParticipant1.asDataModel().orgMemberId,
            email = EmailAddress.of("<EMAIL>"),
        )
        service.sendThreadInviteEmails(
            sender = authorIdentity.id.value,
            orgId = org.idValue,
            threadId = thread.idValue,
            orgMemberAndEmails = listOf(orgMemberAndEmail),
            trigger = EmailTrigger.API_CALL,
        )

        val captor = argumentCaptor<String>()
        verify(sqsProvider, Mockito.times(1)).sendMessage(captor.capture(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull())
        val threadInviteJoinEmailPayload = eventPayloadCompressor.decompressBody(captor.firstValue)
            .decode<NotificationEvent>() as NotificationOrgEvent.ThreadInviteJoinEmailEvent
        assertThat(threadInviteJoinEmailPayload.recipients).containsExactlyInAnyOrder(
            orgMemberAndEmail.email,
        )
    }

    @Test
    fun `validate emails are not being sent if preferences are disabled`() = suspendingDatabaseTest {
        val (service, sqsProvider) = setup()
        val author = ModelBuilders.makePerson()
        val org = ModelBuilders.makeOrg()
        val scmTeam = ModelBuilders.makeScmTeam(org = org)
        val authorIdentity = ModelBuilders.makeIdentity(
            person = author,
            provider = GitHub,
        )
        val thread = ModelBuilders.makeThread(org = org)
        ModelBuilders.makeMember(identity = authorIdentity, scmTeam = scmTeam)

        val person1 = ModelBuilders.makePerson()
        val identity1 = ModelBuilders.makeIdentity(person = person1)
        val member1 = ModelBuilders.makeMember(identity = identity1, scmTeam = scmTeam, isCurrentMember = true)
        val threadParticipant1 = ModelBuilders.makeThreadParticipant(thread = thread, member = member1)
        ModelBuilders.makePersonEmailPreferences(
            person = person1,
            threadInviteEmails = false,
        )

        service.sendThreadInviteEmails(
            sender = authorIdentity.id.value,
            orgId = org.idValue,
            threadId = thread.idValue,
            orgMemberAndEmails = listOf(
                threadParticipant1.asDataModel().let { OrgMemberAndEmail(it.orgMemberId, null) },
            ),
            trigger = EmailTrigger.API_CALL,
        )
        verify(sqsProvider, Mockito.never()).sendMessage(any(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull())
    }
}
