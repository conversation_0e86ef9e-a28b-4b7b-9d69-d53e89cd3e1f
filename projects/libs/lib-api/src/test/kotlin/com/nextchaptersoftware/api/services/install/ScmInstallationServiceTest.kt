package com.nextchaptersoftware.api.services.install

import com.nextchaptersoftware.db.utils.DatabaseTestsBase

@Suppress("unused")
class ScmInstallationServiceTest : DatabaseTestsBase() {

    /*
    @Test
    fun `install api returns not installed state when no teammembers exist for user identity`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        makeIdentity(
            id = identityId,
        )
        val install = makeInstall()
        val state = install.installState(
            identityId = identityId,
            repoUri = null,
        )
        assertThat(state).isNotNull
        assertThat(InstallState.State.notInstalled).isEqualTo(state?.state)
        assertThat("https://example.com/user-install").isEqualTo(state?.installUrl)
    }

    @Test
    fun `install api returns not install state with org url when supplied`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        makeIdentity(
            id = identityId,
        )
        val install = makeInstall()
        val state = install.installState(
            identityId = identityId,
            repoUri = "org",
        )
        assertThat(state).isNotNull
        assertThat(InstallState.State.notInstalled).isEqualTo(state?.state)
        assertThat("https://example.com/org-install").isEqualTo(state?.installUrl)
    }

    @Test
    fun `install api returns configured state when teammember exists for identity but repoUri not supplied`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val identity = makeIdentity(identityId)
        makeMember(identity = identity)
        val install = makeInstall()
        val state = install.installState(
            identityId = identityId,
            repoUri = null,
        )
        assertThat(state).isNotNull
        assertThat(InstallState.State.configured).isEqualTo(state?.state)
        assertThat("https://example.com/user-install").isEqualTo(state?.installUrl)
    }

    @Test
    fun `not installed state when teammember exists for identity but no teams match the supplied repo uri`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val identity = makeIdentity(identityId)
        makeMember(identity = identity)
        val install = makeInstall()
        val state = install.installState(
            identityId = identityId,
            repoUri = "https://example.com/doesnt/matter",
        )
        assertThat(state).isNotNull
        assertThat(InstallState.State.notInstalled).isEqualTo(state?.state)
        assertThat("https://example.com/org-install").isEqualTo(state?.installUrl)
    }

    @Test
    fun `install api returns configured when a team exists that matches the matched teammember`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val identity = makeIdentity(identityId)
        val scmTeam = makeTeam(
            providerExternalId = "orgId",
        )
        makeMember(
            identity = identity,
            scmTeam = scmTeam,
        )
        val install = makeInstall()
        val state = install.installState(
            identityId = identityId,
            repoUri = "https://example.com/doesnt/matter",
        )
        assertThat(state).isNotNull
        assertThat(InstallState.State.configured).isEqualTo(state?.state)
        assertThat("https://example.com/configure").isEqualTo(state?.installUrl)
    }

    @Test
    fun `install api returns null when no identity exists`() = suspendingDatabaseTest {
        val install = makeInstall()
        assertThat(
            install.installState(
                identityId = IdentityId.random(),
                repoUri = "https://example.com/doesnt/matter",
            ),
        ).isNull()
    }

    private fun makeInstall(
        providerApiFactory: ProviderApiFactory = makeApiFactoryMock(),
        providersConfig: ProvidersConfig = GlobalConfig.instance.providers,
    ) = InstallService(
        providerApiFactory = providerApiFactory,
        providersConfig = providersConfig,
    )

    private fun makeApiFactoryMock(
        orgConfigureUrl: Url = Url("https://example.com/configure"),
        orgInstallUrl: Url = Url("https://example.com/org-install"),
        userInstallUrl: Url = Url("https://example.com/user-install"),
        orgFromRemoteUri: ScmOrg = ScmOrg(id = "orgId"),
    ): ProviderApiFactory {
        val apiFactoryMock = mock<ProviderApiFactory>()
        val abstractMockProvider = object : AbstractMockProvider() {
            override suspend fun orgConfigureUrl(orgId: String): Url = orgConfigureUrl

            override fun orgInstallUrl(orgId: String): Url = orgInstallUrl

            override suspend fun orgFromRemoteUri(uri: String): ScmOrg = orgFromRemoteUri

            override fun userInstallUrl(userId: String): Url = userInstallUrl
        }
        `when`(apiFactoryMock.api(any(), any())).thenReturn(abstractMockProvider)
        return apiFactoryMock
    }
     */
}
