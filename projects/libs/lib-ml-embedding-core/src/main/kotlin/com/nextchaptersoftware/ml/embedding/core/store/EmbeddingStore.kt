package com.nextchaptersoftware.ml.embedding.core.store

import com.nextchaptersoftware.ml.embedding.core.models.MLVectorMetadata
import com.nextchaptersoftware.ml.embedding.core.models.MLVectorPair
import com.nextchaptersoftware.ml.embedding.core.store.filter.Filter
import kotlinx.coroutines.flow.Flow

/**
 * Interface representing a generic embedding store.
 *
 * This interface provides an abstract, platform-agnostic approach for managing ML vector embeddings,
 * ensuring that the implementation is not bound to any specific data store or platform.
 */
interface EmbeddingStore {
    /**
     * Adds a single vector embedding to the specified namespace.
     *
     * @param namespace The logical namespace to which the embedding belongs.
     * @param vectorPair The vector and its associated data to store.
     * @param metadata Optional metadata associated with the vector.
     * @return The generated unique identifier for the stored embedding.
     */
    suspend fun add(namespace: String, vectorPair: MLVectorPair, metadata: MLVectorMetadata?): String

    /**
     * Adds a single vector embedding with a specified ID to the given namespace.
     *
     * @param namespace The logical namespace to which the embedding belongs.
     * @param id The unique identifier for the embedding.
     * @param vectorPair The vector and its associated data to store.
     * @param metadata Optional metadata associated with the vector.
     */
    suspend fun add(namespace: String, id: String, vectorPair: MLVectorPair, metadata: MLVectorMetadata?)

    /**
     * Adds multiple vector embeddings with specified IDs to the given namespace.
     *
     * @param namespace The logical namespace to which the embeddings belong.
     * @param ids The list of unique identifiers for each embedding.
     * @param vectorPairs The list of vectors and their associated data to store.
     * @param metadata Optional list of metadata objects associated with each vector.
     */
    suspend fun addAll(namespace: String, ids: List<String>, vectorPairs: List<MLVectorPair>, metadata: List<MLVectorMetadata>?)

    /**
     * Adds multiple vector embeddings to the given namespace, generating unique IDs for each.
     *
     * @param namespace The logical namespace to which the embeddings belong.
     * @param vectorPairs The list of vectors and their associated data to store.
     * @param metadata Optional list of metadata objects associated with each vector.
     * @return The list of generated unique identifiers for the stored embeddings.
     */
    suspend fun addAll(namespace: String, vectorPairs: List<MLVectorPair>, metadata: List<MLVectorMetadata>?): List<String> {
        val ids = generateIds(vectorPairs.size)
        addAll(namespace, ids, vectorPairs, metadata)
        return ids
    }

    /**
     * Generates a list of unique identifiers.
     *
     * @param count The number of unique IDs to generate.
     * @return A list of generated unique identifiers.
     */
    suspend fun generateIds(count: Int): List<String> {
        return List(count) { java.util.UUID.randomUUID().toString() }
    }

    /**
     * Fetches embeddings by their IDs from the specified namespace.
     *
     * @param namespace The logical namespace from which to fetch embeddings.
     * @param ids The list of unique identifiers for the embeddings to fetch.
     * @return An [EmbeddingMatches] object containing the fetched embeddings.
     */
    suspend fun fetch(namespace: String, ids: List<String>): EmbeddingMatches

    /**
     * Lists all embedding IDs in the namespace that match the given prefix.
     *
     * @param namespace The logical namespace to search within.
     * @param request The request specifying the ID prefix to filter by.
     * @return A [Flow] emitting matching embedding IDs.
     */
    fun listByIdPrefix(namespace: String, request: EmbeddingListRequest.EmbeddingListIdPrefixRequest): Flow<String>

    /**
     * Lists all embedding IDs in the namespace that match the given filter.
     *
     * @param namespace The logical namespace to search within.
     * @param request The request specifying the filter criteria.
     * @return A [Flow] emitting matching embedding IDs.
     */
    fun listByFilter(namespace: String, request: EmbeddingListRequest.EmbeddingListFilterRequest): Flow<String>

    /**
     * Removes a single embedding by its ID from the specified namespace.
     *
     * @param namespace The logical namespace from which to remove the embedding.
     * @param id The unique identifier of the embedding to remove.
     * @throws IllegalArgumentException if the ID is blank.
     */
    suspend fun remove(namespace: String, id: String) {
        require(id.isNotBlank()) { "id must not be blank" }
        removeAll(namespace, listOf(id))
    }

    /**
     * Removes multiple embeddings by their IDs from the specified namespace.
     *
     * @param namespace The logical namespace from which to remove embeddings.
     * @param ids The collection of unique identifiers for the embeddings to remove.
     */
    suspend fun removeAll(namespace: String, ids: Collection<String>)

    /**
     * Removes all embeddings from the specified namespace that match the given filter.
     *
     * @param namespace The logical namespace from which to remove embeddings.
     * @param filter The filter criteria to select embeddings for removal.
     */
    suspend fun removeAll(namespace: String, filter: Filter)

    /**
     * Removes all embeddings from the specified namespace.
     *
     * @param namespace The logical namespace from which to remove all embeddings.
     */
    suspend fun removeAll(namespace: String)

    /**
     * Searches for embeddings in the specified namespace that match the given search request.
     *
     * @param namespace The logical namespace to search within.
     * @param request The search request specifying query parameters.
     * @return An [EmbeddingMatches] object containing the search results.
     */
    suspend fun search(namespace: String, request: EmbeddingSearchRequest): EmbeddingMatches
}
