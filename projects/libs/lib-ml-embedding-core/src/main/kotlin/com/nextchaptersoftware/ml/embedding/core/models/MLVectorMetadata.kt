package com.nextchaptersoftware.ml.embedding.core.models

import java.math.BigDecimal
import java.util.UUID

/**
 * Type-checked wrapper around a key–value map intended for embedding-vector metadata.
 * Supported value types: String, Boolean, UUID, and any Number subclass.
 */
@Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
data class MLVectorMetadata(
    private val metadata: MutableMap<String, Any> = mutableMapOf(),
) {

    // ───────────────────── Companion ─────────────────────

    companion object {
        fun from(key: String, value: String) = MLVectorMetadata().put(key, value)

        fun from(metadata: Map<String, Any>) = MLVectorMetadata(metadata.toMutableMap())

        fun metadata(key: String, value: String) = from(key, value)
    }

    // ───────────────────── Validation ─────────────────────

    private fun isSupported(v: Any) =
        v is String || v is UUID || v is Boolean || v is Number

    private fun validateEntry(k: String, v: Any?) {
        require(k.isNotBlank()) { "Metadata key is blank (value: '$v')" }
        requireNotNull(v) { "Metadata value for key '$k' is null." }
        require(isSupported(v)) {
            "Metadata key '$k' has unsupported type '${v::class.java.name}'. " +
                    "Supported: String, Boolean, UUID, Number."
        }
    }

    private fun validateAll(m: Map<String, Any>) = m.forEach(::validateEntry)

    init {
        validateAll(metadata)
    }

    // ───────────────────── Internal helpers ─────────────────────

    /** Coerce to String; returns *null* on key absence or wrong type. */
    private fun coerceString(k: String): String? =
        (metadata[k] as? String)

    /** Coerce to UUID; returns *null* on failure. */
    private fun coerceUUID(k: String): UUID? =
        when (val v = metadata[k]) {
            null -> null
            is UUID -> v
            is String -> runCatching { UUID.fromString(v) }.getOrNull()
            else -> null
        }

    /** Coerce to Number; returns *null* on failure. */
    @Suppress("NestedBlockDepth")
    private fun coerceNumber(k: String): Number? =
        when (val raw = metadata[k]) {
            null -> null

            is Number -> raw

            is String -> raw.trim().takeIf { it.isNotEmpty() }?.let { str ->
                runCatching { BigDecimal(str) }.getOrNull()
                    ?.stripTrailingZeros()
                    ?.let { bd ->
                        if (bd.scale() <= 0) {
                            bd.longValueExact()
                                .let { if (it in Int.MIN_VALUE..Int.MAX_VALUE) it.toInt() else it }
                        } else {
                            bd.toDouble()
                        }
                    }
            }

            else -> null
        }

    private fun requireNumeric(k: String): Number =
        coerceNumber(k) ?: error("Value for '$k' is missing or not numeric.")

    // ───────────────────── Public API ─────────────────────

    fun containsKey(key: String) = metadata.containsKey(key)

    // ---------- String getters ----------

    /** Hard-fail: throws if key absent or value not a String. */
    fun getString(key: String): String =
        coerceString(key) ?: error("No String value found for key '$key'.")

    /** Soft variant: never throws – returns null on absence or wrong type. */
    fun getStringOrNull(key: String): String? = coerceString(key)

    // ---------- UUID getters ----------

    fun getUUID(key: String): UUID =
        coerceUUID(key) ?: error("No UUID value found for key '$key'.")

    fun getUUIDOrNull(key: String): UUID? = coerceUUID(key)

    // ---------- Numeric getters (hard-fail) ----------

    fun getInteger(key: String): Int = requireNumeric(key).toInt()

    fun getLong(key: String): Long = requireNumeric(key).toLong()

    fun getFloat(key: String): Float = requireNumeric(key).toFloat()

    fun getDouble(key: String): Double = requireNumeric(key).toDouble()

    // ---------- Numeric getters (soft) ----------

    fun getIntOrNull(key: String): Int? = coerceNumber(key)?.toInt()

    fun getLongOrNull(key: String): Long? = coerceNumber(key)?.toLong()

    fun getFloatOrNull(key: String): Float? = coerceNumber(key)?.toFloat()

    fun getDoubleOrNull(key: String): Double? = coerceNumber(key)?.toDouble()

    // ---------- Mutators (fluent) ----------

    fun put(key: String, value: String) = apply {
        validateEntry(key, value)
        metadata[key] = value
    }

    fun put(key: String, value: UUID) = apply {
        validateEntry(key, value)
        metadata[key] = value
    }

    fun put(key: String, value: Int) = apply {
        validateEntry(key, value)
        metadata[key] = value
    }

    fun put(key: String, value: Long) = apply {
        validateEntry(key, value)
        metadata[key] = value
    }

    fun put(key: String, value: Float) = apply {
        validateEntry(key, value)
        metadata[key] = value
    }

    fun put(key: String, value: Double) = apply {
        validateEntry(key, value)
        metadata[key] = value
    }

    fun putAll(meta: Map<String, Any>) = apply {
        validateAll(meta)
        metadata.putAll(meta)
    }

    fun remove(key: String) = apply { metadata.remove(key) }

    // ---------- Copy / merge / export ----------

    fun copy(): MLVectorMetadata = MLVectorMetadata(metadata.toMutableMap())

    fun toMap(): Map<String, Any> = HashMap(metadata)

    fun merge(other: MLVectorMetadata?): MLVectorMetadata {
        if (other == null || other.metadata.isEmpty()) return copy()
        val overlap = metadata.keys intersect other.metadata.keys
        require(overlap.isEmpty()) { "Metadata keys are not unique. Common keys: $overlap" }
        return MLVectorMetadata((metadata + other.metadata).toMutableMap())
    }
}
