package com.nextchaptersoftware.slack.services

import com.nextchaptersoftware.slack.api.SlackInstance
import com.nextchaptersoftware.slack.serialization.SlackSerializer
import com.slack.api.Slack
import com.slack.api.app_backend.interactive_components.response.ActionResponse
import com.slack.api.model.block.LayoutBlock

class SlackActionResponseService(
    private val slack: Slack = SlackInstance.DEFAULT_INSTANCE,
) {
    // https://stackoverflow.com/questions/47496747/how-to-remove-ephemeral-messages
    fun deleteEphemeral(responseUrl: String) {
        serializeActionResponse {
            isDeleteOriginal = true
            isReplaceOriginal = true
            text = ""
            responseType = "ephemeral"
        }?.let { body ->
            Slack.getInstance().send(
                responseUrl,
                body,
            )
        }
    }

    fun respondEphemeral(responseUrl: String, blocks: List<LayoutBlock>) {
        serializeActionResponse {
            this.blocks = blocks
            responseType = "ephemeral"
        }?.let { body ->
            Slack.getInstance().send(
                responseUrl,
                body,
            )
        }
    }

    private fun serializeActionResponse(actionResponseConfig: ActionResponse.() -> Unit): String? {
        return ActionResponse().apply(actionResponseConfig).let {
            SlackSerializer.toJson(it)
        }
    }
}
