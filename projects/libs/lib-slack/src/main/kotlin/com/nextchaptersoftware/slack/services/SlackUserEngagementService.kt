package com.nextchaptersoftware.slack.services

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.ActivityType
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.stores.MemberAndPerson
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.metrics.MetricsService
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SlackUserEngagementService(
    private val memberStore: MemberStore = Stores.memberStore,
    private val metricsService: MetricsService,
) {
    suspend fun handleEvent(
        orgId: OrgId,
        memberId: MemberId,
        activityType: ActivityType,
    ) {
        withLoggingContextAsync(
            "orgId" to orgId,
            "memberId" to memberId,
            "activityType" to activityType.name,
        ) {
            LOGGER.debugAsync { "handle slack bot user engagement event" }
            runSuspendCatching {
                val memberInfo = Database.suspendedTransaction {
                    val slackMember = memberStore.findById(
                        trx = this,
                        memberId = memberId,
                    )

                    val actualMember = slackMember?.associatedPrimaryMember ?: slackMember
                    actualMember?.let {
                        MemberAndPerson(
                            member = it.asDataModel(),
                            identity = it.identity.asDataModel(),
                            person = it.identity.person?.asDataModel(),
                        )
                    }
                } ?: run {
                    LOGGER.warnAsync { "could not find associated team member" }
                    return@withLoggingContextAsync
                }

                val orgMemberId = memberStore.getOrgMemberId(memberId = memberId)

                metricsService.handleEvent(
                    personId = memberInfo.person?.id,
                    orgId = orgId,
                    orgMemberId = orgMemberId,
                    productAgent = ProductAgentType.Slack,
                    activityType = activityType,
                )
            }.onFailure {
                LOGGER.errorAsync(it) { "Failed to send slack user engagement event" }
            }
        }
    }
}
