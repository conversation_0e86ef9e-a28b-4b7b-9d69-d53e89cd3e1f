plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(libs.bundles.slack)

    /**
     *  !!!!!!!WARNING!!!!!!!!
     *  DO NOT ADD DEPENDENCIES ON API SERVICE MODULES
     *  !!!!!!!WARNING!!!!!!!!
     */
    implementation(project(":projects:clients:client-slack", "default"))
    implementation(project(":projects:libs:lib-event-queue", "default"))
    implementation(project(":projects:libs:lib-feedback", "default"))
    implementation(project(":projects:libs:lib-product-feedback", "default"))
    implementation(project(":projects:libs:lib-search-semantic", "default"))
    implementation(project(":projects:libs:lib-slack-ingestion", "default"))
    implementation(project(":projects:libs:lib-bot-toolbox", "default"))
    implementation(project(":projects:models", "default"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)
    testImplementation(testLibs.bundles.test.postgresql)
}
