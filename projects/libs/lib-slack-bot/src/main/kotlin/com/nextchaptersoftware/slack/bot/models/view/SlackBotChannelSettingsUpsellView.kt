package com.nextchaptersoftware.slack.bot.models.view

import com.nextchaptersoftware.common.model.link
import com.nextchaptersoftware.slack.notify.models.block.SlackBlockText
import com.slack.api.model.block.element.RichTextSectionElement
import com.slack.api.model.kotlin_extension.block.element.ListStyle
import com.slack.api.model.kotlin_extension.view.blocks
import com.slack.api.model.view.View
import com.slack.api.model.view.ViewTitle
import com.slack.api.model.view.Views.view
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
class SlackBotChannelSettingsUpsellView(
    private val title: SlackBlockText.ViewTitleText = SlackBlockText.ViewTitleText("Channel Settings"),
    @Contextual val upgradeLink: Url,
) {
    fun createView(): View {
        return view { builder ->
            builder.apply {
                type("modal")
                notifyOnClose(false)
                title(ViewTitle.builder().type("plain_text").text(title.text).build())
                blocks {
                    richText {
                        elements {
                            richTextSection {
                                elements {
                                    text(
                                        text = "Your team is currently on an Unblocked Legacy Plan.\n\n",
                                        style = RichTextSectionElement.TextStyle.builder().bold(true).build(),
                                    )
                                }
                            }
                            richTextSection {
                                elements {
                                    text(
                                        text = "When you upgrade to a paid plan, you'll be able to:\n\n",
                                    )
                                }
                            }
                            richTextList {
                                style(ListStyle.BULLET)
                                indent(0)
                                elements {
                                    richTextSection {
                                        elements {
                                            text(
                                                text = "choose the data sources used to answer questions in each channel",
                                            )
                                        }
                                    }
                                    richTextSection {
                                        elements {
                                            text(
                                                text = "adjust the conciseness, tone, or depth of responses",
                                            )
                                        }
                                    }
                                    richTextSection {
                                        elements {
                                            text(
                                                text = "adjust how frequently Unblocked auto-responds to questions\n\n\n",
                                            )
                                        }
                                    }
                                }
                            }
                            richTextSection {
                                elements {
                                    link(
                                        url = upgradeLink.toString(),
                                        text = "View all plans and features",
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
