package com.nextchaptersoftware.slack.bot.models.payload

import com.nextchaptersoftware.db.models.AnswerPreferences
import com.nextchaptersoftware.db.models.SlackAutoAnswerMode
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.slack.notify.models.block.SlackBlockText
import com.slack.api.model.block.LayoutBlock
import com.slack.api.model.block.element.RichTextSectionElement
import com.slack.api.model.kotlin_extension.block.element.ListStyle
import com.slack.api.model.kotlin_extension.block.withBlocks
import io.ktor.http.Url

sealed class SlackBotChannelSettingsPayload : SlackBotPayload() {
    companion object {
        const val CHANNEL_SETTINGS_HELP_BLOCK_ID = "channel_settings_help"
        const val CHANNEL_SETTINGS_CHANGED_BLOCK_ID = "channel_settings_changed"
        const val CHANNEL_SETTINGS_LIST_BLOCK_ID = "channel_settings_list"
    }

    class SlackBotChannelSettingsHelpPayload(
        val command: String,
        val dashboardUrl: Url,
        val slackAppHomeUrl: Url,
        val urlBuilderProvider: UrlBuilderProvider,
        val welcomeMessage: SlackBlockText = SlackBlockText.TextBlockText(
            """
            Hello :wave: - I’m the <$dashboardUrl|Unblocked> bot.

            I use the documents, discussions, and data across your data sources to provide the answers and information you need.

            Here are a couple ways to ask questions:
            • Simply mention <@unblocked> in any public or private channels
            • <$slackAppHomeUrl|DM the bot> directly to ask a private question

            From supported channels, you can also enter the command `$command` to adjust how Unblocked responds.
            """.trimIndent(),
        ),
    ) : SlackBotChannelSettingsPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(CHANNEL_SETTINGS_HELP_BLOCK_ID)
                    markdownText(welcomeMessage.text)
                }
            }
        }
    }

    class SlackBotChannelSettingsChangedPayload(
        private val slackUserId: String,
        private val dataSourcePresetName: String,
        private val answerPreferences: AnswerPreferences,
        private val autoAnswerMode: SlackAutoAnswerMode,
    ) : SlackBotChannelSettingsPayload() {
        override val blocks: List<LayoutBlock> by lazy {
            withBlocks {
                section {
                    blockId(CHANNEL_SETTINGS_CHANGED_BLOCK_ID)
                    markdownText(
                        "Unblocked's settings for this channel were changed by <@$slackUserId>. Here are the details:\n\n",
                    )
                }
                richText {
                    blockId(CHANNEL_SETTINGS_LIST_BLOCK_ID)
                    elements {
                        richTextList {
                            style(ListStyle.BULLET)
                            indent(0)
                            elements {
                                richTextSection {
                                    elements {
                                        text("Data Sources:", style = RichTextSectionElement.TextStyle.builder().bold(false).build())
                                        text(" $dataSourcePresetName")
                                    }
                                }
                                richTextSection {
                                    elements {
                                        text("Answer Length:", style = RichTextSectionElement.TextStyle.builder().bold(false).build())
                                        text(" ${answerPreferences.conciseness.label}")
                                    }
                                }
                                richTextSection {
                                    elements {
                                        text("Answer Tone:", style = RichTextSectionElement.TextStyle.builder().bold(false).build())
                                        text(" ${answerPreferences.tone.label}")
                                    }
                                }
                                richTextSection {
                                    elements {
                                        text("Research Depth:", style = RichTextSectionElement.TextStyle.builder().bold(false).build())
                                        text(" ${answerPreferences.depth.label}")
                                    }
                                }
                                richTextSection {
                                    elements {
                                        text("Auto-Response Confidence:", style = RichTextSectionElement.TextStyle.builder().bold(false).build())
                                        text(" ${autoAnswerMode.label}")
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
