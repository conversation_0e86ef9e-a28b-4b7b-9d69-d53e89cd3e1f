package com.nextchaptersoftware.slack.bot.services.inference

import com.nextchaptersoftware.db.common.Database
import com.nextchaptersoftware.db.models.MLInference
import com.nextchaptersoftware.db.models.Message
import com.nextchaptersoftware.db.models.Org
import com.nextchaptersoftware.db.models.Thread
import com.nextchaptersoftware.db.stores.MLInferenceStore
import com.nextchaptersoftware.db.stores.MessageStore
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores

data class InferenceAndRelatedModels(
    val inference: MLInference,
    val thread: Thread,
    val message: Message,
    val org: Org,
)

class SlackMessageInferenceExampleService(
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    private val messageStore: MessageStore = Stores.messageStore,
    private val inferenceStore: MLInferenceStore = Stores.mlInferenceStore,
) {
    suspend fun findInference(
        slackExternalTeamId: String,
        slackMessageTs: String,
    ): InferenceAndRelatedModels? {
       val org = Database.suspendedTransaction {
            val slackTeam = slackTeamStore.findBySlackExternalTeamId(
                trx = this,
                slackExternalTeamId = slackExternalTeamId,
            )

            checkNotNull(slackTeam)
            slackTeam.installation.org.asDataModel()
        }

        val (thread, message) = Database.suspendedTransaction {
            val message = messageStore.findForSlackMessageTs(
                trx = this,
                slackMessageTs = slackMessageTs,
            )

            checkNotNull(message)

            Pair(
                message.thread.asDataModel(),
                message.asDataModel(),
            )
        }

        return inferenceStore.findByMessageId(messageId = message.id)?.let { inference ->
            InferenceAndRelatedModels(
                inference = inference,
                thread = thread,
                message = message,
                org = org,
            )
        }
    }
}
