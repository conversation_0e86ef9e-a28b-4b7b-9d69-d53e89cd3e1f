package com.nextchaptersoftware.slack.bot.models.blockaction

import com.nextchaptersoftware.slack.bot.models.blockaction.SlackBotChannelSettingsAnswerPreferencesSelectOption.Conciseness.Companion.CONCISENESS_ACTION_ID
import com.nextchaptersoftware.slack.notify.models.block.SlackBlockText
import kotlinx.serialization.Serializable

@Serializable
sealed class SlackBotChannelSettingsAnswerPreferencesSelectOption : SlackBotBlockAction.SlackBotSelectOption() {
    @Serializable
    data class Conciseness(
        override val actionId: String = CONCISENESS_ACTION_ID,
        override val optionText: SlackBlockText.SelectOptionBlockText,
        override val optionValue: SlackBlockText.SelectOptionBlockText,
        override val isSelected: Boolean,
        override val optionGroup: String? = null,
    ) : SlackBotChannelSettingsAnswerPreferencesSelectOption() {
        companion object {
            const val CONCISENESS_ACTION_ID = "slack_channel_settings_answer_preferences_conciseness_option"
        }
    }

    @Serializable
    data class Tone(
        override val actionId: String = TONE_ACTION_ID,
        override val optionText: SlackBlockText.SelectOptionBlockText,
        override val optionValue: SlackBlockText.SelectOptionBlockText,
        override val isSelected: Boolean,
        override val optionGroup: String? = null,
    ) : SlackBotChannelSettingsAnswerPreferencesSelectOption() {
        companion object {
            const val TONE_ACTION_ID = "slack_channel_settings_answer_preferences_tone_option"
        }
    }

    @Serializable
    data class Depth(
        override val actionId: String = DEPTH_ACTION_ID,
        override val optionText: SlackBlockText.SelectOptionBlockText,
        override val optionValue: SlackBlockText.SelectOptionBlockText,
        override val isSelected: Boolean,
        override val optionGroup: String? = null,
    ) : SlackBotChannelSettingsAnswerPreferencesSelectOption() {
        companion object {
            const val DEPTH_ACTION_ID = "slack_channel_settings_answer_preferences_depth_option"
        }
    }
}
