package com.nextchaptersoftware.slack.bot.services.completion

import com.nextchaptersoftware.api.serialization.SerializationExtensions.lenientDecode
import com.nextchaptersoftware.db.models.MLInferenceTemplate
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class IsQuestionCompletionService(
    private val promptCompilerService: PromptCompilerService,
    private val templateService: MLInferenceTemplateService,
    private val completionService: CompletionService,
    private val orgStore: OrgStore = Stores.orgStore,
) {
    suspend fun isQuestion(
        orgId: OrgId,
        input: String,
        overrideTemplate: MLInferenceTemplate? = null,
    ): IsQuestionResult? {
        val isQuestionTemplate = overrideTemplate ?: templateService.orgTemplate(orgId, MLInferenceTemplateKind.IsQuestion)

        val compilerResult = promptCompilerService.compilePrompt(
            template = isQuestionTemplate,
            query = input,
            org = orgStore.findById(orgId = orgId),
        )

        val result = completionService.query(
            prompt = compilerResult.prompt,
            template = isQuestionTemplate,
        )

        LOGGER.debugSensitiveAsync(
            fields = arrayOf("orgId" to orgId),
            sensitiveFields = mapOf(
                "input" to input,
                "prompt" to compilerResult.prompt,
                "result" to result,
            ),
        ) { "IsQuestionCompletionService:: isQuestion result" }

        return runSuspendCatching {
            result.lenientDecode<IsQuestionResult>()
        }.onFailure {
            LOGGER.errorAsync(it) { "Error decoding isQuestion result" }
        }.getOrNull()
    }
}

@Serializable
data class IsQuestionResult(
    @SerialName("isQuestionOrAsksForHelp")
    val isQuestion: String,

    @SerialName("rationale")
    val rationale: String,
) {
    fun isQuestion(): Boolean = isQuestion == "YES"
}
