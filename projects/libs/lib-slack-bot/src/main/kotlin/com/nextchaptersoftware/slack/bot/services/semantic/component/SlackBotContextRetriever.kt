package com.nextchaptersoftware.slack.bot.services.semantic.component

import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.stores.MLInferenceTemplateStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.DataSourceAccessControlManager
import com.nextchaptersoftware.slack.bot.models.semantic.SlackBotContext
import com.nextchaptersoftware.slack.bot.models.semantic.SlackBotInputContext
import com.nextchaptersoftware.slack.bot.models.semantic.SlackContext

class SlackBotContextRetriever(
    private val templateStore: MLInferenceTemplateStore = Stores.mlInferenceTemplateStore,
    private val slackMemberContextRetriever: SlackMemberContextRetriever,
    private val slackBotSlackMessageResolver: SlackBotSlackMessageResolver,
    private val slackBotQuestionTransformer: SlackBotQuestionTransformer,
    private val dataSourceAccessControlManager: DataSourceAccessControlManager = DataSourceAccessControlManager(),
) {
    private suspend fun createHumanAndBotContext(
        slackContext: SlackContext,
        slackThreadTs: String,
        slackTs: String,
        slackUserId: String,
        question: String,
        pendingResponseTs: String?,
        attemptCount: Int,
    ): SlackBotContext.HumanAndBotContext {
        val humanMemberContext = slackMemberContextRetriever.getSlackMemberContext(
            orgId = slackContext.org.id,
            slackTeamId = slackContext.slackTeam.id,
            slackUserId = slackUserId,
        )

        val botMemberContext = slackMemberContextRetriever.getSlackMemberContext(
            orgId = slackContext.org.id,
            slackTeamId = slackContext.slackTeam.id,
            slackUserId = slackContext.slackTeam.botUserId,
        )

        val template = templateStore.orgTemplate(orgId = slackContext.org.id, kind = MLInferenceTemplateKind.Search)

        val dsacContext = dataSourceAccessControlManager.getDsacContext(
            orgId = slackContext.org.id,
            orgMemberId = humanMemberContext.member.orgMemberId,
        )

        return SlackBotContext.HumanAndBotContext(
            slackContext = slackContext,
            question = question,
            slackThreadTs = slackThreadTs,
            slackTs = slackTs,
            humanMemberContext = humanMemberContext,
            botMemberContext = botMemberContext,
            slackUserId = slackUserId,
            pendingResponseTs = pendingResponseTs,
            attemptCount = attemptCount,
            template = template,
            dsacContext = dsacContext,
        )
    }

    suspend fun createHumanAndBotContext(
        input: SlackBotInputContext,
        slackContext: SlackContext,
    ): SlackBotContext.HumanAndBotContext {
        val question = slackBotQuestionTransformer.transform(
            orgId = slackContext.org.id,
            slackTeamId = slackContext.slackTeam.id,
            slackExternalTeamId = slackContext.slackTeam.slackExternalTeamId,
            question = input.text,
        )

        val resolvedPendingResponseTs = slackBotSlackMessageResolver.resolvePendingResponseTs(
            slackContext = slackContext,
            slackThreadTs = input.slackThreadTs ?: input.slackTs,
            slackTs = input.slackTs,
            question = question,
            pendingResponseTs = input.pendingResponseTs,
        )

        return createHumanAndBotContext(
            slackContext = slackContext,
            slackThreadTs = input.slackThreadTs ?: input.slackTs,
            slackTs = input.slackTs,
            question = question,
            slackUserId = input.slackUserId,
            pendingResponseTs = resolvedPendingResponseTs,
            attemptCount = input.attemptCount,
        )
    }
}
