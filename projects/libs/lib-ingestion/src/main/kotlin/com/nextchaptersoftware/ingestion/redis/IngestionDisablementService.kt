package com.nextchaptersoftware.ingestion.redis

import com.nextchaptersoftware.redis.Redis
import io.lettuce.core.cluster.api.coroutines.RedisClusterCoroutinesCommands

class IngestionDisablementService(
    private val redis: RedisClusterCoroutinesCommands<String, String> = Redis.API,
    private val key: String = "ingestion-disabled",
) {
    suspend fun isDisabled(): Boolean {
        return redis.get(key) != null
    }

    suspend fun toggle() {
        when (isDisabled()) {
            true -> redis.del(key)
            false -> redis.set(key, "1")
        }
    }
}
