package com.nextchaptersoftware.web.ingestion

import io.ktor.client.request.head
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.Url
import kotlin.time.Duration.Companion.seconds

class HeadUriService {
    companion object {
        private val HEAD_TIMEOUT = 5.seconds
    }

    private val client = HttpClient.create(
        proxyConfig = null, // No need to use the proxy for validating a URL
        accept = ContentType.Text.Html,
        timeout = HttpClient.Timeout(
            requestTimeoutDuration = HEAD_TIMEOUT,
            socketTimeoutDuration = HEAD_TIMEOUT,
        ),
    )

    suspend fun getHeadResponse(uri: Url): HttpResponse {
        return client.head(uri)
    }
}
