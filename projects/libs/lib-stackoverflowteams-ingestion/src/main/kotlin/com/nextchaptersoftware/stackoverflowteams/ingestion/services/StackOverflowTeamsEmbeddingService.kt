package com.nextchaptersoftware.stackoverflowteams.ingestion.services

import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.models.EmbeddingMetadata
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.infoSync
import com.nextchaptersoftware.log.kotlin.withLoggingContextSync
import com.nextchaptersoftware.stackoverflowteams.models.Answer
import com.nextchaptersoftware.stackoverflowteams.models.Article
import com.nextchaptersoftware.stackoverflowteams.models.Question
import io.ktor.http.Url
import java.util.UUID
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class StackOverflowTeamsEmbeddingService(
    private val embeddingEventEnqueueService: EmbeddingEventEnqueueService,
) {
    fun embedQuestion(
        orgId: OrgId,
        installationId: InstallationId,
        question: Question,
        answers: List<Answer>,
    ) {
        val sourceDocumentId = question.uuid(installationId = installationId)

        withLoggingContextSync(
            "orgId" to orgId,
            "installationId" to installationId,
            "documentId" to sourceDocumentId,
            "questionId" to question.questionId,
        ) {
            LOGGER.infoSync { "Embedding Stack Overflow for Teams question" }

            val content = (listOf(question.body) + answers.map { it.body }).joinToString("\n\n")

            embed(
                orgId = orgId,
                installationId = installationId,
                sourceDocumentId = sourceDocumentId,
                content = content,
                timestamp = question.creationDate,
                title = question.title,
                externalUrl = question.link,
            )
        }
    }

    fun embedArticle(
        orgId: OrgId,
        installationId: InstallationId,
        article: Article,
    ) {
        val sourceDocumentId = article.uuid(installationId = installationId)

        withLoggingContextSync(
            "orgId" to orgId,
            "installationId" to installationId,
            "documentId" to sourceDocumentId,
            "articleId" to article.articleId,
        ) {
            LOGGER.infoSync { "Embedding Stack Overflow for Teams article" }

            embed(
                orgId = orgId,
                installationId = installationId,
                sourceDocumentId = sourceDocumentId,
                content = article.body,
                timestamp = article.creationDate,
                title = article.title,
                externalUrl = article.link,
            )
        }
    }

    private fun embed(
        orgId: OrgId,
        installationId: InstallationId,
        sourceDocumentId: UUID,
        content: String,
        timestamp: Instant,
        title: String,
        externalUrl: Url,
    ) {
        val metadata = EmbeddingMetadata(
            documentGroup = null,
            documentInstallation = installationId.value,
            documentOrg = orgId.value,
            documentSource = Provider.StackOverflowTeams,
            documentType = DocumentType.Documentation,
            externalUrl = externalUrl.asString,
            insightType = InsightType.Documentation,
            sourceDocument = sourceDocumentId,
            timestamp = timestamp,
            title = title,
        )

        embeddingEventEnqueueService.enqueueEmbedDocumentEvent(
            orgId = orgId,
            installationId = installationId,
            sourceDocumentId = sourceDocumentId,
            content = content,
            metadata = metadata,
        )
    }
}
