package com.nextchaptersoftware.slack.webhook.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.event.SlackEventType
import com.nextchaptersoftware.slack.event.SlackPayloadExtractor
import com.nextchaptersoftware.slack.event.SlackPayloadParser
import com.nextchaptersoftware.slack.webhook.queue.payloads.SlackWebhookEvent
import com.nextchaptersoftware.slack.webhook.services.SlackMemberJoinedChannelEventService
import com.slack.api.app_backend.events.payload.MemberJoinedChannelPayload
import com.slack.api.model.event.MemberJoinedChannelEvent

class MemberJoinedChannelEventHandler(
    private val slackMemberJoinedChannelEventService: SlackMemberJoinedChannelEventService,
) : TypedEventHandler<SlackWebhookEvent.SlackWebhookApiEvent> {
    override suspend fun handle(event: SlackWebhookEvent.SlackWebhookApiEvent): Boolean {
        val webhookEventPayload = SlackPayloadExtractor.extract(event.body) ?: return false
        if (!SlackEventType.isEventType(
                webhookEventPayload,
                MemberJoinedChannelEvent.TYPE_NAME,
            )
        ) {
            return false
        }

        val memberJoinedChannelPayload = SlackPayloadParser.parsePayload<MemberJoinedChannelPayload>(webhookEventPayload) ?: return false
        slackMemberJoinedChannelEventService.process(payload = memberJoinedChannelPayload)
        return true
    }
}
