package com.nextchaptersoftware.slack.webhook.queue.handlers

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.slack.event.SlackEventType
import com.nextchaptersoftware.slack.event.SlackPayloadExtractor
import com.nextchaptersoftware.slack.event.SlackPayloadParser
import com.nextchaptersoftware.slack.webhook.queue.payloads.SlackWebhookEvent
import com.nextchaptersoftware.slack.webhook.services.SlackChannelLeftEventService
import com.slack.api.app_backend.events.payload.ChannelLeftPayload
import com.slack.api.model.event.ChannelLeftEvent

class ChannelLeftEventHandler(
    private val slackChannelLeftEventService: SlackChannelLeftEventService,
) : TypedEventHandler<SlackWebhookEvent.SlackWebhookApiEvent> {
    override suspend fun handle(event: SlackWebhookEvent.SlackWebhookApiEvent): Boolean {
        val webhookEventPayload = SlackPayloadExtractor.extract(event.body) ?: return false
        if (!SlackEventType.isEventType(
                webhookEventPayload,
                ChannelLeftEvent.TYPE_NAME,
            )
        ) {
            return false
        }

        val channelLeftPayload = SlackPayloadParser.parsePayload<ChannelLeftPayload>(webhookEventPayload) ?: return false
        slackChannelLeftEventService.process(payload = channelLeftPayload)
        return true
    }
}
