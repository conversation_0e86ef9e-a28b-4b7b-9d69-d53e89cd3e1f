package com.nextchaptersoftware.slack.webhook.services

import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.webhook.services.deletion.SlackFileEventDeletionService
import com.nextchaptersoftware.slack.webhook.services.models.SlackWebhookEventContext
import com.slack.api.app_backend.events.payload.FileDeletedPayload

class SlackFileDeletedEventService(
    private val slackFileEventDeletionService: SlackFileEventDeletionService,
) {
    suspend fun process(payload: FileDeletedPayload) = withLoggingContextAsync(
        "slackTeamId" to payload.teamId,
        "fileId" to payload.event?.fileId,
        "type" to payload.event?.type,
        "subType" to payload.event?.subtype,
    ) {
        slackFileEventDeletionService.process(
            context = SlackWebhookEventContext.SlackFileEventContext.SlackFileDeletedEventContext(
                slackExternalTeamId = payload.teamId,
                slackFileId = payload.event.fileId,
                slackChannelExternalIds = payload.event.channelIds,
            ),
        )
    }
}
