package com.nextchaptersoftware.api.auth.services.identity

import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.slack.auth.services.SlackUserAuthLoginExchange
import io.ktor.http.Url
import java.util.UUID

class SlackIdentityAuthExchangeService(
    private val slackUserAuthLoginExchange: SlackUserAuthLoginExchange,
) : IdentityAuthExchangeService<Provider> {
    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: Provider,
        overrideOAuthRedirectUrl: Url?,
        sessionId: UUID?,
    ): Identity {
        return slackUserAuthLoginExchange.handleAuthExchange(
            oAuthTokenExchangeContext = OAuthTokenExchangeContext(
                code = code,
                state = state,
                overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
            ),
        )
    }
}
