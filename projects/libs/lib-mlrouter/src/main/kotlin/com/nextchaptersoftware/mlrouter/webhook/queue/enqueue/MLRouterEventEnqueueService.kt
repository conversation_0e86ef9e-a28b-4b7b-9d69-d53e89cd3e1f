package com.nextchaptersoftware.mlrouter.webhook.queue.enqueue

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.activemq.models.messageProperties
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.event.queue.enqueue.EventEnqueueService
import com.nextchaptersoftware.mlrouter.webhook.queue.payloads.MLRouterWebhookEvent
import kotlin.time.Duration

class MLRouterEventEnqueueService(
    private val eventEnqueueService: EventEnqueueService,
) {
    fun enqueueEvent(
        event: MLRouterWebhookEvent.StandardMLRouterWebhookEvent,
        priority: MessagePriority = MessagePriority.DEFAULT,
        withDelay: Duration? = null,
    ) {
        eventEnqueueService.enqueueEvent(
            body = event.encode(),
            priority = priority,
            properties = withDelay?.let {
                messageProperties { withScheduledDelay(it) }
            },
        )
    }
}
