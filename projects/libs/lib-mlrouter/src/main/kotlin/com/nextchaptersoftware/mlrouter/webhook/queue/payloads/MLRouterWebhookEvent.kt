package com.nextchaptersoftware.mlrouter.webhook.queue.payloads

import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@SerialName("MLRouterWebhookEvent")
sealed class MLRouterWebhookEvent {
    abstract val routeId: String
    abstract val body: String
    abstract val enqueuedAt: Instant
    abstract val attemptNumber: Int

    @Serializable
    @SerialName("StandardMLRouterWebhookEvent")
    data class StandardMLRouterWebhookEvent(
        override val routeId: String,
        override val body: String,
        override val enqueuedAt: Instant = Instant.nowWithMicrosecondPrecision(),
        override val attemptNumber: Int = 0,
    ) : MLRouterWebhookEvent()
}
