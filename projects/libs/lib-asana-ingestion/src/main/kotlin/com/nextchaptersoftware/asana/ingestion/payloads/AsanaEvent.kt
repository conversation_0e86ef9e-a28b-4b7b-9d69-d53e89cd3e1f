package com.nextchaptersoftware.asana.ingestion.payloads

import com.nextchaptersoftware.db.models.InstallationId
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class AsanaEvent {
    @SerialName("AsanaPartialProjectIngestionEvent")
    @Serializable
    data class AsanaPartialProjectIngestionEvent(
        @Contextual val installationId: InstallationId,
    ) : AsanaEvent()
}
