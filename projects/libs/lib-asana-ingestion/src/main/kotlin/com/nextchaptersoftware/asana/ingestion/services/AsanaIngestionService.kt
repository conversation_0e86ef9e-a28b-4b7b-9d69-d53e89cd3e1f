package com.nextchaptersoftware.asana.ingestion.services

import com.nextchaptersoftware.asana.api.AsanaApiProvider
import com.nextchaptersoftware.asana.services.AsanaTokenProvider
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.AsanaWorkspaceStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ingestion.common.services.BaseIngestionService
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.RedisLock
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class AsanaIngestionService(
    provider: Provider = Provider.Asana,
    lockProvider: LockProvider,
    private val asanaTokenProvider: AsanaTokenProvider,
    private val asanaApiProvider: AsanaApiProvider,
    private val asanaProjectIngestionService: AsanaProjectIngestionService,
    private val asanaTeamIngestionService: AsanaTeamIngestionService,
    private val asanaWorkspaceStore: AsanaWorkspaceStore = Stores.asanaWorkspaceStore,
) : BaseIngestionService(
    provider = provider,
    lockProvider = lockProvider,
) {

    @Suppress("TooGenericExceptionCaught")
    override suspend fun ingest(
        installation: Installation,
        lock: RedisLock,
    ) {
        try {
            // TODO ASANA: We are not handling cleanup of s3 when workspaces are deleted/tombstoned
            // rapid and pinecone is deleted
            LOGGER.debugAsync("installationId" to installation.id) { "AsanaIngestionService.ingest: beginning" }
            ingestWorkspace(installation = installation)
        } catch (e: Exception) {
            LOGGER.errorAsync(e) { "AsanaIngestionService.ingest: error" }
        } finally {
            LOGGER.debugAsync("installationId" to installation.id) { "AsanaIngestionService.ingest: completed" }
        }
    }

    private suspend fun ingestWorkspace(installation: Installation) {
        val checkWorkspaces = asanaWorkspaceStore.list(installationId = installation.id)
        when (checkWorkspaces.size) {
            1 -> {
                // NOOP
            }

            0 -> throw IllegalStateException("No workspace found for installation ${installation.id}")

            else -> throw IllegalStateException("Too many workspaces for installation ${installation.id}, found ${checkWorkspaces.size}")
        }

        // TODO ASANA: What do we do if the workspace is deleted
        val tokens = asanaTokenProvider.getOAuthTokens(installationId = installation.id)
        val refreshedWorkspace = asanaApiProvider.workspacesApi.get(resourceId = installation.installationExternalId, tokens = tokens)
        val name = requireNotNull(refreshedWorkspace.name) {
            "Workspace name cannot be null for workspace with gid: ${refreshedWorkspace.gid}"
        }

        asanaWorkspaceStore.upsert(
            installationId = installation.id,
            asanaGid = refreshedWorkspace.gid,
            name = name,
            isOrganization = refreshedWorkspace.isOrganization ?: false,
            emailDomains = refreshedWorkspace.emailDomains ?: emptyList(),
        )

        asanaTeamIngestionService.ingest(installation = installation)
        asanaProjectIngestionService.ingest(installation = installation)
    }
}
