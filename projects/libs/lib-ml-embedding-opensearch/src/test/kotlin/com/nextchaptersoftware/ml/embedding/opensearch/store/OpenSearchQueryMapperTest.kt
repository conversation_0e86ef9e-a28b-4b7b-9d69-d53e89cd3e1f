package com.nextchaptersoftware.ml.embedding.opensearch.store

import com.nextchaptersoftware.ml.embedding.core.store.filter.Or
import com.nextchaptersoftware.ml.embedding.core.store.filter.filter
import java.io.StringWriter
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.opensearch.client.json.jackson.JacksonJsonpMapper
import org.opensearch.client.opensearch._types.query_dsl.Query

/**
 * Unit‑tests for [OpenSearchQueryMapper].
 *
 * **Requirements (Apr 2025)**
 * 1. All numeric literals in the expected JSON must be integers (no trailing ".0").
 * 2. Within each JSON object, fields must appear in **lexicographic order**.
 */
internal class OpenSearchQueryMapperTest {
    private val mapper = OpenSearchQueryMapper()

    // ---------------------------------------------------------------------
    // Atomic predicates
    // ---------------------------------------------------------------------

    @Test
    fun equal() {
        val should = "{\"term\":{\"key\":{\"value\":1}}}"
        val query = mapper.map(filter { eq("key", 1) })
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun notEqual() {
        val should = "{\"bool\":{\"must_not\":[{\"term\":{\"key\":{\"value\":1}}}]}}"
        val query = mapper.map(filter { neq("key", 1) })
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun greaterThan() {
        val should = "{\"range\":{\"key\":{\"gt\":1}}}"
        val query = mapper.map(filter { gt("key", 1) })
        assertThat(query.toJson()).isEqualTo(should)
    }

    // ---------------------------------------------------------------------
    // AND / OR compositions
    // ---------------------------------------------------------------------

    @Test
    fun combineAnd() {
        val should = "{\"bool\":{\"must\":[{\"range\":{\"key\":{\"gt\":1}}},{\"range\":{\"key\":{\"lt\":10}}}]}}"
        val query = mapper.map(
            filter {
            gt("key", 1)
            lt("key", 10)
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun combineOr() {
        val should = "{\"bool\":{\"minimum_should_match\":\"1\",\"should\":[{\"range\":{\"key\":{\"gt\":1}}},{\"range\":{\"key\":{\"lt\":10}}}]}}"
        val query = mapper.map(
            filter(operator = ::Or) {
            gt("key", 1)
            lt("key", 10)
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    // ---------------------------------------------------------------------
    // NOT and De Morgan handling
    // ---------------------------------------------------------------------

    @Test
    fun notAtomic() {
        val should = "{\"range\":{\"key\":{\"lte\":1}}}"
        val query = mapper.map(filter { not { gt("key", 1) } })
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun notCombineOr() {
        val should = "{\"bool\":{\"must\":[{\"range\":{\"key\":{\"lte\":1}}},{\"range\":{\"key\":{\"gte\":10}}}]}}"
        val query = mapper.map(
            filter {
            not {
                or {
                    gt("key", 1)
                    lt("key", 10)
                }
            }
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun notCombineAnd() {
        val should = "{\"bool\":{\"minimum_should_match\":\"1\",\"should\":[{\"range\":{\"key\":{\"lte\":1}}},{\"range\":{\"key\":{\"gte\":10}}}]}}"
        val query = mapper.map(
            filter {
            not {
                gt("key", 1)
                lt("key", 10)
            }
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    // ---------------------------------------------------------------------
    // Extended compound‑conditional coverage
    // ---------------------------------------------------------------------
    @Suppress("MaxLineLength")
    @Test
    fun orThreeWayNested() {
        val should = "{\"bool\":{\"minimum_should_match\":\"1\",\"should\":[{\"range\":{\"a\":{\"gt\":1}}},{\"bool\":{\"minimum_should_match\":\"1\",\"should\":[{\"range\":{\"b\":{\"lt\":5}}},{\"term\":{\"c\":{\"value\":10}}}]}}]}}"
        val query = mapper.map(
            filter(operator = ::Or) {
            gt("a", 1)
            or {
                lt("b", 5)
                eq("c", 10)
            }
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Suppress("MaxLineLength")
    @Test
    fun nestedAndOr() {
        val should = "{\"bool\":{\"must\":[{\"range\":{\"a\":{\"gt\":1}}},{\"bool\":{\"minimum_should_match\":\"1\",\"should\":[{\"range\":{\"b\":{\"lt\":5}}},{\"term\":{\"c\":{\"value\":10}}}]}}]}}"
        val query = mapper.map(
            filter {
            gt("a", 1)
            or {
                lt("b", 5)
                eq("c", 10)
            }
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun notOr() {
        val should = "{\"bool\":{\"must\":[{\"range\":{\"a\":{\"lte\":1}}},{\"range\":{\"b\":{\"gte\":5}}}]}}"
        val query = mapper.map(
            filter {
            not {
                or {
                    gt("a", 1)
                    lt("b", 5)
                }
            }
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    @Test
    fun doubleNot() {
        val should = "{\"term\":{\"a\":{\"value\":1}}}"
        val query = mapper.map(
            filter {
            not {
                not {
                    eq("a", 1)
                }
            }
        },
        )
        assertThat(query.toJson()).isEqualTo(should)
    }

    // ---------------------------------------------------------------------
    // Helper
    // ---------------------------------------------------------------------

    private fun Query.toJson(): String {
        val writer = StringWriter()
        val mapper = JacksonJsonpMapper()
        val generator = mapper.jsonProvider().createGenerator(writer)
        this.serialize(generator, mapper)
        generator.close()
        return writer.toString()
    }
}
