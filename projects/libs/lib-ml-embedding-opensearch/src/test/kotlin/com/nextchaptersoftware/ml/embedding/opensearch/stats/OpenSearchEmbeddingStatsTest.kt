package com.nextchaptersoftware.ml.embedding.opensearch.stats

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ml.embedding.core.models.MLSparseVector
import com.nextchaptersoftware.ml.embedding.core.models.MLVectorMetadata
import com.nextchaptersoftware.ml.embedding.core.models.MLVectorPair
import com.nextchaptersoftware.ml.embedding.opensearch.store.OpenSearchEmbeddingStore
import com.nextchaptersoftware.opensearch.api.utils.OpenSearchTestUtils
import com.nextchaptersoftware.opensearch.config.OpenSearchConfig
import com.nextchaptersoftware.opensearch.index.OpenSearchIndexLoader
import com.nextchaptersoftware.test.utils.TestScopeExtensions.sleep
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class OpenSearchEmbeddingStatsTest {
    companion object {
        private const val ROOT_INDEX = "test-stats-index"
        private const val ROOT_NAMESPACE = "test-namespace"

        private lateinit var indexLoader: OpenSearchIndexLoader
        private val openSearchApiProvider = OpenSearchTestUtils.OPENSEARCH_API_PROVIDER

        @BeforeAll
        @JvmStatic
        fun setup() = runTest {
            teardown()
            indexLoader = OpenSearchIndexLoader(openSearchApiProvider)
            indexLoader.loadIndex(
                OpenSearchConfig.INSTANCE.openSearchIndex.copy(indexName = ROOT_INDEX),
            )
            OpenSearchTestUtils.deleteAllIndicesWithPrefix(prefix = ROOT_INDEX)
        }

        @AfterAll
        @JvmStatic
        fun teardown() = runTest {
            runSuspendCatching {
                OpenSearchTestUtils.deleteAllIndicesWithPrefix(prefix = ROOT_INDEX)
            }
        }
    }

    private val openSearchApiProvider = OpenSearchTestUtils.OPENSEARCH_API_PROVIDER
    private val embeddingStatsFacade = OpenSearchEmbeddingStats(indexName = ROOT_INDEX, openSearchApiProvider = openSearchApiProvider)
    private val embeddingStore = OpenSearchEmbeddingStore(indexName = ROOT_INDEX, openSearchApiProvider = openSearchApiProvider)

    @Test
    fun `test vector count greater than zero`() = runTest {
        val vectorPair = MLVectorPair(
            denseVector = List(4096) { 0.1 },
            sparseVector = MLSparseVector.fromMap(mapOf("1" to 0.5)),
        )
        val metadata = MLVectorMetadata.from(mapOf("key" to "value"))
        embeddingStore.add(ROOT_NAMESPACE, vectorPair, metadata)

        sleep(2000)

        // Use the embeddingStatsFacade instance to get vector count
        val vectorCount = embeddingStatsFacade.getTotalVectorCount()
        assertThat(vectorCount).isGreaterThan(0)

        val namespaceVectorCount = embeddingStatsFacade.getNamespaceVectorCount(namespace = ROOT_NAMESPACE)
        assertThat(namespaceVectorCount).isNull()

        val namespaceVectorCounts = embeddingStatsFacade.getTopNamespaceVectorCounts(topN = 5)
        assertThat(namespaceVectorCounts).isEmpty()
    }
}
