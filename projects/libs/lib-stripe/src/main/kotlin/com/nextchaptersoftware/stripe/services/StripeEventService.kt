package com.nextchaptersoftware.stripe.services

import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.stripe.services.StripeUtils.cardType
import com.stripe.model.Event
import com.stripe.model.Invoice
import com.stripe.model.SetupIntent
import com.stripe.model.StripeObject
import com.stripe.model.Subscription
import kotlin.time.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class StripeEventService(
    private val stripeService: StripeService,
    private val orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    private val billingEventEnqueueService: BillingEventEnqueueService,
) : TypedEventHandler<Event> {
    override suspend fun handle(event: Event): Boolean {
        fun getStripeObject(): StripeObject {
            val dataObjectDeserializer = event.dataObjectDeserializer
            return when (dataObjectDeserializer.`object`.isPresent) {
                true -> dataObjectDeserializer.`object`.get()
                else -> throw IllegalStateException("Stripe event object is missing")
            }
        }

        return withLoggingContextAsync(
            "eventId" to event.id,
            "eventType" to event.type,
        ) {
            when (event.type) {
                "setup_intent.succeeded" -> {
                    LOGGER.debugAsync { "Handling setup_intent.succeeded" }

                    val setupIntent = getStripeObject() as SetupIntent

                    val paymentMethod = stripeService.getPaymentMethod(
                        paymentMethodId = setupIntent.paymentMethod,
                    )

                    stripeService.setDefaultPaymentMethod(
                        customerId = setupIntent.customer,
                        paymentMethod = paymentMethod,
                    )

                    paymentMethod.card?.let { card ->
                        orgBillingStore.setCardInfo(
                            stripeCustomerId = paymentMethod.customer,
                            cardType = cardType(card),
                            cardLastFour = card.last4,
                            cardExpMonth = card.expMonth.toInt(),
                            cardExpYear = card.expYear.toInt(),
                        )
                    }

                    true
                }

                "invoice.payment_succeeded" -> {
                    LOGGER.debugAsync { "Handling invoice.payment_succeeded" }

                    val invoice = getStripeObject() as Invoice

                    val subscription = stripeService.getSubscription(customerId = invoice.customer)
                        ?: error("Subscription not found for customer: ${invoice.customer}")

                    orgBillingStore.setBillingPeriod(
                        stripeCustomerId = invoice.customer,
                        currentBillingPeriodStart = Instant.fromEpochSeconds(subscription.currentPeriodStart),
                        currentBillingPeriodEnd = Instant.fromEpochSeconds(subscription.currentPeriodEnd), // next get charged at this time
                    )

                    maybeDowngrade(stripeCustomerId = subscription.customer)

                    true
                }

                "invoice.payment_failed" -> {
                    LOGGER.debugAsync { "Handling invoice.payment_failed" }

                    // TODO send email to customer to update payment method
                    val invoice = getStripeObject() as Invoice

                    // Still downgrade the plan even though payment failed
                    maybeDowngrade(stripeCustomerId = invoice.customer)

                    true
                }

                "customer.subscription.deleted" -> { // Occurs when a team downgrades to the free plan
                    LOGGER.debugAsync { "Handling customer.subscription.deleted" }

                    val subscription = getStripeObject() as Subscription

                    orgBillingStore.setBillingPeriod(
                        stripeCustomerId = subscription.customer,
                        currentBillingPeriodStart = null,
                        currentBillingPeriodEnd = null,
                    )

                    maybeDowngrade(stripeCustomerId = subscription.customer)

                    true
                }

                else -> {
                    LOGGER.errorAsync { "Unhandled Stripe event type received" }

                    true
                }
            }
        }
    }

    private suspend fun maybeDowngrade(
        stripeCustomerId: String,
    ) {
        if (orgBillingStore.maybeDowngradeToNextPlan(stripeCustomerId = stripeCustomerId)) {
            orgBillingStore.findByStripeCustomerId(stripeCustomerId = stripeCustomerId)?.let {
                billingEventEnqueueService.enqueueCapabilityDowngradeEvent(orgId = it.orgId)
            }
        }
    }
}
