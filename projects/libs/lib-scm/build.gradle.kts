plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

configurations {
    create("test")
}

dependencies {
    implementation(project(":projects:clients:client-scm", "default"))
    implementation(project(":projects:libs:lib-common", "default"))
    implementation(project(":projects:libs:lib-embedding", "default"))
    implementation(project(":projects:libs:lib-event-queue", "default"))
    implementation(project(":projects:libs:lib-log-kotlin", "default"))
    implementation(project(":projects:libs:lib-search-events", "default"))
    implementation(project(":projects:libs:lib-user-secret", "default"))
    implementation(project(":projects:models", "default"))
    implementation(project(":projects:libs:lib-repo-access", configuration = "default"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:clients:client-scm", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.postgresql)
    testImplementation(testLibs.bundles.test.ktor)
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=io.lettuce.core.ExperimentalLettuceCoroutinesApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }

    register<Jar>("testArchive") {
        archiveBaseName.set("${project.name}-test")
        from(project.the<SourceSetContainer>()["test"].output)
    }
}

artifacts {
    add("test", tasks["testArchive"])
}
