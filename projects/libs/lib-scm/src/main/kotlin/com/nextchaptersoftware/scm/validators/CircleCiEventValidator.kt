package com.nextchaptersoftware.scm.validators

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.security.HMACAuthenticator
import com.sksamuel.hoplite.Secret

private val LOGGER = mu.KotlinLogging.logger {}

object CircleCiEventValidator {

    fun isEventValid(
        secret: Secret,
        signature: String,
        payload: String,
    ): <PERSON><PERSON><PERSON> {
        val authenticator = HMACAuthenticator(
            authenticationSecret = secret,
            signaturePrefix = "v1=",
        )
        return runSuspendCatching {
            authenticator.validate(
                signature = signature,
                payload = payload,
            )
        }
            .onFailure {
                LOGGER.errorSync(it) { "HMAC authentication failed for CircleCI payload" }
            }
            .isSuccess
    }
}
