package com.nextchaptersoftware.scm.validators

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.security.HMACAuthenticator.Algorithm
import com.sksamuel.hoplite.Secret

private val LOGGER = mu.KotlinLogging.logger {}

object BuildkiteEventValidator {

    fun isEventValid(
        secret: Secret,
        signature: String,
        payload: String,
    ): <PERSON><PERSON><PERSON> {
        val fragments = signature.split(",")
        val timestamp = fragments[0].split("=")[1]
        val signature = fragments[1].split("=")[1]

        val authenticator = HMACAuthenticator(
            authenticationSecret = secret,
        )

        return runSuspendCatching {
            authenticator.validate(
                payload = "$timestamp.$payload",
                signature = signature,
                algorithm = Algorithm.SHA256,
            )
        }
            .onFailure {
                LOGGER.errorSync(it) { "HMAC authentication failed for Buildkite payload" }
            }
            .isSuccess
    }
}
