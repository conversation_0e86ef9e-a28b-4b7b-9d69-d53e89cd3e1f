package com.nextchaptersoftware.scm.queue.payloads

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.models.ScmTeamId
import kotlinx.serialization.Serializable

@Serializable
sealed class ScmEvent {
    abstract val priority: MessagePriority
    abstract val teamId: ScmTeamId

    @Serializable
    data class InstallRepos(
        override val teamId: ScmTeamId,
        val additionalRepoExternalIds: List<String>,
    ) : ScmEvent() {
        override val priority: MessagePriority = MessagePriority.HIGH
    }

    @Serializable
    data class RefreshResources(
        override val teamId: ScmTeamId,
    ) : ScmEvent() {
        override val priority: MessagePriority = MessagePriority.HIGH
    }
}
