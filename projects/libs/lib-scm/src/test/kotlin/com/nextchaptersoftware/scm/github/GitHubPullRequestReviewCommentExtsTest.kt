package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.asThreadedComments
import com.nextchaptersoftware.test.utils.TestUtils.getResource
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitHubPullRequestReviewCommentExtsTest {

    @Test
    fun asThreads() {
        val body = getResource(this, "/scm/github/PullRequestReviewComments.json")
        val comments = body.decode<List<GitHubPullRequestReviewComment>>().map { it.asScmPrComment }
        assertThat(comments).hasSize(14)
        val threads = comments.asThreadedComments
        assertThat(threads).hasSize(6)
        assertThat(threads[0]).hasSize(1)
        assertThat(threads[1]).hasSize(2)
        assertThat(threads[2]).hasSize(1)
        assertThat(threads[3]).hasSize(3)
        assertThat(threads[4]).hasSize(5)
        assertThat(threads[5]).hasSize(2)
        assertThat(threads).allMatch { it.first().inReplyToId == null }
        assertThat(threads).allMatch { thread -> thread.drop(1).all { it.inReplyToId == thread.first().id } }
        assertThat(threads).allMatch { it.isSorted }
        assertThat(threads.flatten()).allMatch { it.pullRequestNumber == 15 }
    }

    private val List<ScmPrComment>.isSorted: Boolean
        get() = zipWithNext().all { (a, b) -> a.createdAt < b.createdAt }
}
