package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.models.EnterpriseAppConfig
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubReaction.Emoji
import com.nextchaptersoftware.scm.github.models.GithubReactionBody
import com.nextchaptersoftware.scm.utils.NoOpEnterpriseAppConfigOrgIdsResolver
import com.nextchaptersoftware.scm.utils.TestUtils.testMockEngineHandler
import com.nextchaptersoftware.test.utils.TestUtils.getResource
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.sksamuel.hoplite.Secret
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.MockEngineConfig
import io.ktor.client.engine.mock.MockRequestHandleScope
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.client.request.HttpRequestData
import io.ktor.client.request.HttpResponseData
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import io.ktor.http.fullPath
import io.ktor.http.headersOf
import io.ktor.server.util.url
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.spy
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock

class GitHubUserApiTest {

    private val config = GlobalConfig.INSTANCE
    private val scmConfig = ScmConfig.INSTANCE
    private val mockEnterpriseAppConfigStore = mock<EnterpriseAppConfigStore>()

    private val scmUserApiFactory by lazy {
        ScmUserApiFactory(
            scmConfig = scmConfig,
            userSecretServiceResolver = UserSecretServiceResolver(
                userSecretServiceRSA = mock(),
                userSecretServiceAES = mock(),
            ),
            scmAuthApiFactory = ScmAuthApiFactory(
                authenticationConfig = config.authentication,
                scmConfig = scmConfig,
                scmWebFactory = ScmWebFactory(scmConfig, mockEnterpriseAppConfigStore),
                enterpriseAppConfigOrgIdsResolver = NoOpEnterpriseAppConfigOrgIdsResolver,
            ),
            appConfigStore = mockEnterpriseAppConfigStore,
            enterpriseAppConfigOrgIdsResolver = NoOpEnterpriseAppConfigOrgIdsResolver,
        )
    }

    @Suppress("LongMethod")
    @Test
    fun `retrieve user succeeds for GitHub Cloud`() = runTest {
        val handler: suspend MockRequestHandleScope.(HttpRequestData) -> HttpResponseData = { request ->
            assertThat(request.headers[HttpHeaders.Authorization]).isEqualTo("token userToken")
            assertThat(request.url.host).isEqualTo("api.github.com")
            assertThat(request.url.fullPath).startsWith("/user")
            when (request.url.encodedPath) {
                "/user" -> respond(
                    content = ByteReadChannel(
                        """
                        {
                            "id":12345,
                            "avatar_url":"http://example.com/avatar",
                            "html_url":"http://example.com/html",
                            "login":"username",
                            "name":"displayName",
                            "type": "User"
                        }
                        """.trimIndent(),
                    ),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )

                "/user/emails" -> respond(
                    content = ByteReadChannel(
                        """
                        [
                            {
                                "email":"<EMAIL>",
                                "primary":true,
                                "verified":true
                            },
                            {
                                "email":"<EMAIL>",
                                "primary":false,
                                "verified":true
                            },
                            {
                                "email":"<EMAIL>",
                                "primary":false,
                                "verified":false
                            }
                        ]
                        """.trimIndent(),
                    ),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )

                else -> respondError(HttpStatusCode.BadRequest)
            }
        }

        scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }
            .also {
                val user = it.user()
                assertThat(user.externalId).isEqualTo("12345")
                assertThat(user.avatarUrl).isEqualTo(Url("http://example.com/avatar"))
                assertThat(user.htmlUrl).isEqualTo(Url("http://example.com/html"))
                assertThat(user.username).isEqualTo("username")
                assertThat(user.displayName).isEqualTo("displayName")
                assertThat(user.primaryEmail.value).isEqualTo("<EMAIL>")
                assertThat(user.emails.map { it.value }).isEqualTo(listOf("<EMAIL>", "<EMAIL>"))
                assertThat(user.oauthTokens.accessToken.value).isEqualTo("userToken")
            }
    }

    @Suppress("LongMethod")
    @Test
    fun `retrieve user succeeds for GitHub Enterprise`() = runTest {
        val handler: suspend MockRequestHandleScope.(HttpRequestData) -> HttpResponseData = { request ->
            assertThat(request.headers[HttpHeaders.Authorization]).isEqualTo("token userToken")
            assertThat(request.url.host).isEqualTo("github.nike.com")
            assertThat(request.url.fullPath).startsWith("/api/v3/user")
            when (request.url.encodedPath) {
                "/api/v3/user" -> respond(
                    content = ByteReadChannel(
                        """
                        {
                            "id":12345,
                            "avatar_url":"http://example.com/avatar",
                            "html_url":"http://example.com/html",
                            "login":"username",
                            "name":"displayName",
                            "type": "User"
                        }
                        """.trimIndent(),
                    ),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )

                "/api/v3/user/emails" -> respond(
                    content = ByteReadChannel(
                        """
                        [
                            {
                                "email":"<EMAIL>",
                                "primary":true,
                                "verified":true
                            },
                            {
                                "email":"<EMAIL>",
                                "primary":false,
                                "verified":true
                            },
                            {
                                "email":"<EMAIL>",
                                "primary":false,
                                "verified":false
                            }
                        ]
                        """.trimIndent(),
                    ),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )

                else -> respondError(HttpStatusCode.BadRequest)
            }
        }

        val enterpriseProviderId = EnterpriseAppConfigId.random()
        `when`(mockEnterpriseAppConfigStore.getById(any(), eq(Provider.GitHubEnterprise))).thenReturn(
            EnterpriseAppConfig.GitHub(
                id = enterpriseProviderId,
                externalAppId = "123",
                hostAndPort = "github.nike.com",
                slug = "slug",
                owner = "differentOwner",
                appHtmlUrl = Url("github.nike.com"),
                oauthClientId = "oauthClientId2",
                oauthClientSecretEncrypted = "oauthClientSecretEncrypted".encodeToByteArray(),
                webhookSecretEncrypted = "webhookSecretEncrypted".encodeToByteArray(),
                privateKeyPemEncrypted = "privateKeyPemEncrypted".encodeToByteArray(),
            ),
        )

        scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHubEnterprise(enterpriseProviderId),
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }.also {
            val user = it.user()
            assertThat(user.externalId).isEqualTo("12345")
            assertThat(user.avatarUrl).isEqualTo(Url("http://example.com/avatar"))
            assertThat(user.htmlUrl).isEqualTo(Url("http://example.com/html"))
            assertThat(user.username).isEqualTo("username")
            assertThat(user.displayName).isEqualTo("displayName")
            assertThat(user.primaryEmail.value).isEqualTo("<EMAIL>")
            assertThat(user.emails.map(EmailAddress::value)).isEqualTo(listOf("<EMAIL>", "<EMAIL>"))
            assertThat(user.oauthTokens.accessToken.value).isEqualTo("userToken")
        }
    }

    @Test
    fun `retrieve user fails throws`() = runTest {
        val handler: suspend MockRequestHandleScope.(HttpRequestData) -> HttpResponseData = {
            respondError(HttpStatusCode.Unauthorized)
        }
        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = OAuthTokens(accessToken = Secret("")),
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        assertThrows<Exception> {
            client.user()
        }
    }

    @Test
    fun `create issue comment`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "issues", "920", "comments")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Post)
            val body = getResource(
                this,
                "/scm/github/IssueComment.json",
            )
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }
        val comment = client.createIssueComment(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            issueNumber = 920,
            body = GitHubBody("Creating an issue comment"),
        )
        assertThat(comment.createdAt).isEqualTo(Instant.parse("2022-08-25T00:14:21Z"))
    }

    @Test
    fun `update issue comment`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "issues", "comments", "1226621894")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Patch)
            val body = getResource(
                this,
                "/scm/github/IssueComment.json",
            )
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        val comment = client.updateIssueComment(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            commentId = "1226621894",
            body = GitHubBody("editing a reply from the API"),
        )
        assertThat(comment.createdAt).isEqualTo(Instant.parse("2022-08-25T00:14:21Z"))
    }

    @Test
    fun `delete issue comment`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "issues", "comments", "1226621894")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Delete)
            respond(
                content = ByteReadChannel(""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        client.deleteIssueComment(owner = "NextChapterSoftware", repoName = "unblocked", commentId = "1226621894")
    }

    @Test
    fun `update pull request review`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "pulls", "920", "reviews", "1074984345")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Put)
            val body = getResource(
                this,
                "/scm/github/PullRequestReview.json",
            )
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        val review = client.updateReviewComment(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            pullRequestNumber = 920,
            reviewId = "1074984345",
            body = GitHubBody("editing a review from the API"),
        )
        assertThat(review.submittedAt).isEqualTo(Instant.parse("2022-08-17T03:06:34Z"))
    }

    @Test
    fun `create pull request review comment`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf(
                        "repos",
                        "NextChapterSoftware",
                        "unblocked",
                        "pulls",
                        "937",
                        "comments",
                        "854586541",
                        "replies",
                    )
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Post)
            val body = getResource(
                this,
                "/scm/github/PullRequestReviewComment.json",
            )
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        val comment = client.createPullRequestReviewComment(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            pullRequestNumber = 937,
            commentId = "854586541",
            body = GitHubBody("creating a reply from the API"),
        )
        assertThat(comment.createdAt).isEqualTo(Instant.parse("2022-04-20T23:02:22Z"))
    }

    @Test
    fun `update pull request review comment`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "pulls", "comments", "854628315")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Patch)
            val body = getResource(
                this,
                "/scm/github/PullRequestReviewComment.json",
            )
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        val comment = client.updatePullRequestReviewComment(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            commentId = "854628315",
            body = GitHubBody("creating a reply from the API"),
        )
        assertThat(comment.createdAt).isEqualTo(Instant.parse("2022-04-20T23:02:22Z"))
    }

    @Test
    fun `delete pull request review comment`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "pulls", "comments", "854628315")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Delete)
            respond(
                content = ByteReadChannel(""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }.let(::spy)

        val client = ScmUserApiFactory(
            scmConfig = scmConfig,
            userSecretServiceResolver = UserSecretServiceResolver(
                userSecretServiceRSA = mock(),
                userSecretServiceAES = mock(),
            ),
            scmAuthApiFactory = ScmAuthApiFactory(
                authenticationConfig = config.authentication,
                scmConfig = scmConfig,
                scmWebFactory = ScmWebFactory(scmConfig, mockEnterpriseAppConfigStore),
                enterpriseAppConfigOrgIdsResolver = NoOpEnterpriseAppConfigOrgIdsResolver,
            ),
            appConfigStore = mockEnterpriseAppConfigStore,
            enterpriseAppConfigOrgIdsResolver = NoOpEnterpriseAppConfigOrgIdsResolver,
        ).getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        client.deletePullRequestReviewComment(owner = "NextChapterSoftware", repoName = "unblocked", commentId = "854628315")
    }

    @Test
    fun `create pull request review comment reaction`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf(
                        "repos",
                        "NextChapterSoftware",
                        "unblocked",
                        "pulls",
                        "comments",
                        "1",
                        "reactions",
                    )
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Post)
            val body = getResource(
                this,
                "/scm/github/PullRequestReviewCommentReaction.json",
            )
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        val reaction = client.createPullRequestReviewCommentReaction(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            commentId = "1",
            body = GithubReactionBody(Emoji.Heart),
        )
        assertThat(reaction.content).isEqualTo(Emoji.Heart)
    }

    @Test
    fun `delete pull request review comment reaction`() = runTest {
        val handler = testMockEngineHandler { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repos", "NextChapterSoftware", "unblocked", "pulls", "comments", "1", "reactions", "1")
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Delete)
            respond(
                content = ByteReadChannel(""),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = scmUserApiFactory.getApiFromTokens(
            orgIds = null,
            tokens = oauthTokens,
            scm = Scm.GitHub,
            engineFactory = MockEngine,
        ) {
            (this as? MockEngineConfig)?.let {
                requestHandlers.add(handler)
            }
        }

        client.deletePullRequestReviewCommentReaction(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            commentId = "1",
            reactionId = "1",
        )
    }

    private val oauthTokens
        get() = OAuthTokens(accessToken = Secret("userToken"))
}
