package com.nextchaptersoftware.userengagement

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.MLInferenceCategory
import com.nextchaptersoftware.db.models.MLInferenceModel
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.SlackAutoAnswerModel
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.ActiveSegmentation
import com.nextchaptersoftware.db.stores.Cohort
import com.nextchaptersoftware.utils.KotlinUtils.required
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.neq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.notInList
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.booleanLiteral
import org.jetbrains.exposed.sql.countDistinct
import org.jetbrains.exposed.sql.min
import org.jetbrains.exposed.sql.or
import org.jetbrains.exposed.sql.update

class SlackQuestionAnalyticsService {

    @Suppress("MagicNumber")
    suspend fun backfillSlackMemberFirstInteractions() {
        firstInteractionBySlackMember().entries.chunked(100).forEach { batch ->
            suspendedTransaction {
                batch.forEach { (memberId, firstInteractionAt) ->
                    MemberModel.update({
                        AllOp(
                            MemberModel.id eq memberId,
                            MemberModel.firstInteractionAt eq null,
                        )
                    }) {
                        it[MemberModel.firstInteractionAt] = firstInteractionAt
                    }
                }
            }
        }
    }

    /**
     * Returns the date of the first interaction for every Slack member.
     * The first interaction is defined as the first time a Slack member asked a question.
     */
    private suspend fun firstInteractionBySlackMember(): Map<MemberId, Instant> {
        return suspendedTransaction {
            MLInferenceModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MemberModel,
                    otherColumn = MemberModel.orgMember,
                    onColumn = MLInferenceModel.questionerOrgMember,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = SlackAutoAnswerModel,
                    otherColumn = SlackAutoAnswerModel.inference,
                    onColumn = MLInferenceModel.id,
                )
                .select(MemberModel.id, MLInferenceModel.createdAt.min())
                .whereAll(
                    MLInferenceModel.botMessage.isNotNull(),
                    MLInferenceModel.isSuggestion eq false,
                    MLInferenceModel.category eq MLInferenceCategory.Search,
                    MLInferenceModel.productAgent eq ProductAgentType.Slack,
                    SlackAutoAnswerModel.id eq null,
                    IdentityModel.provider eq Provider.Slack,
                    IdentityModel.username neq "slackbot",
                    Coalesce(IdentityModel.isBot, booleanLiteral(false)) eq false,
                    MemberModel.firstInteractionAt eq null,
                )
                .groupBy(MemberModel.id).associate {
                    val firstQuestionAt = (it[MLInferenceModel.createdAt.min()] as? Instant).required()
                    it[MemberModel.id].value to firstQuestionAt
                }
        }
    }

    suspend fun getActiveMembersInRange(
        period: OpenEndRange<Instant>,
        orgId: OrgId,
    ): Set<MemberId> {
        return getActiveMembersInRange(
            orgClause = OrgModel.id eq orgId,
            period = period,
        )
    }

    suspend fun getActiveMembersInRange(
        period: OpenEndRange<Instant>,
        excludeIds: List<OrgId>,
    ): Set<MemberId> {
        return getActiveMembersInRange(
            orgClause = OrgModel.id notInList excludeIds,
            period = period,
        )
    }

    private suspend fun getActiveMembersInRange(
        orgClause: Op<Boolean>,
        period: OpenEndRange<Instant>,
    ): Set<MemberId> {
        val primaryMember = MemberModel.alias("pm")
        val primaryIdentity = IdentityModel.alias("pi")
        val primaryPerson = PersonModel.alias("pp")

        return suspendedTransaction {
            MLInferenceModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgModel,
                    otherColumn = OrgModel.id,
                    onColumn = MLInferenceModel.org,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MemberModel,
                    otherColumn = MemberModel.orgMember,
                    onColumn = MLInferenceModel.questionerOrgMember,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = PersonModel,
                    otherColumn = PersonModel.id,
                    onColumn = IdentityModel.person,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = SlackAutoAnswerModel,
                    otherColumn = SlackAutoAnswerModel.inference,
                    onColumn = MLInferenceModel.id,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = primaryMember,
                    otherColumn = primaryMember[MemberModel.id],
                    onColumn = MemberModel.associatedPrimaryMember,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = primaryIdentity,
                    otherColumn = primaryIdentity[IdentityModel.id],
                    onColumn = primaryMember[MemberModel.identity],
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = primaryPerson,
                    otherColumn = primaryPerson[PersonModel.id],
                    onColumn = primaryIdentity[IdentityModel.person],
                )
                .select(MemberModel.id)
                .whereAll(
                    MLInferenceModel.botMessage.isNotNull(),
                    MLInferenceModel.isSuggestion eq false,
                    MLInferenceModel.category eq MLInferenceCategory.Search,
                    MLInferenceModel.createdAt greaterEq period.start,
                    MLInferenceModel.createdAt less period.endExclusive,
                    MLInferenceModel.productAgent eq ProductAgentType.Slack,
                    orgClause,
                    ((primaryPerson[PersonModel.id] eq null) or (primaryPerson[PersonModel.createdAt] greaterEq period.endExclusive)),
                    ((PersonModel.id eq null) or (PersonModel.createdAt greaterEq period.endExclusive)),
                    SlackAutoAnswerModel.id eq null,
                    IdentityModel.provider eq Provider.Slack,
                    IdentityModel.username neq "slackbot",
                    Coalesce(IdentityModel.isBot, booleanLiteral(false)) eq false,
                )
                .withDistinct()
                .map { it[MemberModel.id].value }
                .toSet()
        }
    }

    @Suppress("LongMethod")
    suspend fun countDistinctMembers(
        period: OpenEndRange<Instant>,
        previouslyActiveIds: Set<MemberId>,
        excludeIds: List<OrgId>,
        segment: ActiveSegmentation,
        cohort: Cohort,
    ): Int {
        val primaryMember = MemberModel.alias("pm")
        val primaryIdentity = IdentityModel.alias("pi")
        val primaryPerson = PersonModel.alias("pp")

        return suspendedTransaction {
            MLInferenceModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = OrgModel,
                    otherColumn = OrgModel.id,
                    onColumn = MLInferenceModel.org,
                ) {
                    when (segment) {
                        ActiveSegmentation.NewAcquisition,
                            -> OrgModel.createdAt greater period.start

                        ActiveSegmentation.ExistingAcquisition,
                            -> OrgModel.createdAt lessEq period.start

                        ActiveSegmentation.Active,
                        ActiveSegmentation.Returning,
                        ActiveSegmentation.All,
                            -> Op.TRUE
                    }
                }
                .join(ScmTeamModel, JoinType.INNER, ScmTeamModel.org, OrgModel.id) {
                    when (cohort) {
                        Cohort.Solo -> ScmTeamModel.memberCount eq 1
                        Cohort.Team -> ScmTeamModel.memberCount greater 1
                        Cohort.All -> Op.TRUE
                    }
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = MemberModel,
                    otherColumn = MemberModel.orgMember,
                    onColumn = MLInferenceModel.questionerOrgMember,
                ) {
                    when (segment) {
                        ActiveSegmentation.NewAcquisition,
                        ActiveSegmentation.ExistingAcquisition,
                            -> MemberModel.firstInteractionAt greater period.start

                        ActiveSegmentation.Active,
                            -> (MemberModel.firstInteractionAt less period.start) and (MemberModel.id inList previouslyActiveIds)

                        ActiveSegmentation.Returning,
                            -> (MemberModel.firstInteractionAt less period.start) and (MemberModel.id notInList previouslyActiveIds)

                        ActiveSegmentation.All,
                            -> Op.TRUE
                    }
                }
                .join(
                    joinType = JoinType.INNER,
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = PersonModel,
                    otherColumn = PersonModel.id,
                    onColumn = IdentityModel.person,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = SlackAutoAnswerModel,
                    otherColumn = SlackAutoAnswerModel.inference,
                    onColumn = MLInferenceModel.id,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = primaryMember,
                    otherColumn = primaryMember[MemberModel.id],
                    onColumn = MemberModel.associatedPrimaryMember,
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = primaryIdentity,
                    otherColumn = primaryIdentity[IdentityModel.id],
                    onColumn = primaryMember[MemberModel.identity],
                )
                .join(
                    joinType = JoinType.LEFT,
                    otherTable = primaryPerson,
                    otherColumn = primaryPerson[PersonModel.id],
                    onColumn = primaryIdentity[IdentityModel.person],
                )
                .select(MemberModel.id.countDistinct())
                .whereAll(
                    MLInferenceModel.botMessage.isNotNull(),
                    MLInferenceModel.isSuggestion eq false,
                    MLInferenceModel.category eq MLInferenceCategory.Search,
                    MLInferenceModel.createdAt greaterEq period.start,
                    MLInferenceModel.createdAt less period.endExclusive,
                    MLInferenceModel.productAgent eq ProductAgentType.Slack,
                    OrgModel.id notInList excludeIds,
                    ((primaryPerson[PersonModel.id] eq null) or (primaryPerson[PersonModel.createdAt] greaterEq period.endExclusive)),
                    ((PersonModel.id eq null) or (PersonModel.createdAt greaterEq period.endExclusive)),
                    SlackAutoAnswerModel.id eq null,
                    IdentityModel.provider eq Provider.Slack,
                    IdentityModel.username neq "slackbot",
                    Coalesce(IdentityModel.isBot, booleanLiteral(false)) eq false,
                )
                .first()[MemberModel.id.countDistinct()]
                .toInt()
        }
    }
}
