plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:clients:client-ml", "default"))
    implementation(project(":projects:libs:lib-document-embedding", "default"))
    implementation(project(":projects:libs:lib-embedding", "default"))
    implementation(project(":projects:libs:lib-event-queue", "default"))

    testImplementation(project(":projects:models", "test"))
    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:libs:lib-log", "test"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)
    testImplementation(testLibs.bundles.test.postgresql)
}
