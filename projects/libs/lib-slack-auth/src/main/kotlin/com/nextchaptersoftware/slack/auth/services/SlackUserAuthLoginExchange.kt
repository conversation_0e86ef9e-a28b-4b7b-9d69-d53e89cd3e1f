package com.nextchaptersoftware.slack.auth.services

import com.nextchaptersoftware.api.services.PersonUpsertService
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.OrgAndMemberAndIdentity
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.membership.alignment.IdentityAlignment
import com.nextchaptersoftware.slack.notify.UnblockedSlackUserSignInEvent
import com.nextchaptersoftware.slack.services.SlackMemberModelService
import com.nextchaptersoftware.slack.services.SlackUserConnectService
import com.slack.api.methods.response.openid.connect.OpenIDConnectUserInfoResponse
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SlackUserAuthLoginExchange(
    private val slackOpenIdConnectService: SlackOpenIdConnectService,
    private val slackMemberModelService: SlackMemberModelService,
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    private val slackUserConnectService: SlackUserConnectService,
    private val identityAlignment: IdentityAlignment,
    private val personUpsertService: PersonUpsertService,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val identityStore: IdentityStore = Stores.identityStore,
) {
    suspend fun handleAuthExchange(oAuthTokenExchangeContext: OAuthTokenExchangeContext): Identity {
        val openIdConnectResponse = slackOpenIdConnectService.performOpenIdConnect(
            orgId = null,
            code = oAuthTokenExchangeContext.code,
        ) { openIdSignIn }

        return withLoggingContextAsync(
            "slackTeamId" to openIdConnectResponse.userInfoResponse.teamId,
            "slackUserId" to openIdConnectResponse.userInfoResponse.userId,
        ) {
            runSuspendCatching {
                val installation = getInstallation(userInfoResponse = openIdConnectResponse.userInfoResponse)
                val memberAndIdentity = upsertMember(installation = installation, userInfoResponse = openIdConnectResponse.userInfoResponse)
                triggerPostInstallation(orgAndMemberAndIdentity = memberAndIdentity)

                LOGGER.infoAsync(
                    "memberAndIdentity" to memberAndIdentity,
                ) { "Succeeded in doing login exchange for slack user" }

                memberAndIdentity.identity
            }.onFailure {
                LOGGER.errorAsync(it) { "Failed to do login exchange for slack user" }
            }.getOrThrow()
        }
    }

    @Suppress("ktlint:nextchaptersoftware:no-single-or-null-expression-rule")
    private suspend fun getInstallation(userInfoResponse: OpenIDConnectUserInfoResponse): Installation {
        val externalTeamId: String = requireNotNull(userInfoResponse.teamId) { "Expected slack external teamId" }

        return installationStore.findAllByExternalId(
            provider = Provider.Slack,
            installationExternalId = externalTeamId,
        ).singleOrNull() ?: throw UnknownSlackInstallationException(userInfoResponse.teamName)
    }

    @Suppress("ThrowsCount")
    private suspend fun upsertMember(
        installation: Installation,
        userInfoResponse: OpenIDConnectUserInfoResponse,
    ): OrgAndMemberAndIdentity {
        val slackTeamDaos = slackTeamStore.findBySlackExternalTeamId(
            slackExternalTeamId = userInfoResponse.teamId,
        ) ?: throw BadRequestException("Could not find slack team")

        val member = slackMemberModelService.upsertOpenIdConnect(
            orgId = slackTeamDaos.org.idValue,
            installationId = installation.id,
            slackTeamId = slackTeamDaos.slackTeam.idValue,
            userInfoResponse = userInfoResponse,
            personId = null,
        ).asDataModel()

        val identity = identityStore.findById(identityId = member.identityId)
            ?: throw BadRequestException("Identity does not exist")

        val resolvedIdentity = if (identity.person == null) {
            personUpsertService.upsertPerson(identity = identity, currentlySignedInPersonId = null)
            identityStore.findById(identityId = member.identityId)
        } else {
            identity
        } ?: throw BadRequestException("Identity does not exist")

        slackUserConnectService.triggerUserConnect(
            trigger = null,
            event = UnblockedSlackUserSignInEvent.UNBLOCKED_SLACK_USER_SIGNED_IN,
            orgId = slackTeamDaos.org.idValue,
            slackTeamId = slackTeamDaos.slackTeam.idValue,
            slackMemberId = member.id,
            slackChannelId = null,
            slackThreadTs = null,
            origin = null,
        )

        return OrgAndMemberAndIdentity(member = member, identity = resolvedIdentity, org = slackTeamDaos.org.asDataModel())
    }

    // Step 3: Trigger post-installation logic
    private suspend fun triggerPostInstallation(orgAndMemberAndIdentity: OrgAndMemberAndIdentity) {
        LOGGER.infoAsync { "Triggering post-installation tasks for Slack auth" }
        orgAndMemberAndIdentity.identity.person?.let { person ->
            identityAlignment.alignMemberForPerson(
                member = orgAndMemberAndIdentity.member,
                personId = person,
                orgId = orgAndMemberAndIdentity.org.id,
            )
        }
    }
}

class UnknownSlackInstallationException(val slackWorkspaceName: String) : Exception(slackWorkspaceName)
