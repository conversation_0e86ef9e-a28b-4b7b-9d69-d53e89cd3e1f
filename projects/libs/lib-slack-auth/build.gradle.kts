plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    implementation(project(":projects:libs:lib-api", "default"))
    implementation(project(":projects:libs:lib-auth", "default"))
    implementation(project(":projects:libs:lib-auth-provider", "default"))
    implementation(project(":projects:libs:lib-slack", "default"))
    implementation(project(":projects:libs:lib-slack-bot-events", "default"))
    implementation(project(":projects:libs:lib-slack-events", "default"))
    implementation(project(":projects:libs:lib-slack-ingestion", "default"))

    testImplementation(testLibs.bundles.test.core)
}
