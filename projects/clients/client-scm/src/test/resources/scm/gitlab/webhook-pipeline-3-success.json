{"object_kind": "pipeline", "object_attributes": {"id": 1861159502, "iid": 13, "name": null, "ref": "mrtn/foo-2", "tag": false, "sha": "9884b8e6aa698b8a8543ef2849545e2b2eb2ed19", "before_sha": "5611da01d69b4b30909335911f684c6aa426ae11", "source": "push", "status": "success", "detailed_status": "passed", "stages": ["build", "test"], "created_at": "2025-06-09 20:32:42 UTC", "finished_at": "2025-06-09 23:20:55 UTC", "duration": 89, "queued_duration": 1, "variables": [], "url": "https://gitlab.com/martin-ncs/gitlab-test/-/pipelines/1861159502"}, "merge_request": null, "user": {"id": 21278870, "name": "<PERSON>", "username": "martin-ncs", "avatar_url": "https://secure.gravatar.com/avatar/1e2cec2d157877863e6e290ad79feb08feb2ce96a0d7fb885e73fce184b8dbbe?s=80&d=identicon", "email": "[REDACTED]"}, "project": {"id": 70675702, "name": "gitlab-test", "description": null, "web_url": "https://gitlab.com/martin-ncs/gitlab-test", "avatar_url": null, "git_ssh_url": "**************:martin-ncs/gitlab-test.git", "git_http_url": "https://gitlab.com/martin-ncs/gitlab-test.git", "namespace": "<PERSON>", "visibility_level": 0, "path_with_namespace": "martin-ncs/gitlab-test", "default_branch": "main", "ci_config_path": ""}, "commit": {"id": "9884b8e6aa698b8a8543ef2849545e2b2eb2ed19", "message": "qsdsada", "title": "qsdsada", "timestamp": "2025-06-09T20:32:40+00:00", "url": "https://gitlab.com/martin-ncs/gitlab-test/-/commit/9884b8e6aa698b8a8543ef2849545e2b2eb2ed19", "author": {"name": "<PERSON>", "email": "<EMAIL>"}}, "builds": [{"id": 10297416518, "stage": "build", "name": "build1", "status": "success", "created_at": "2025-06-09 20:32:42 UTC", "started_at": "2025-06-09 20:32:43 UTC", "finished_at": "2025-06-09 20:33:13 UTC", "duration": 29.837642, "queued_duration": 0.255127, "failure_reason": null, "when": "on_success", "manual": false, "allow_failure": false, "user": {"id": 21278870, "name": "<PERSON>", "username": "martin-ncs", "avatar_url": "https://secure.gravatar.com/avatar/1e2cec2d157877863e6e290ad79feb08feb2ce96a0d7fb885e73fce184b8dbbe?s=80&d=identicon", "email": "[REDACTED]"}, "runner": {"id": 12270848, "description": "2-green.saas-linux-small-amd64.runners-manager.gitlab.com/default", "runner_type": "instance_type", "active": true, "is_shared": true, "tags": ["saas-linux-small-amd64"]}, "artifacts_file": {"filename": null, "size": null}, "environment": null}, {"id": 10298471208, "stage": "test", "name": "test1", "status": "success", "created_at": "2025-06-09 23:20:24 UTC", "started_at": "2025-06-09 23:20:25 UTC", "finished_at": "2025-06-09 23:20:55 UTC", "duration": 29.960165, "queued_duration": 0.11776, "failure_reason": null, "when": "on_success", "manual": false, "allow_failure": false, "user": {"id": 21278870, "name": "<PERSON>", "username": "martin-ncs", "avatar_url": "https://secure.gravatar.com/avatar/1e2cec2d157877863e6e290ad79feb08feb2ce96a0d7fb885e73fce184b8dbbe?s=80&d=identicon", "email": "[REDACTED]"}, "runner": {"id": 12270857, "description": "4-green.saas-linux-small-amd64.runners-manager.gitlab.com/default", "runner_type": "instance_type", "active": true, "is_shared": true, "tags": ["saas-linux-small-amd64"]}, "artifacts_file": {"filename": null, "size": null}, "environment": null}, {"id": 10297416529, "stage": "test", "name": "test2", "status": "success", "created_at": "2025-06-09 20:32:42 UTC", "started_at": "2025-06-09 20:33:13 UTC", "finished_at": "2025-06-09 20:33:43 UTC", "duration": 29.719906, "queued_duration": 0.293811, "failure_reason": null, "when": "on_success", "manual": false, "allow_failure": false, "user": {"id": 21278870, "name": "<PERSON>", "username": "martin-ncs", "avatar_url": "https://secure.gravatar.com/avatar/1e2cec2d157877863e6e290ad79feb08feb2ce96a0d7fb885e73fce184b8dbbe?s=80&d=identicon", "email": "[REDACTED]"}, "runner": {"id": 12270852, "description": "3-green.saas-linux-small-amd64.runners-manager.gitlab.com/default", "runner_type": "instance_type", "active": true, "is_shared": true, "tags": ["saas-linux-small-amd64"]}, "artifacts_file": {"filename": null, "size": null}, "environment": null}]}