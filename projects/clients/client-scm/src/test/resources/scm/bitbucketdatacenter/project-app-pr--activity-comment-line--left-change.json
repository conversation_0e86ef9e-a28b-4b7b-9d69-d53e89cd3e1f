{"id": 356, "createdDate": 1717021872199, "user": {"name": "admin", "emailAddress": "<EMAIL>", "active": true, "displayName": "admin", "id": 3, "slug": "admin", "type": "NORMAL", "links": {"self": [{"href": "https://atlassian-test.secops.getunblocked.com:7990/users/admin"}]}}, "action": "COMMENTED", "commentAction": "ADDED", "comment": {"properties": {"repositoryId": 1}, "id": 32, "version": 0, "text": "left side", "author": {"name": "admin", "emailAddress": "<EMAIL>", "active": true, "displayName": "admin", "id": 3, "slug": "admin", "type": "NORMAL", "links": {"self": [{"href": "https://atlassian-test.secops.getunblocked.com:7990/users/admin"}]}}, "createdDate": 1717021872197, "updatedDate": 1717021872197, "comments": [], "threadResolved": false, "severity": "NORMAL", "state": "OPEN", "permittedOperations": {"editable": true, "transitionable": true, "deletable": true}}, "commentAnchor": {"fromHash": "85f905bd4ee9f6edb0427856b30efff2a88f8b3e", "toHash": "313e2f5887c53b66fbf86f8381bb9b40ac23f394", "line": 3, "lineType": "REMOVED", "fileType": "FROM", "path": "src/foo.sh", "diffType": "EFFECTIVE", "orphaned": false}, "diff": {"source": null, "destination": {"components": ["src", "foo.sh"], "parent": "src", "name": "foo.sh", "extension": "sh", "toString": "src/foo.sh"}, "hunks": [{"sourceLine": 1, "sourceSpan": 3, "destinationLine": 1, "destinationSpan": 3, "segments": [{"type": "CONTEXT", "lines": [{"destination": 1, "source": 1, "line": "#!/usr/bin/env bash", "repository": false}, {"destination": 2, "source": 2, "line": "", "repository": false}], "truncated": false}, {"type": "REMOVED", "lines": [{"destination": 3, "source": 3, "line": "echo test", "repository": false, "commentIds": [32]}], "truncated": false}, {"type": "ADDED", "lines": [{"destination": 3, "source": 4, "line": "echo this is a test", "repository": false}], "truncated": false}], "truncated": false}], "truncated": false, "properties": {"toHash": "313e2f5887c53b66fbf86f8381bb9b40ac23f394", "current": true, "fromHash": "85f905bd4ee9f6edb0427856b30efff2a88f8b3e"}}}