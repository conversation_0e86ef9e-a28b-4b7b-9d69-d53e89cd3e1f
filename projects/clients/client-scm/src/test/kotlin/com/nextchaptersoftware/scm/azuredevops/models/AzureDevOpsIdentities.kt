package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData

object AzureDevOpsIdentities {

    private val decoder = { text: String -> text.decode<AzureDevOpsIdentity>() }

    val <PERSON> by lazy {
        testData(
            file = "/scm/azure-devops/identity-matt.json",
            decoder = decoder,
        )
    }

    val <PERSON> by lazy {
        testData(
            file = "/scm/azure-devops/identity-mrtn.json",
            decoder = decoder,
        )
    }

    val <PERSON> by lazy {
        testData(
            file = "/scm/azure-devops/identity-richie.json",
            decoder = decoder,
        )
    }
}
