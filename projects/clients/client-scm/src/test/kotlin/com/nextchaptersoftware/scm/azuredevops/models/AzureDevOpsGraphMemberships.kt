package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData

object AzureDevOpsGraphMemberships {

    private val decoder = { text: String -> text.decode<AzureDevOpsGraphMembership>() }

    val aadMartin by lazy {
        testData(
            file = "/scm/azure-devops/user-aad-mrtn-membership.json",
            decoder = decoder,
        )
    }
    val msaRichie by lazy {
        testData(
            file = "/scm/azure-devops/user-msa-richie-membership.json",
            decoder = decoder,
        )
    }
}
