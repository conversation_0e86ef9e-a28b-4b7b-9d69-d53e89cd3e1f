package com.nextchaptersoftware.scm.github

import io.ktor.client.HttpClient
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class GitHubActionsClientTest {

    private val client = mock<HttpClient>()

    private fun createGitHubActionsClient() = GitHubActionsClient(
        baseUrl = "https://api.example.org/",
        client = client,
    )

    @Nested
    inner class WorkflowRunTest {
        @Test
        fun `workflowRunUrl -- runId`() {
            val client = createGitHubActionsClient()

            assertThat(
                client.workflowRunUrl(
                    repositoryId = "repoId",
                    runId = "runId",
                ),
            ).isEqualTo(
                "https://api.example.org/repositories/repoId/actions/runs/runId",
            )
        }

        @Test
        fun `workflowRunUrl -- list`() {
            val client = createGitHubActionsClient()

            assertThat(
                client.workflowRunUrl(
                    repositoryId = "repoId",
                ),
            ).isEqualTo(
                "https://api.example.org/repositories/repoId/actions/runs",
            )

            assertThat(
                client.workflowRunUrl(
                    repositoryId = "repoId",
                    headSha = "headSha",
                ),
            ).isEqualTo(
                "https://api.example.org/repositories/repoId/actions/runs?head_sha=headSha",
            )

            assertThat(
                client.workflowRunUrl(
                    repositoryId = "repoId",
                    checkSuiteId = "checkSuiteId",
                    branch = "branch",
                    headSha = "headSha",
                ),
            ).isEqualTo(
                "https://api.example.org/repositories/repoId/actions/runs?check_suite_id=checkSuiteId&branch=branch&head_sha=headSha",
            )
        }
    }
}
