package com.nextchaptersoftware.scm.azuredevops.models

import com.nextchaptersoftware.test.utils.TestDataFactory
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ArgumentsSource

class AzureDevOpsProfileTest {

    class Resources : TestDataFactory<AzureDevOpsProfile>(
        AzureDevOpsProfiles::<PERSON>,
        AzureDevOpsProfiles::MartinBasic,
    )

    @ParameterizedTest
    @ArgumentsSource(Resources::class)
    fun parse(make: () -> AzureDevOpsProfile) {
        val instance = make()
        assertThat(instance).isNotNull()
    }
}
