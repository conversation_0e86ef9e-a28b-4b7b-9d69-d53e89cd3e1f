package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.scm.ScmWeb
import io.ktor.http.URLBuilder
import io.ktor.http.Url

internal class GitLabWeb(
    private val webAuthority: String,
) : ScmWeb {

    // Not applicable
    override val appInstallGeneralUrl: Url? = null

    // Not applicable
    override fun getAppInstallTargetedUrl(externalId: String): Url? = null

    override val oauthTokenExchangeUrl: Url
        get() = Url("https://$webAuthority/oauth/token")

    override fun fileUrl(repo: Repo, filePath: String): Url {
        return URLBuilder("https://$webAuthority").apply {
            pathSegments = pathSegments
                .plus(repo.externalOwner.split("/"))
                .plus(listOf(repo.externalName, "-", "blob", "HEAD"))
                .plus(filePath.split("/", "\\"))
        }.build()
    }
}
