package com.nextchaptersoftware.scm.models

import com.nextchaptersoftware.db.models.BuildResult
import com.nextchaptersoftware.db.models.BuildStatus
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant

/** DEPRECATED: to be removed next */
interface CiExecutionLegacy {
    val id: String
    val displayName: String
    val htmlUrl: Url?
    val status: Status
    val result: Result
    val output: Flow<String>?
    val startedAt: Instant?
    val completedAt: Instant?

    val lastUpdatedAt get() = completedAt
        ?: startedAt
        ?: Instant.nowWithMicrosecondPrecision()

    val isCompleted: Boolean get() = status == Status.Complete
    val isSuccess: Boolean get() = result == Result.Success
    val isFailure: Boolean get() = result == Result.Failure

    enum class Status {
        Waiting,
        Running,
        Complete,
        ;

        fun asBuildStatus(): BuildStatus = when (this) {
            Waiting -> BuildStatus.Waiting
            Running -> BuildStatus.Running
            Complete -> BuildStatus.Complete
        }
    }

    enum class Result {
        Pending,
        Success,
        Failure,
        Unknown,
        ;

        fun asBuildResult(): BuildResult = when (this) {
            Pending -> BuildResult.Pending
            Success -> BuildResult.Success
            Failure -> BuildResult.Failure
            Unknown -> BuildResult.Ignored
        }
    }
}
