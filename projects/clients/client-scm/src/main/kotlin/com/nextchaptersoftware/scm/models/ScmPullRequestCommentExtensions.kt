package com.nextchaptersoftware.scm.models

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.markdown.MessageBodyExtensions.threadTitleFromPRComment
import com.nextchaptersoftware.models.SourceSnippet
import com.nextchaptersoftware.scm.diff.DiffHunkParser
import com.nextchaptersoftware.utils.UnblockedPRCommentSignature.withSignatureStripped
import com.nextchaptersoftware.utils.safeTake
import com.nextchaptersoftware.utils.substringBeforeNewline
import java.util.LinkedList
import java.util.Queue
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

/**
 * Creates a SourceSnippet from GitHubPullRequestReviewComment.diffHunk
 *
 * Note: The GitHub pull request review comment API returns a truncated
 * diff hunk where the last line in that hunk is the line that anchors
 * the comment (i.e. GitHubPullRequestReviewComment.line)
 *
 * @param [defaultContextLines] the number of lines to include before
 *                              the selected range
 * @return the source snippet
 */
fun ScmPrComment.LineLevel.sourceSnippet(
    defaultContextLines: Int = 3, // Same as GitHub (the range plus the 3 preceding lines)
): SourceSnippet {
    if (diffHunk.isBlank()) {
        return SourceSnippet.emptySnippet()
    }

    val finalStartLine = startLine ?: line ?: return SourceSnippet.emptySnippet()
    val finalEndLine = line ?: return SourceSnippet.emptySnippet()
    val finalRange = finalStartLine..finalEndLine
    val finalRangeSize = finalRange.last - finalRange.first

    // When the comment is moved by a newer commit, the [originalLine] for multi-line comments is not reliable.
    // Therefore, we reconstruct the [originalRange] using the final range size and the [originalStartLine].
    val originalStartLine = originalStartLine ?: originalLine ?: finalStartLine
    val originalRange = originalStartLine..(originalStartLine + finalRangeSize)

    // The diff hunk is for the original lines, not the final lines.
    val lines = DiffHunkParser(diffHunk).parseSnippet(originalRange.last)
        .takeLast(finalRangeSize + 1 + defaultContextLines)

    // Line number of the snippet's first line, not the first line of the selected range.
    val firstLine = originalRange.last - lines.size + 1

    // There are multiple valid snippets here: one for the original commit and one for the final commit.
    // Currently, we choose the original snippet, since it matches the snippet shown on the Conversation page of the GitHub PR,
    // which is where the View in GitHub link points to.
    //
    // Ideally, we should construct both snippets and associate each to their respective original or final commits.
    //
    //   - ORIGINAL
    //     SourceSnippet(firstLine = firstLine, lines = lines)
    //
    //   - FINAL
    //     // This is how much the final range has moved from the original range.
    //     val snippetLineDelta = finalStartLine - originalStartLine
    //     SourceSnippet(firstLine = firstLine + snippetLineDelta, lines = lines)
    return SourceSnippet(firstLine = firstLine, lines = lines)
}

// Important to use the [startLine line] range instead of the [originalStartLine originalLine] range,
// because this gives Unblocked the best hint to where to snippet lives after changes have been made.
fun ScmPrComment.LineLevel.lineRange(): IntRange? {
    val lineEnd = line ?: return null
    val lineStart = startLine ?: lineEnd
    return lineStart..lineEnd
}

@Suppress("MagicNumber")
private val ScmPrComment.defaultThreadTitle: String
    get() = body.substringBeforeNewline.safeTake(2000)

val ScmPrComment.threadTitle: String
    get() = runSuspendCatching {
        body.withSignatureStripped.asMessageBody().threadTitleFromPRComment() ?: defaultThreadTitle
    }.getOrElse {
        LOGGER.errorSync(it) { "Unable to parse message body from GitHubPullRequestReviewComment" }
        defaultThreadTitle
    }

/**
 * Transforms a list of comments for a PR into a list of threaded comments.
 */
val List<ScmPrComment>.asThreadedComments: List<List<ScmPrComment>>
    get() =
        filter { it.associatedCommentId == null }.map { firstComment ->
            val thread = mutableSetOf<ScmPrComment>()

            val commentsToVisit: Queue<ScmPrComment> = LinkedList()
            commentsToVisit.add(firstComment)

            while (commentsToVisit.isNotEmpty()) {
                val comment = commentsToVisit.remove()
                thread.add(comment)
                val replies = filter { !thread.contains(it) && it.associatedCommentId == comment.id }
                commentsToVisit.addAll(replies)
            }

            thread.toList().sortedBy { it.createdAt }
        }
