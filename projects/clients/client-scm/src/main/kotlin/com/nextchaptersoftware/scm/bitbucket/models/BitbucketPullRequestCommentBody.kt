package com.nextchaptersoftware.scm.bitbucket.models

import kotlinx.serialization.Serializable

/**
 * https://developer.atlassian.com/cloud/bitbucket/rest/api-group-pullrequests/#api-repositories-workspace-repo-slug-pullrequests-pull-request-id-comments-post
 */
@Serializable
data class BitbucketPullRequestCommentBody(
    val content: Content,
) {
    @Serializable
    data class Content(
        val raw: String,
    )
}
