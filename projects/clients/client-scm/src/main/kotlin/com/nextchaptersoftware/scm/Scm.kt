package com.nextchaptersoftware.scm

import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores

sealed class Scm(
    override val displayName: String,
    val provider: Provider,
) : OAuthApiType {

    companion object {

        fun fromProvider(provider: Provider, enterpriseId: EnterpriseAppConfigId?): Scm {
            return when (provider) {
                Provider.AzureDevOps -> AzureDevOps

                Provider.Bitbucket -> Bitbucket

                Provider.GitHub -> GitHub

                Provider.GitLab -> GitLab

                Provider.BitbucketDataCenter -> BitbucketDataCenter(requireNot<PERSON>ull(enterpriseId))

                Provider.GitHubEnterprise -> GitHubEnterprise(requireNotNull(enterpriseId))

                Provider.GitLabSelfHosted -> GitLabSelfHosted(requireNotNull(enterpriseId))

                Provider.Asana,
                Provider.Aws,
                Provider.AwsIdentityCenter,
                Provider.BitbucketPipelines,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.Confluence,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHubActions,
                Provider.GitLabPipelines,
                Provider.GoogleDrive,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.Jira,
                Provider.JiraDataCenter,
                Provider.Linear,
                Provider.MicrosoftEntra,
                Provider.Notion,
                Provider.Okta,
                Provider.PingOne,
                Provider.Slack,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> error("Not an SCM Provider")
            }
        }

        fun fromIdentity(identity: Identity): Scm {
            val enterpriseId = when (identity.provider) {
                Provider.BitbucketDataCenter,
                Provider.GitHubEnterprise,
                Provider.GitLabSelfHosted,
                    -> identity.externalTeamId.let(EnterpriseAppConfigId::fromString)

                else -> null
            }
            return fromProvider(identity.provider, enterpriseId)
        }

        suspend fun fromHostname(authority: String): Scm? {
            return when (val normalizedAuthority = authority.removeSuffix(":443").lowercase()) {
                GitHub.hostname -> GitHub

                Bitbucket.hostname -> Bitbucket

                GitLab.hostname -> GitLab

                else -> {
                    Stores.enterpriseAppConfigStore.findByHostAndPort(normalizedAuthority, null)?.let { config ->
                        fromProvider(config.provider, config.id)
                    }
                }
            }
        }

        fun fromTeam(scmTeam: ScmTeam): Scm {
            return fromProvider(scmTeam.provider, scmTeam.providerEnterpriseId)
        }
    }

    sealed class Public(
        val hostname: String,
        provider: Provider,
    ) : Scm(
        displayName = provider.displayName,
        provider = provider,
    )

    data object AzureDevOps : Public(provider = Provider.AzureDevOps, hostname = "dev.azure.com")

    data object Bitbucket : Public(provider = Provider.Bitbucket, hostname = "bitbucket.org")

    data object GitHub : Public(provider = Provider.GitHub, hostname = "github.com")

    data object GitLab : Public(provider = Provider.GitLab, hostname = "gitlab.com")

    sealed class OnPremise(
        val enterpriseId: EnterpriseAppConfigId,
        provider: Provider,
    ) : Scm(
        displayName = provider.displayName,
        provider = provider,
    )

    class GitHubEnterprise(enterpriseId: EnterpriseAppConfigId) : OnPremise(provider = Provider.GitHubEnterprise, enterpriseId = enterpriseId) {

        // override equals and hashCode to ensure that the enterpriseId is used for equality
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is GitHubEnterprise) return false
            if (enterpriseId != other.enterpriseId) return false
            return true
        }

        override fun hashCode(): Int {
            return javaClass.hashCode()
        }
    }

    class GitLabSelfHosted(enterpriseId: EnterpriseAppConfigId) : OnPremise(provider = Provider.GitLabSelfHosted, enterpriseId = enterpriseId) {

        // override equals and hashCode to ensure that the enterpriseId is used for equality
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is GitLabSelfHosted) return false
            if (enterpriseId != other.enterpriseId) return false
            return true
        }

        override fun hashCode(): Int {
            return javaClass.hashCode()
        }
    }

    class BitbucketDataCenter(enterpriseId: EnterpriseAppConfigId) : OnPremise(provider = Provider.BitbucketDataCenter, enterpriseId = enterpriseId) {

        // override equals and hashCode to ensure that the enterpriseId is used for equality
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (other !is BitbucketDataCenter) return false
            if (enterpriseId != other.enterpriseId) return false
            return true
        }

        override fun hashCode(): Int {
            return javaClass.hashCode()
        }
    }

    val providerEnterpriseId: EnterpriseAppConfigId?
        get() = when (this) {
            is Public -> null
            is OnPremise -> enterpriseId
        }

    /**
     * A stable value that uniquely describes this [Scm].
     *
     * For the public SCM case, we use the stable [Provider.dbOrdinal].
     * The enclosing colons ensure that the DB ordinal never collides with real world IDs.
     */
    val uniqueSignature: String by lazy {
        when (this) {
            is Public -> provider.uniqueSignature
            is OnPremise -> enterpriseId.toString()
        }
    }

    suspend fun lookupAuthority(enterpriseAppConfigStore: EnterpriseAppConfigStore): String {
        return when (this) {
            is Public -> hostname
            is OnPremise -> enterpriseAppConfigStore.getById(enterpriseId, this.provider).authority
        }
    }
}
