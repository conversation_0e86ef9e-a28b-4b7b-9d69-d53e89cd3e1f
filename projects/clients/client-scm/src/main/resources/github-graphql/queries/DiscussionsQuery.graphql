query DiscussionsQuery(
    $owner: String!,
    $name: String!,
    $discussionsAfterCursor: String,
    $discussionsPageSize: Int!
) {
    rateLimit {
        limit
        cost
        remaining
        resetAt
    }
    repository(
        name: $name,
        owner: $owner
    ) {
        id
        databaseId
        url
        discussions(first: $discussionsPageSize, after: $discussionsAfterCursor, orderBy: {direction: DESC, field: UPDATED_AT}) {
            totalCount
            pageInfo {
                startCursor
                hasNextPage
                endCursor
            }
            nodes {
                number
                title
                url
                createdAt
                updatedAt
                lastEditedAt
                body
                author {
                    __typename
                    login
                    avatarUrl
                    resourcePath
                    url
                    ... on User {
                        databaseId
                        name
                        createdAt
                    }
                    ... on Bot {
                        databaseId
                        createdAt
                    }
                    ... on EnterpriseUserAccount { # does not have a databaseId
                        id
                        name
                        createdAt
                    }
                    ... on Mannequin {
                        databaseId
                        createdAt
                    }
                    ... on Organization {
                        databaseId
                        name
                        createdAt
                    }
                },
                comments(first: 100) {
                    totalCount
                    pageInfo {
                        startCursor
                        hasNextPage
                        endCursor
                    }
                    nodes {
                        id
                        body
                        createdAt
                        updatedAt
                        lastEditedAt
                        author {
                            __typename
                            login
                            avatarUrl
                            resourcePath
                            url
                            ... on User {
                                databaseId
                                name
                                createdAt
                            }
                            ... on Bot {
                                databaseId
                                createdAt
                            }
                            ... on EnterpriseUserAccount { # does not have a databaseId
                                id
                                name
                                createdAt
                            }
                            ... on Mannequin {
                                databaseId
                                createdAt
                            }
                            ... on Organization {
                                databaseId
                                name
                                createdAt
                            }
                        }
                    }
                }
            }
        }
    }
}
