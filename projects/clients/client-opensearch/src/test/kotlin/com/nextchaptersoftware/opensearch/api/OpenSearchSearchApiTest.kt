package com.nextchaptersoftware.opensearch.api

import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.opensearch.api.utils.OpenSearchTestUtils
import java.util.UUID
import kotlin.math.sqrt
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.junit.jupiter.api.TestMethodOrder
import org.opensearch.client.opensearch._types.FieldValue
import org.opensearch.client.opensearch._types.Refresh
import org.opensearch.client.opensearch._types.mapping.TypeMapping
import org.opensearch.client.opensearch._types.query_dsl.Query
import org.opensearch.client.opensearch._types.query_dsl.QueryBuilders
import org.opensearch.client.opensearch.core.SearchResponse
import org.opensearch.client.opensearch.indices.IndexSettings

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(OrderAnnotation::class)
class OpenSearchSearchApiTest {

    companion object {
        private const val TEST_INDEX_NAME = "test-search-index"
        private const val TEST_FIELD_NAME = "field"
        private const val TEST_UUID_FIELD_NAME = "uuid"
        private const val TEST_VECTOR_FIELD_NAME = "testEmbedding"
        private const val TEST_RANK_FEATURES_FIELD_NAME = "testSparseEmbedding"

        // innerproduct requires normalized vectors
        private fun normalizeVector(vector: Array<Float>): Array<Float> {
            // length = √(x² + y² + z² …)
            val magnitude = sqrt(vector.sumOf { (it * it).toDouble() })

            // Avoid dividing by zero for an all‑zero vector (optional safeguard)
            if (magnitude == 0.0) return vector

            return vector.map { it / magnitude.toFloat() }.toTypedArray()
        }

        private val TEST_VECTOR_1: Array<Float> = normalizeVector(arrayOf(0.2f, 0.3f, 0.4f))
        private val TEST_VECTOR_2: Array<Float> = TEST_VECTOR_1

        private val TEST_UUID_1: String = UUID.randomUUID().toString()
        private val TEST_UUID_2: String = UUID.randomUUID().toString()

        private val openSearchApiProvider = OpenSearchTestUtils.OPENSEARCH_API_PROVIDER
        private val openSearchSearchApi = openSearchApiProvider.openSearchSearchApi
        private val openSearchDocumentApi = openSearchApiProvider.openSearchDocumentApi

        @BeforeAll
        @JvmStatic
        fun setUp() {
            tearDown()
            // Ensure the index exists before running tests
            openSearchApiProvider.openSearchIndexApi.createIndex(
                indexName = TEST_INDEX_NAME,
                settings = IndexSettings.of { indexSettings ->
                    indexSettings.knn(true)
                },
                mappings = TypeMapping.of { typeMapping ->
                    typeMapping.properties(TEST_VECTOR_FIELD_NAME) { property ->
                        property.knnVector { knnVectorProperty ->
                            knnVectorProperty.dimension(3)
                            knnVectorProperty.method { knnVectorPropertyMethod ->
                                knnVectorPropertyMethod.name("hnsw")
                                knnVectorPropertyMethod.engine("faiss")
                                knnVectorPropertyMethod.spaceType("innerproduct")
                            }
                        }
                    }

                    typeMapping.properties(TEST_UUID_FIELD_NAME) { property ->
                        property.keyword { keyword ->
                            keyword
                        }
                    }

                    typeMapping.properties(TEST_RANK_FEATURES_FIELD_NAME) { property ->
                        property.rankFeatures { rankFeaturesProperty ->
                            rankFeaturesProperty
                        }
                    }
                },
            )
            // Add some documents for search tests
            openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "doc1", mapOf(TEST_FIELD_NAME to "value1")) {
                refresh(Refresh.True)
            }
            openSearchDocumentApi.createDocumentById(TEST_INDEX_NAME, "doc2", mapOf(TEST_FIELD_NAME to "value2")) {
                refresh(Refresh.True)
            }
            openSearchDocumentApi.createDocumentById(
                TEST_INDEX_NAME, "vec1",
                mapOf(
                    TEST_VECTOR_FIELD_NAME to TEST_VECTOR_1,
                    TEST_FIELD_NAME to "vec1",
                    TEST_UUID_FIELD_NAME to TEST_UUID_1,
                    TEST_RANK_FEATURES_FIELD_NAME to mapOf(
                        "1" to 0.5,
                        "2" to 0.8,
                        "3" to 0.9,
                    ),
                ),
            ) {
                refresh(Refresh.True)
            }
            openSearchDocumentApi.createDocumentById(
                TEST_INDEX_NAME, "vec2",
                mapOf(
                    TEST_VECTOR_FIELD_NAME to TEST_VECTOR_2,
                    TEST_FIELD_NAME to "vec2",
                    TEST_UUID_FIELD_NAME to TEST_UUID_2,
                    TEST_RANK_FEATURES_FIELD_NAME to mapOf(
                        "1" to 3.0,
                        "2" to 3.0,
                        "3" to 20.0,
                    ),
                ),
            ) {
                refresh(Refresh.True)
            }
        }

        @AfterAll
        @JvmStatic
        fun tearDown() {
            runSuspendCatching {
                openSearchApiProvider.openSearchIndexApi.deleteIndex(TEST_INDEX_NAME)
            }
        }
    }

    @Test
    @Order(1)
    fun `test search documents`() {
        val matchQuery = QueryBuilders.match().field(TEST_FIELD_NAME).query(FieldValue.of("value1")).build()
        val query = Query.Builder().match(matchQuery).build()
        val response: SearchResponse<Map<String, Any>> = openSearchSearchApi.searchDocuments(indexName = TEST_INDEX_NAME) {
            query(query)
        }
        assertThat(response).isNotNull
        assertThat(response.hits().hits()).hasSize(1)
        assertThat(response.hits().hits()[0].source()).containsEntry(TEST_FIELD_NAME, "value1")
    }

    @Test
    @Order(2)
    fun `test search documents with builder`() {
        val response: SearchResponse<Map<String, Any>> = openSearchSearchApi.searchDocuments(
            indexName = TEST_INDEX_NAME,
        ) {
            query { q ->
                q.match { m ->
                    m.field(TEST_FIELD_NAME).query(FieldValue.of("value2"))
                }
            }
        }
        assertThat(response).isNotNull
        assertThat(response.hits().hits()).hasSize(1)
        assertThat(response.hits().hits()[0].source()).containsEntry(TEST_FIELD_NAME, "value2")
    }

    @Test
    @Order(3)
    fun `test search documents with vector`() {
        val response: SearchResponse<Map<String, Any>> = openSearchSearchApi.searchDocuments(
            indexName = TEST_INDEX_NAME,
        ) {
            query { q ->
                q.knn { knn ->
                    knn.field(TEST_VECTOR_FIELD_NAME).vector(TEST_VECTOR_1.toFloatArray()).k(2)
                }
            }
        }
        assertThat(response).isNotNull
        assertThat(response.hits().hits()).hasSize(2)
        val testVector = response.hits().hits()[0].source()?.get(TEST_VECTOR_FIELD_NAME) as? ArrayList<*>
        checkNotNull(testVector)

        // We expect that the rank feature will push vec2 above vec1
        assertThat(response.hits().hits()[0].id()).isEqualTo("vec1")
        assertThat(response.hits().hits()[1].id()).isEqualTo("vec2")
    }

    /**
     * Tests searching documents using a combination of KNN vector search and rank features.
     *
     * OpenSearch does not natively support sparse vectors, which are vectors with many zero values.
     * Instead, we use rank features to simulate sparse vector behavior. Rank features allow us to
     * assign importance to specific fields, effectively boosting the relevance score of documents
     * based on these features. In this test, we use a rank feature query on the field
     * `"$TEST_RANK_FEATURES_FIELD_NAME.3"` to influence the ranking of search results.
     *
     * The expectation is that the rank feature will push `vec2` above `vec1` in the search results,
     * despite both having the same KNN vector, due to the higher rank feature value associated with `vec2`.
     */
    @Test
    @Order(3)
    fun `test search documents with sparse vector`() {
        val response: SearchResponse<Map<String, Any>> = openSearchSearchApi.searchDocuments(
            indexName = TEST_INDEX_NAME,
        ) {
            query { q ->
                q.bool { qb ->
                    qb.must { qbm ->
                        qbm.knn { knn ->
                            knn.field(TEST_VECTOR_FIELD_NAME).vector(TEST_VECTOR_1.toFloatArray()).k(2)
                        }
                    }
                    // sparse vector query must be a post-op query
                    qb.should { qbs ->
                        qbs.rankFeature { rankFeature ->
                            rankFeature.field("$TEST_RANK_FEATURES_FIELD_NAME.3")
                        }
                    }
                }
            }
        }
        assertThat(response).isNotNull
        assertThat(response.hits().hits()).hasSize(2)

        // We expect that the rank feature will push vec2 above vec1
        assertThat(response.hits().hits()[0].id()).isEqualTo("vec2")
        assertThat(response.hits().hits()[1].id()).isEqualTo("vec1")
    }

    @Test
    @Order(5)
    fun `test search documents with wildcard`() {
        val response: SearchResponse<Map<String, Any>> = openSearchSearchApi.searchDocuments(
            indexName = TEST_INDEX_NAME,
        ) {
            query { q ->
                q.wildcard { wildcard ->
                    wildcard.field(TEST_UUID_FIELD_NAME).wildcard("${TEST_UUID_1.substring(0, 15)}*")
                }
            }
        }
        assertThat(response).isNotNull
        assertThat(response.hits().hits()).hasSize(1)

        assertThat(response.hits().hits()[0].id()).isEqualTo("vec1")
    }
}
