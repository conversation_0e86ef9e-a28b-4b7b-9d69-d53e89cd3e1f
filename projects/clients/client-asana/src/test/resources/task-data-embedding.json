{"data": {"gid": "1210048961530019", "assignee": {"gid": "5024049525512", "name": "<PERSON>", "resource_type": "user"}, "assignee_status": "upcoming", "completed": false, "created_at": "2025-04-22T17:22:13.569Z", "due_on": "2025-05-01", "memberships": [{"project": {"gid": "1210048961530016", "name": "Project 4 (no <PERSON><PERSON>)", "resource_type": "project"}, "section": {"gid": "1210048961530017", "name": "To Do Section", "resource_type": "section"}}], "name": "Implement Asana Data Embedding", "notes": "Create the HumanReadableAsanaTask class and implement the embedding job", "permalink_url": "https://app.asana.com/1/1913674533085/project/1210048961530016/task/1210048961530019", "projects": [{"gid": "1210048961530016", "name": "Project 4 (no <PERSON><PERSON>)", "resource_type": "project"}]}}