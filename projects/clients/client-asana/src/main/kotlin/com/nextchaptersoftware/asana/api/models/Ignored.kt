package com.nextchaptersoftware.asana.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// TODO ASANA: Implement a polymorphic deserializer so we don't have to have these

@Serializable
@SerialName("attachment")
data class Attachment(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
) : Resource()

@Serializable
@SerialName("like")
data class Like(
    override val gid: String,
    @SerialName("resource_type")
    override val resourceType: String,
) : Resource()
