package com.nextchaptersoftware.asana.api

import com.nextchaptersoftware.asana.api.AsanaHttpClient.setAuthorizationHeader
import com.nextchaptersoftware.asana.api.models.Results
import com.nextchaptersoftware.asana.api.models.Single
import com.nextchaptersoftware.asana.api.models.Workspace
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.HttpClientPagination.batchStream
import com.nextchaptersoftware.pagination.asFlatItemsFlow
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.path
import kotlinx.coroutines.flow.Flow

class AsanaWorkspacesApi(
    private val client: HttpClient,
    private val baseUrl: Url,
) {
   companion object {
     private const val RESOURCE_PATH = "workspaces"
   }

    suspend fun find(
        tokens: OAuthTokens,
        limit: Int = 100,
        offset: String? = null,
    ): Results<Workspace> {
        val url = URLBuilder(baseUrl).apply {
            path(*baseUrl.segments.toTypedArray(), RESOURCE_PATH)
            parameters["limit"] = limit.toString()
            offset?.let { parameters["offset"] = it }
        }.build()

        return client.get(url) {
            setAuthorizationHeader(accessToken = tokens.accessToken)
        }.body()
    }

    suspend fun get(resourceId: String, tokens: OAuthTokens): Workspace {
        val url = URLBuilder(baseUrl).apply {
            path(*baseUrl.segments.toTypedArray(), RESOURCE_PATH, resourceId)
        }.build()

        val wrapper: Single<Workspace> = client.get(url) {
            setAuthorizationHeader(accessToken = tokens.accessToken)
        }.body()

        return wrapper.data
    }

    private fun batchStream(
        tokens: OAuthTokens,
    ): Flow<HttpClientBatch<Workspace>> {
        return client.batchStream(
            // We hard code these because that's all we need for
            // asana ingestion
            initialUri = "$baseUrl$RESOURCE_PATH?opt_fields=is_organization,email_domains,path,uri,name",
            dataProvider = { response ->
                response.body<Results<Workspace>>().data
            },
            nextUriProvider = { response ->
                response.body<Results<Workspace>>().nextPage?.uri?.let { Url(it) }
            },
            block = {
                setAuthorizationHeader(tokens.accessToken)
            },
        )
    }

    fun stream(
        tokens: OAuthTokens,
    ): Flow<Workspace> =
        batchStream(tokens).asFlatItemsFlow()
}
