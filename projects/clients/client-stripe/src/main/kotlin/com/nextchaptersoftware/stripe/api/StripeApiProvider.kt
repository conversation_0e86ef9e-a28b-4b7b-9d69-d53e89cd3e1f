package com.nextchaptersoftware.stripe.api

import com.nextchaptersoftware.stripe.config.StripeApiConfig
import com.stripe.StripeClient

class StripeApiProvider {
    private val client by lazy {
        StripeClient(StripeApiConfig.INSTANCE.stripeApi.key.value)
    }

    val chargesApi by lazy {
        StripeChargesApi(client)
    }

    val customerApi by lazy {
        StripeCustomerApi(client)
    }

    val invoicesApi by lazy {
        StripeInvoicesApi(client)
    }

    val paymentMethodsApi by lazy {
        StripePaymentMethodsApi(client)
    }

    val pricesApi by lazy {
        StripePricesApi(client)
    }

    val setupIntentsApi by lazy {
        StripeSetupIntentsApi(client)
    }

    val subscriptionsApi by lazy {
        StripeSubscriptionsApi(client)
    }

    val subscriptionSchedulesApi by lazy {
        StripeSubscriptionsScheduleApi(client)
    }
}
