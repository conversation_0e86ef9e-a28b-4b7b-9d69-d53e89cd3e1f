package com.nextchaptersoftware.stripe.api

import com.stripe.StripeClient
import com.stripe.model.Price
import com.stripe.param.PriceCreateParams
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StripePricesApi(
    private val client: StripeClient,
) {
    suspend fun createPrice(
        productId: String,
        nickname: String,
        unitAmount: Long, // in cents
        interval: PriceCreateParams.Recurring.Interval,
    ): Price {
        val params = PriceCreateParams.builder()
            .setProduct(productId)
            .setUnitAmount(unitAmount)
            .setCurrency("usd")
            .setRecurring(PriceCreateParams.Recurring.builder().setInterval(interval).build())
            .setNickname(nickname)
            .build()

        return withContext(Dispatchers.IO) {
            client.prices().create(params)
        }
    }
}
