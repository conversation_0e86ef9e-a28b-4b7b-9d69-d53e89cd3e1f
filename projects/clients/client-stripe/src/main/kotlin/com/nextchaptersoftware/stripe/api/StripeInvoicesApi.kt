package com.nextchaptersoftware.stripe.api

import com.stripe.StripeClient
import com.stripe.model.Invoice
import com.stripe.model.InvoiceItem
import com.stripe.model.StripeCollection
import com.stripe.param.InvoiceCreateParams
import com.stripe.param.InvoiceItemCreateParams
import com.stripe.param.InvoiceListParams
import com.stripe.param.InvoiceUpcomingParams
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class StripeInvoicesApi(
    private val client: StripeClient,
) {
    suspend fun getInvoices(
        customerId: String,
        limit: Long = 100L,
    ): StripeCollection<Invoice> {
        val params = InvoiceListParams.builder()
            .setCustomer(customerId)
            .setLimit(limit)
            .addExpand("data.payment_intent.payment_method")
            .build()

        return withContext(Dispatchers.IO) {
            client.invoices().list(params)
        }
    }

    suspend fun getUpcomingInvoice(
        customerId: String,
    ): Invoice {
        val params = InvoiceUpcomingParams.builder()
            .setCustomer(customerId)
            .build()

        return withContext(Dispatchers.IO) {
            client.invoices().upcoming(params)
        }
    }

    suspend fun createPendingInvoiceItem(
        customerId: String,
        description: String,
        quantity: Long,
        unitAmount: Long,
    ): InvoiceItem {
        val params = InvoiceItemCreateParams.builder()
            .setCustomer(customerId)
            .setCurrency("usd")
            .setDescription(description)
            .setQuantity(quantity)
            .setUnitAmount(unitAmount)
            .build()

        return withContext(Dispatchers.IO) {
            client.invoiceItems().create(params)
        }
    }

    suspend fun createDraftInvoice(
        customerId: String,
    ): Invoice {
        val params = InvoiceCreateParams.builder()
            .setCustomer(customerId)
            .setAutoAdvance(true)
            .setCollectionMethod(InvoiceCreateParams.CollectionMethod.CHARGE_AUTOMATICALLY)
            .setPendingInvoiceItemsBehavior(InvoiceCreateParams.PendingInvoiceItemsBehavior.INCLUDE)
            .build()

        return withContext(Dispatchers.IO) {
            client.invoices().create(params)
        }
    }

    suspend fun finalizeInvoice(
        invoiceId: String,
    ): Invoice {
        return withContext(Dispatchers.IO) {
            client.invoices().finalizeInvoice(invoiceId)
        }
    }

    suspend fun payInvoice(
        invoiceId: String,
    ): Invoice {
        return withContext(Dispatchers.IO) {
            client.invoices().pay(invoiceId)
        }
    }
}
