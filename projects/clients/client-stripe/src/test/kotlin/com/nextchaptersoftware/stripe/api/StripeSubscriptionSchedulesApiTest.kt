package com.nextchaptersoftware.stripe.api

import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled("For manual testing only")
class StripeSubscriptionSchedulesApiTest {
    private val apiProvider = StripeApiProvider()

    @Test
    fun `createSubscriptionSchedule, appendPhaseToSubscriptionSchedule, and releaseSubscriptionSchedule`() = runTest {
        val subscriptionId = "sub_1PwIBt04IOT1VvLMJDS8xjrk"

        val schedule = apiProvider.subscriptionSchedulesApi.createSubscriptionSchedule(subscriptionId = subscriptionId)
        assertThat(schedule.subscription).isEqualTo(subscriptionId)

        val subscription = apiProvider.subscriptionsApi.getSubscription(subscriptionId = subscriptionId)
        assertThat(subscription.schedule).isEqualTo(schedule.id)

        val updatedSchedule = apiProvider.subscriptionSchedulesApi.appendPhaseToSubscriptionSchedule(
            schedule = schedule,
            priceId = "price_1PwHOV04IOT1VvLM7V4ECDVF",
            quantity = 2,
        )

        assertThat(updatedSchedule.phases).hasSize(2)

        val releasedSchedule = apiProvider.subscriptionSchedulesApi.releaseSubscriptionSchedule(subscriptionScheduleId = schedule.id)
        assertThat(releasedSchedule.subscription).isNull()

        // Sanity check
        val updatedSubscription = apiProvider.subscriptionsApi.getSubscription(subscriptionId = subscriptionId)
        assertThat(updatedSubscription.schedule).isNull()
    }
}
