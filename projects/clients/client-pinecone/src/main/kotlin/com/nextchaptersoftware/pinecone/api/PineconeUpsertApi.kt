package com.nextchaptersoftware.pinecone.api

import com.nextchaptersoftware.pinecone.api.models.PineconeDocument
import com.nextchaptersoftware.pinecone.api.utils.PineconeConstants.MAX_SPARSE_SIZE
import com.nextchaptersoftware.pinecone.api.utils.PineconeStructUtils
import io.pinecone.clients.AsyncIndex
import io.pinecone.unsigned_indices_model.SparseValuesWithUnsignedIndices
import io.pinecone.unsigned_indices_model.VectorWithUnsignedIndices
import kotlinx.coroutines.guava.asDeferred

class PineconeUpsertApi(
    private val pineconeIndex: AsyncIndex?,
) {
    companion object {
        private const val MIN_UPSERT_CHUNK_SIZE = 25

        /**
         * The recommended number of vectors that should be upserted in a single request.
         * The maximum size of the upsert request is 2MB.
         * https://docs.pinecone.io/docs/limits#upserts
         */
        private const val MAX_UPSERT_CHUNK_SIZE = 100
    }

    suspend fun upsert(
        namespace: String,
        documents: List<PineconeDocument>,
    ) {
        pineconeIndex ?: throw PineconeAvailabilityException()

        val vectors = documents.map { document ->
            val filteredSparseValues = document.sparseValues?.takeIf { it.indicesList.size in 1..MAX_SPARSE_SIZE }
            VectorWithUnsignedIndices(
                document.id,
                document.floats,
                PineconeStructUtils.mapToStruct(document.metadata),
                filteredSparseValues?.let { sparseValues ->
                    SparseValuesWithUnsignedIndices(sparseValues)
                },
            )
        }

        val upsertChunkSize = vectors.size
            .coerceAtMost(MAX_UPSERT_CHUNK_SIZE)
            .div(2)
            .coerceAtLeast(MIN_UPSERT_CHUNK_SIZE)

        vectors.chunked(upsertChunkSize).forEach { chunkedVectors ->
            pineconeIndex.upsert(
                chunkedVectors,
                namespace,
            ).asDeferred().await()
        }
    }

    // This will only upsert the specified metadata fields of the vector
    suspend fun updateMetadata(
        namespace: String,
        vectorId: String,
        metadata: Map<String, Any>,
    ) {
        if (metadata.isEmpty()) {
            return
        }

        pineconeIndex ?: throw PineconeAvailabilityException()

        pineconeIndex.update(
            vectorId,
            null,
            PineconeStructUtils.mapToStruct(metadata),
            namespace,
            null,
            null,
        ).asDeferred().await()
    }
}
