package com.nextchaptersoftware.slack.api

import com.slack.api.Slack
import com.slack.api.webhook.Payload
import com.slack.api.webhook.WebhookResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class SlackWebhookApi(
    private val client: Slack = Slack.getInstance(),
) {
    suspend fun sendMessage(webhookUrl: String, payload: Payload): WebhookResponse = withContext(Dispatchers.IO) {
        client.send(webhookUrl, payload)
    }
}
