package com.nextchaptersoftware.jira.api

import com.nextchaptersoftware.atlassian.api.AtlassianHttpClientFactory
import com.nextchaptersoftware.atlassian.api.AtlassianHttpClientFactory.setAuthorizationHeader
import com.nextchaptersoftware.atlassian.api.AtlassianSiteApi
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.jira.models.DataCenterIssueField
import com.nextchaptersoftware.jira.models.IssueField
import com.sksamuel.hoplite.Secret
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.path

// https://developer.atlassian.com/cloud/jira/platform/rest/v2/api-group-issue-fields/#api-group-issue-fields
class JiraIssueFieldsApi {
    @Suppress("SpreadOperator")
    companion object {
        private const val PATH = "rest/api/2/field"

        private fun url(
            baseUrl: Url,
        ): Url {
            return URLBuilder(baseUrl).also { builder ->
                when (builder.pathSegments.isNotEmpty()) {
                    true -> builder.path(*builder.pathSegments.toTypedArray(), PATH)
                    else -> builder.path(PATH)
                }
            }.build()
        }
    }

    suspend fun getIssueFields(
        orgIds: Set<OrgId>?,
        baseUrl: Url,
        tokens: OAuthTokens,
    ): List<IssueField> {
        return AtlassianSiteApi.get(
            orgIds = orgIds,
            url = url(baseUrl),
            tokens = tokens,
        )
    }

    suspend fun getIssuesFieldsForDataCenter(
        orgIds: Set<OrgId>?,
        baseUrl: Url,
        accessToken: Secret,
    ): List<IssueField> {
        val results = AtlassianHttpClientFactory.withAtlassianHttpClient(
            orgIds = orgIds,
        ) {
            get(url(baseUrl)) {
                setAuthorizationHeader(accessToken)
            }.body<List<DataCenterIssueField>>()
        }

        return results.map { it.asIssueField }
    }
}
