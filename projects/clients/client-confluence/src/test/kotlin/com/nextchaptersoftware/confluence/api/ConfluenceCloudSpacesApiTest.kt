package com.nextchaptersoftware.confluence.api

import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.confluence.models.Space
import com.nextchaptersoftware.test.utils.SkipInCI
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

@SkipInCI
class ConfluenceCloudSpacesApiTest {
    private val tokens = OAuthTokens(accessToken = Utils.CONFLUENCE_CLOUD_API_TOKEN)
    private val api = ConfluenceCloudApiProvider().spacesApi

    @Test
    fun streamSpaces() = runTest {
        val spaces = mutableListOf<Space>()

        val url = ConfluenceCloudSpacesApi.url(
            baseUrl = Utils.CONFLUENCE_CLOUD_BASE_URL,
            limit = 2,
        )

        var batchCount = 0

        api.streamSpaces(
            orgIds = null,
            url = url,
            tokens = tokens,
        ).onEach { batch ->
            spaces.addAll(batch.items)
            println(batch.nextCursor)
            batchCount++
        }.catch {
            println(it)
        }.collect()

        assertThat(spaces).hasSize(12)
    }
}
