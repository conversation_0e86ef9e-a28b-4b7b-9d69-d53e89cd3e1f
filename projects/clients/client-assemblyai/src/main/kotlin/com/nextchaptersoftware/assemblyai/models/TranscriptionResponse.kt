package com.nextchaptersoftware.assemblyai.models

import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class TranscriptionStatus {
    @SerialName("error")
    Error,

    @SerialName("queued")
    Queued,

    @SerialName("completed")
    Completed,

    @SerialName("processing")
    Processing,
}

// https://www.assemblyai.com/docs/reference#transcript
@Serializable
data class TranscriptionResponse(
    @SerialName("acoustic_model")
    val acousticModel: String? = null,
    @SerialName("audio_duration")
    val audioDuration: Int? = null,
    @SerialName("audio_end_at")
    val audioEndAt: Int? = null,
    @SerialName("audio_start_from")
    val audioStartFrom: Int? = null,
    @Contextual
    @SerialName("audio_url")
    val audioUrl: Url,
    @SerialName("auto_chapters")
    val autoChapters: Boolean? = null,
    @SerialName("auto_highlights")
    val autoHighlights: Boolean? = null,
    @SerialName("confidence")
    val confidence: Double? = null,
    @SerialName("content_safety")
    val contentSafety: Boolean? = null,
    @SerialName("disfluencies")
    val disfluencies: Boolean? = null,
    @SerialName("dual_channel")
    val dualChannel: Boolean? = null,
    @SerialName("error")
    val error: String? = null,
    @SerialName("entity_detection")
    val entityDetection: Boolean? = null,
    @SerialName("filter_profanity")
    val filterProfanity: Boolean? = null,
    @SerialName("format_text")
    val formatText: Boolean? = null,
    @SerialName("iab_categories")
    val iabCategories: Boolean? = null,
    @SerialName("id")
    val id: String,
    @SerialName("language_code")
    val languageCode: String? = null,
    @SerialName("language_model")
    val languageModel: String? = null,
    @SerialName("punctuate")
    val punctuate: Boolean? = null,
    @SerialName("redact_pii")
    val redactPii: Boolean? = null,
    @SerialName("redact_pii_audio")
    val redactPiiAudio: Boolean? = null,
    @SerialName("sentiment_analysis")
    val sentimentAnalysis: Boolean? = null,
    @SerialName("speaker_labels")
    val speakerLabels: Boolean? = null,
    @SerialName("speed_boost")
    val speedBoost: Boolean? = null,
    @SerialName("status")
    val status: TranscriptionStatus,
    @SerialName("text")
    val text: String? = null,
    @Contextual
    @SerialName("webhook_url")
    val webhookUrl: Url? = null,
    @SerialName("words")
    val words: List<TranscriptionWord>? = null,
    @SerialName("chapters")
    val chapters: List<TranscriptionChapter>? = null,
    @SerialName("utterances")
    val utterances: List<TranscriptionUtterance>? = null,
)
