package com.nextchaptersoftware.coda.api

import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.trace.ktor.KtorClientTracing
import com.nextchaptersoftware.utils.ensureTrailingSlash
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import io.ktor.network.sockets.SocketTimeoutException
import kotlin.time.Duration
import kotlin.time.Duration.Companion.minutes

object CodaClient {
    fun makeClient(
        baseUrl: String,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
        timeout: Duration = 1.minutes,
    ): HttpClient {
        return HttpClient(clientEngine) {
            install(ContentNegotiation) {
                installJsonSerializer()
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                url(baseUrl.ensureTrailingSlash)
            }
            install(HttpRequestRetry) {
                retryOnExceptionIf(maxRetries = 3) { _, cause ->
                    when (cause) {
                        is HttpRequestTimeoutException,
                        is SocketTimeoutException,
                            -> true

                        else -> false
                    }
                }
            }
            install(HttpTimeout) {
                requestTimeoutMillis = timeout.inWholeMilliseconds
                socketTimeoutMillis = timeout.inWholeMilliseconds
            }
            install(KtorClientTracing)
            expectSuccess = true
        }
    }

    fun HttpRequestBuilder.setAuthorizationHeader(
        accessToken: Secret,
    ) {
        headers[HttpHeaders.Authorization] = "Bearer ${accessToken.value}"
    }
}
