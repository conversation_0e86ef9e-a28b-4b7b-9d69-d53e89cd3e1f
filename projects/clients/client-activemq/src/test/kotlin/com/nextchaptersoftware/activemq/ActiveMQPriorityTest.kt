package com.nextchaptersoftware.activemq

import com.nextchaptersoftware.activemq.extensions.MessageExtensions.body
import com.nextchaptersoftware.activemq.models.MessagePriority.Eight
import com.nextchaptersoftware.activemq.models.MessagePriority.Five
import com.nextchaptersoftware.activemq.models.MessagePriority.One
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class ActiveMQPriorityTest {

    @Test
    fun `test priority`() {
        val queueName = "test-priority-queue-${UUID.randomUUID()}"
        val producer = ActiveMQProducer.producer(queueName)
        producer.sendMessage(One.priority.toString(), One)
        producer.sendMessage(Eight.priority.toString(), Eight)
        producer.sendMessage(Five.priority.toString(), Five)

        val consumer = ActiveMQConsumer.consumer(queueName, transacted = false)
        val message1 = consumer.receive()
        val message2 = consumer.receive()
        val message3 = consumer.receive()

        assertThat(message1?.body()).isEqualTo("8")
        assertThat(message2?.body()).isEqualTo("5")
        assertThat(message3?.body()).isEqualTo("1")
    }
}
