package com.nextchaptersoftware.ml.api.models.topicmapping

import com.nextchaptersoftware.db.models.RepoId
import kotlinx.serialization.SerialName

@kotlinx.serialization.Serializable
data class TopicMappingParameters(
    @SerialName("alpha")
    val alpha: Float,
    @SerialName("top_k")
    val topK: Int,
)

@kotlinx.serialization.Serializable
data class TopicMappingConfiguration(
    @SerialName("pinecone_namespace")
    val pineconeNamespace: String,
    @SerialName("pinecone_hybrid_index")
    val pineconeHybridIndex: String,
    @SerialName("repo_id")
    val repoId: RepoId? = null,
)

@kotlinx.serialization.Serializable
data class TopicMappingRequest(
    @SerialName("inputs")
    val inputs: List<String>,
    @SerialName("parameters")
    val parameters: TopicMappingParameters,
    @SerialName("configuration")
    val configuration: TopicMappingConfiguration,
)
