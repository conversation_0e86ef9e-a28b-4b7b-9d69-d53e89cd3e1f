package com.nextchaptersoftware.ml.api.models.topicmapping

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.db.models.RepoId
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class TopicMappingRequestTest {
    @Test
    fun `test serialization with null repoId`() {
        val topicMappingRequest = TopicMappingRequest(
            inputs = listOf("hello"),
            parameters = TopicMappingParameters(
                alpha = .5f,
                topK = 3,
            ),
            configuration = TopicMappingConfiguration(
                pineconeNamespace = "test",
                pineconeHybridIndex = "test",
                repoId = null,
            ),
        )

        val encoded = topicMappingRequest.encode()
        assertThat(encoded).doesNotContain("repo_id")
    }

    @Test
    fun `test serialization with repoId`() {
        val topicMappingRequest = TopicMappingRequest(
            inputs = listOf("hello"),
            parameters = TopicMappingParameters(
                alpha = .5f,
                topK = 3,
            ),
            configuration = TopicMappingConfiguration(
                pineconeNamespace = "test",
                pineconeHybridIndex = "test",
                repoId = RepoId.random(),
            ),
        )

        val encoded = topicMappingRequest.encode()
        assertThat(encoded).contains("repo_id")
    }
}
