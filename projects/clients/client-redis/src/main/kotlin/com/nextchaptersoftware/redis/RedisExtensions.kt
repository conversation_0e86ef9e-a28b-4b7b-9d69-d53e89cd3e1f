package com.nextchaptersoftware.redis

import io.lettuce.core.ScanArgs
import io.lettuce.core.ScanCursor
import io.lettuce.core.cluster.api.coroutines.RedisClusterCoroutinesCommands

object RedisExtensions {
    private const val DEFAULT_SCAN_LIMIT = 1000L

    /**
     * Delete all keys with prefix.
     *
     * @param prefix the prefix of the keys to delete.
     * @return Long integer-reply The number of keys that were removed.
     */
    suspend fun RedisClusterCoroutinesCommands<String, String>.delKeys(
        prefix: String,
    ): Long {
        assert(prefix.isNotEmpty()) { "Prefix must not be empty" }

        val scanArgs = ScanArgs.Builder.matches("$prefix*").limit(DEFAULT_SCAN_LIMIT)
        return delKeys(scanArgs)
    }

    internal suspend fun RedisClusterCoroutinesCommands<String, String>.delKeys(
        scanArgs: ScanArgs,
    ): Long {
        var scanCursor: ScanCursor? = null
        var delCount: Long = 0

        do {
            scanCursor = when (scanCursor) {
                null -> scan(scanArgs)
                else -> scan(scanCursor, scanArgs)
            }?.also { cursor ->
                cursor.keys.forEach {
                    del(it)
                    delCount++
                }
            }
        } while (scanCursor != null && !scanCursor.isFinished)

        return delCount
    }
}
