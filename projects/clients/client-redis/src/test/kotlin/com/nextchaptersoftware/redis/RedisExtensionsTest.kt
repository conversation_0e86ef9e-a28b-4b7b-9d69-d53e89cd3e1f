package com.nextchaptersoftware.redis

import com.nextchaptersoftware.redis.RedisExtensions.delKeys
import io.lettuce.core.ScanArgs
import java.util.UUID
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class RedisExtensionsTest {
    private val redis = Redis.API

    @Test
    fun delKeys() = runTest {
        val prefixA = UUID.randomUUID().toString()
        val prefixB = UUID.randomUUID().toString()

        val keyA1 = "$prefixA:1".also { redis.set(it, "1") }
        val keyA2 = "$prefixA:2".also { redis.set(it, "2") }
        val keyB1 = "$prefixB:1".also { redis.set(it, "1") }
        val keyB2 = "$prefixB:$prefixA".also { redis.set(it, "2") }

        assertThat(redis.get(keyA1)).isEqualTo("1")
        assertThat(redis.get(keyA2)).isEqualTo("2")
        assertThat(redis.get(keyB1)).isEqualTo("1")
        assertThat(redis.get(keyB2)).isEqualTo("2")

        assertThat(redis.delKeys(prefix = prefixA)).isEqualTo(2L)

        assertThat(redis.get(keyA1)).isNull()
        assertThat(redis.get(keyA2)).isNull()
        assertThat(redis.get(keyB1)).isEqualTo("1")
        assertThat(redis.get(keyB2)).isEqualTo("2")

        assertThat(redis.delKeys(prefix = prefixB)).isEqualTo(2L)

        assertThat(redis.get(keyB1)).isNull()
        assertThat(redis.get(keyB2)).isNull()
    }

    @Test
    fun `delKeys with pagination`() = runTest {
        val prefix = UUID.randomUUID()
        val scanArgs = ScanArgs.Builder.matches("$prefix*").limit(1)

        val key1 = "$prefix:1".also { redis.set(it, "1") }
        val key2 = "$prefix:2".also { redis.set(it, "2") }
        val key3 = "$prefix:3".also { redis.set(it, "3") }
        val key4 = "$prefix:4".also { redis.set(it, "4") }

        assertThat(redis.get(key1)).isEqualTo("1")
        assertThat(redis.get(key2)).isEqualTo("2")
        assertThat(redis.get(key3)).isEqualTo("3")
        assertThat(redis.get(key4)).isEqualTo("4")

        assertThat(redis.delKeys(scanArgs = scanArgs)).isEqualTo(4L)

        assertThat(redis.get(key1)).isNull()
        assertThat(redis.get(key2)).isNull()
        assertThat(redis.get(key3)).isNull()
        assertThat(redis.get(key4)).isNull()
    }
}
