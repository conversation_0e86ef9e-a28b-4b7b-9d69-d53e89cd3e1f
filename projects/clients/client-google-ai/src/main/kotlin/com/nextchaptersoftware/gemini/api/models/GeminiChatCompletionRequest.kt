package com.nextchaptersoftware.gemini.api.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GeminiChatCompletionRequest(
    @SerialName("contents")
    val contents: List<GeminiChatMessage>,

    @SerialName("safetySettings")
    val safetySettings: List<GeminiSafetySetting>? = null,

    @SerialName("systemInstruction")
    val systemInstructions: GeminiChatMessage? = null,

    @SerialName("generationConfig")
    val generationConfig: GeminiGenerationConfig,
)

@Serializable
data class GeminiSafetySetting(
    @SerialName("category")
    val category: GeminiHarmCategory,

    @SerialName("threshold")
    val threshold: GeminiHarmThreshold,
)

@Serializable
data class GeminiGenerationConfig(
    @SerialName("maxOutputTokens")
    val maxOutputTokens: Int? = null,

    @SerialName("temperature")
    val temperature: Float? = null,

    @SerialName("topP")
    val topP: Float? = null,

    @SerialName("topK")
    val topK: Int? = null,
)

@Serializable
enum class GeminiHarmCategory {

    @SerialName("HARM_CATEGORY_UNSPECIFIED")
    HARM_CATEGORY_UNSPECIFIED,

    @SerialName("HARM_CATEGORY_DEROGATORY")
    HARM_CATEGORY_DEROGATORY,

    @SerialName("HARM_CATEGORY_TOXICITY")
    HARM_CATEGORY_TOXICITY,

    @SerialName("HARM_CATEGORY_VIOLENCE")
    HARM_CATEGORY_VIOLENCE,

    @SerialName("HARM_CATEGORY_SEXUAL")
    HARM_CATEGORY_SEXUAL,

    @SerialName("HARM_CATEGORY_MEDICAL")
    HARM_CATEGORY_MEDICAL,

    @SerialName("HARM_CATEGORY_DANGEROUS")
    HARM_CATEGORY_DANGEROUS,

    @SerialName("HARM_CATEGORY_HARASSMENT")
    HARM_CATEGORY_HARASSMENT,

    @SerialName("HARM_CATEGORY_HATE_SPEECH")
    HARM_CATEGORY_HATE_SPEECH,

    @SerialName("HARM_CATEGORY_SEXUALLY_EXPLICIT")
    HARM_CATEGORY_SEXUALLY_EXPLICIT,

    @SerialName("HARM_CATEGORY_DANGEROUS_CONTENT")
    HARM_CATEGORY_DANGEROUS_CONTENT,
}

@Serializable
enum class GeminiHarmThreshold {

    @SerialName("HARM_THRESHOLD_UNSPECIFIED")
    HARM_THRESHOLD_UNSPECIFIED,

    @SerialName("BLOCK_LOW_AND_ABOVE")
    BLOCK_LOW_AND_ABOVE,

    @SerialName("BLOCK_MEDIUM_AND_ABOVE")
    BLOCK_MEDIUM_AND_ABOVE,

    @SerialName("BLOCK_ONLY_HIGH")
    BLOCK_ONLY_HIGH,

    @SerialName("BLOCK_NONE")
    BLOCK_NONE,
}
