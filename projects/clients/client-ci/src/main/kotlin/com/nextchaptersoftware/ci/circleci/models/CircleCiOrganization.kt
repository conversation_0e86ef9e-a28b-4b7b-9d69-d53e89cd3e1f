package com.nextchaptersoftware.ci.circleci.models

import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CircleCiOrganization(

    @Contextual
    @SerialName("id")
    val id: UUID? = null,

    @SerialName("name")
    val name: String,

    @SerialName("slug")
    val slug: String? = null,
)
