package com.nextchaptersoftware.ci.github.models

import com.nextchaptersoftware.ci.models.CiExecution
import com.nextchaptersoftware.scm.github.models.GitHubCheckSuite
import com.nextchaptersoftware.scm.github.models.isCompleted
import com.nextchaptersoftware.scm.github.models.isRunning

fun GitHubCheckSuite.asCiBuild() = CiExecution.CiBuild(
    runner = app.name,
    type = javaClass.simpleName,
    externalId = id.toString(),
    displayName = app.name,
    apiUrl = apiUrl,
    htmlUrl = null,
    status = status.asCiStatus(),
    result = conclusion.asCiResult(),
    createdAt = createdAt,
    startedAt = status.isRunning.takeIf { it }?.let { updatedAt },
    completedAt = status.isCompleted.takeIf { it }?.let { updatedAt },
    pullRequestNumber = pullRequests.firstOrNull()?.number,
    baseSha = pullRequests.firstOrNull()?.base?.sha,
    headSha = headSha,
)
