package com.nextchaptersoftware.ci.circleci.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.TestDecoder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class CircleCiWorkflowEventTest : TestDecoder<CircleCiWorkflowEvent>(
    decoder = { it.decode<CircleCiWorkflowEvent>() },
) {
    @Expect
    fun toCiBuild(event: CircleCiWorkflowEvent) {
        assertThat(event.asCiBuild()).isNotNull()
    }

    @Test
    fun `workflow-1-success`() = test("/ci/circleci/workflow-1-success.json")

    @Test
    fun `workflow-2-failed`() = test("/ci/circleci/workflow-2-failed.json")

    @Test
    fun `workflow-3-branch-null`() = test("/ci/circleci/workflow-3-branch-null.json.gz")
}
