fragment PageInfo on PageInfo {
    __typename
    # Cursor representing the first result in the paginated results.
    startCursor
    # Cursor representing the last result in the paginated results.
    endCursor
    # Indicates if there are more results when paginating backward.
    hasPreviousPage
    # Indicates if there are more results when paginating forward.
    hasNextPage
}

# A project.
fragment Project on Project {
    __typename
    # Project URL.
    url
    # Settings for all integrations associated with that project.
    integrationsSettings {
        id
    }
    # The estimated completion date of the project.
    targetDate
    # The icon of the project.
    icon
    # The last time at which the entity was meaningfully updated, i.e. for all changes of syncable properties except those
    #     for which updates should not produce an update to updatedAt (see skipUpdatedAtKeys). This is the same as the creation time if the entity hasn't
    #     been updated after creation.
    updatedAt
    # The number of completed estimation points after each week.
    completedScopeHistory
    # The number of completed issues in the project after each week.
    completedIssueCountHistory
    # The number of in progress estimation points after each week.
    inProgressScopeHistory
    # The overall progress of the project. This is the (completed estimate points + 0.25 * in progress estimate points) / total estimate points.
    progress
    # The overall scope (total estimate points) of the project.
    scope
    # The project lead.
    lead {
        id
    }
    # The project was created based on this issue.
    convertedFromIssue {
        id
    }
    # The project's color.
    color
    # The project's description.
    description
    # The project's name.
    name
    # The project's unique URL slug.
    slugId
    # The sort order for the project within the organization.
    sortOrder
    # The time at which the entity was archived. Null if the entity has not been archived.
    archivedAt
    # The time at which the entity was created.
    createdAt
    # The time at which the project was automatically archived by the auto pruning process.
    autoArchivedAt
    # The time at which the project was moved into canceled state.
    canceledAt
    # The time at which the project was moved into completed state.
    completedAt
    # The time at which the project was moved into started state.
    startedAt
    # The time until which project update reminders are paused.
    projectUpdateRemindersPausedUntilAt
    # The total number of estimation points after each week.
    scopeHistory
    # The total number of issues in the project after each week.
    issueCountHistory
    # The type of the state.
    state
    # The unique identifier of the entity.
    id
    # The user who created the project.
    creator {
        id
    }
    # Whether to send new issue comment notifications to Slack.
    slackIssueComments
    # Whether to send new issue notifications to Slack.
    slackNewIssue
    # Whether to send new issue status updates to Slack.
    slackIssueStatuses
}

fragment ProjectConnection on ProjectConnection {
    __typename
    nodes {
        ...Project
    }
    pageInfo {
        ...PageInfo
    }
}

# Projects associated with the team.
query TeamProjectsQuery(
    $id: String!
    # A cursor to be used with first for forward pagination
    $after: String
    # A cursor to be used with last for backward pagination.
    $before: String
    # Filter returned projects.
    $filter: ProjectFilter
    # The number of items to forward paginate (used with after). Defaults to 50.
    $first: Int
    # Should archived resources be included (default: false)
    $includeArchived: Boolean
    # The number of items to backward paginate (used with before). Defaults to 50.
    $last: Int
    # By which field should the pagination order by. Available options are createdAt (default) and updatedAt.
    $orderBy: PaginationOrderBy
) {
    team(id: $id) {
        projects(
            after: $after
            before: $before
            filter: $filter
            first: $first
            includeArchived: $includeArchived
            last: $last
            orderBy: $orderBy
        ) {
            ...ProjectConnection
        }
    }
}
