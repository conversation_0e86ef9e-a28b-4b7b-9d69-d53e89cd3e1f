package com.nextchaptersoftware.dbmigrator.commands

import com.diogonunes.jcolor.Ansi
import com.diogonunes.jcolor.Attribute
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.common.config.DbConfigProviderDelegate
import com.nextchaptersoftware.db.migration.FlywayMigrator
import com.sksamuel.hoplite.Secret
import kotlinx.cli.ArgType
import kotlinx.cli.ExperimentalCli
import kotlinx.cli.Subcommand
import kotlinx.coroutines.runBlocking

/**
 * `migrate` – Apply Flyway migrations to a target database.
 *
 * Usage examples:
 *   dbmigrator migrate -e dev -u postgres -p secret
 *   dbmigrator migrate --environment=staging --username=flyway --password=blah
 */
@OptIn(ExperimentalCli::class)
class MigrateCommand : Subcommand("migrate", "Run Flyway migrations on a database") {

    // Named options (all required)
    private val environment by option(
        type = ArgType.String,
        shortName = "e",
        fullName = "environment",
        description = "Configuration environment key (dev, staging, prod, …)",
    )

    private val user by option(
        type = ArgType.String,
        shortName = "u",
        fullName = "username",
        description = "Database user name override",
    )

    private val pass by option(
        type = ArgType.String,
        shortName = "p",
        fullName = "password",
        description = "Database password override",
    )

    override fun execute() = runBlocking {
        val env = environment?.takeIf { it.isNotBlank() } ?: error("Missing --environment/-e")
        val dbUser = user?.takeIf { it.isNotBlank() } ?: error("Missing --username/-u")
        val dbPass = pass?.takeIf { it.isNotBlank() } ?: error("Missing --password/-p")

        // Load base config and patch credentials
        val baseConfig = GlobalConfig.getTestInstance(
            overrideEnvironment = env,
            overrideUser = dbUser,
        )

        val dbConfig = baseConfig.database.copy(
            userName = dbUser,
            password = Secret(dbPass),
            enableStsAuth = false,
        )

        val dbConfigProvider = DbConfigProviderDelegate(dbConfig).getValue()

        FlywayMigrator(dbConfigProvider = dbConfigProvider)
            .runMigration(baseConfig.database.flywayMigration)

        println(
            Ansi.colorize(
                "✅ Migration completed for environment \"$env\" as user \"$dbUser\".",
                Attribute.GREEN_TEXT(),
            ),
        )
    }
}
