replicaCount: 1

image:
  nginx:
    repository: nginx
    tag: latest
    pullPolicy: IfNotPresent
  tgi:
    repository: ghcr.io/huggingface/text-generation-inference
    tag: 3.1.0
    pullPolicy: IfNotPresent

resources:
  nginx:
    requests:
      cpu: "1"
      memory: "1Gi"
    limits:
      cpu: "1"
      memory: "1Gi"
  tgi:
    requests:
      cpu: "4"
      memory: "8Gi"
      gpu: "1"
    limits:
      cpu: "4"
      memory: "8Gi"
      gpu: "1"
    gpu:
      type: nvidia-l4
      #gpuPartitionSize: 3g.20gb
    shm:
      size: 512Mi

deploymentStrategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 30%
    maxSurge: 50%

readinessProbe:
  httpGet:
    path: /            # The endpoint to check
    port: 8080          # The port to make the request to
    scheme: HTTP        # Use HTTP (can change to HTTPS if required)
  initialDelaySeconds: 120
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 20
  successThreshold: 1

pvc:
  storage: 20Gi
  accessModes: ["ReadWriteMany"]
  storageClassName: csi-filestore-multishare-128-rwx

hpa:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  scaleUpStabilizationWindowSeconds: 60
  targetCPUUtilizationPercentage: 80
  #targetGPUUtilizationPercentage: 80
  targetRequestCount: 30

nginxConf: |
  events {
    worker_connections 768;
  }

  http {
    gzip on;
    gzip_proxied any;
    gzip_types text/plain application/json;
    gzip_min_length 1000;

    # Define a custom log format to include the request time
    log_format custom_format '$remote_addr - $remote_user [$time_local] '
                             '"$request" $status $body_bytes_sent '
                             '"$http_referer" "$http_user_agent" '
                             'Request time: $request_time seconds';

    # Apply the custom log format to the access log
    access_log /var/log/nginx/access.log custom_format;

    server {
      listen 80;
      listen [::]:80;

      client_max_body_size 20M;  # Increase to 20MB

      server_name localhost;

      location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
      }
      location = /healthz {
        return 200 'OK';
        add_header Content-Type text/plain;
      }
      location = /metrics {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
      }

      location / {
        if ($request_method !~ ^(GET|POST|HEAD|OPTIONS|PUT|DELETE)$) {
          return 405;
        }

        rewrite ^/(.*)$ / break;

        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
      }

      location ~ ^(.*/)?v1/(.*) {
        if ($request_method !~ ^(GET|POST|HEAD|OPTIONS|PUT|DELETE)$) {
          return 405;
        }

        rewrite ^(.*/)?v1/(.*) /v1/$2 break;

        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
      }
    }
  }
