const assert = require('assert').strict;
const testEventTemplate = require('./app-event.json');
const lambdaLocal = require('lambda-local');
const landingPageEventTemplate = require('./landing-page-event.json');
const path = require('path');

describe('Test', function () {
    it('reroutes root dashboard to index path', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dashboard/';
        assert.equal((await runLambda(testEvent)).uri, '/dashboard/index.html');
    });

    it('reroutes root dashboard without trailing slash to index ', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dashboard';
        assert.equal((await runLambda(testEvent)).uri, '/dashboard/index.html');
    });

    it('do not reroute if script file for dashboard', async function () {
        let testEvent = clone(testEventTemplate);
        testEvent.Records[0].cf.request.uri = '/dashboard/dashboard.js';
        assert.equal((await runLambda(testEvent)).uri, '/dashboard/dashboard.js');
    });

    it('reroutes root privacy with trailing slash to index path', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/privacy/';
        assert.equal((await runLambda(testEvent)).uri, '/privacy/index.html');
    });

    it('reroutes root privacy without trailing slash to index path', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/privacy';
        assert.equal((await runLambda(testEvent)).uri, '/privacy/index.html');
    });

    it('keeps root landing page as root', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/';
        assert.equal((await runLambda(testEvent)).uri, '/index.html');
    });

    it('reroutes index landing page to index path', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/index.html';
        assert.equal((await runLambda(testEvent)).uri, '/index.html');
    });

    it('do not reroute if script file for landing', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/landing.js';
        assert.equal((await runLambda(testEvent)).uri, '/landing.js');
    });

    it('do not reroute if script file for landing in sub dir', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/privacy/landing.js';
        assert.equal((await runLambda(testEvent)).uri, '/privacy/landing.js');
    });

    it('do not reroute if script file for landing in nested sub dir', async function () {
        let testEvent = clone(landingPageEventTemplate);
        testEvent.Records[0].cf.request.uri = '/privacy/p1/landing.js';
        assert.equal((await runLambda(testEvent)).uri, '/privacy/p1/landing.js');
    });
});

async function runLambda(testEvent) {
    return new Promise((resolve, reject) => {
        lambdaLocal
            .execute({
                event: testEvent,
                lambdaPath: path.join(__dirname, './index.js'),
                timeoutMs: 3000,
                verboseLevel: 0,
            })
            .then(function (data) {
                resolve(data);
            })
            .catch(function (err) {
                console.log(err);
                reject(err, null);
            });
    });
}

function clone(a) {
    return JSON.parse(JSON.stringify(a));
}
