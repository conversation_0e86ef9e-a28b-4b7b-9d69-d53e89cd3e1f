from typing import Optional

import base64
import boto3
import gzip
import json
import logging
import os
import stomp
import time
from jsonpath_ng.ext import parse

import ssl

# Configure logging
logging.basicConfig(level=logging.INFO)

# Print input environment for debugging
logging.error("INPUT: %s", os.environ)

AWS_REGION = os.environ.get("AWS_REGION", "us-west-2")

secrets_client = boto3.Session().client("secretsmanager", region_name=AWS_REGION)
s3_client = boto3.Session().client("s3", region_name=AWS_REGION)

CONTEXT_EXECUTION_ID_JSON_PATH = os.environ.get(
    "CONTEXT_EXECUTION_ID_JSON_PATH", "$.Execution.Id"
)
CONTEXT_EXECUTION_INPUT_TEAM_ID_JSON_PATH = os.environ.get(
    "CONTEXT_EXECUTION_INPUT_TEAM_ID_JSON_PATH", "$.Execution.Input.TeamId"
)
CONTEXT_EXECUTION_INPUT_REPO_ID_JSON_PATH = os.environ.get(
    "CONTEXT_EXECUTION_INPUT_REPO_ID_JSON_PATH", "$.Execution.Input.RepoId"
)

PROCESS_OUTPUT_JSON_PATH = os.environ.get("PROCESS_OUTPUT_JSON_PATH")
PROCESS_ERROR_JSON_PATH = os.environ.get("PROCESS_ERROR_JSON_PATH")

AMQ_HOST = os.environ.get("AMQ_HOST")
AMQ_USER = os.environ.get("AMQ_USER")
AMQ_PASSWORD_SECRET_NAME = os.environ.get("AMQ_PASSWORD_SECRET_NAME")
AMQ_QUEUE = os.environ.get("AMQ_QUEUE")
AMQ_EVENT_TYPE = os.environ.get("AMQ_EVENT_TYPE", "")
AMQ_PORT = 61614

# Create logger
logger = logging.getLogger(__name__)


def send_payload(payload: str):
    AMQ_PASSWORD_JSON = secrets_client.get_secret_value(
        SecretId=AMQ_PASSWORD_SECRET_NAME
    )
    AMQ_PASSWORD = AMQ_PASSWORD_JSON["SecretString"]

    class MsgListener(stomp.ConnectionListener):
        def on_error(self, message):
            logger.error('received an error "%s"', message.body)

    headers = {"persistent": "true", "priority": 9}
    hosts = AMQ_HOST.split(',')
    host_and_ports = [(host, AMQ_PORT) for host in hosts]
    logger.error("host_and_ports: %s", host_and_ports)
    conn = stomp.Connection(host_and_ports=host_and_ports)
    conn.set_listener("stomp_listener", MsgListener())
    conn.set_ssl(
        for_hosts=host_and_ports,
        cert_file="ssl/cert.pem",
        key_file="ssl/privateKey.key",
        ssl_version=ssl.PROTOCOL_TLSv1_2,
    )
    conn.connect(login=AMQ_USER, passcode=AMQ_PASSWORD, wait=True)
    conn.send(destination=AMQ_QUEUE, body=payload, headers=headers)
    time.sleep(1)
    conn.disconnect()


def find_json_expression(event: str, json_path: Optional[str]) -> str:
    if json_path:
        jsonpath_expression = parse(json_path)
        for match in jsonpath_expression.find(event):
            return match.value


def get_key(event: str, key: str) -> str:
    return event.get(key)


def generate_payload(event: str) -> (str, str):
    payload = {
        "executionId": find_json_expression(event, CONTEXT_EXECUTION_ID_JSON_PATH),
        "teamId": find_json_expression(
            event, CONTEXT_EXECUTION_INPUT_TEAM_ID_JSON_PATH
        ),
        "s3OutputPrefix": find_json_expression(event, PROCESS_OUTPUT_JSON_PATH),
    }

    error = find_json_expression(event, PROCESS_ERROR_JSON_PATH)
    if error:
        payload["error"] = error

    repo_id = find_json_expression(event, CONTEXT_EXECUTION_INPUT_REPO_ID_JSON_PATH)
    if repo_id:
        payload["repoId"] = repo_id

    if AMQ_EVENT_TYPE:
        payload["type"] = AMQ_EVENT_TYPE

    payload_json = json.dumps(payload)
    payload_zlib = gzip.compress(payload_json.encode())
    payload_base64 = base64.b64encode(payload_zlib)
    payload_base64_ascii = str(payload_base64, encoding="ascii")
    payload_final = {"base64CompressedBody": payload_base64_ascii}

    # Log output payload for debugging
    logger.error("OUTPUT: %s", payload_json)
    return json.dumps(payload_final), error


# When invoked in a step function state machine the event will
# include the entire state machine input.
def handler(event, context):
    logger.error("EVENT: %s", event)
    logger.error("CONTEXT: %s", context)
    (payload, error) = generate_payload(event)
    send_payload(payload)

    if error:
        logger.error("Error occurred: %s", error)
