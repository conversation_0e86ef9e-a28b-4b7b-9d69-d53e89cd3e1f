{"comment": {"body": "Nice. Thanks for the pointer.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850668670"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942656376", "body": ""}
{"comment": {"body": "interesting, I would have thought that this callback would also need to close? i.e. `() => { createDiscussion(); closeModal(); }` -- but it looks fine in the video? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850702347"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942656902", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#pullrequestreview-942782683", "body": ""}
{"comment": {"body": "We only want to close the modal if the discussion is successfully created. If we failed for whatever reason, closing the modal === losing the editor content which isn't great.\r\n\r\nWe will close the modal on `createdDiscussion` state which comes after a successful discussion creation.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/890#discussion_r850793505"}}
{"title": "Add copy and paste support for images and load images into editor", "number": 891, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891", "body": "Adds basic support for copy and paste and loading images with source files.\nI plan to transition this over to asset service soon."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-941659185", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-941659374", "body": ""}
{"comment": {"body": "This will have to be reworked, for now, just a temp placeholder.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#discussion_r849978799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942512704", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942521400", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942561674", "body": ""}
{"comment": {"body": "Is the logic here something like \"prefer rich html/text, then files, then plain html/text?\" -- I'm not even sure what \"rich\" means in this context?  Might be worth adding a comment here?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#discussion_r850640836"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942565479", "body": ""}
{"comment": {"body": "The functions here are all named after image processing, but they do more then that, really they're handling inserting external data of any kind into the document.  Maybe this file and the functions should be named something more generically?  `HandleInsertData` or something?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#discussion_r850643261"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942566905", "body": ""}
{"comment": {"body": "OK \ud83d\udc4d  -- typically we'd try to avoid regexes and do something like `['first', 'second', 'third'].includes(url)`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#discussion_r850644299"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942572813", "body": ""}
{"comment": {"body": "I don't know if this would make the code any cleaner, but you could probably just call the DragAndDrop function directly in here instead of passing it through all the functionas as a callback?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#discussion_r850648491"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942572893", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#pullrequestreview-942579505", "body": ""}
{"comment": {"body": "Roger that!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/891#discussion_r850653035"}}
{"title": "204s shouldn't specify content", "number": 892, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/892", "body": "Fixes PUT /sourcepoints API is failing: see logzio for invalid json response body at  reason: Unexpected end of JSON input\n"}
{"comment": {"body": "This was all Matt", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/892#issuecomment-1098576947"}}
{"comment": {"body": "(identifying the issue, that is. The bug is all me :|.)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/892#issuecomment-1098577124"}}
{"comment": {"body": "reason no 6224 why yaml is a bag of shit. would not happen with type-safe language", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/892#issuecomment-1098578356"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/892#pullrequestreview-941652310", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/892#pullrequestreview-941654663", "body": "Wow. Nice find. Surprised zally did not catch this."}
{"title": "Remove unnecessary error response codes", "number": 893, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/893", "body": "all error objects have the exact same shape in the unblocked API, so there is no point in documenting\nall standard http code should be handled in a standard way (ie. don't retry 4xx, do retry 5xx)\nonly non-standard codes should be documented, the rest is just debt"}
{"comment": {"body": "\ud83c\udf89 !", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/893#issuecomment-1098585913"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/893#pullrequestreview-941666064", "body": ""}
{"title": "Remove param", "number": 894, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/894", "body": "Don't think we need pararam, only header."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/894#pullrequestreview-941670828", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/894#pullrequestreview-941671015", "body": "Approved with a minor comment"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/894#pullrequestreview-941671183", "body": ""}
{"title": "App permissions", "number": 895, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895", "body": "Summary\nExpected behaviour:\n1. The first time the app is launched, the permissions request sheet will be presented\n2. Clicking allow on the mic and camera permissions brings up the usual permissions actions dialogs. Clicking allow will turn the button to a green check back on the request sheet\n3. Clicking on screen recording will take you to system preferences with screen recording selected\n4. Modifying screen recording settings should prompt to quit and restart the app. \n5. After restarting the app you should not see the sheet, but the permissions should be set \nThings that aren't completely wired up yet:\n- Starting the camera as soon as permissions are given. It's not clear if we even want to do that\n- Quitting the app from settings seems to fail when the permissions sheet is up. There's apparently an AppKit hack for that.\nTo play around with the sheet toggle the mic button (for now)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-941673897", "body": ""}
{"comment": {"body": "@matthewjamesadam if you know how to load these with `NSImage` system images it would future proof us against OS image changes. I couldn't find the names for these", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#discussion_r849990876"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-941674893", "body": ""}
{"comment": {"body": "Doing this in the rare case where the permissions dialog is obscured out of the gate. It shouldn't be but it's not modal so it's definitely possible", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#discussion_r849991653"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-941675472", "body": ""}
{"comment": {"body": "Instead of reading the value directly, I'm loading it back from the query API. I remember having to do this on iOS way back in the day because there were bugs in the callbacks for permissions requests. No harm no foul...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#discussion_r849992166"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946002624", "body": ""}
{"comment": {"body": "I think we may want to use our own icon for this anyways -- if we use a system icon it might look out of place if they change it in the future.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#discussion_r853277787"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946081064", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946083473", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946086044", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946088806", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946089643", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/895#pullrequestreview-946242257", "body": ""}
{"title": "Fix point cache for uncommitted changes", "number": 896, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/896", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/896#pullrequestreview-941773258", "body": ""}
{"title": "Fix command registration bug", "number": 897, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/897", "body": "Was removed in refactor"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/897#pullrequestreview-942583003", "body": ""}
{"title": "Reference images in VSCode code directly", "number": 898, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/898", "body": "For VSCode we have been copying the entire src/assets folder into the dist folder, and referencing images manually by piecing together file paths.  This only worked in local builds, VSIX packages were missing the asset folder, and so images would be missing at runtime.\nThis PR changes how we reference images.  We now use import MyImage from 'MyImage.svg' to reference images in code, which causes the image to be referenced by webpack. This means webpack puts in the right place and returns the correct (relative) URL for it.  It also means the build will fail at compile time if we rename/move a file accidentally.\n(this is what we're currently doing in the dashboard and web extension)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/898#pullrequestreview-942608643", "body": ""}
{"comment": {"body": "@jeffrey-ng @kaych I'm open to a different way of structuring this -- it's a little awkward because we need to have the extension base URI to find the image URI.  Putting all the images here lets us resolve the final URI consistently.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/898#discussion_r850673897"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/898#pullrequestreview-944685486", "body": ""}
{"title": "Schema manager retries actualization", "number": 899, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899", "body": "Currently, schema update fails not only when the new schema is invalid,\nbut also when the application cannot get a DB busy-lock.\nWe rollover all instances for all services around the same time,\nso it's pretty likely that some instances will fail to acquire the lock\non first attempt, which will terminate the instance.\nThis change reduces the churn in instance termination by retrying a few\ntimes before eventually giving up due to invalid schema."}
{"comment": {"body": "This is general, but should we handle the lock acquisition failure specifically? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099499056"}}
{"comment": {"body": "> This is general, but should we handle the lock acquisition failure specifically?\r\n\r\nyeah, I just wrap this bit, if that's what you mean:\r\n```kotlin\r\n  withDataBaseLock {\r\n     createStoredProcedures(this)\r\n     SchemaUtils.createMissingTablesAndColumns(tables = tables, inBatch = false, withLogs = false)\r\n  }\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099511980"}}
{"comment": {"body": "> > This is general, but should we handle the lock acquisition failure specifically?\r\n> \r\n> yeah, I just wrap this bit, if that's what you mean:\r\n> \r\n> ```kotlin\r\n>   withDataBaseLock {\r\n>      createStoredProcedures(this)\r\n>      SchemaUtils.createMissingTablesAndColumns(tables = tables, inBatch = false, withLogs = false)\r\n>   }\r\n> ```\r\n\r\nhmm, no actually can't ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099513709"}}
{"comment": {"body": "> > > This is general, but should we handle the lock acquisition failure specifically?\r\n> > \r\n> > \r\n> > yeah, I just wrap this bit, if that's what you mean:\r\n> > ```kotlin\r\n> >   withDataBaseLock {\r\n> >      createStoredProcedures(this)\r\n> >      SchemaUtils.createMissingTablesAndColumns(tables = tables, inBatch = false, withLogs = false)\r\n> >   }\r\n> > ```\r\n> \r\n> hmm, no actually can't\r\n\r\nThis is fine - but I meant is there a way to specifically detect that the lock acquisition failed over some other kind of failure", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099520988"}}
{"comment": {"body": "> > > > This is general, but should we handle the lock acquisition failure specifically?\r\n> > > \r\n> > > \r\n> > > yeah, I just wrap this bit, if that's what you mean:\r\n> > > ```kotlin\r\n> > >   withDataBaseLock {\r\n> > >      createStoredProcedures(this)\r\n> > >      SchemaUtils.createMissingTablesAndColumns(tables = tables, inBatch = false, withLogs = false)\r\n> > >   }\r\n> > > ```\r\n> > \r\n> > \r\n> > hmm, no actually can't\r\n> \r\n> This is fine - but I meant is there a way to specifically detect that the lock acquisition failed over some other kind of failure\r\n\r\nExposed does not expose an exception or boolean from `withDataBaseLock`, so there's no way we can tell. We could write our own lock though, and maybe we should at some point so that we can have parameterized DB locks that can be used in many places.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099526284"}}
{"comment": {"body": ">Exposed does not expose an exception or boolean from withDataBaseLock, so there's no way we can tell. We could write our own lock though, and maybe we should at some point so that we can have parameterized DB locks that can be used in many places.\r\n\r\nWould be pretty easy to dupe and modify, Exposed's locking logic is quite simple", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099529199"}}
{"comment": {"body": "I have a specific situation in mind:\n\n1. Lots of write requests coming in simultaneously from clients\n2. Service boots up and obtains the lock\n3. Do pending client writes all fail? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#issuecomment-1099547023"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/899#pullrequestreview-942668788", "body": ""}
{"title": "Test branch7", "number": 9, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/9"}
{"title": "Add Button component to vscode", "number": 90, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90", "body": "Add base Button component and stories\nStorybook is running off of a test theme that's in themes/test-theme.scss\nIssues\n\nNot sure why the @use import isn't working, it compiles fine using the relative imports \nI didn't install stylelint -- was wondering if this is better off as a shared config for both web and vscode"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90#pullrequestreview-858725834", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90#pullrequestreview-858726126", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90#pullrequestreview-858737099", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90#pullrequestreview-858822398", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90#pullrequestreview-858825236", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/90#pullrequestreview-858825496", "body": ""}
{"title": "Add logging", "number": 900, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/900", "body": "Trying to address "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/900#pullrequestreview-942678560", "body": ""}
{"title": "Upstream sourcepoint even if the fileHash hasn't changed", "number": 901, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/901", "body": "Creates a source point for a commit even if the file hasn't changed from the previous commit returned by git rev-list\nFixes "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/901#pullrequestreview-943075675", "body": ""}
{"title": "Move environments to roles", "number": 902, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/902", "body": "We should be using roles to specify various environment contexts"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/902#pullrequestreview-945967446", "body": "I'm down"}
{"title": "Add link to the web dashboard message", "number": 903, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903", "body": "\n\nClient-side for now (should be easy enough to abstract to an API property if necessary)\nCurrent behaviour: all links go to the web dashboard instance of the message. Browser scrolls to the message given the url search query \nWill need to eventually add support to open message in any client"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-943096843", "body": ""}
{"comment": {"body": "URL could be much simpler as `/team/id/thread/id` or `/team/id/message/id`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#discussion_r851003699"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-944596994", "body": ""}
{"comment": {"body": "@jeffrey-ng you're going to have to weigh in here.  I know we have the repo in here so the UI bootup is easier, but I don't remember the details.  We can discuss when I'm in the office -- I'd imagine we need to separate out the case where you get to this UI from the main navigator (where we have the repo/thread from the store) vs the direct navigation case (where we need to fetch on load)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#discussion_r852252384"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-944828696", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-947806339", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-947818291", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-947823974", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/903#pullrequestreview-947841807", "body": ""}
{"title": "Setup web extension deployment", "number": 904, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/904", "body": "Setup packaging and deployment of ZIP for web extension\nFollow these instructions to install unpacked extension. "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/904#pullrequestreview-942901268", "body": ""}
{"title": "Defer upstreaming sourcepoints until after calculation", "number": 905, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/905", "body": "We might consider doing this periodically rather than just at the end"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/905#pullrequestreview-942846518", "body": "neat "}
{"title": "Unsubscribe from sourcemark stream once it has been initialized", "number": 906, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906", "body": "Only need to do full calculation once per calculator instance, which is basically once per repo per VSCode session."}
{"comment": {"body": "This is probably fine for April, but I'm wondering if this will cause bugs in the future.  If you add/remove resolved workspace Repos (you add a submodule, you change a local repo's origin, etc) I think the sourcemark engine would want to know about that?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#issuecomment-1102847864"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#pullrequestreview-944561602", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#pullrequestreview-944564457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#pullrequestreview-944880337", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#pullrequestreview-944891916", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#pullrequestreview-944891985", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/906#pullrequestreview-946291404", "body": ""}
{"title": "Proper range adjustment", "number": 907, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/907", "body": "Handles inserts:\n- [x] whole line insert\nHandles removals:\n- [x] whole line remove\n- [x] whole line remove inside range that is insignificant\n- [x] whole range remove\n- [ ] whole line move within the file\n- [ ] whole line move to any file\n- [ ] whole line move to any file with modifications\nHandles modifications:\n- [x] modification outside range\n- [ ] modification inside range that is insignificant\n- [ ] modification inside range that matches an insert in the file\n- [ ] modification inside range that matches an insert in any file"}
{"title": "Interface for getting SourceMark history", "number": 908, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/908", "body": "Given a SourceMark, retrieve the SourcePoint history relevant to the current branch.\nThis is intended to be used when we can no longer track a point (due to either code\nremoval or significant code modification beyond what we can reliably track). By\nproviding the history of points on this branch the user can at least browse previous\npoints at earlier revisions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/908#pullrequestreview-944046373", "body": ""}
{"comment": {"body": "So this should exclude source points not on this branch?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/908#discussion_r851858303"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/908#pullrequestreview-944059586", "body": ""}
{"comment": {"body": "yup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/908#discussion_r851868770"}}
{"title": "Use ktor 2.0.0", "number": 909, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909", "body": ""}
{"comment": {"body": "I thought this would be easy. Looks like they changed the API quite a bit. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#issuecomment-1101531315"}}
{"comment": {"body": "Submitted a slew of fixes for all the build issues.\r\nThis is a massive api change..", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#issuecomment-1101571414"}}
{"comment": {"body": "We should try to get this in soon so we're not stacking over it too much", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#issuecomment-1102844120"}}
{"comment": {"body": "> We should try to get this in soon so we're not stacking over it too much\r\n\r\nAnnoyingly, there's a bug (or a behaviour change) in that the authentication layer is no longer correctly parsing path parameters.\r\n\r\n                val teamId = parameters[\"teamId\"]?.asUUIDOrNull()\r\n                jwt.authTokenValidator(\r\n                    teamId = teamId,\r\n                    credential = credential,\r\n                )\r\n\r\ni..e The parameters map is now empty. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#issuecomment-1102874181"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-944514654", "body": "Fucj yeah"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-944671545", "body": ""}
{"comment": {"body": "looks like we can leverage the `authHeader` directly below, rather than searching for one from the `request.headers`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#discussion_r852305199"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-944879500", "body": ""}
{"comment": {"body": "meh", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#discussion_r852454380"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-945974938", "body": ""}
{"comment": {"body": "wow they misspelled the package. Ouch", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#discussion_r853257833"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-945977293", "body": ""}
{"comment": {"body": "This is useful. I'll followup because the way we're extracting the token info right now is brittle and partly copy-paste from ktor beta source. More to it than simply pulling out the Authorization header", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#discussion_r853259568"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-*********", "body": ""}
{"comment": {"body": "HACK!\r\nWe are hitting an issues where certain phases in the ktors pipeline are not exposed to parameters.\r\n\r\nEven more interestingly, I was able to confirm this was not somehow a weird dependency issue.\r\nThe routing parameters were being correctly resolved first, and somehow not being use during authentication calls.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#discussion_r860419562"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#pullrequestreview-*********", "body": ""}
{"comment": {"body": "smart!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/909#discussion_r860426055"}}
{"title": "Security plugin needs to be installed before routes", "number": 91, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/91", "body": "Or it will throw errors."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/91#pullrequestreview-*********", "body": ""}
{"title": "Disable log line in recalculateMarkForCommit", "number": 910, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/910", "body": ""}
{"comment": {"body": "@richiebres I'll remove and create an issue so that we can track investigating the logger", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/910#issuecomment-1101577219"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/910#pullrequestreview-944613845", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/910#pullrequestreview-944617356", "body": "just remove it. not that useful anyway"}
{"title": "Better error message when failing to create discussion for moved/renamed repo", "number": 914, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/914", "body": "This is better, but only slightly.\n\n\nWe need to audit all instances of window.showErrorMessage as we're dumping Errors in here right now.\n\n\n\nAlso the repo resolution and associated error messaging needs to consolidated so that we don't repeat the error handling whenever we need a repo instance."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/914#pullrequestreview-945978357", "body": ""}
{"comment": {"body": "@benedict-jw we should put some examples of error message wording up on Notion so we can use them as a template/reference.  Let us know if you have any thoughts on this.  We used to not use terms like 'Please' historically but I'm not sure if we want to continue with that or not...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/914#discussion_r853260324"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/914#pullrequestreview-945979308", "body": ""}
{"comment": {"body": "We should probably have a separate 'getDisplayName' helper function so we detach the enum value (which should be invariant and unchanging, in case we serialize it) from a displayed phrase.  Right now they'd be the same of course...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/914#discussion_r853261008"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/914#pullrequestreview-*********", "body": ""}
{"title": "Allow the CodeSwellDemo account", "number": 915, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/915", "body": "see task to remove allowlist\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/915#pullrequestreview-*********", "body": ""}
{"title": "Lint", "number": 917, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/917"}
{"title": "Simple gutter icon tooltips", "number": 918, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918", "body": "Display a tooltip and link to the thread on all sourcemark gutter icons.\n\nThis required joining thread data into the sourcemark data for each file.  This is not particularly efficient, but isn't adding any extra network overhead since we're loading this data anyways for the sidebar."}
{"comment": {"body": "@jeffrey-ng somewhat similar to what you're doing in the web extension...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944843108", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944849019", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944851247", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944851384", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944852925", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944866247", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/918#pullrequestreview-944888210", "body": ""}
{"title": "Basic source mark rendering", "number": 919, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919", "body": "Hook up sourcemark store to web extension UI.\nTODO: Need to integrate thread and message preview.\nBetter UI.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944856927", "body": ""}
{"comment": {"body": "For development I would assert that there is exactly one point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852436979"}}
{"comment": {"body": "- or if `targetLineNumber` < 1\r\n- or if `targetLineNumber` > max lines on page\r\n\r\nI would factor these sanity checks into the `getLineNumber` method ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852437402"}}
{"comment": {"body": "does this assume that there is only one mark per line? there can be many", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852438558"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944869496", "body": ""}
{"comment": {"body": "That's a good point. I'm not sure *how* we want to handle multiple source marks per line atm. Do we have designs for this? @benedict-jw ?\r\n\r\nHow are we handling / planning on handling this in VSCode? @matthewjamesadam ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852446570"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944871294", "body": ""}
{"comment": {"body": "I don't think we need these hard assertions.\n\n`getSourcePointContainer` will search the DOM for the row with matching line number. If it's missing, we'll log at that point.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852447897"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944882619", "body": ""}
{"comment": {"body": "Let's use the logger instead of console.error here (and throughout the file)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852456878"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944887015", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944888940", "body": ""}
{"comment": {"body": "In VSCode, right now, we will only show one (random, essentially) item per line.  We can combine them into a single tooltip, though I'm not sure how great a UI this will end up being.  It's effectively a problem we haven't thought about too much.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852460799"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-944889944", "body": ""}
{"comment": {"body": "In VSCode our options are extremely limited, but the idea I have is we change the icon to one that indicates multiple bubbles, and when hovered (or clicked?) we show two summaries, allowing you to click on each. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r852461570"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-946034315", "body": ""}
{"comment": {"body": "Going to keep one per line for this PR. Can update this at similar time with VSCode.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#discussion_r853300634"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-946077259", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-946341802", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/919#pullrequestreview-946343971", "body": ""}
{"title": "Make database calls in tests suspending", "number": 92, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92", "body": "Part of \nMakes transactionWithLogger suspending and adds ability to specify tests as suspending.\nFuture PR will establish a pattern for making suspending database calls in prod."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#pullrequestreview-858875121", "body": ""}
{"comment": {"body": "First main change", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#discussion_r789202704"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#pullrequestreview-858875230", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#pullrequestreview-858875917", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#pullrequestreview-858928153", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#pullrequestreview-858928711", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/92#pullrequestreview-858928793", "body": ""}
{"title": "Order pull requests in GraphQL query by updated_at desc", "number": 920, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920", "body": "Currently, new PRs that are merged are never ingested after the initial ingestion for a repo happens. To fix this, we need to poll the API periodically to get new comments (ultimately we want webhooks but for now we'll poll). \nTo do this, we need to update our initial ingestion logic to grab the updatedAt for the latest PR ingested. Then after the initial ingestion is complete, we can poll the rest API for pull requests that have been updated since that date. The updatedAt field changes whenever a PR is changed, including when comments are created, updated, or deleted. We can use this with the rest API etag to efficiently poll the rest API for new comments.\nThis PR updates the GraphQL query to sort PRs by updatedAt (descending). Next PR will add the query that polls for newly updated PRs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1067431399", "body": "Adding a review"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1067431888", "body": "Add another review"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1067440044", "body": "-"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1070267747", "body": "-"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1070270003", "body": "-"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1074984345", "body": "-"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-1075084706", "body": "-"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/920#pullrequestreview-944954634", "body": ""}
{"title": "Column ranges can be independently null", "number": 921, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/921", "body": "Turns out that columns are completely independent. They cannot (and should not) be\nrepresented as a range, since a range of [100, 1] is impossible.\nThey can also be independently null. This happens when the starting lines\nof a point range are deleted by a user; in this case we need to nullify the start\ncolumn, but not the end column. Similar when the ending lines of a point range\nare deleted by a user."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/921#pullrequestreview-945985254", "body": "Makes sense"}
{"title": "Proper range adjustment", "number": 922, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922", "body": "Handles inserts:\n- [x] whole line insert\nHandles removals:\n- [x] whole line remove\n- [x] whole line remove inside range that is insignificant\n- [x] whole range remove\n- [ ] DEFER: whole line move within the file\n- [ ] DEFER: whole line move to any file\n- [ ] DEFER: whole line move to any file with modifications\nHandles modifications:\n- [x] modification outside range\n- [ ] DEFER: modification inside range that is insignificant\n- [ ] DEFER: modification inside range that matches an insert in the file\n- [ ] DEFER: modification inside range that matches an insert in any file"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#pullrequestreview-946108632", "body": ""}
{"comment": {"body": "Just a heads up, the sourcepoints for this sourceMarkId in the local cache will be overwritten if the stream above receives an update", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#discussion_r853353940"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#pullrequestreview-946116512", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#pullrequestreview-946116931", "body": ""}
{"comment": {"body": "Chatted IRL, we need to update the logic above to merge updates with the local only changes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#discussion_r853360193"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#pullrequestreview-946170232", "body": ""}
{"comment": {"body": "next, we integrate this and merge server points with local points\r\nhttps://github.com/NextChapterSoftware/unblocked/pull/927", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/922#discussion_r853398450"}}
{"title": "Custom notifications for calls", "number": 923, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923", "body": "Summary\nImplemented with an NSPanel subclass with SwiftUI content. The tricky parts of this is getting the thing to \"float\" above all other content on the screen. The notifications stuff here will be pulled out into the menu bar app very soon.\nThe colours are off, but this is mostly just to get the mechanics working. Polish will come later. Parking the video app after this for now..."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944994994", "body": ""}
{"comment": {"body": "The real notification system always drops the notification onto macbook screen. I'm inventing a heuristic here by checking for the lowest screen id. It's a guess about the real heuristic", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852547060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944995276", "body": ""}
{"comment": {"body": "Probably will do this differently eventually", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852547307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944995469", "body": ""}
{"comment": {"body": "Set panel to edge of screen", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852547450"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944995525", "body": ""}
{"comment": {"body": "Then animate it in from the right", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852547500"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944995765", "body": ""}
{"comment": {"body": "Nasty", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852547675"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944996092", "body": ""}
{"comment": {"body": "I was playing with the video settings. This gives us a framerate at reasonable quality that does just as good if not better than Slack's video IMO", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852547921"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944996417", "body": ""}
{"comment": {"body": "TODO: Replace with brand icon", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852548213"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944997092", "body": ""}
{"comment": {"body": "I was having trouble setting this to true. REALLY don't like how macOS is releasing memory underneath the reference pointer", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852548695"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944997564", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944997825", "body": ""}
{"comment": {"body": "This is just here to trigger notifications from the app so we can see them in action. To be removed", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852549317"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-944998785", "body": ""}
{"comment": {"body": "This gives SwiftUI pre-12.0 background blending powers", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r852550118"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-945024717", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-945970697", "body": ""}
{"comment": {"body": "This looks pretty self contained. Refactor this out?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r853254858"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-945976765", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-945984703", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-946088111", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-946117375", "body": ""}
{"comment": {"body": "I think this is a pretty common issue when using SwiftUI in an NSWindow like this -- I don't think there's a problem setting this to false.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r853360511"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-946117880", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-946248422", "body": ""}
{"comment": {"body": "Will do if re-use is required", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#discussion_r853453417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/923#pullrequestreview-946253002", "body": ""}
{"title": "Make pull request ingestion resilient", "number": 924, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924", "body": "Part two of the work to ingest new pull request comments after the initial ingestion during onboarding. \nThis uses the RepoModel to store a cursor so that if the SCM service is restarted during ingestion, the process can pick up where it left off.\nOne more PR to come after this that will ingest comments from PRs as they're merged."}
{"comment": {"body": "Have you thought about how this will this cooperate with webhooks in future? In between the periodic ingestion runs the webhook handler will take care of ingest most (hopefully all) of the PR comments.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#issuecomment-1102811772"}}
{"comment": {"body": "@richiebres its a different cursor that we'll use for ongoing ingestion. The first initial phase during onboarding will use `prIngestionCursor` (used by the graphql api) and then after that phase is done we'll use `prIngestionETag` to get new PRs through the rest api. I'm doing it this way because the graphql api is more efficient in that we can tell which PRs have comments.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#issuecomment-1102814186"}}
{"comment": {"body": "> Have you thought about how this will this cooperate with webhooks in future? In between the periodic ingestion runs the webhook handler will take care of ingest most (hopefully all) of the PR comments.\r\n\r\nEach ingested comment becomes a `Message` model where `prCommentId` equals the ingested comment ID, so the periodic runs can check for existence before creating, as we'd expect the webhook handler will have created these messages.\r\n\r\nFor updates, the closest thing we have to versioning is the `updated_at` field on the GitHub comment object, so if we store that on the Message model we can check to see if we have the latest version before updating our Message model.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#issuecomment-1102918494"}}
{"comment": {"body": "> > Have you thought about how this will this cooperate with webhooks in future? In between the periodic ingestion runs the webhook handler will take care of ingest most (hopefully all) of the PR comments.\r\n> \r\n> Each ingested comment becomes a `Message` model where `prCommentId` equals the ingested comment ID, so the periodic runs can check for existence before creating, as we'd expect the webhook handler will have created these messages.\r\n> \r\n> For updates, the closest thing we have to versioning is the `updated_at` field on the GitHub comment object, so if we store that on the Message model we can check to see if we have the latest version before updating our Message model.\r\n\r\nSo it sounds like we need to traverse the GitHub API for every comment even when we have webhooks. And, in fact, it sounds like this incurs more work that the polling approach because we have to read from the DB before writing to ensure idempotency. I wonder if we should just use the comment webhook just as a signal to run the etag-based API ingest; in other words, ignore the webhook payload and just use it to trigger the API from the last etag. We can still poll very infrequently. Does that make sense?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#issuecomment-1103168559"}}
{"comment": {"body": "@richiebres I think the web hook payload has everything we need (https://docs.github.com/en/developers/webhooks-and-events/webhooks/webhook-events-and-payloads#pull_request_review_comment) though to be absolutely safe, hitting the API is the way to go, in which case what you suggest makes sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#issuecomment-1103186806"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945912998", "body": "I dont get the concept of complete or maybe Im missing where it is marked as incomplete? PR ingestion is ongoing basically forever, so we should just wake up periodically and suck down everything since the last cursor."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945914649", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945923151", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945928457", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945948512", "body": ""}
{"comment": {"body": "I wonder if we should change these lock semantics so that the lock either manages renewing itself, or passes a \"renewal block\" that the consumer can use. `lock.acquire()` changes to `lock.withAutoRenewingLock { }` or `lock.withLock { renew -> renew() }` where everything executes within the lock... block \ud83d\ude42", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#discussion_r853239719"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945951048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945953250", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945958993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-945965273", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-946055573", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/924#pullrequestreview-946331734", "body": ""}
{"title": "update office IP in waf whitelist", "number": 925, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/925", "body": "Removed old office IP addresses and added the new one."}
{"comment": {"body": "Just had a chat with folks and we might get rid of the IP block rule in favour of Github team whitelisting. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/925#issuecomment-1102827978"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/925#pullrequestreview-945939419", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/925#pullrequestreview-945939492", "body": "Is there a shared config (cross-environment) where we can put stuff like this?"}
{"title": "Fix concurrent race condition in sourcepoint calc", "number": 926, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/926", "body": "The recalculateMarkForCommit method was not reentrant, because concurrent\nexecutions would mutate the unsafe shared mark.sourcePoints array.\nThis addresses the error:\nfromPoint does not exist, should never happen"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/926#pullrequestreview-945974895", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/926#pullrequestreview-945992117", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/926#pullrequestreview-946082084", "body": ""}
{"title": "Add option for DataCacheStream to only send diffs", "number": 927, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/927", "body": "Right now this is needed for the VSCode SourceMark store, but I think more stores will use this eventually, as it's more efficient when the end targets are able to deal with diffs."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/927#pullrequestreview-946165084", "body": ""}
{"comment": {"body": "So we'll need to add:\r\n\r\n`returnedDataKind: ReturnedDataKind.changes`\r\n\r\nto the `CreateAPIDataCacheTraits` in SourceMarkStore.ts", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/927#discussion_r853394846"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/927#pullrequestreview-946165199", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/927#pullrequestreview-946166970", "body": ""}
{"comment": {"body": "Yep \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/927#discussion_r853396169"}}
{"title": "Add ability to get pull requests with etag", "number": 928, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928", "body": "Will be used for getting new pull request comments after the initial ingestion"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946271610", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946272110", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946273084", "body": ""}
{"comment": {"body": "lol", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#discussion_r853470694"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946273531", "body": "LGTM"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946274063", "body": "we could probably do this with a smaller test fixture?"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946294828", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946299547", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/928#pullrequestreview-946317994", "body": ""}
{"title": "Add hub app", "number": 929, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/929"}
{"comment": {"body": "The top-level `/video-app` folder name is becoming more and more inaccurate over time \ud83d\ude04 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/929#issuecomment-1102988112"}}
{"comment": {"body": "> The top-level `/video-app` folder name is becoming more and more inaccurate over time \ud83d\ude04\r\n\r\nYeah I'm going to move `macos` up eventually. It's painful because of submodules", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/929#issuecomment-1103127244"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/929#pullrequestreview-947802714", "body": ""}
{"title": "[WIP] Setup CORS and port handling", "number": 93, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/93"}
{"comment": {"body": "Getting an error when running this:\r\n\r\n```\r\n2022-01-20 17:44:58.564 [main] INFO  ktor.application - Autoreload is disabled because the development mode is off.\r\nException in thread \"main\" java.lang.NoClassDefFoundError: io/ktor/server/plugins/CORS\r\n\tat com.codeswell.plugins.CORSKt.configureCORS(CORS.kt:7)\r\n\t```\r\n\r\nAny ideas?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/93#issuecomment-1018086309"}}
{"title": "TextEditor line range off by one", "number": 930, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/930"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/930#pullrequestreview-*********", "body": ""}
{"title": "Fix gutter rendering", "number": 931, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/931", "body": "Dumb syntax mistake.  I'm surprised but there doesn't seem to be an eslint rule for this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/931#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This meant that we never latched the latest thread map, and couldn't render.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/931#discussion_r853465640"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/931#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This was another mistake that had no real effect -- we wouldn't remove the cached editor data when the last editor for a document closed.  We may even eventually *want* that behaviour, but for now we'll go with this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/931#discussion_r853466284"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/931#pullrequestreview-*********", "body": ""}
{"title": "Adding support for secure HLS streaming", "number": 932, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932", "body": "Modified Lambda Lookup function to handle requests for streaming (agora) assets\nI have added temporary support for special path handling to help us test this while we figure out how to register agora assets. Right now we can address an asset with .m3u8 extension directly. Eventually all assets will be accessed only using UUIDs\nAdded support to handle streaming assets similar to regular assets (for later once we have agora asset registration working!)\nWe no parse and modify playlist files on the fly to replace each part with a signed url\nAdded a separate config file to help with managing streaming configs\nAdded integration tests to infra deployment. These tests make sure Cloudfront in Dev is functional before allowing promotion to next env\nMade infra deploys sequential"}
{"comment": {"body": "I ran the code formatter on this which addresses most of the comments. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#issuecomment-1103198307"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946307607", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946308127", "body": ""}
{"comment": {"body": "minor nit: stylistically the close parenthesis should be newlined", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#discussion_r853495519"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946308390", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946308571", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946309992", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946310369", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946310659", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946311501", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946314410", "body": ""}
{"comment": {"body": "```suggestion\r\n    return uri.startsWith(\"/assets\") ||     // Auth Lambda@Edge should remove any instances of this prefix\r\n        pathComponents.length !== 3 ||   // Expected => \"\" + teamID + assetID\r\n        !isUUID(pathComponents[1]) ||    // Team ID is always a UUID\r\n        (!isUUID(pathComponents[2]) && !pathComponents[2].endsWith(\".m3u8\"))  // Non-uuid asset names are only allowed for streaming playlists\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#discussion_r853500170"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946315059", "body": "minor nits"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#pullrequestreview-946325705", "body": ""}
{"comment": {"body": "ops missed this. I'll make another PR for this. \r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/932#discussion_r853508393"}}
{"title": "Force display the sidebar for a split second on startup, to allow badges to display", "number": 933, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/933", "body": "See this for more details: \nThe main downside of this, as far as I can tell, is that we always jump back to the explorer view afterwards.  This makes sense on startup (because that's what is displayed by default), but when you're installing the extension VSIX it means we jump from the extension view to the explorer.  I can't find any way to determine which view the user is currently on, but I'll keep digging."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/933#pullrequestreview-946338659", "body": ""}
{"title": "Added debug information when clearing calculated source points", "number": 934, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/934", "body": "Handy for me when clearing points."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/934#pullrequestreview-946379747", "body": ""}
{"title": "fix path prefix in cloudfront tests", "number": 935, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/935", "body": "The extra output in diffs is because last deploy failed. Those same changes should go out with this PR"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/935#pullrequestreview-946398513", "body": ""}
{"title": "unblock infra deployments while I figure this out", "number": 936, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/936"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/936#pullrequestreview-946408694", "body": ""}
{"title": "Dont use OffSetDateTime", "number": 937, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/937", "body": "Saves us having to convert"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/937#pullrequestreview-946471198", "body": ""}
{"title": "Persistence layer for web extension", "number": 938, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938", "body": "Whenever the web extension's service worker is refreshed by the KeepAlive helper (~ 5 minutes), the data kept in memory is cleaned up. New streams are then created but left in an uninitialized state with no data. \nThis PR brings a persistence layer where the new streams can grab cached data and start in an initialized state.\nCurrently using web extension storage () to store the data."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-946437390", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-947418164", "body": ""}
{"comment": {"body": "Currently does not support partial updates.\r\n\r\nWith a simple key-value storage (e.g. browser.storage), we could introduce compound keys (e.g. PREFIX-ID) which will help optimize updates but will make get more troublesome.\r\n\r\nAn alternative is to use a more complicated data cache that can support multiple stores (e.g. idb?)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r854314635"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-947787511", "body": ""}
{"comment": {"body": "This is basically a part of the stream traits, and I wouldn't expect to have to pass `getCachedData` / `updateCache` into traits, I think the traits should just have the key, and the cache methods should just be pulled from the static LocalCache instance?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r854572695"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-947851604", "body": ""}
{"comment": {"body": "Yes.... That works.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r854617807"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949053765", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949057284", "body": ""}
{"comment": {"body": "A todo for later, we probably need a test that verifies that providing new stream data actually stores the cached API correctly?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r855478867"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949060313", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949062516", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949063785", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949067194", "body": ""}
{"comment": {"body": "Ah cool this will let us do piecewise updates efficiently...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r855489152"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949070055", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949070448", "body": ""}
{"comment": {"body": "I don't understand why we need two accessors, and it's taking a long time for me to trace through the code to figure it out.  Maybe add a couple comments explaining the difference between this and getCache?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r855491407"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949070567", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949071768", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949134832", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949146284", "body": ""}
{"comment": {"body": "Added tests", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r855543796"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949148940", "body": ""}
{"comment": {"body": "Added comments describing the difference between the different caches.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r855545794"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949149504", "body": ""}
{"comment": {"body": "But hoping to remove the need for two accessors soon. Will require converging DatacacheStream and ValueCacheStream.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#discussion_r855546203"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949174263", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/938#pullrequestreview-949174641", "body": ""}
{"title": "Sourcemark debug assertions mode", "number": 939, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/939", "body": "Intention is that the team runs in assertion mode.\nFound an issue right away:\njs\n{\n  context: 'SourceMarkStore',\n  service: 'vscode',\n  environment: 'local',\n  type: 'nodejs',\n  process: 'extension',\n  marksWithNoOriginal: [\n    '7e5e908f-3497-4b7f-81fe-21c26ecab3fa',\n    '658a3edc-4d84-415f-a038-6944dee94c6f',\n    'f72294b2-de61-4392-bb1f-064adc735ad6',\n    '4dab14a9-f9fe-4bb8-9078-f333e2b00383',\n    'f056a74a-6bea-4852-b997-88ba4a2fdbdd',\n    'ae6f9f9d-99f2-47ea-8179-004f42b943a2',\n    '30df9b1c-993d-4890-8c53-492fd807fbd1',\n    'cab06b7c-d841-4008-a447-de00187e39bc'\n  ],\n  level: 'error',\n  message: 'found mark(s) with no original point',\n  timestamp: '2022-04-20T00:18:34.246Z'\n}"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/939#pullrequestreview-946463614", "body": ""}
{"title": "Remove duplicated line", "number": 94, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/94", "body": ":)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/94#pullrequestreview-859129226", "body": ""}
{"title": "Add cors", "number": 940, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/940", "body": "For now, this is opening it up to everyone. We can clean this up later."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/940#pullrequestreview-946480809", "body": ""}
{"title": "Fix sidebar initialization", "number": 941, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/941", "body": "Looks like it needs a bit more time to initialize the webview."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/941#pullrequestreview-946477212", "body": ""}
{"title": "Lint", "number": 942, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/942"}
{"title": "Fix", "number": 943, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/943"}
{"title": "Merge modified server partial sourcemarks with locally cached marks", "number": 944, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/944", "body": "Addresses issue found in https://github.com/NextChapterSoftware/unblocked/pull/939"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/944#pullrequestreview-946558909", "body": ""}
{"comment": {"body": "Ah... damn, none of the tests use this helper, I missed this.  I'll add one.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/944#discussion_r853703320"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/944#pullrequestreview-946559328", "body": ""}
{"title": "Ingest recently merged PRs and updated comments for previously merged PRs", "number": 945, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945", "body": "Adds logic to ingest comments from newly merged PRs as well as updating already ingested PR comments."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#pullrequestreview-947643491", "body": "Right now if the SCM service is shut down the PR ingest will be killed immediately. Need to make the PR ingest cancelable, and make the Background job orchestration invoke job.cancel and add a timeout before instance termination (see BackgroundJobs.close()."}
{"comment": {"body": "This is currently unbounded. Consequence is that:\r\n\r\n1. this could run longer than the allowed time for the background job (what is the max time?)\r\n2. this is more likely to get interrupted by service shutdown\r\n3. this is more likely to get throttled by GitHub rate limits\r\n4. if it is interrupted (for whatever reason), then the job will be re-ingested because we do not persist the cursor until this entire function completes\r\n\r\nSuggestion: make this more like an any-time algorithm, one that can be safely interrupted at any time, and it will pickup from where it left off. Specifically:\r\n\r\n1. cap this to a maximum number of items (or pages, or time, or whatever)\r\n2. persist the cursor as soon as each page of comments is persisted (leverage SQL to do this transactionally)\r\n3. this must be cooperatively cancellable, so that when the background job is shutting down this loop does not start another iteration.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#discussion_r854470105"}}
{"comment": {"body": "Let's generalize the eTag caching, so that it's easy for all of the GitHub APIs to take advantage of this. Maybe make a `CacheableResult` here, and a `cacheableRequest()` function that is wrapped by `getMergedPullRequests()`. This will make the PR business logic much easier to understand/maintain.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#discussion_r854489883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#pullrequestreview-947726423", "body": ""}
{"comment": {"body": "This'll polls every second and will grab closed PRs, so will likely not need require grabbing a second page. But fair point: if the service is down for a while then this could take a long time. Ok if we merge this PR as is just to get ingestion working, and then I'll work on rearchitecting this later this week?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#discussion_r854528291"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#pullrequestreview-947731335", "body": ""}
{"comment": {"body": "Sure. Same thing happens on initial ingestion btw.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#discussion_r854531840"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#pullrequestreview-947735558", "body": ""}
{"comment": {"body": "Yep that takes even longer and is at greater risk of interruption", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#discussion_r854534838"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#pullrequestreview-947742079", "body": ""}
{"comment": {"body": "Created an issue to track https://github.com/NextChapterSoftware/unblocked/issues/952", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#discussion_r854539365"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/945#pullrequestreview-947757079", "body": ""}
{"title": "Enable SourceMark engine by default", "number": 946, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946", "body": "Seems stable enough now. People can still turn it off in settings if something goes wrong.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946#pullrequestreview-947441610", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946#pullrequestreview-947449354", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946#pullrequestreview-947468402", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946#pullrequestreview-947514736", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946#pullrequestreview-947544139", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/946#pullrequestreview-947550962", "body": ""}
{"title": "Funnel through participants on thread creation", "number": 947, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947", "body": "Add participant ids to the CreateThreadRequest body\nInsert ThreadParticipantModels on thread creation\nCreate new ThreadUnreads for each participant team member \n\nNOTE: Right now we only pass through team member ids -- next bit of work is handling what we do with users that have not authed/signed up with Unblocked yet (right now we do nothing)"}
{"comment": {"body": "General comment: why couple this with thread creation? Would be simpler to add/remove members to a thread as an independent API. Plus, we need this separate API anyway.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1104389660"}}
{"comment": {"body": "@richiebres do you mean something like a separate ThreadParticipantStore ? \r\n\r\nThe method is mostly isolated from the actual thread creation so it should be simple to refactor out when necessary. I think I have a preference to get this code in first to get this feature working since I'm mostly unfamiliar with the kotlin architecture -- unless y'all feel this is a blocking issue ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1104455574"}}
{"comment": {"body": "> General comment: why couple this with thread creation? Would be simpler to add/remove members to a thread as an independent API. Plus, we need this separate API anyway.\r\n\r\nI thought we wanted thread creation to be atomic -- the thread, its first message, its participants, etc, are all created atomically?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1104460072"}}
{"comment": {"body": "> > General comment: why couple this with thread creation? Would be simpler to add/remove members to a thread as an independent API. Plus, we need this separate API anyway.\r\n> \r\n> I thought we wanted thread creation to be atomic -- the thread, its first message, its participants, etc, are all created atomically?\r\n\r\nThey are already created atomically before this change. This change is about adding additional participants right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1104517854"}}
{"comment": {"body": "> They are already created atomically before this change. This change is about adding additional participants right?\r\n\r\nNo this change is about adding additional participants when the thread is first created.  Right now, when a thread is created, we only add the person who created the thread to the set of thread participants.  With this change, whoever was selected in the participant list (in the \"Start Discussion\" UI) will be added as participants in the thread as well, at the time it is created.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1105480612"}}
{"comment": {"body": "Ah, or maybe I mis-read and you think it's fine to create the thread with one call, and add the initial participant set in a second.  I don't have much of an opinion about this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1105494746"}}
{"comment": {"body": "> Ah, or maybe I mis-read and you think it's fine to create the thread with one call, and add the initial participant set in a second. I don't have much of an opinion about this.\r\n\r\n\r\nSo when a discussion is created right now, the following things are created atomically, which means everything is consistent:\r\n1. thread\r\n2. first message\r\n3. author of message as participant\r\n\r\nMain concern is just the complexity of overloading this api operation with additional parameters, especially if there will a dedicated api to do the same thing later. I guess we'll see how it plays out. I\u2019m good \u2705", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#issuecomment-1105607355"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947531246", "body": "Couple of comments"}
{"comment": {"body": "Can you update ThreadUnreadStore.create to `insertIgnore` too?\r\n\r\nSo change `ThreadUnreadModel.insert` to `ThreadUnreadModel.insertIgnore`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854401049"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947555025", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947556664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947560922", "body": ""}
{"comment": {"body": "I'm not sure that `reduce` helps legibility here?\r\n```\r\nconst teamMembers: string[] = [];\r\nconst emails: string[] = [];\r\nfor (const contributor of props.participants) {\r\n if (contributor.teamMemberId) {\r\n                         teamMembers.push(contributor.teamMemberId);\r\n                     } else {\r\n                         emails.push(contributor.email);\r\n                     }\r\n}\r\n```\r\n\r\nUp to you which one reads better...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854416933"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947567909", "body": ""}
{"comment": {"body": "Is this raised on startup?  I would want to make sure we aren't re-resolving the repos multiple times on startup if we can avoid it, as it could be expensive.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854421741"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947568225", "body": ""}
{"comment": {"body": "(And do we need to handle `onDidCloseRepository` too?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854421974"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947568924", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947569628", "body": ""}
{"comment": {"body": "I don't think this needs to be async since we're just hooking up an event handler?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854422975"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947656259", "body": ""}
{"comment": {"body": "it needs the git api to load (line 142) to hook into the event", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854479169"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947675425", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947729060", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947775517", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-947831577", "body": ""}
{"comment": {"body": "this is done", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r854603182"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-949093932", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-949097571", "body": ""}
{"comment": {"body": "Instead of caching teams here, access it from teamstore?\r\n\r\nWant to avoid having duplicate caches in our codebase like this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r855510177"}}
{"comment": {"body": "I understand the rationality behind these changes. curious to how they're related to participants on thread creation?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r855510885"}}
{"comment": {"body": "Also, need to try catch where Git fails to instantiate.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r855511926"}}
{"comment": {"body": "Instead of resolving all the repositories, try doing a diff and only resolving new repos ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r855513344"}}
{"comment": {"body": "Can be a todo.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r855514014"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#pullrequestreview-949114859", "body": ""}
{"comment": {"body": "Not related, I wasn't able to do anything on my local IDE until I fixed this issue, should have extracted it to a different branch ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/947#discussion_r855521148"}}
{"title": "forgot to add build step before deployments", "number": 948, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/948", "body": "I forgot to add the build step (which bundles NPM modules) to deployment jobs. We need this for both test and deploy jobs but we were only running this in the test jobs!"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/948#pullrequestreview-*********", "body": ""}
{"title": "Fix sidebar overlay", "number": 949, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/949", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/949#pullrequestreview-*********", "body": ""}
{"title": "Fix DB access in development environment", "number": 95, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/95", "body": "This pr does several things:\n1. Associates a serviceAccount with our services deployed via helm.\n2. Fixes liveness probes for services deployed via helml.\n3. Add database config that allows us to authenticate db access against sts. \nTesting:\n1. Deployed here \n2. Validated pod is running and connected to db!\n```\n20220121T203954Z\n20220121/us-west-2/rds-db/aws4_request\n549ac458bbbd793998d92a35faebe39f6b858847c10332b1f22bf6b3fc8089d1\n2022-01-21 20:39:55.035 [main] DEBUG Exposed - CREATE EXTENSION IF NOT EXISTS pg_trgm\n2022-01-21 20:39:55.169 [main] TRACE s.a.awssdk.auth.signer.Aws4Signer - AWS4 Canonical Request: GET\n/\nAction=connect&DBUser=postgres&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAR4KJXTWR4JCJFZEE%2F20220121%2Fus-west-2%2Frds-db%2Faws4_request&X-Amz-Date=20220121T203955Z&X-Amz-Expires=900&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEA0aCXVzLXdlc3QtMiJIMEYCIQCDGiOr09aOScd3Y05Y8DWMN0DFlJ%2BGIcwOIrkvBoKjlgIhAIsxrP8k%2BpdrtusjJPyg11K%2FE012Qc67uuyU9BwBxijmKoAFCDYQABoMMTI5NTQwNTI5NTcxIgw3ytN5Ch%2FWRQ4qWQQq3QTgsSBZqvDEJVzw9ascxRz4riJeb3gSU3RyKK5oZA%2FndByaycnHIPGfR4kXpGVnBu8aaunJQEFRKimK%2FDG4mLL83FQIIslh%2B3jKC5OAKBl2pf%2Fc3XLLkTx8sWY9toqxhF9MKdnh5a8Sh732JHvfOlslw274SDpOIdFJK3zON48dlZk2MD8n6RZ94p6D3QJXa5k1Xdz74UK%2BVr90s9HL7eXiqCBc9G5dDTxCzzcwKQ0ta9Zu0kQvRZ2IaDk7vK6aGaBPxrN87LJP%2BhfBrW9%2F696iDumUmq7CISJwjqKgR1rH%2FcyX8a3daOgstIQoC6limT%2FISGYzThSMi2I4mmMLi5bglwI3jrk1kv3NLZbHU5JN88PwbL%2B2Vfzi87sw8ELCVw7LHnNnpST4AovjFIRW3lIYprGU2p41V2JmF6rKXygfo6CotztMqPtQfSR4tnL77jpG%2FS0YwHYvjSaNtglzG6QQ4M%2Fyc0Elt5QdDZdjKPeZE2Hr96uasl8Mt%2FFtkhOWBnUXm1GJLfh12YJn9wTw%2B78m9wsB8jP6%2B7PQqTFoDhSo9CWxg42WOAJGA2UWHtvQXMQDAs8C%2B6iPM4sBAbf62kUMSHGLEvX0D2RWffJNCe8wll5whNtMyk3eYPYVVe1XhN6e3kde49dMkhYAsliyotHrH9%2FxLIz8EhCN0dhWKR5z73hJyJ5W2yDtw%2BVdJUp9ws1EEJRSmzgKdUUkTmdWn3zM6hVOr2f16X0KbndBu1G71j8v0rePH5gvgqWmmjdLMzt2Uv79KgIM6ELhmD3Yc3YdJBvvkm3t8GF%2FfPCxuDCatKyPBjqZAVsaW3TbfjAHLKHJSTsyjt7my4NB7m95%2FR2tdFRStjPpCt2cuDRSslchN75QehXTiQKiq1IGEMs6A4U8XOZ728kY%2BpIVDbEt7R9w0IGJXhzAzqjW6YEyPcwIrR1jiQy4lx42i55i78t%2BSA7X%2BwdG3iNt3AVw8x6A6uNpM3vqSrkCWJeGOOJ8njbT5iZzhxGxYKvfNERDAHOwQQ%3D%3D&X-Amz-SignedHeaders=host\nhost:databasestack-mainapp32bc6243-1vvgj496lx136.cluster-csga8shziqqm.us-west-2.rds.amazonaws.com:5432\nhost\ne3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\n2022-01-21 20:39:55.170 [main] DEBUG s.a.awssdk.auth.signer.Aws4Signer - AWS4 String to sign: AWS4-HMAC-SHA256\n20220121T203955Z\n20220121/us-west-2/rds-db/aws4_request\n5275846048d8619af3c28e7037330122d210cd002954fd8d7287b9020eb05731\n2022-01-21 20:39:55.267 [main] DEBUG Exposed - SELECT busy.busy FROM busy FOR UPDATE\n2022-01-21 20:39:55.280 [main] DEBUG Exposed - INSERT INTO busy (busy) VALUES (TRUE)\n2022-01-21 20:39:55.291 [main] INFO  Exposed - Preparing create tables statements took 10ms\n2022-01-21 20:39:55.297 [main] INFO  Exposed - Executing create tables statements took 5ms\n2022-01-21 20:39:55.316 [main] INFO  Exposed - Extracting table columns took 19ms\n2022-01-21 20:39:55.373 [main] INFO  Exposed - Extracting column constraints took 56ms\n2022-01-21 20:39:55.373 [main] INFO  Exposed - Preparing alter table statements took 76ms\n2022-01-21 20:39:55.376 [main] INFO  Exposed - Executing alter table statements took 3ms\n2022-01-21 20:39:55.419 [main] INFO  Exposed - Checking mapping consistence took 43ms\n2022-01-21 20:39:55.423 [main] DEBUG Exposed - DELETE FROM busy\n2022-01-21 20:39:55.521 [main] INFO  ktor.application - Autoreload is disabled because the development mode is off.\n2022-01-21 20:39:56.014 [main] INFO  ktor.application - Application auto-reloaded in 0.493 seconds.\n2022-01-21 20:39:56.015 [main] DEBUG ktor.application - Application started: io.ktor.server.application.Application@7aac8884\n2022-01-21 20:39:56.210 [DefaultDispatcher-worker-2] INFO  ktor.application - Responding at \n2022-01-21 20:41:53.905 [idle-connection-reaper] DEBUG s.a.a.h.a.internal.net.SdkSslSocket - shutting down output of sts.us-west-2.amazonaws.com/54.240.252.193:443\n2022-01-21 20:41:53.906 [idle-connection-reaper] DEBUG s.a.a.h.a.internal.net.SdkSslSocket - closing sts.us-west-2.amazonaws.com/54.240.252.193:443\n2022-01-21 20:46:21.325 [eventLoopGroupProxy-4-1] DEBUG ktor.application - Path: /, Status: 404 Not Found, HTTP method: GET, User agent: curl/7.68.0, Parameters: []\n2022-01-21 20:46:24.173 [eventLoopGroupProxy-4-2] DEBUG ktor.application - Path: /, Status: 404 Not Found, HTTP method: GET, User agent: curl/7.68.0, Parameters: []\n```"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/95#pullrequestreview-860028451", "body": "nice!"}
{"title": "Refactor out the ContributorsList for reuse", "number": 950, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950", "body": "Rename and move component into the shared directory so it can be used in multiple clients \nAdd stories for testing\nNo logic change, just moving the location of the files"}
{"comment": {"body": "Test comment", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#issuecomment-1104558314"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#pullrequestreview-948974302", "body": ""}
{"comment": {"body": "This seems wrong?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#discussion_r855422953"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#pullrequestreview-948975097", "body": ""}
{"comment": {"body": "Using px instead of fixed heights/rems?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#discussion_r855423301"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#pullrequestreview-948983899", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#pullrequestreview-949005044", "body": ""}
{"comment": {"body": "We do this for all the stylesheets in the shared/ directory since there's no alias for the shared stylesheets", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#discussion_r855441739"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#pullrequestreview-949005993", "body": ""}
{"comment": {"body": "(Not to say that we can't make this nicer, just that this is the pattern we've been following thus far)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/950#discussion_r855442106"}}
{"title": "Create and populate asset urls in message", "number": 951, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951", "body": "This pr enables using asset service to retrieve s3 presigned urls for uploads/downloads.\nKeep in mind several things:\n1. Webview tells extension to call AssetService for presigned urls.\n2. We use a very stupid request/response model to update the webview when urls are available for upload to s3.\n3. The Webview then does an asychronous fetch request to upload to s3. Only the Webview can do this as the underlying file stream is local to the Webview instance. (File Paths are not exposed in most browsers for security reasons).\nOther notes:\n1. We transparently switch the image src from local data uris to s3 urls when the asset has been officially uploaded.\n2. No idea why we moved away from Image Blocks, but there's no reason we would consider using inline images rather than image blocks for representing this stuff.\nTesting:\n1. Validated s3 urls were correctly being retrieved and uploads of images was working."}
{"comment": {"body": "> No idea why we moved away from Image Blocks, but there's no reason we would consider using inline images rather than image blocks for representing this stuff.\r\n\r\nI'm not 100% I understand what you're asking here -- but we allow inline images in the Block model (as we have to represent what we get from GitHub), but the idea is that we would only allow ImageElements at the block (top) level in the editor...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#issuecomment-1104516898"}}
{"comment": {"body": "> > No idea why we moved away from Image Blocks, but there's no reason we would consider using inline images rather than image blocks for representing this stuff.\r\n> \r\n> I'm not 100% I understand what you're asking here -- but we allow inline images in the Block model (as we have to represent what we get from GitHub), but the idea is that we would only allow ImageElements at the block (top) level in the editor...\r\n\r\nWhat I was asking is why we removed support for top-level ImageBlock (not nested in paragraphs).\r\nI don't see why we can't support inline images (nested in paragraphs) and top level image block. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#issuecomment-1104519420"}}
{"comment": {"body": "> What I was asking is why we removed support for top-level ImageBlock (not nested in paragraphs). I don't see why we can't support inline images (nested in paragraphs) and top level image block.\r\n\r\nAh.  @jeffrey-ng can probably answer this best, but I think the idea is that you can always represent a block-level Image as a Paragraph block containing an inline image.  It does make the translation a bit more awkward though, as you need to flatten the inline images into top-level elements.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#issuecomment-1104520409"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947669919", "body": ""}
{"comment": {"body": "Todo is to move to this to a webworker...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#discussion_r854488738"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947671221", "body": ""}
{"comment": {"body": "This is basically the request/response behaviour between webview/extension.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#discussion_r854489597"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947675587", "body": ""}
{"comment": {"body": "We make a best effort to load data uri (base64 representation of image) into image src.\r\nThat will be replaced by s3 url when upload is complete.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#discussion_r854492608"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947675840", "body": ""}
{"comment": {"body": "On completion, we alter the image uri to s3 url.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#discussion_r854492786"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947846508", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947846915", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947856668", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947858758", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947881316", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/951#pullrequestreview-947884574", "body": ""}
{"title": "Track changed files and write out to tmp directory as needed", "number": 953, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947757202", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947872842", "body": "Cool thanks."}
{"comment": {"body": "I\u2019ll do this bit.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#discussion_r854629161"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947885508", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947888159", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947889916", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947890797", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-947900826", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-948854964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-948887771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-948890331", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-948895351", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-948939306", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/953#pullrequestreview-948939432", "body": ""}
{"title": "Remove MessageEditorPlayground dead code", "number": 954, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/954", "body": "Just getting rid of test code we aren't using."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/954#pullrequestreview-948939754", "body": ""}
{"title": "Custom tabview", "number": 955, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955", "body": "The secret sauce was to create a Protocol with associated types over the concrete type, then make clever use of builders. The resulting API feels very SwiftUI-like. Ignore some of the styling things for now, I'm still a n00b and was playing around with inner drop shadows and stuff.\nI'd appreciate any advice on how to adopt platform colours that will update between light/dark mode for macOS 11 without resorting to Environment hacks. Trying to get to this:\n\n"}
{"comment": {"body": "There's a bit of cleanup in the next PR: https://github.com/NextChapterSoftware/unblocked/pull/964/files", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#issuecomment-1105611389"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-947775686", "body": ""}
{"comment": {"body": "This is nasty - I'll pull this out but this is just to get the inner shadow and border effect", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r854564014"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-948993927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-948998973", "body": ""}
{"comment": {"body": "Should we put both the tab content, and the view content (ie the content of the view that will be displayed when this tab is selected) in here?  Tying them together is what you will want to have happen almost all the time, and it would make the API for this pretty nice.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855437211"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949052760", "body": ""}
{"comment": {"body": "Isn't that what this is already doing? Can you describe what you mean with an example? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855472679"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949054833", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949055432", "body": ""}
{"comment": {"body": "Never mind, you're right!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855476364"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949106180", "body": ""}
{"comment": {"body": "We might want to move some of these bits below here that aren't inherently tied to the tabview into separate files...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855515072"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949106956", "body": ""}
{"comment": {"body": "Huh, can we not use system colours for these?  Or we need light/dark mode variants for them to look right?  Or maybe these just work fine in both?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855515555"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949107117", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-949133960", "body": ""}
{"comment": {"body": "This particular one is a hack, because SwiftUI will treat a Color with opacity 0 as non-interactive. This means that the tab background would be unclickable (only the foreground image would be clickable), which feels wrong. Maybe there's a cleaner workaround for this but I haven't found one yet. \r\n\r\n\r\nAs for the rest of the colours I wasn't able to find a system colour that looked \"right\". Apple doesn't seem to expose a system colour or style that looks like the native `TabView` background. \r\n\r\nYour comment about light/dark mode is correct, we will need multiple variants", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855535060"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Agreed - I will do", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/955#discussion_r855535518"}}
{"title": "Spinning up Notification service", "number": 956, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/956", "body": "At the moment we are only planning to add email notification logic to this. Further down the road we might add slack and other notification types to it as well. I'll be adding service accounts and ECR repos in a separate PR\n- Setup a new gradle project\n- Added source code for an empty app with health probes\n- Generated deployment helm charts\n- Added the new service to Github actions workflow to have it deployed\n- Tiny change in .gitignore to hide all those annoying bin directories"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/956#pullrequestreview-*********", "body": ""}
{"title": "Adding service acccount and ECR repo for new service", "number": 957, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/957", "body": "Added a notification repo to ECR\nAdded a new service account for  notification service"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/957#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/957#pullrequestreview-*********", "body": ""}
{"comment": {"body": "I had to setup this identity manually. Unfortunately CDK has no way of setting it up. We use `<EMAIL>` for dev", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/957#discussion_r854584776"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/957#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Same as my last comment. We use `<EMAIL>` for prod", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/957#discussion_r854584963"}}
{"title": "Updating assets that represent the Unblocked extension in VSCode", "number": 958, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/958", "body": "Replacing the Codeswell icons with Unblocked. We will most likely tweak the shape and colours but this at least gets us closer to our ultimate goal. \nSidebar\n\nExtension Icon\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/958#pullrequestreview-947807418", "body": ""}
{"title": "Update assets for web dashboard and extension", "number": 959, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959", "body": "\n\n"}
{"comment": {"body": "???", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#issuecomment-1104558604"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#pullrequestreview-947887932", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#pullrequestreview-947897109", "body": ""}
{"comment": {"body": "Interesting", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#discussion_r854658155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#pullrequestreview-947903304", "body": ""}
{"comment": {"body": "Added typing here", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#discussion_r854663275"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#pullrequestreview-*********", "body": ""}
{"comment": {"body": "fdsfsf", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/959#discussion_r854663530"}}
{"title": "Redesign user auth flow sequence for optimal UX", "number": 96, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96", "body": "Problem\nThe previous auth flow design had 3 major shortcomings:\n\nThe web-app wasn't taken into account. A response token body via a redirect to the api-service would stop the browser dead in its tracks\nThe native client experience relied on a custom uri scheme handler to pop the user back to the client to finish the token exchange\nThe browser wasn't treated like a first class citizen during the native auth flow\n\nProposal\nWith these changes we can create an experience where the browser has a consistent authentication flow that ends up on the dashboard, possibly adorned with some extras if the entry point was a native client.\nThe native client can also authenticate in the background seamlessly without any additional user interaction after the browser auth flow is complete.\nWe're also taking some security measure that should prevent token sidejacking (like setting an httpOnly strict cookie), and storing a relationship between the secret and nonce for token retrieval"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Is this the request that the client is polling?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#discussion_r789858273"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#pullrequestreview-859916659", "body": ""}
{"comment": {"body": "Yes", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#discussion_r789916922"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#pullrequestreview-861342522", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#pullrequestreview-861342748", "body": ""}
{"comment": {"body": "I would change this to a GET instead of a POST", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#discussion_r791024818"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#pullrequestreview-861418205", "body": ""}
{"comment": {"body": "Modified in next PR: https://github.com/Chapter2Inc/codeswell/pull/107", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/96#discussion_r791079948"}}
{"title": "Cleaning up thread layout", "number": 960, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960", "body": "Moved code block into the left side panel\n\nDiscussion titles have been moved into the first post.\n\nTune responsive layout (thanks @matthewjamesadam)\nWhen the discussion window hits the narrow breakpoint, the message content tucks underneath the avatar, name, and date. Beyond this breakpoint, the discussion thread takes up more horizontal space, where the avatars all align visually in one column\n\nMargins, Padding, line heights\nCleaned up the spacing between elements in each message, as well as the surrounding container. I also adjusted the line heights.\n\nParticipants side panel\nRemoved the title divider as it was clashing with the horizontal lines in the main discussion panel. Also cleaned up the spacing between participants.\n\nMessage editor controls and styling\nSwapped out the text for font awesome icons, added a min height, and styled the various states, including the generic code block. \n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-952554488", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-952554886", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-954175681", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955133132", "body": ""}
{"comment": {"body": "Replaced text with font awesome icons for the message editor controls.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859960492"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955138000", "body": ""}
{"comment": {"body": "Ran into a few scenarios that needed more granularity for margins, padding, and font size. It's largely because there are slight differences between how things are rendered in Figma vs code.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859964019"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"comment": {"body": "The styling here is meant to replicate the font styling used in in the editor, with the exception of syntax highlighting. This is also used in the rendered code block. If there is anything existing that we can use to share this, we can rectify this.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859970758"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"comment": {"body": "See related comment here: https://github.com/NextChapterSoftware/unblocked/pull/960/files#r859970758", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859971970"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Pass thread information down to the first Message, so it can render the title", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859983435"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Hmm...\r\nCan you put a comment in here explaining this?  This will *not* match the editor settings if the user has actually changed them (in Code -> Settings).  To fix this we would need to pass all those settings through to the webview which will be a bit complicated and we can do it later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859989828"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955183641", "body": ""}
{"comment": {"body": "Something to think about for the future: it looks like we're overriding a bunch of header styles throughout the UI which isn't ideal, should we standardize this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r859994020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955184860", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955185130", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955197136", "body": ""}
{"comment": {"body": "Sure \u2014\u00a0the font was previously undefined, and it defaulted to \"monospace\". It looked wildly different than the editor which is what lead me to change it. I just inspected what was in the editor with the default themes, and matched font-family, font-weight, font-size, font-feature-settings, line-height, and letter spacing. You're right, if the user changes their default font, this won't update. I still prefer this over what was undefined previously, but one better would be to match their default font.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r860003740"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955198953", "body": ""}
{"comment": {"body": "Is there a way to leverage aspects of the styling for the discussion code block?\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r860005068"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955257968", "body": ""}
{"comment": {"body": "Structurally I think it'll be pretty much similar or identical, but stylistically these values will likely change per client. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#discussion_r860047662"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955332600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955333172", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955333827", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955334180", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955335256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955335524", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955340551", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955345371", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-955345740", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/960#pullrequestreview-*********", "body": ""}
{"title": "Fix recalc for uncommitted changes", "number": 961, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/961", "body": "Now correctly takes into account uncommitted changes, triggered on save.\nAlso now hides deleted and significantly modified points from editor.\n\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/961#pullrequestreview-*********", "body": ""}
{"title": "Generalize spinner", "number": 962, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/962", "body": "Generalize spinner and add storybooks to validate.\n"}
{"comment": {"body": "The idea is that you'll add another Spinner style file for VSCode in another PR, that uses the VSCode theme colour?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/962#issuecomment-**********"}}
{"comment": {"body": "> The idea is that you'll add another Spinner style file for VSCode in another PR, that uses the VSCode theme colour?\r\n\r\nThat's right. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/962#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/962#pullrequestreview-948858981", "body": "Im curious - how does the shared Spinner story look?? I dont see a file styling the colour for it (for other shared stories the pattern we use is to add a Stories.scss file which is only imported into the shared story file)."}
{"title": "Drop macos build timeout to 20 minutes", "number": 963, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/963"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/963#pullrequestreview-948058116", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/963#pullrequestreview-948058793", "body": ""}
{"title": "Integrate with status bar", "number": 964, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/964", "body": "Summary\nBunch of stuff going on here:\n- Had to fall back to AppKit for status bar integration because SwiftUI just doesn't have the facilities for it\n- Used storyboard with no window hack\n- Had to use global event hack to close popover in some \"special\" cases (comments inline)\n- Popover is not perfectly centred as shown in the designs. NSPopover provides a \"preferred edge\" interface. Maybe we can hack around this, maybe not. If we have to implement our own popover using NSWindow we can do that, it just gets a little more complicated because we have to draw out own path shape etc"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/964#pullrequestreview-950618229", "body": ""}
{"comment": {"body": "Given this monitor, how does it differentiate between mouse events *within* the view vs outside the view?\r\n\r\nI guess it doesn't? If so, how is this currently being handled? The demo today did not close when you clicked within the popover.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/964#discussion_r856598432"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/964#pullrequestreview-950618607", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/964#pullrequestreview-950631395", "body": ""}
{"comment": {"body": "It does... magically :) \r\n\r\nThe secret sauce to this is in the documentation for `addGlobalMonitorForEvents`, namely this:\r\n```\r\nUse +addGlobal to install an event monitor that receives copies of events posted to other applications.\r\n```\r\n\r\nThe local variant gives you events in your own app", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/964#discussion_r856605454"}}
{"title": "Add GitHubAppClient methods for updating pull request comments", "number": 965, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/965", "body": "These will be used for pushing message updates back to GitHub on threads that were ingested from pull requests."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/965#pullrequestreview-948086395", "body": ""}
{"title": "Extend sidebar load time", "number": 966, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/966", "body": "Looks like 100 wasn't enough, sometimes the sidebar content doesn't load.  Hacky hack."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/966#pullrequestreview-948861634", "body": ""}
{"title": "Notarize only after merging to main", "number": 967, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/967"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/967#pullrequestreview-948883434", "body": ""}
{"title": "Up cpu and memory for services", "number": 968, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/968"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/968#pullrequestreview-948888406", "body": ""}
{"title": "Set to thread to read on discussion load", "number": 969, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/969", "body": "Parity with web/vscode\nAlso some small styling patches to the extension sidebar"}
{"comment": {"body": "This means we're always making the API call to set this flag, even if we have no unread messages in the thread?  That's probably fine for now, just something to keep in mind that we can optimize later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/969#issuecomment-1105466574"}}
{"comment": {"body": "@matthewjamesadam No [this](https://github.com/NextChapterSoftware/unblocked/pull/969/files#diff-d02ad5285216cf7cd3e6c9fc6684ad06ad28bdb841c587a873a830891e85e46bR52) line should gate the code from always making the call", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/969#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/969#pullrequestreview-*********", "body": ""}
{"title": "Ckd rds access for eks", "number": 97, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/97", "body": "Added EKS VPC as an imported resource (infrastructure/cdk/core/cdk.context.json  are generated by cdk)\nAdded routes to both coreVPC as well as EKS VPC to route traffic through VPC peering connection \nAdded service account definition for RDS IAM based access\nCreated documentation for managing IAM mapped service accounts using eksctl\n\nAll changes have been deployed. RDS connection is now working thanks to all of Rashin's hard work."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/97#pullrequestreview-*********", "body": ""}
{"title": "Sourcemark Engine processes file changes incrementally", "number": 970, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/970", "body": "Combination of changes below make the lookup of marks for a file O(1) as long as\nthe current commit has been previously fully calculated for all sourcemarks at\nleast once.\n\nUse Git tree SHA\n\nA commit SHA is a hash of the content tree and metadata, whereas a tree SHA is\n  a hash of the content tree only.\nSince the sourcemark engine only needs to rerun when the content tree changes,\n  we now use tree SHAs where possible to avoid unnecessary re-work.\nThe impact of this change is that the source mark engine will not unnecessarily\n  rerun when a user amends a commit message, for example.\n\nCache marks by file\n\nFor every each tree SHA, create a cache from file-to-markIDs at the given tree\n  SHA. Prune the set of marks passed to the engine to only those that can possibly\n  affect the current file, which includes all uncommitted files if the current\n  file is edited.\n\nCache point by mark\n\nFor every each tree SHA, maintain a cache from markID-to-point at the given\n  tree SHA."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/970#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/970#pullrequestreview-*********", "body": ""}
{"title": "Adding SQS queue to CDK", "number": 971, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/971", "body": "Added config for SQS queues\nAdded a email_notification queue in both Dev and Prod configs\nAdded a new stack to create SQS queues\nUpdated notification service account to add SQS reader permissions\nRemoved Redis creds from notification service helm values files. I will be removing extra permissions from service account once these changes have been deployed. If I remove them now it will cause service crashloop"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/971#pullrequestreview-*********", "body": ""}
{"title": "Background sourcepoint upload", "number": 972, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972", "body": "Background so it does not affect critical path to showing marks, so takes 0 seconds now.\nAdd debug metrics.\nReduce batch size to 500 which bring batch request latency down\n  to ~800ms. Was up to 3s per batch.\nFixed a reentrant bug where the same points were uploaded multiple\n  times by different calls. JS is single-threaded but not thread-safe.\nOverall latency went from ~16 second to ~5, due to contention probably.\n\nThread safety issue\nSee example debug output below which show how multiple concurrent runs process different batches in parallel. The runs are interleaved. These were previously reading from the same shared state and clobbering the server.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#pullrequestreview-949169035", "body": ""}
{"comment": {"body": "Is this for some kind of linter you're using?  We should probably use eslint for enforcing this kind of thing?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#discussion_r855559915"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#pullrequestreview-949173138", "body": ""}
{"comment": {"body": "IntelliJ complains. I think it's builtin.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#discussion_r855562893"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#pullrequestreview-949182272", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#pullrequestreview-949190518", "body": ""}
{"comment": {"body": "We should probably try to be consistent with our tooling/linting, and rely on eslint?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#discussion_r855575231"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#pullrequestreview-949190585", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/972#pullrequestreview-952610574", "body": ""}
{"title": "Assert that range is valid and report errors", "number": 973, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/973"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/973#pullrequestreview-949193399", "body": ""}
{"comment": {"body": "@davidkwlam heads up seems to happen a lot! I'll dig and see if this is related to PR ingested threads or otherwise...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/973#discussion_r855577304"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/973#pullrequestreview-949194156", "body": ""}
{"comment": {"body": "This is happening due to bugs in handling modify ranges. tracked here:\r\nhttps://www.notion.so/nextchaptersoftware/SourceMark-Open-Issues-3cdea5e5cd37494c94b2899d259408bf#644b0c873190453fbd2e48e3a7cddab8", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/973#discussion_r855577862"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/973#pullrequestreview-949250391", "body": ""}
{"title": "Update CORS for safari extension", "number": 974, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/974", "body": "Similar to Chrome extension, we need to punch a hole into our CORS setup for safari web extension...\nThis sucks...\nWe may want to figure out a better way to do this post April.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/974#pullrequestreview-949246848", "body": ""}
{"title": "Add spinner and fix stuff", "number": 975, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975", "body": "Pr does a few things.\n1. Adds an overlay spinner on images being uploaded.\n2. Associated a unique upload id with uploads so we're no longer dependent on file names.\n3. Fixes a bug with Normalization of nodes when dragging and dropping into an empty editor."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949250042", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949258919", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949259807", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949260023", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949260541", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949265983", "body": ""}
{"comment": {"body": "Need to ensure editor has at least one child to do following logic (in particular for drag and rop), otherwise Editor.end will produce an exception when you call Editor.end.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#discussion_r855631209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949274694", "body": "Might be good to have Matt look over some of the slate stuff? Otherwise lgtm :)"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949301041", "body": ""}
{"comment": {"body": "Should we *not* display the `img` element above this if we're loading?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#discussion_r855658375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949301807", "body": ""}
{"comment": {"body": "Can we put this as a comment?  Anyone else looking at this will be lost I think.  I'm guessing this is forcing there to always be a paragraph at the *end* of the document?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#discussion_r855659049"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949303013", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#pullrequestreview-949306333", "body": ""}
{"comment": {"body": "Yeah, something of that sort. I didn't write this code and I'm just working to get around the exception.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/975#discussion_r855662642"}}
{"title": "add redis access back", "number": 976, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/976"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/976#pullrequestreview-949299719", "body": ""}
{"title": "Launch thread UI and text editor UI in split view", "number": 977, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/977", "body": "This is an attempt to match how we displayed thread views in the OctoberDemo:\n\nWhen you display a thread view, display a text editor for the code associated with that view beside it (in a split view)\nReuse the single webview, so if you launch another thread UI it re-focuses that tab\nDisplay the text editor in a \"preview\" UI so it is also reused."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/977#pullrequestreview-949297234", "body": ""}
{"comment": {"body": "I think this is correct -- we don't want to re-fetch the anchor point any time the UI is updating for any reason?  Just once?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/977#discussion_r855655518"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/977#pullrequestreview-949307926", "body": "Nice!"}
{"title": "Update signatures for passing unsaved files to engine", "number": 978, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/978", "body": "Changes, building on #953:\n- just plumbing, no behaviour change yet\n- unrelated lint fixes"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/978#pullrequestreview-949306919", "body": ""}
{"title": "Safari web extension", "number": 979, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/979", "body": "Quick fix and docs for safari extension\nCurrently not checking in the generated xcodeproj."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/979#pullrequestreview-949323137", "body": ""}
{"title": "Add cors config", "number": 98, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98", "body": "Similar to Jeff's PR but leverages hoplite config for env specific cors. \n@rasharab I tried the following to get reference substitution working, which didn't work:\n```js\napiservice {\n    hostName: localhost\n    port: 1234\n    schemes: [http, https]\n}\ncors {\n    hosts: [\n        ${?apiservice}\n    ]\n}\n```\nAnd several other variants like \n```js\napiservice = {\n ...\n}\ncors {\n    hosts: [\n        ${?apiservice}\n    ]\n}\n```\netc"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#pullrequestreview-859938155", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#pullrequestreview-859938585", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#pullrequestreview-859940271", "body": ""}
{"comment": {"body": "While we're here, it would be nice if we have the actual *host* of our API service definable within a config.\r\nI think there are some defaults floating around which sets it at localhost:8080.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#discussion_r789933928"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#pullrequestreview-859943476", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#pullrequestreview-859945059", "body": ""}
{"comment": {"body": "It's set in the embeddedWebservice dsl. That could easily be loaded from config. I'm also thinking we might want to move away from that bootstrap style to EngineMain because it's more configurable...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#discussion_r789937277"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#pullrequestreview-860084215", "body": ""}
{"comment": {"body": "I've added this now as `DeploymentConfig`", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/98#discussion_r790034101"}}
{"title": "VSCode thread view: open file on correct repo", "number": 980, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/980", "body": "We were using Git.getCurrentRepo, which doesn't work correctly in multi-repo workspaces (it picks a random repo).  So randomly we couldn't open the correct file for a thread in the Unblocked repo.\nThis is the worst offender, but there are still a couple places using Git.getCurrentRepo, I'll look into removing the rest in a follow-on PR."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/980#pullrequestreview-949322864", "body": ""}
{"title": "Create, update, and delete messages in GitHub from unblocked", "number": 981, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981", "body": "This all runs in the API service. Once we have encrypted user secrets and a message queue, we can move this to the SCM service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#pullrequestreview-949478305", "body": ""}
{"comment": {"body": "This is a classically hard problem to solve unfortunately. If GitHub is temporarily unavailable then the delivery never occurs; if we put in the PG transaction to guarantee delivery then transaction retries will deliver multiple duplicates, or worse that the message is sent to GitHub without having been persisted in PG.\r\n\r\nBest approach we came up with in SW was to post after the transaction (like you did here) to a highly available queue, meaning that it\u2019s very unlikely to fail. The queues takes care of delivery to at least one consumer. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#discussion_r855803827"}}
{"comment": {"body": "Does the client have retries built in? If not then we should backoff and retry. \r\nWe recently added a retry utility that could refactored for use here. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#discussion_r855808451"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#pullrequestreview-950429370", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#pullrequestreview-950436335", "body": ""}
{"comment": {"body": "No retries built in, but that would be useful for different HTTP clients", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#discussion_r856471442"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#pullrequestreview-950437322", "body": ""}
{"comment": {"body": "Issue: https://github.com/NextChapterSoftware/unblocked/issues/984", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/981#discussion_r856472050"}}
{"title": "Recalculate unsaved files", "number": 982, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/982", "body": "It works."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/982#pullrequestreview-949432987", "body": ""}
{"title": "Add assets to knowledge", "number": 983, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/983", "body": "Ideally, this will be simplified if I can move everything within the webview.\nRight now, this separation of authorized api calls between extension and webview is annoying as fuck for vscode."}
{"title": "Fix badge clearing", "number": 985, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/985", "body": "For some reason setting the badge to undefined doesn't seem to remove the badge anymore -- manually setting the badge to 0 and an empty string tooltip seems to result in the same desired behaviour"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/985#pullrequestreview-950488014", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/985#pullrequestreview-950489891", "body": ""}
{"title": "Updates Agora submodule URL to use public https endpoint", "number": 986, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/986"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/986#pullrequestreview-950576423", "body": "for consistency with the other submodule?\nnote that if you have this in your ~/.gitconfig then it doesn't matter what type of url you put in there:\n[url \"\"]\n        insteadOf = **************:"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/986#pullrequestreview-950576669", "body": ""}
{"title": "Add threads and team member UX to Hub App", "number": 987, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987", "body": "Summary\nImplements views for threads and teams. The list is not implemented as a real List because I was struggling with styling, meaning if the list is very long it may impact performance. There is a max size to the popover height, and I had to implement a nasty preference hack to coerce the ScrollView to resize itself when its content height changes.\nThere's quite a bit of cleanup in this one too"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950641593", "body": ""}
{"comment": {"body": "Wheeeeee", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#discussion_r856613622"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950641816", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950648423", "body": ""}
{"comment": {"body": "I missed this flag before. It supercedes `swiftPackagePath` and generates sources in a way SPM (and Xcode) expects", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#discussion_r856619029"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950651048", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950651156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950663707", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950921635", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-950921810", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-952296343", "body": ""}
{"comment": {"body": "Eventually these \"ViewModels\" may have to be a combination of ObservableObjects and Bindings, possibly wrapped in a \"Loader\" enum (yay loader pattern)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#discussion_r857919490"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/987#pullrequestreview-952299767", "body": ""}
{"title": "Setup Expiring extension cache", "number": 988, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/988", "body": "Current cache used in web extension may not handle API schema updates gracefully.\nAdded a git commit versioning identifier within the cache to trigger dropping cache."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/988#pullrequestreview-952500712", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/988#pullrequestreview-952501706", "body": ""}
{"comment": {"body": "Curious why you need this -- ENVIRONMENT doesn't.\r\n\r\nMaybe you should use the same pattern when referencing this and it will work without needing this?\r\n\r\n```\r\nconst version = typeof VERSION !== 'undefined' ? VERSION : undefined;\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/988#discussion_r858064278"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/988#pullrequestreview-952503336", "body": ""}
{"title": "Add cookie auth to edge lambdas", "number": 989, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/989", "body": "Added basic test to validate."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/989#pullrequestreview-*********", "body": ""}
{"title": "Change RDS to create a default mind", "number": 99, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/99", "body": "Re-created RDS cluster so it would create a default db named \"maindb\"\nUpdated service account role in EKS\n\nThese changes have been deployed"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/99#pullrequestreview-*********", "body": "This is beauitufl!!!"}
{"title": "Convert unblocked messages to markdown", "number": 990, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990", "body": "This will be used when posting messages to GitHub from unblocked."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Have you seen this fail in real life? Can't imagine how this would ever be null.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#discussion_r857802418"}}
{"comment": {"body": "what is invisible ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#discussion_r857987586"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Might not be the greatest application of the decorator pattern here @rasharab :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#discussion_r857813708"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#pullrequestreview-952150271", "body": ""}
{"comment": {"body": "Ideally the test would convert to markdown then convert back and compare the two but we are missing some things that markdown and GitHub flavoured markdown support, like html blocks and task lists.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#discussion_r857816392"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#pullrequestreview-952432499", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#pullrequestreview-952471719", "body": ""}
{"comment": {"body": "@jeffrey-ng do you know what invisible is? From `Message.proto`:\r\n\r\n```\r\nmessage ListBlock {\r\n  required Style style = 1;\r\n  repeated ListBlockItem items = 2;\r\n\r\n  enum Style {\r\n    UNKNOWN = 0;\r\n    INVISIBLE = 1;\r\n    ORDERED = 2;\r\n    UNORDERED = 3;\r\n  }\r\n\r\n  message ListBlockItem {\r\n    repeated Block blocks = 1;\r\n  }\r\n}\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#discussion_r858041414"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#pullrequestreview-952754156", "body": ""}
{"comment": {"body": "I vaguely remember seeing it but I could be imagining", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/990#discussion_r858271584"}}
{"title": "Update web-extension README", "number": 991, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/991", "body": "Update docs for chrome extension."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/991#pullrequestreview-952503597", "body": ""}
{"title": "Web extension reinitialize auth.", "number": 992, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/992", "body": "Reinitialize auth whenever web extension reinitializes due to service worker restart."}
{"title": "Compress outgoing API service content", "number": 993, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/993", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/993#pullrequestreview-952279383", "body": ""}
{"title": "Use SourceMark engine to determine SourcePoint in discussion view", "number": 996, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/996", "body": "Two TBD issues:\n1) What should we do when the SourcePoint isn't resolvable (requires design input)\n2) The first time you open any thread on a particular repo, loading can be quite slow, if the SM engine needs to do work.  Not sure what we should do in this scenario."}
{"comment": {"body": "@matthewjamesadam agree those are issues, but they do not block this PR change.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/996#issuecomment-1108914589"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/996#pullrequestreview-952301609", "body": ""}
{"comment": {"body": "you can remove the comment above now \u2191 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/996#discussion_r857923195"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/996#pullrequestreview-952304821", "body": ""}
{"comment": {"body": "\ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/996#discussion_r857925402"}}
{"title": "Temp disable chromatic", "number": 998, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/998"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/998#pullrequestreview-952311137", "body": ""}
{"title": "Update vscode treeview color", "number": 999, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/999", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/999#pullrequestreview-952374552", "body": ""}
{"title": "Lorem", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1", "body": "Lorem lorem lorem ipsum"}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#pullrequestreview-1398667347", "body": ""}
{"comment": {"body": "This is a really important part of this file.  I want to add some thoughts that will be recorded as an insight:\r\n\r\n- Interesting\r\n- Is lorem\r\n- Is Ipsum\r\n- Very nice\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#discussion_r1175700605"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#pullrequestreview-1398668069", "body": ""}
{"comment": {"body": "More feedback goes here:\r\n\r\n1. This is interesting but could be better\r\n2. Add markdown?  Could make this look nicer\r\n3. Add links to lorem generator\r\n4. Could use different languages", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#discussion_r1175701128"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#pullrequestreview-1398668400", "body": ""}
{"comment": {"body": "Some additional feedback:\r\n\r\n* We should generate this dynamically so that there is always enough lorem", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#discussion_r1175701390"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#pullrequestreview-1398668771", "body": ""}
{"comment": {"body": "Some additional feedback here", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#discussion_r1175701633"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/1#pullrequestreview-1398669041", "body": "Looks good lorem ipsum\nCould be better"}
{"title": "test", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/2"}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/2#pullrequestreview-1398750850", "body": ""}
{"comment": {"body": "Fix this", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/2#discussion_r1175754600"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/2#pullrequestreview-1398750997", "body": ""}
{"comment": {"body": "Fix this too, it is no good", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/2#discussion_r1175754716"}}
{"title": "New updates", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/3"}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/3#pullrequestreview-1398763078", "body": ""}
{"comment": {"body": "This is silly, change it back", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/3#discussion_r1175762926"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/3#pullrequestreview-1398763229", "body": ""}
{"comment": {"body": "This is also goofy change it back", "htmlUrl": "https://github.com/NextChapterSoftware/SwingPlayground/pull/3#discussion_r1175763023"}}
{"title": "Upgrade Agora frameworks to beta 2", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/agora-macos-frameworks/pull/1"}
{"title": "Update agora sdk to release version", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/agora-macos-frameworks/pull/2"}
{"title": "Cleanup after split from monorepo", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/machine-learning/pull/1", "body": "1: put all the data files from the open source investigation in the ./data folder"}
{"title": "we have a single notebook to do open source investigations", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/machine-learning/pull/2"}
{"title": "spring-cleaning-001", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/machine-learning/pull/3", "body": "cleaning up the sandbox before finishing the GCP integration."}
{"title": "UI Changes", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/WebPrototype/pull/1"}
{"title": "Breaking change", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/WebPrototype/pull/2"}
{"title": "Update to latest build", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/ShikiBundle/pull/1", "body": "Updated with tip of main from shiki"}
{"title": "Updated shiki", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/ShikiBundle/pull/2", "body": "UPdated to newest"}
{"html_url": "https://github.com/NextChapterSoftware/ShikiBundle/pull/2#issuecomment-1242543217", "body": "Does this correspond to the tip of this PR? \nhttps://github.com/matthewjamesadam/shiki/pull/1/files ?"}
{"html_url": "https://github.com/NextChapterSoftware/ShikiBundle/pull/2#issuecomment-1245836983", "body": "Does this correspond to the tip of this PR?\nhttps://github.com/matthewjamesadam/shiki/pull/1/files ?\n\nWas referencing https://github.com/matthewjamesadam/shiki/pull/1/files"}
{"title": "test test", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/2048-matt/pull/1"}
{"title": "test test thing", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/2048-matt/pull/2"}
{"title": "test 3", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/2048-matt/pull/3"}
{"html_url": "https://github.com/NextChapterSoftware/2048-matt/pull/3#issuecomment-1206818840", "body": "_ No description provided. _\n\ntest\n"}
{"title": "First pass", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/ec2-action-builder/pull/1"}
{"title": "Add fallback for spot instances", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/ec2-action-builder/pull/2", "body": "Added fallback strategies for MaxPerformance and BestEffort schemes. \nAdded unattended flag to gihub actions configuration script (it was causing random errors with host names) \nChanged TS config to allow for unknown type exceptions \n\nWith these new changes if we fail to provision a Spot instance due to Insufficient Capacity, we will try lower tiers depending on the selected strategy. The last option is set to On-Demand if all spot instance attempts fail. \nI will not merge this PR until we have ensured these new changes are stable."}
{"title": "Docker support", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/ec2-action-builder/pull/3"}
{"title": "fix to avoid runner name conflicts", "number": 4, "htmlUrl": "https://github.com/NextChapterSoftware/ec2-action-builder/pull/4", "body": "Sample error: A runner exists with the same name ip-192-168-1-42.\nThis error will cause the runner to fail registration with Github. I had added a fix for this to the Docker support branch but it's not ready for merge to main yet. This should address our issue for now."}
{"html_url": "https://github.com/NextChapterSoftware/ec2-action-builder/pull/4#issuecomment-1148985816", "body": "Build status on this can be ignored. We don't have the docker image on main branch yet."}
{"title": "First PR!", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/OurFirstRepo/pull/1"}
{"title": "Add company name to placeholder website", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/NextChapterSoftware.github.io/pull/1", "body": ""}
{"title": "add doepicshit image.", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/NextChapterSoftware.github.io/pull/2"}
