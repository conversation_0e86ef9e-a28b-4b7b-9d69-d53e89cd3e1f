{"title": "removing obsolete code", "number": 1013, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1013", "body": "device_classification_api - is_ip_indication_detected is not in use after the refactor <PERSON><PERSON> did.\nnonrandom_device_name - no need to check that the tuples are valid as the text search 3rd library takes care of it. (removing the reporter error that doesnt send error log because its to deep away from the connection result).\nkafka_init - no need to catch TopicAlreadyExistsError as the if before that prevents it.\nRegression passed:\n{: data-inline-card='' } \nAPI passed (almost):\n{: data-inline-card='' }\nsmoke passed:\n{: data-inline-card='' }"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} please add description to illustrate why it\u2019s ok to remove this code \\(it\u2019s API change\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1013/_/diff#comment-356993336"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1013/_/diff#comment-357116528"}}
{"comment": {"body": "Where is the nonrandom\\_device\\_name changes? There are only changes in the test", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1013/_/diff#comment-357361717"}}
{"comment": {"body": "former PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1013/_/diff#comment-357361906"}}
{"title": "add apple watch series 6 fp", "number": 1014, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1014", "body": ""}
{"comment": {"body": "regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12793](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12793){: data-inline-card='' }  \nsmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12789](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12789){: data-inline-card='' }   \napi [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12786](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12786){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1014/_/diff#comment-356995582"}}
{"title": "MEROSP-2685 add os range typing filter", "number": 1015, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015", "body": "Perform an intersection of os ranges between the os candidates and the typing candidate, to enhance the result resolution is some cases."}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} Were you able to run the regression tests on your change? Were there any changes?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-356998012"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} having a hard time locally. Some tests just hang the system \\(both on this branch and dev\\). Will give the cloud another shot once the latest commit is pushed to ECR.\n\nThe first 3165 tests before it hangs do pass.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-356998898"}}
{"comment": {"body": "Regressions passed locally after deleting one test that hanged the entire process \\(tests\\_collection/regression\\_suite/replay\\_tests/expected\\_results\\_replay/Test\\_01\\_24Ghz\\_identification\\_HW\\_To\\_Random\\_DISCONNECT\\_GLI\\_NET/2.4Ghz\\_identification\\_HW\\_To\\_Random\\_DISCONNECT\\_iPhone\\_12\\_#108\\_GLI\\_NET\\):\n\n![](https://bitbucket.org/repo/o5KReBa/images/3532056978-image.png)\nSmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13003](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13003){: data-inline-card='' } \n\n6 API passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13002](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13002){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357001336"}}
{"comment": {"body": "It passed when I tried to try to run it through the pipelines: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13008](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13008){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357005747"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} Another though: regarding the maximal version. \n\nIt\u2019s impossible to keep track of the maximal minor version. For example iPhone 6/iPhone 7 devices are not upgraded to iOS 16 so their maximal major is 15, but there are always more minor iOS 15 version coming out for important fixes \\(iOS 15.7, iOS 15.8, iOS 15.9, etc\\).\n\nI think for the filtering here, we should consider only the major versions, because it practice this the version when device manufacturer decide to not upgrade \\(for example example a certain device not going to iOS 16, a certain device not going to Android 13, etc\\).", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357061985"}}
{"comment": {"body": "That will actually simplify everything quite a bit. I\u2019ll start working on that and probably solve some of the other comments along the way", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357064059"}}
{"comment": {"body": "Yes. Let\u2019s do that.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357065291"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} From the devices DB perspective, can we just add \u201c15,255,255\u201d for all the devices that are not upgraded to iOS 16?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357065438"}}
{"comment": {"body": "Yes, I\u2019ll do that", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357065658"}}
{"comment": {"body": "Regressions are now failing. I think these are only improvements. I\u2019ll make a parallel automation branch.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357105818"}}
{"comment": {"body": "Can you also added the maximal limit for the iPod touch 7th and iPhone SE \\(1st gen\\) as well?\n\nFor reference: \n\niOS16 supported devices [https://support.apple.com/en-il/guide/iphone/iphe3fa5df43/ios](https://support.apple.com/en-il/guide/iphone/iphe3fa5df43/ios){: data-inline-card='' } \n\niOS15 supported devices [https://www.lifewire.com/which-devices-are-compatible-with-ios-15-5193428](https://www.lifewire.com/which-devices-are-compatible-with-ios-15-5193428){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357138495"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357138543"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357141379"}}
{"comment": {"body": "Regression passed with same branch name [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13376](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13376){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357141730"}}
{"comment": {"body": "What is that security/snyk error? Clicking on it leads me to this page: \n\n![](https://bitbucket.org/repo/o5KReBa/images/929571274-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357348162"}}
{"comment": {"body": "after you synced from dev it disappeared. it\u2019s about 3rd party lib licenses and vulnerabilities. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357358755"}}
{"comment": {"body": "next time it is preferred to return the `intersection_of_candidates` because you don\u2019t really know if python passes it by ref or by value", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1015/_/diff#comment-357564098"}}
{"title": "added wispr and rpvr values to iOS 16.1 and 16.1.1", "number": 1016, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1016", "body": "added wispr and rpvr values to iOS 16.1 and 16.1.1\nMEROSP-2687"}
{"comment": {"body": "regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12941](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12941){: data-inline-card='' }   \nsmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12937](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12937){: data-inline-card='' }   \napi [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12936](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/12936){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1016/_/diff#comment-356999388"}}
{"comment": {"body": "MEROSP-2687", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1016/_/diff#comment-357045733"}}
{"title": "MEROSP-789", "number": 1017, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1017", "body": "Modify eros-chart umbrella chart, so that client-configuraiton-api version is not overridden"}
{"title": "added iPhone XR l2 fp to glinet", "number": 1018, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018", "body": "added iPhone XR l2 fp to glinet"}
{"comment": {"body": "is this file affects anything in the code? or just the `l2_model.csv` i.e. default?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357069311"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} @{6265307b185ac200692f9bd9} Is the l2\\_model.csv is even used? Can we removed it?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357099619"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} No as far as I know  \n@{5dbeb866c424110de52552cc}  @{5b41d9de10d57114135eca66} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357107160"}}
{"comment": {"body": "No as we are not using or no as we cannot remove? :upside_down: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357108140"}}
{"comment": {"body": "No, we are not using this file", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357133496"}}
{"comment": {"body": "If the platform type we get doesn\u2019t exist in the multiplatform CSV, we resort to this table.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357243735"}}
{"comment": {"body": "reg [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13860](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13860){: data-inline-card='' }   \napi [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13855](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13855){: data-inline-card='' }   \nsmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13854](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13854){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1018/_/diff#comment-357319945"}}
{"title": "classifier's LNDI logic unit tests", "number": 1019, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1019", "body": ""}
{"comment": {"body": "@{5f82bf320756940075db755e} moto pkg was removed from requirements.txt, it already exists in test-requirements.txt ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1019/_/diff#comment-357545428"}}
{"title": "new version", "number": 102, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/102", "body": ""}
{"title": "MEROSP-2714 parse DHCPv4 Discover packets", "number": 1020, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1020", "body": "This PR adds the support in parsing DHCPv4 Discover packets as well, in addition to DHCPv4 Request & DHCPv4 ACK packets.\nIn our work with Cujo we saw some sessions that the only DHCPv4 data we got from them is DHCP Discover messages. Those messages are quite identical in structure to the DHCP Request messages, which were already parsing anyway. We can easily get the data were looking for (especially the DHCP fp) to help us later on in the typing procedure, if we didnt get the DHCP request message.\nImportant: Theres a PR in eros-automation as well to match those changes in the regression."}
{"comment": {"body": "Regression pass: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13547](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/13547)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1020/_/diff#comment-357235705"}}
{"comment": {"body": "@{************************} is not in front a computer but he said we could merge :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1020/_/diff#comment-357240838"}}
{"title": "MEROSP-2642 radiotap to 802.11", "number": 1021, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021", "body": "separating 802.11 parsing from radio tap\nregression:\n{: data-inline-card='' } \napi:\n{: data-inline-card='' } \nsmoke:\n{: data-inline-card='' }"}
{"comment": {"body": "Do you want to have a parallel separation here to a `Ieee80211` class the same way you added for Radiotap?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021/_/diff#comment-357275534"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021/_/diff#comment-357279137"}}
{"comment": {"body": "let\u2019s make real UT with asserts.. positive and negative tests..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021/_/diff#comment-357320951"}}
{"comment": {"body": "Why not testing all the packets you have added ?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021/_/diff#comment-357321093"}}
{"comment": {"body": "in the next PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021/_/diff#comment-357326198"}}
{"comment": {"body": "in the next PR. this PR is big enough", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1021/_/diff#comment-357326276"}}
{"title": "fix datetime serialization error", "number": 1022, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1022", "body": ""}
{"title": "removed typing hack from TypeClassifier", "number": 1023, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023", "body": "{: data-inline-card='' }\n\nchanged the hack for now\nno need to disable the release date feature, we need to reimplement it as filter\nalso, Ariel added the DHCP Discover and it solves some of the cases"}
{"comment": {"body": "what about the performance?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357285222"}}
{"comment": {"body": "We can address the timing performance later. We should give a correct result first.  \nDo you have a suggestion how should we fix this?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357293905"}}
{"comment": {"body": "what is the problem this PR wants to solve?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357296740"}}
{"comment": {"body": "The because of this hack, \u2018Android\u2019 is returned when it shouldn\u2019t be returned\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357298731"}}
{"comment": {"body": "what should be returned?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357299290"}}
{"comment": {"body": "In this specific case, \u2018Generic device\u2019 was supposed to be the returned final result.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357299623"}}
{"comment": {"body": "so use:  \n\n```\nif len(res.score_row_indices) > MAX_NUM_OF_ROW_INDICES:\n    res.data_frame = pd.DataFrame( \n        {\n            \"specific_os\": [],\n            \"general_os\": [],\n            \"vendor\": [],\n        },\n    )\nelse:\n    ...\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357301459"}}
{"comment": {"body": "It could be that the correct result should be \u2018Android\u2019 and this will return 'Generic device'", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357302281"}}
{"comment": {"body": "so decline this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357303383"}}
{"comment": {"body": "I think that If all of the 20k\\+ options have Android OS then the result should remain as-is. If not it should be a generic device.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357303718"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{5a4500fe0cacf235de82a9d4} @{5dbeb866c424110de52552cc} let\u2019s remove the \u2018release date\u2019 feature. This should fix it without harming latency and performance. And fix this correctly once and for all.. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357313329"}}
{"comment": {"body": "this PR is not about release date", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357325644"}}
{"comment": {"body": "I second that, @{5f82bf320756940075db755e} , this will bring back other latency issues that were there prior to the detection of the release date issue. We need to discuss a proper solution and compromise on the result quality until a strategy is decided.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357332641"}}
{"comment": {"body": "Passed local regression\n\n![](https://bitbucket.org/repo/o5KReBa/images/2673835167-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357628064"}}
{"comment": {"body": "Passed regression[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14558](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14558){: data-inline-card='' }   \nPassed smoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14557](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14557){: data-inline-card='' }   \nPassed 6/16 API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14556](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/14556){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1023/_/diff#comment-357771451"}}
{"title": "removing obsolete global vars", "number": 1024, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1024", "body": "smoke: {: data-inline-card='' }\nAPI: {: data-inline-card='' }\nregression: {: data-inline-card='' }"}
{"title": "adding return value typing hints", "number": 1025, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1025", "body": "revive some unit tests in pipeline\nAPI: {: data-inline-card='' }\nregression:{: data-inline-card='' } \nsmoke:{: data-inline-card='' }"}
{"title": "add metrics to model loader", "number": 1026, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1026", "body": ""}
{"comment": {"body": "@{6335b848a84c7f79c387915c} @{637f5c5e3e79f12e572115d7} @{5fd5d5149edf2800759cc96d} the labels used in the metrics might exceed the usage and cause delays on the dashboards, can you please elaborate on the metrics?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1026/_/diff#comment-358467439"}}
{"comment": {"body": "@{5f82bf320756940075db755e}   \nupgrading models is divided to 2 main processes:  \n1\\.  `_download_eros_models` - this process is monitored by the following metrics:   \n`download_eros_models_success_count` \\+ `download_eros_models_success_latency`  >> for success  \n`download_eros_models_failure_count` \\+ `download_eros_models_failure_latency` >> for failure\n\n2\\. `_upgrade_eros_models` - this process is monitored by the following metrics:   \n`upgrade_eros_models_success_count` \\+ `upgrade_eros_models_success_latency`  >> for success  \n`upgrade_eros_models_failure_count` \\+ `upgrade_eros_models_failure_latency` >> for failure\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1026/_/diff#comment-358469355"}}
{"title": "unit tests for ieee802.11 parse", "number": 1027, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1027", "body": "added unit tests based on 802.11 packets from cujo\nstarting support cujo ingestion events (without radio tap headers).\nregression: {: data-inline-card='' } \napi:\nsmoke: {: data-inline-card='' }"}
{"comment": {"body": "can we add also some negative tests to validate the parser error handling robust?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1027/_/diff#comment-358013634"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1027/_/diff#comment-358013897"}}
{"comment": {"body": "done, will add more in the next PRs Be\u201dH", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1027/_/diff#comment-358457009"}}
{"title": "typing hints for return value", "number": 1028, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1028", "body": "no imports in the middle of the file\nAPI:{: data-inline-card='' } \nSmoke:{: data-inline-card='' } \nRegression:{: data-inline-card='' }"}
{"title": "no imports in the middle of the file", "number": 1029, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1029", "body": "imports in the middle of the file can lead to circular imports.\nsmoke: {: data-inline-card='' } \nAPI: {: data-inline-card='' } \nregression: {: data-inline-card='' }"}
{"title": "Feature/partition level processing", "number": 103, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/103", "body": "single worker with batch polling\ncannot be parallel since it is processing every message  multiple times"}
{"title": "Disable ReleaseDate Feature by Default", "number": 1030, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1030", "body": "The high latency peaks are caused by large amount of rows being queried for the release date feature. Currently the feature doesnt have any efficacy significance. Until an adjustment is done to the typing flow, we disable this feature.\n\nAPI:{: data-inline-card='' }\nSmoke: {: data-inline-card='' }\nlocal regression passed"}
{"comment": {"body": "Why we need several places in code to disable the feature?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1030/_/diff#comment-358454703"}}
{"comment": {"body": "Changing both in code and in configuration.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1030/_/diff#comment-358454753"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Why isn\u2019t the code uses the configuration by default?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1030/_/diff#comment-358464414"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} The **system** uses the configuration by default. The code should always have a default value for when for some reason the configuration doesn\u2019t exist. The expected behavior is that the configuration will determine the de-facto behavior in all run scenarios. The default value in the code is set just because this is not some ad hoc operational change.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1030/_/diff#comment-358464557"}}
{"title": "added logger to file_validators and no import in the middle of the file", "number": 1031, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1031", "body": ""}
{"title": "Retire old ingestion event files from test utils", "number": 1032, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1032", "body": "Convert old bin/gz files to json files that hold an EventMsg\nChange the EventMsg class to be json serializable -> make vault id a computed attribute derived fron agent serial, in order to prevent the need to update both and to cause a logical gap. It is debatable whether the vault id belongs in the EventMsg class since it belongs to the internal logic of classifier.\nAdd functions to convert EventMsg to/from json file\nAdd the ability to switch the translation in the manual test tool when using a .bin file\nAdd unit test to EventMsg serialization\nUnify the API in unit tests to use only json files in the new cujo ml2 schema, in order to depracate all the functions that were used to hold the data about ingestion events in the old schema.\nAdd lfs tracking of json files in eros_pipelines/resources\n\n"}
{"comment": {"body": "Can we put all json files in LFS? I think it should be the same as with the bin files, their content isn\u2019t trackable anyway.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1032/_/diff#comment-358487512"}}
{"comment": {"body": "\\(The ones in eros\\_pipelines/tests/resources are not\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1032/_/diff#comment-358487677"}}
{"comment": {"body": "sure, do we want to add the .bin files in this location as well?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1032/_/diff#comment-358489244"}}
{"comment": {"body": "Smoke: passed[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15754](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15754){: data-inline-card='' }   \nAPI: broken \\(known issue - 8 tests fail\\) [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15753](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15753){: data-inline-card='' }   \nRegression: passed [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15755](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/15755){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1032/_/diff#comment-358490454"}}
{"comment": {"body": "Oh they weren\u2019t LFS before? Then yeah they should be\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1032/_/diff#comment-358555486"}}
{"title": "typing hints for return value", "number": 1033, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1033", "body": ""}
{"title": "test classifier with local models", "number": 1034, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1034", "body": "Add ability to test classifier with models located in cloned local models repo\ndefine the following env vars: \n1. TEST_WITH_LOCAL_MODELS set to True\n2. LOCAL_MODELS_PREFIX_PATH set to your local models dir prefix, for example: /Users/<USER>/repos/eros-models/src/v1"}
{"comment": {"body": "remove `, None`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1034/_/diff#comment-358618923"}}
{"comment": {"body": "remove `, \u201d/\u201d` everywhere", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1034/_/diff#comment-358619218"}}
{"title": "Fix lfs", "number": 1035, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1035", "body": "trying to fix errors of git lfs\nmore\nmore gitattributes\n\n"}
{"title": "return models tag in feature_log and result_log", "number": 1036, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1036", "body": "models tag is saved under MODEL_TAG env var\nthis env var is first being set on classifiers upload through terragrunt value (primary models version, for example 1.0.0, which represents classifier's default models version)"}
{"title": "typing hints for return value", "number": 1037, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1037", "body": ""}
{"title": "MEROSP-2706 fetch models from eros-models repo", "number": 1038, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038", "body": ""}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} @{637f5c5e3e79f12e572115d7}   \nWe also need this models-fetch ability in the MakeFile scripts that build classifier images/dockers", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359043531"}}
{"comment": {"body": "done\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359186253"}}
{"comment": {"body": "let\u2019s combine to makefile command call, not separate step in bitbucket as this step is part of building the classifier container\n\n\u200c\n\nmake `docker_build`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359264540"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} @{6335b848a84c7f79c387915c} how do we make sure working locally classifier will not end up without models? local classifier is working without the docker inside venv does git submodules solves this as well? `git pull --recurse-submodules`\n\n@{6335b848a84c7f79c387915c} @{637f5c5e3e79f12e572115d7} please refer the design", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359265483"}}
{"comment": {"body": "@{5f82bf320756940075db755e} we decided that submodule is not the best approach for us:  \nAs you can see in @{6335b848a84c7f79c387915c} HLD:  \n  \n  \n  \n\n![](https://bitbucket.org/repo/o5KReBa/images/4141588879-Screenshot%202023-01-11%20at%2013.29.57.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359266963"}}
{"comment": {"body": "Thanks! @{621df03094f7e20069fd6ab2} FYI, please comment", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359288806"}}
{"comment": {"body": "@{6335b848a84c7f79c387915c} @{637f5c5e3e79f12e572115d7} please make sure to share with all RnD the design and illustrate the readme of the working process with classifier, as it changes the todays work and we need to make sure everyone is aware and ready to adopt!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359289156"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-359290030"}}
{"comment": {"body": "I think that it\u2019s too complicated.  \nCan we keep it simple?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1038/_/diff#comment-360242743"}}
{"title": "typing hint for return value", "number": 1039, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1039", "body": ""}
{"title": "support async io to macthmaker", "number": 104, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/104", "body": ""}
{"title": "remove redundant lines", "number": 1040, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1040", "body": "and add unit tests to cap processors\nand all timestamps to UTC by using datetime.now(timezone.utc)\nAPI: {: data-inline-card='' } \nsmoke: \nregression: {: data-inline-card='' }"}
{"title": "remove obsolete files", "number": 1041, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1041", "body": ""}
{"title": "delete models dir from classifier", "number": 1042, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1042", "body": ""}
{"title": "typing hints for return values", "number": 1043, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1043", "body": ""}
{"title": "[Snyk] Fix for 3 vulnerabilities", "number": 1044, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1044", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nrequirements.txt\n\n\n\nWarning\n```\ntextsearch 0.0.24 requires anyascii, which is not installed.\ndataclasses-avroschema 0.26.1 requires dacite, which is not installed.\ndataclasses-avroschema 0.26.1 requires faker, which is not installed.\ndataclasses-avroschema 0.26.1 requires fastavro, which is not installed.\ndataclasses-avroschema 0.26.1 requires inflect, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n | Insufficient Verification of Data Authenticity  SNYK-PYTHON-CERTIFI-3164749 |  certifi: 2021.10.8 - 2022.12.7  |  No  | No Known Exploit \n | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3113904 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \n | Regular Expression Denial of Service (ReDoS)  SNYK-PYTHON-SETUPTOOLS-3180412 |  setuptools: 39.0.1 - 65.5.1  |  No  | No Known Exploit \nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "MEROSP-2670 hostname typing", "number": 1045, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045", "body": "Implements a new low-score feature of typing from information in hostname strings."}
{"comment": {"body": "Regression against same branch name: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17136](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17136){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17134](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17134){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17132](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17132){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045/_/diff#comment-359267662"}}
{"comment": {"body": "Does this set bounded by any length?\n\nWhat size is it expected to be for each device record?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045/_/diff#comment-360258522"}}
{"comment": {"body": "The number of hostname data sources we have. So 3 now max.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045/_/diff#comment-360258608"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} Ok, please update the schema in this [document](https://levltech.atlassian.net/wiki/spaces/~************************/pages/8278933564/DB+Schema+-+Device+Class) with your change :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045/_/diff#comment-360347770"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} it\u2019s not that the result actually changed. Under dev it is also GLOBALLY\\_UNIQUE, but for some reason that I don\u2019t understand the assertion failure is excepted and ignored.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045/_/diff#comment-360395841"}}
{"comment": {"body": "@{************************} done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1045/_/diff#comment-360406761"}}
{"title": "typing hints for return value", "number": 1046, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1046", "body": ""}
{"title": "MEROSP-2641 - Filter ongoing events by types", "number": 1047, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1047", "body": "To reduce classifier load and results amount - we nee to filter and not handle non user agent/MDNS ongoing events\nCujo is raising now ongoing events with multiple data types that LEVL does not require, this will increase classifier load and results load  cost.\nWe want at the first stage filter out some of the ongoing events in Classifier to prevent any additional load. The ongoing events that should be handled and not dropped are:\n\nMDNS\nHTTP_UA\nQUIC_UA\n\nAs we know ongoing events may contain multiple messages, and we want to filter these messages to relevant types only as mentioned above, in the event of the entire list of messages is filtered out well drop the event all at once, in other situations well process only relevant messages(partially)."}
{"title": "typing hints for return values", "number": 1048, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1048", "body": ""}
{"title": "fix naming", "number": 1049, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1049", "body": ""}
{"comment": {"body": "it\u2019s api break change", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1049/_/diff#comment-359942229"}}
{"comment": {"body": "no it\u2019s not.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1049/_/diff#comment-360634436"}}
{"title": "Feature/kafka python consumer", "number": 105, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/105", "body": "use kafka-python as kafka consumer\nemit events directly to the pipeline (abstract as Stream())\nadjust the batch size and polling interval\n\n"}
{"title": "Make bssid be parsed as a MAC address", "number": 1050, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1050", "body": "BSSID was sent in the wrong format from the LEVL CPE agent, save_debug processed it correctly but when met by CUJO events, it failed. This updates the parsing done there to support the correct format. A parallel PR in data-collector will update the format from the LEVL agent.\nIP was parsed in a way that could create unnecessary errors, this was changed.\nTODO: Collect errors in debug_data such that it wouldnt fail the pipeline entirely.\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17614](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17614){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17613](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17613){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17612](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/17612){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1050/_/diff#comment-360208718"}}
{"title": "MEROSP-2777 Save debug data exception handling", "number": 1051, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1051", "body": "MEROSP-2775: revert test-requirements with lock versions and fix the flake error.\n\n{: data-inline-card='' }\n\nAvoid crashes\nHandle both BSSID formats, from translated LEVL event and from the correct CUJO event\n\n\n\n"}
{"comment": {"body": "let\u2019s document it somewhere, so Dror, Artur will know where it comes from\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1051/_/diff#comment-360249319"}}
{"comment": {"body": "very good, looks much better that my initial implementation :wink:\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1051/_/diff#comment-360249350"}}
{"comment": {"body": "Any idea on where to document? This is internal and probably temporary but the Logs schema pages are \u201cpublic\u201d.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1051/_/diff#comment-360249762"}}
{"comment": {"body": "Classifier regression passed locally", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1051/_/diff#comment-360249805"}}
{"comment": {"body": "API: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18080](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18080){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18079](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18079){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18081](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18081){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1051/_/diff#comment-360250493"}}
{"title": "minor alert flake8", "number": 1052, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1052", "body": ""}
{"title": "Return function result in exception handler", "number": 1053, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1053", "body": "Exception handler didnt return result."}
{"title": "Feature/MEROSP-2741 debug data extraction tool", "number": 1054, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1054", "body": "TLDR: A tool to automate the downloading of files from S3 based on an Athena query.\nAdd a script tool to find & download files in S3\nThe tool is based on integrating 2 components of our backend system and an assumption about the way our files are stored in S3.\nThe tool will receive an SQL query that will be executed by an Athena client. The records returned from the query will hold the partial timestamp(no micro seconds), event id & type that relates to a session that matched our query. This data will allow us to search for a specific directory in S3, using the assumption of a time prefix in the directory name, and the fact that it contains the event id & type. Once a match is found in S3, it is downloaded with all of its content.\n"}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18933](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18933){: data-inline-card='' }   \nAPI passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18932](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18932){: data-inline-card='' }   \nSmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18931](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18931){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1054/_/diff#comment-360510146"}}
{"title": "Bugfix/MEROSP-2777 fix exception decorator", "number": 1055, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1055", "body": "Another fix to the exception case.\n"}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18442](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18442){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18441](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18441){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18440](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/18440){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1055/_/diff#comment-360349570"}}
{"title": "MEROSP-2642 support xb7 platform type", "number": 1056, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1056", "body": "Add platform type support for Comcasts XB7\n\nregression: {: data-inline-card='' } \nsmoke: {: data-inline-card='' } \napi: {: data-inline-card='' }"}
{"title": "MEROSP-2791 enable recusive session loading in test utils", "number": 1057, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1057", "body": "Currently, test_utils only loads files that are immediately inside the specified directory.\nFor more rapid debugging of regressions, it would help to have this be a recursive file loading, since in the automation repository sessions are held in separate sub-directories of each test.\nIf the file loading will be recursive, then all the sessions will be loaded in order and regressions debugging will be easier and more swift."}
{"comment": {"body": "the way it is now allows us to load the regression files by the chronological order in their batch. this PR is not necessary. you can use this script to convert the regression files from different subdirs to one dir:\n\n```\nimport os\nimport os.path as osp\nimport shutil\n\ndir_ = \"/Users/<USER>/work/eros-automation/tests_collection/regression_suite/replay_tests/expected_results_replay/events_from_cujo_14_15_12_2022/SonyExperia10_Hop_Bands\"\n\nnew_dir = \"/Users/<USER>/temp/sony/\"\n\nif osp.exists(new_dir):\n    shutil.rmtree(new_dir)\nos.mkdir(new_dir)\n\nfor root, dirs, files in os.walk(dir_):\n    for file_ in files:\n        if file_ == \"ingestion_event.bin\":\n            shutil.copy(osp.join(root, file_), osp.join(new_dir, osp.basename(root) + \".bin\"))\n```\n\nmaybe you need to change it a bit to get the new json files", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1057/_/diff#comment-360457301"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} I think it\u2019s better to have an in-tree solution that requires as few steps as possible.\n\nThe chronological order functionality is kept here completely as far as I understand. Can you explain how this hurts it?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1057/_/diff#comment-360457980"}}
{"comment": {"body": "you can\u2019t really run it on regression as some of the dirs contain both json and bin files, not all of them are sessions.  \nyou need some kind of prep here. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1057/_/diff#comment-360458768"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} @{62e7c839e50f2f2a395430c2}  Didn\u2019t dive deep enough to contribute to your discussion, my 2 cents are that possibly not every user would like to get the files recursively so this should be separated and both \u201cmodes\u201d should be supported.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1057/_/diff#comment-360463612"}}
{"title": "MEROSP-2132 mono repo models", "number": 1058, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1058", "body": "sync models with dev\nremove get_models_path + update models dir hierarchy\nadd pipelines\nfix image tag script\nfix send SNS step\nupdate classifier's eros-models deployments\nadd models meta version as env var to classifier\nset MODELS_TAG env var value on classifier first load by version.yaml file (based on classifiers model meta version)\n\n"}
{"title": "fixing confusing trailing whitespaces in csv", "number": 1059, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1059", "body": "currently ml_vocabulary.csv uses whitespaces in its words. Each word is in one line. Added quotation marks so IDEs and other linters wont trim the whitespaces and it will be more humman-readable.\nAPI: {: data-inline-card='' }\nsmoke is borken, known issue\nregression: {: data-inline-card='' }"}
{"comment": {"body": "@{6265307b185ac200692f9bd9}   \nNext time please add me to the PR when you are changing the format of algorithm related artifacts  \nI have scripts that build these files, so if I don\u2019t know about it it will change the intended behavior \\(or break\\) when I update the model", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1059/_/diff#comment-360683518"}}
{"comment": {"body": "yes, no problem. @{5d74d49897d8980d8eacd7f8} one should implement unit tests on a new algorithm and let us change the code when we see a problem.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1059/_/diff#comment-360709107"}}
{"title": "Revert \"Merged in async_db (pull request #101)\"", "number": 106, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/106", "body": "This reverts commit fafb820dea4d73ff301f32d1dd5cd2a59d0a0587."}
{"title": "typing hints for return values", "number": 1060, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1060", "body": ""}
{"comment": {"body": "What different here from the PR you have declined?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1060/_/diff#comment-360661919"}}
{"comment": {"body": "no changes\\_requested here", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1060/_/diff#comment-360662289"}}
{"comment": {"body": "It\u2019s not a way to unblock request changes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1060/_/diff#comment-360663020"}}
{"comment": {"body": "I\u2019m tired of commenting \u201ctyping hints everywhere\u201d on every PR. this PR will make my life as a developer easier", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1060/_/diff#comment-360663581"}}
{"title": "Fix logging format, add example and ability to skip previously downloaded dirs", "number": 1061, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1061", "body": ""}
{"title": "ssm parmameter fetched as string and not list", "number": 1062, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1062", "body": "ssm parmameter fetched as string from ssm and not list we need to load it to json first."}
{"title": "Increase files max size detection in directory", "number": 1063, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1063", "body": "To ensure the complete content of the directory is downloaded and not a subset of its files"}
{"title": "Make ACTIVATE_SCHEMA_TRANSLATION default False again", "number": 1064, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1064", "body": "{: data-inline-card='' } merge changed the default value.\n:bangbang: We need to check if any other default values were changed by this delayed merge.\nsmoke: {: data-inline-card='' }\nregression: {: data-inline-card='' } \nAPI: {: data-inline-card='' }"}
{"title": "fixing classifier strings", "number": 1065, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1065", "body": "declaring strings in one place and using them multiple times to avoid bugs of misspeling\nsmoke: {: data-inline-card='' }\nAPI: {: data-inline-card='' }\nregression: {: data-inline-card='' }"}
{"title": "LDI-207 consumer category", "number": 1066, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1066", "body": "changed consumer category and fixed tests\n"}
{"comment": {"body": "smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20633?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20633?launchesParams=page.page%3D1){: data-inline-card='' } \n\napi: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20634?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20634?launchesParams=page.page%3D1){: data-inline-card='' } \n\nregression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20638?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20638?launchesParams=page.page%3D1){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1066/_/diff#comment-362475080"}}
{"title": "no import in the middle of the file", "number": 1067, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1067", "body": "added script that searches for imports in the middle of the file and alerts"}
{"title": "Bugfix/LDI-169 L2 is always 5G in the Data Catalog", "number": 1068, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068", "body": "This PR fixes the problem that was seen in the data catalog for the XB7 recordings that were all subjected to the 5g band, even for 2.4G connections.\nThe problem was that in the XB7 integration, the network_type parameter was removed because we couldnt rely on the RT data for XB7 connections.\nThe network_type is now assigned via the ConnectionResult and not the RT data, as can be seen in export_adapter.py file.\n\nImportant Note: This PR fixes the Data Catalog problem. We still have a problem in the pcap_extraction.py file, which always assumes the PlatformType and NetworkType to have default values. This problem was alerted to @{5fd5d5149edf2800759cc96d} and a relevant issue was opened (LDI-222). We need to merge this PR anyway so we wont block the recordings on the XB7."}
{"comment": {"body": "Writing here before the questions that will be:\n\nThis PR fixes the Data Catalog problem \\(Refer to the change in **export\\_adapter.py**\\). We still have a problem in the **pcap\\_extraction.py** file, which always assumes the PlatformType and NetworkType to have **default** values. This problem was alerted to @{5fd5d5149edf2800759cc96d} and a relevant issue was opened \\([LDI-222](https://levltech.atlassian.net/browse/LDI-222)\\). We need to merge this PR anyway so we won\u2019t block the recordings on the XB7.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484481"}}
{"comment": {"body": "@{************************} the following `build_rt_features_from_session` is now working and remained to fix raw pcap datacatalog parser?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484701"}}
{"comment": {"body": "@{************************} please don\u2019t open story, improvement or bug. in this case I will change to Bug", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484804"}}
{"comment": {"body": "@{5f82bf320756940075db755e} The pcap datacatalog parser is working fine. The problem is the **read\\_pcap\\_file\\(\\)** method that can\u2019t calculate or get the PlatformType or NetworkType by the current design it has.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484867"}}
{"comment": {"body": "@{************************} why it\u2019s broken? we suppose to continue support radiotap header as in this case of pcap parser", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484936"}}
{"comment": {"body": ":pray: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484948"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Thats' right, and that\u2019s why I opened the bug for Itai. It\u2019s broken because the current design of this method can\u2019t calculate or get the PlatformType or NetworkType because it gets only the pcap and nothing else. I notified Itai about it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362484968"}}
{"comment": {"body": "@{************************} The convention in datacatalog pcap parser is platform type extracted from the file path:\n\n`local_files/local_test/ENG_IPQ6018/*.pcap`\n\nDoes this also not working?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362485175"}}
{"comment": {"body": "@{5f82bf320756940075db755e} It looks like you said: Don\u2019t open an issue of any type. I just want to clarify that everyone can open an improvement or a bug.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362563027"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} correct my phrasing was wrong :wink: please open improvements and bugs\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362564677"}}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20924](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20924){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20926](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20926){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20925](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/20925){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1068/_/diff#comment-362586976"}}
{"title": "fix models is_modified logs", "number": 1069, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1069", "body": ""}
{"title": "Changed RandMac inner feature name", "number": 107, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/107", "body": "Changed the feature name of rand address matching to be contained in the returned string value from the query builder, so it would be counted in the decision log properly"}
{"title": "profiler to false by default", "number": 1070, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1070", "body": ""}
{"comment": {"body": "@{637f5c5e3e79f12e572115d7} @{606d973d3e6ea000685ed65f} we need to include this in the tag for GDE", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1070/_/diff#comment-362636297"}}
{"comment": {"body": "@{5f82bf320756940075db755e} as we don't want to block the current deployment we can set to false in the configuration.  we can create new tag later on with this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1070/_/diff#comment-362637803"}}
{"title": "LDI-246 Removed spamming IP log from the packet_parser", "number": 1071, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1071", "body": "At the moment, we have a very spamming log that sometimes being printed hundreds of times in one incoming session. That log (from packet_parer_main.py) alerts us if we got a packet that has no IP field. We get a lot of spamming from that log because there are many packets (such as all of our L2 packets) that dont contain any IP data, and therefore we get many time-consuming prints:\n\n\nNote that we still have enough indications if we dont have an IP in the entire session, but we dont need to print this log for each and every packet we get. This log is really not needed."}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21212](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21212){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21211](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21211){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21210](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21210){: data-inline-card='' }  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1071/_/diff#comment-362711637"}}
{"title": "LDI-31 delay prior logic", "number": 1072, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1072", "body": "It is a product demand to delay prior logic until we give up on obtaining more accurate typing information. Product  have defined that the giving up should be done after 24 hours for now., which is what that is the number is now set to in the model.\nIn addition, an attribute of first_session_ts was added here to the Device object, in order to compare the current session ts to the first session ts. The existing created_on attribute cannot serve this purpose since it registered the classifiers machines clock when the device was first created. When replaying old sessions, this is very different from the time in which the session was recorded. For reproducibility of the results and for creating meaningful regression tests, the actual recorded timestamp in the session must be stored."}
{"title": "more typing hints", "number": 1073, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1073", "body": "regression: {: data-inline-card='' } \nsmoke: {: data-inline-card='' } \nAPI: {: data-inline-card='' }"}
{"title": "MEROSP-2801", "number": 1074, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1074", "body": "unit tests for ieee802.11 parser\nregression: {: data-inline-card='' }\napi: \nsmoke: {: data-inline-card='' }"}
{"comment": {"body": "Please don\u2019t work with MEROSP project, only LDI. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1074/_/diff#comment-363119047"}}
{"comment": {"body": "ok, next PR I will", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1074/_/diff#comment-363119348"}}
{"comment": {"body": "do we get 2 here? as this was hardcoded", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1074/_/diff#comment-363128224"}}
{"comment": {"body": "yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1074/_/diff#comment-363135308"}}
{"title": "force upgrade models when new models version is loaded", "number": 1075, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1075", "body": ""}
{"comment": {"body": "look also for `def load_model` of features like `AppleMdnsRpvr`, `DHCPDB` etc.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1075/_/diff#comment-363138809"}}
{"comment": {"body": "why not create and here load\\_model\\(\\) as others?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1075/_/diff#comment-364017807"}}
{"title": "LDI-451 - move packaging dep", "number": 1076, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1076", "body": "LDI-451- move packaging-21.3 dependency from requirements.txt to setup\n\nLegacyVersion seems to have been removed in packaging in versions greater than 21.3.  \nthe issue is that the version is locked only internally in the classifier (requirmenets.txt) and not in the exported dependencies (setup.py). so we're probably getting a newer version of packaging which conflicts with eros-api when imported as a dependency.  \nneed to move packaging~=21.3 from requirmenets.txt to setup.py."}
{"title": "more unit tests for 802.11 parser", "number": 1077, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1077", "body": ""}
{"title": "LDI-80 Bring back the activity filtering", "number": 1078, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078", "body": "As part of our identification process, we use a few ways to filter our devices from our potential list of candidates to match with: \n\nWe filter out devices that does not match with the type of the current device\nWe filter out devices that has a different prefix for their Bonjour UID value\n\nThis PR adds another filtering ability, by filtering out devices that we recognise as connected at the moment. This is an old component that was taken out and now brought back to our code, that is based on the basic thought that a device that is currently connected cannot be in another connection.\nThis component was checked manually and passed our tests.\n"}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21605](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21605){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21603](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21603){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21604](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/21604){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-363198149"}}
{"comment": {"body": "Did this cause any issues with the regression tests?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-363205194"}}
{"comment": {"body": "Are those configuration controlled from somewhere else in the system or is this is the only place?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-363230045"}}
{"comment": {"body": "Actually no. Since it\u2019s the last filter we use on our candidates, and since it\u2019s performed after we try to match devices by their strong identifiers, by the point we get to the activity filtering, we pretty much have our matching answer already", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-365185709"}}
{"comment": {"body": "That\u2019s probably because the regression tests data set is very biased this direction.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-365185819"}}
{"comment": {"body": "Yes", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-365188202"}}
{"comment": {"body": "So can we merge this PR? :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1078/_/diff#comment-365196492"}}
{"title": "adding time_utils", "number": 1079, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079", "body": "regression: {: data-inline-card='' } \nAPI: \nsmoke: {: data-inline-card='' }"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} what is the purpose of this task?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-363759762"}}
{"comment": {"body": "to unify the format of all timestamps and duration in classifier. see for example this open bug about it: [https://levltech.atlassian.net/browse/LDI-262](https://levltech.atlassian.net/browse/LDI-262){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-363920828"}}
{"comment": {"body": "So please work directly on the bug and not on improvement outside the backlog and if it requires design, please suggest first", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-363921442"}}
{"comment": {"body": "already consulted with PA team. this is not fixing the bug directly", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-363922367"}}
{"comment": {"body": "constructor can init as now\\(\\)  \ncan add an optional parameter to constructor so we can init with alternative value if needed.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365661169"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} what this code is solving ?  \ndid you check if there is native python or pip package that already does it? most likely there is and if so there is no reason to write our own version of it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365664914"}}
{"comment": {"body": "this code will solve a lot of bugs in the future. bugs that will not occur I can\u2019t show you. If you have an alternative package, please offer it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365666634"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} i\u2019m trying to understand what problem \\(defined\\) this solves\n\nIt is hard to find a solution to an undefined problem", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365674833"}}
{"comment": {"body": "We did yesterday a design review meeting about the problem, you were there. I can give you the recording if you want. It was decided that this PR is needed. Please talk to Shimon about your concerns.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365676012"}}
{"comment": {"body": "agreed\n\ni\u2019ll talk to @{5f82bf320756940075db755e} \n\nplease postpone the merge for @{5f82bf320756940075db755e}  approval", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365711229"}}
{"comment": {"body": "I can\u2019t merge, you blocked it", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365735001"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1079/_/diff#comment-365765342"}}
{"title": "Feature/icmp6 mclr", "number": 108, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/108", "body": "Now matching by the icmpv6 mclr addresses as well.\nAfter we add the ongoing learning, this feature will be more useful. But for now its a good start to have it."}
{"title": "update model tag", "number": 1080, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1080", "body": ""}
{"title": "init for test - matchmaker and typeclassifier", "number": 1081, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081", "body": "this PR changes all init method call of type classifier and match maker to init and wrapper init init_for_test.\nnow we can remove the precaution that prevents type classifier and matchmaker to init/load_models multiple times\nregression: {: data-inline-card='' } \napi (almost) : {: data-inline-card='' }"}
{"comment": {"body": "Why having two separate init functions? This is a pending disaster to happen.\n\nPlease use one function and if you want to behave differently in a test scenario, pass a parameter like `.init(reload_models = False)`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-365182351"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} this exactly what we didn\u2019t want to pass force flags because of UT..\n\nHacking function with flags eventualy will cause someone to call it not ftom UT\n\n@{6265307b185ac200692f9bd9} The UT function should be in UT and not part of class", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-365183297"}}
{"comment": {"body": "Exactly the opposite. The hack here is adding specific test functions to an actual production code and not actually going through the real \u201cproduction\u201d initialization code is what will cause the issues in production.\n\nAnd also why are we initializing all the objects in each tests function? Why it\u2019s isn\u2019t done in the setup of the class?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-365184036"}}
{"comment": {"body": "done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-365197831"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} please split the PR with fixing the init of models, between the refactor code, they are not attached", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-366059561"}}
{"comment": {"body": "this is not my task. @{6335b848a84c7f79c387915c} will do it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-366060377"}}
{"comment": {"body": "so decline the PR then", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1081/_/diff#comment-366064424"}}
{"title": "cast filter list from list to string", "number": 1082, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1082", "body": ""}
{"title": "LDI-298 Ensure to output consumer categoty when it can be deduced from the typing candidates", "number": 1083, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1083", "body": "Sometimes we can deduce that there is only 1 possible consumer_category from the typing candidates dataframe, but we dont actually do that, causing us to have a lower hit rate of cujo level 5 typing than we should have. This PR fixes that issue and also correctly assigns TypingModelResolution.VENDOR to when we dont know the consumer category (instead of the wrong TypingModelResolution.TYPE that was assigned previously)."}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22129](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22129){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22130](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22130){: data-inline-card='' } \n\nRegression against same branch name: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22131](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22131){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1083/_/diff#comment-365196536"}}
{"title": "cast uuid's to string instead of serailze it", "number": 1084, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1084", "body": "we want to save uuid as string with dashes, .hex will remove the dashes from."}
{"title": "Update models load", "number": 1085, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1085", "body": "fix IdentificationScoring model loading and tests\nfix HostnameMLClassifier model loading\n\n"}
{"title": "LDI-257 align model loadings with lndi paradigm", "number": 1086, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1086", "body": "The model loading in user agent analyzer and hostname typing is modified here to work with the new LNDI paradigm. No results changes are expected."}
{"title": "LDI-34 update hostname ml model", "number": 1087, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1087", "body": "updated regexes and ml model\nml vocabulary format change\nskipped test\n\n"}
{"comment": {"body": "same branch regression:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/22551](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/22551){: data-inline-card='' } \n\nAPI  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/22550](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/22550){: data-inline-card='' } \n\nSmoke  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/22549](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/22549){: data-inline-card='' }   \n  \nI\u2019m waiting for [https://bitbucket.org/levl/eros-automation/pull-requests/464](https://bitbucket.org/levl/eros-automation/pull-requests/464){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1087/_/diff#comment-365331220"}}
{"title": "Change the path format of the save debug data", "number": 1088, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1088", "body": "TL:DR change the save debug path to S3 in order to improve the recording & debugging process\nThe old format was to build a sessions directory and inside it many sub directories with the name format:\n timestamp_event-CUJO-ML2-event_type\nThe new format is to create sub directories with the following format:\n vault_id/partner_id/agent_serial/year/month/day/hour/timestamp_event_id_mac_event_type\n@{5f82bf320756940075db755e} The comment in the Jira issue denote the following format:\n bucket:/classifier_tag/partner_id/agent_serial/vault_id/year/month/day/hour/event_id_mac_event_type \nbut as we agreed, the bucket:/classifier_tag is already supplied and will not change, and the timestamp part can \nbe useful when working with the data, e.g. to sort events by timestamp without parsing the content of the directories,\ntherefore I added it to the name of the directory.\nAlso, the partner_id was moved to the highest position in the tree."}
{"comment": {"body": "@{6252eba45d1e700069ad0104} Thanks! \n\nTo summarize, we will have?\n\nbucket:tenant\\_name/<config\\_path/classifier\\_tag>/partner\\_id/agent\\_serial/vault\\_id/year/month/day/hour/timestamp\\_event\\_id\\_mac\\_event\\_type/..\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1088/_/diff#comment-365217716"}}
{"comment": {"body": "[https://levltech.atlassian.net/browse/LDI-304?focusedCommentId=12627](https://levltech.atlassian.net/browse/LDI-304?focusedCommentId=12627){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1088/_/diff#comment-365220160"}}
{"comment": {"body": "Regression passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22548](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22548){: data-inline-card='' }   \nSmoke passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22547](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22547){: data-inline-card='' }   \nAPI passed:  \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22546](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22546){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1088/_/diff#comment-365220393"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} this path is very vague, can you please add \u201cpartner\\_id=event\\_msg.metadata.partner\\_id\u201d etc, for a better understanding of the partitions?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1088/_/diff#comment-366437057"}}
{"comment": {"body": "Fine by me, open an improvement that describes the desired format and I\u2019ll do it ASAP", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1088/_/diff#comment-366441520"}}
{"title": "Handle the case of MAC with deduped device being updated", "number": 1089, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1089", "body": "We had a bug since moving to saving and querying the devices by MAC. The scenario is the following: A duplicate model is created from 2 connections of the device with different MAC addresses - MAC_X and MAC_Y accordingly; The 3rd connection is with MAC_Y and it contains features that link the 2 devices. The chosen levl-id is that of MAC_X (the earliest created device), so to update the DB we need to delete the key of MAC_X and upsert the device at key MAC_Y. At the same time, the original MAC_Y device is the deduplicated device that should be removed. The implemented precedence of operations today was: remove updated MAC  upsert device  remove deduped devices. As you can see in the above description, in this case it will lead to deleting both devices, when the expected result was to have the device of MAC_X with the key MAC_Y.\nThe solution was to change the precedence: First remove changed MAC; Then remove deduplicated devices; Then upsert device. *** The removal of deduplicated devices is subject to them not having the same MAC address as the updated device. In that case we just upsert it without deleting the key to avoid an unnecessary call to the DB.\nImportant: The module that updates the DB has no unit tests. This case can not otherwise be tested because it requires the DB interface (the result log was ok, it was just the actual DB write that was wrong). Right now this fix isnt covered in tests and this should be addressed.\n"}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22606](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22606){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22607](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22607){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22608](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/22608){: data-inline-card='' }   \n", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1089/_/diff#comment-365222757"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} we can check in the dedup ut case that we have, if the vault contains the correct devices after the deletion..\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1089/_/diff#comment-365222849"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} Nice catch!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1089/_/diff#comment-365288197"}}
{"comment": {"body": "@{5dbeb866c424110de52552cc} See my comment here: [https://levltech.atlassian.net/browse/LDI-305?focusedCommentId=12629](https://levltech.atlassian.net/browse/LDI-305?focusedCommentId=12629){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1089/_/diff#comment-365291560"}}
{"title": "Snapshot optimization", "number": 109, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/109", "body": "\n\nadded snapshot env vars\n\n"}
{"title": "load filter list to json (yaml format)", "number": 1090, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1090", "body": ""}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} only here we need the change? helm\\_deployment? classifier chart yaml?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1090/_/diff#comment-365347064"}}
{"comment": {"body": "its not strict, the major change is within the configmap, \u201c\u201c will work as well.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1090/_/diff#comment-365347468"}}
{"title": "Add debug flag activation", "number": 1091, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1091", "body": "The functions save_kafka_record and save_old_event_to_pcaps are called inside the pipeline but should only \nexecute if we are in debug mode."}
{"comment": {"body": "smoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24055](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24055){: data-inline-card='' }   \nregression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24056](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24056){: data-inline-card='' }   \nAPI is failing: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24054/5945480?item1Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24054/5945480?item1Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED){: data-inline-card='' }  I don\u2019t see a reason for that, should I merge it anyway? @{5f82bf320756940075db755e} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1091/_/diff#comment-366029315"}}
{"comment": {"body": "Since there is no way this is related to you, I say go ahead and merge.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1091/_/diff#comment-366138040"}}
{"comment": {"body": "OK, thank you", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1091/_/diff#comment-366138467"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} Please validate with Peleg first", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1091/_/diff#comment-366140372"}}
{"title": "LDI-316 extract models logic from stream processing runner to eros models loader", "number": 1092, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1092", "body": "move models logic from StreamProcessingRunner to eros_models_loader\nadd is_new_models_version_exist unit tests\n\n"}
{"title": "[Snyk] Fix for 2 vulnerabilities", "number": 1093, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1093", "body": "Snyk has created this PR to fix one or more vulnerable packages in the pip dependencies of this project.\nAs this is a private repository, Snyk-bot does not have access. Therefore, this PR has been created automatically, but appears to have been created by a real user.\nChanges included in this PR\n\nChanges to the following files to upgrade the vulnerable dependencies to a fixed version:\nhelm_deployment/requirements.txt\n\n\n\nWarning\n```\nkubernetes 10.0.1 requires pyyaml, which is not installed.\ngoogle-auth 2.16.0 requires rsa, which is not installed.\n```\nVulnerabilities that will be fixed\nBy pinning:\nSeverity                   | Issue                   | Upgrade                   | Breaking Change                   | Exploit Maturity\n:-------------------------:|:-------------------------|:-------------------------|:-------------------------|:-------------------------\n | Insufficient Verification of Data Authenticity  SNYK-PYTHON-CERTIFI-3164749 |  certifi: 2021.10.8 - 2022.12.7  |  No  | No Known Exploit \n | Timing Attack  SNYK-PYTHON-RSA-1038401 |  rsa: 4.5 - 4.7  |  No  | No Known Exploit \nSome vulnerabilities couldn't be fully fixed and so Snyk will still find them when the project is tested again. This may be because the vulnerability existed within more than one direct dependency, but not all of the affected dependencies could be upgraded.\nCheck the changes in this PR to ensure they won't cause issues with your project.\n\nNote: You are seeing this because you or someone else with access to this repository has authorized Snyk to open fix PRs.\nFor more information:\nView latest project report\nAdjust project settings\nRead more about Snyk's upgrade and patch logic"}
{"title": "Output Logs with Missing Columns", "number": 1094, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094", "body": "Key-value pairs with valid 0/False values were dropped from outpul logs due to a Kinesis adapter filter. Now filtering None values only.\n@{606d973d3e6ea000685ed65f} - need your input on that."}
{"comment": {"body": "Great catch! :muscle: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365642973"}}
{"comment": {"body": "@{626ed79bd7fd480068d706a5} @{5f82bf320756940075db755e} Is the testing of the fields in athena shouldn\u2019t be some part of our test suites?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365643388"}}
{"comment": {"body": "Yes, and this exactly where the bug raised by @{62c1676dce5a604dbfb32cba} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365645773"}}
{"comment": {"body": "Perfect :ok_hand: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365657285"}}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23290](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23290){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23292](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23292){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23296](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23296){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365673670"}}
{"comment": {"body": "@tamir\n\n> d = \\{  \n> \"d\": \"22\",  \n> \"e\": 1,  \n> \"r\": 0,  \n> \"rr\": None,  \n> \"ww\": \"\"  \n> \\}\n>\n> actual result: \\{'d': '22', 'e': 1\\}\n\nexpected result : \\{'d': '22', 'e': 1, 'r': 0\\}  \nsolution : \\{k: v for k, v in d.items\\(\\) if str\\(v\\) != \"\" and str\\(v\\)!=\"None\"\\}\n\nif you can add it as UT too", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365693575"}}
{"comment": {"body": "Thanks!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-365760917"}}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24186](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24186){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24185](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24185){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24187](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24187){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1094/_/diff#comment-366073096"}}
{"title": "LDI-247 unittest for changes in cosumer category", "number": 1095, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095", "body": "unittest for LDI-207\n{: data-inline-card='' }"}
{"comment": {"body": "what\u2019s the reason for not going through this follow for Apple devices?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095/_/diff#comment-365702713"}}
{"comment": {"body": "It does, but not if I set the general\\_os to `Apple OS` in `handle_one_vendor` function.  \nthe else in lin 244 changes the `model_ui_description` to `Apple OS device`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095/_/diff#comment-365704276"}}
{"comment": {"body": "api: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23525](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23525){: data-inline-card='' }   \nsmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23526](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23526){: data-inline-card='' }   \nregression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23529](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/23529){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095/_/diff#comment-365717978"}}
{"comment": {"body": "Got it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095/_/diff#comment-365718895"}}
{"comment": {"body": "Didn\u2019t it change anything on the regression side?\n\nI would at least expect to see some changes there.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095/_/diff#comment-365719282"}}
{"comment": {"body": "No", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1095/_/diff#comment-365729404"}}
{"title": "update s3 models.tar.gz ETag claculation methos", "number": 1096, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1096", "body": ""}
{"title": "Feature/LDI-300 remove erroneous connection result", "number": 1097, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097", "body": "Remove the creation of erroneous connection result\nIt was decided that an event that cannot be deserialized will be ignored and will not cause the system to return a response, rather a None value. The reasoning was that if we cannot deserialize the event, there is little to be done with this logical case since we cannot conclude anything except that an error occurred during the ingestion of the event.\n"}
{"comment": {"body": "all `Reporter.` return value should be caught and send to the right place if possible.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-365743214"}}
{"comment": {"body": "This PR is a part of the task that deals with the errors handling and reporting inside classifier.  \nThe Reporter module is currently returning the error log it creates but this will change since there is no meaning to it.  \nThe call to Reporter here is in order to use the logging and metrics channels to report that this error occurred.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-365745706"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} can we also in case of save debug enabled store the bytes as is in S3? just partitioned by time in error folder under:\n\nbucket: tenant\\_name/classifier\\_tag/deserialize\\_errors/<time>.bin\n\nThis might help us to rerun such events in small scale environments and fix the issue.\n\nFor example it occurred today once Cujo updated the kafka headers that our decode method not supports, we failed to deserialize and no event stored, thus harder to fix..\n\nHigher priority is to cover the code with error reporting and not crash as part of the error handling story, so if my suggestion will put this at risk for Sprint, please open improvement and handle next sprint.. \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-365936585"}}
{"comment": {"body": "I think that it\u2019s better to open a ticket for the next sprint, since we don\u2019t have a lot of time left.  \nAlso, the save debug is already heavy and there is the open bug [https://levltech.atlassian.net/browse/LDI-260](https://levltech.atlassian.net/browse/LDI-260){: data-inline-card='' } regarding the opening of many files, so I recommend a wider perspective on the save debug before adding more functionality.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-366093430"}}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24286](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24286){: data-inline-card='' }   \nSmoke passed:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24280](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24280){: data-inline-card='' }   \nAPI passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24278](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24278){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-366134340"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} why API test fails?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-366176046"}}
{"comment": {"body": "I have rerun the tests :grinning: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24489](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24489){: data-inline-card='' } - All greeeen", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1097/_/diff#comment-366261398"}}
{"title": "fix models backup tmp dir location", "number": 1098, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1098", "body": ""}
{"title": "LDI-338 lndi models fix singleton like models loading", "number": 1099, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1099", "body": "set s3_client type to Optional[s3fs.S3FileSystem] in model_loader/src/model_loader.py methods\nfix matchmaker init + update UTs\nfix features init - remove check of load model once\nfix type_classifier init + update UTs\n\nContains @{62e7c839e50f2f2a395430c2} changes for user_analyzer and hostname_typing\nJira: {: data-inline-card='' } \nRegression tests link: Regression tests\nAPI tests link: api tests\nSmoke tests link: smoke tests"}
{"comment": {"body": "please fix Bitbucket pipeline time as we said. it went from ~12 minutes to ~36 minutes:  \n\n![](https://bitbucket.org/repo/o5KReBa/images/3390054109-Screenshot%202023-02-01%20at%2013.28.04.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1099/_/diff#comment-366097688"}}
{"title": "Feature/basic matching", "number": 11, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/11", "body": "Make integration test work since it depends on postgres\n\n"}
{"title": "Feature/hw_mac", "number": 110, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/110", "body": "Now matching by the hw mac (until now we only had the previous rand mac feature), and this PR will close our V1 algorithm goal.\nNote that our DB schemas already had an hw_mac column so this PR can be merged without any needed migration."}
{"title": "Add basic error mapping", "number": 1100, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100", "body": "Needs to be merged after this one{: data-inline-card='' } \n\nChange logging calls to reporter calls\nAdd types of errors to the reporter\nChange the reporter's behavior to create an error log only when supplied with a session's error log list since it has no meaning out of this context. It will not return any value\nMake the classifier pipeline drop an event that failed the deserializion and/or translation from the communication channel. \n  This will be reported but the classifier will not process these events any further. \n  A ConnectionResult object will not be created.\nAdd an error report to several modules.\n\n"}
{"comment": {"body": "will this raise cause another error log somewhere? and will it crash the calssifeir?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100/_/diff#comment-366177656"}}
{"comment": {"body": "The _test\\_connection\\(\\)_ is called from the `start_pipeline` \u2192 `create_pipeline` functions and will be caught and an initialization error will be reported.|  \nThe classifier will not pass the initialization step due to this code that will reach the main where it will be reported\n\n```\ndef start_pipeline(pipeline_name: str):\n    logging.info(f\"topics in start_pipeline {_decision_log_topic} {_result_client_topic} {_error_log_topic}\")\n    _consumer_group_id = classifier_config.get(CONSUMER_GROUP_ID, pipeline_name)\n    try:\n        source, pipeline = create_pipeline(pipeline_name)\n        if pipeline_name == ARCHIVE_AVRO_TO_S3_SINK_RESULT:\n            _hot_path = True\n        elif pipeline_name == ARCHIVE_AVRO_TO_S3_SINK_LOG:\n            _hot_path = False\n        elif pipeline_name == ARCHIVE_AVRO_TO_S3_SINK_ERROR_LOG:\n            _hot_path = False\n        else:\n            _hot_path = True\n        com_channel = KafkaComChannel(\n            _bootstrap_servers, _topic, _hot_path, _poll_interval, _consumer_group_id, _max_batch_size\n        )\n\n        runner = StreamProcessingRunner(source, com_channel)\n\n        runner.start()\n    except Exception as e:\n        Reporter.fatal(label=SystemErrors.INITIALIZATION_FAILED, exception=e)\n        raise e\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100/_/diff#comment-366396576"}}
{"comment": {"body": "Is this necessarily the cause? Is it because other errors should be covered inside `_logic_to_execute`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100/_/diff#comment-366411012"}}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25089](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25089){: data-inline-card='' }   \nSmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25087](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25087){: data-inline-card='' }   \nAPI failing HTTP: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25088/6204600/6205357/6205458/log?item1Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25088/6204600/6205357/6205458/log?item1Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DFAILED%252CINTERRUPTED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100/_/diff#comment-367085304"}}
{"comment": {"body": "@{6252eba45d1e700069ad0104} bump", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100/_/diff#comment-367213439"}}
{"comment": {"body": "No it isn\u2019t.   \nThis is just the POC level of error handling, so it will be changed to another error. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1100/_/diff#comment-367214550"}}
{"title": "LDI-337 Updated data-collector version (Cujo Kafka Header fix)", "number": 1101, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1101", "body": "Following the fix in eros-data-collector, this PR updated the classifier to use the latest version\n({: data-inline-card='' } )"}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24461](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24461){: data-inline-card='' }\n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24457](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24457){: data-inline-card='' }\n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24456](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/24456){: data-inline-card='' }  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1101/_/diff#comment-366239669"}}
{"comment": {"body": "Thanks @{************************} ! ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1101/_/diff#comment-366258748"}}
{"title": "LDI-353 fix pipeline duration and move to latest stage before sink", "number": 1102, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1102", "body": "Fix both the logs and the metric for pipeline and agent duration.\nMove to later in pipeline (before sink) to have more accurate stats\n\n"}
{"comment": {"body": "Regression:\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25408](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25408){: data-inline-card='' }  - Smoke\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25409](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25409){: data-inline-card='' }  - API\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25410](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25410){: data-inline-card='' }  - Regression\n\n\u200c\n\nGrafana metric working \\(changed, so double check\\)\n\n![](https://bitbucket.org/repo/o5KReBa/images/2074616211-Screenshot%202023-02-06%20at%2020.01.26.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1102/_/diff#comment-367355694"}}
{"comment": {"body": "After checking there is some regression issues in dev. Not relevant to this PR.\n\nWill take it with the PR that introduced them. This can be merged..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1102/_/diff#comment-367366617"}}
{"title": "check if bucket exists is s3 before checking if file exists", "number": 1103, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1103", "body": "Regression tests: Link\nAPI tests: Link\nSmoke test: Link"}
{"comment": {"body": "what is mock\\_s3?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1103/_/diff#comment-366440647"}}
{"comment": {"body": "mock\\_s3 is utility class the can be used for UTs with boto3 AWS s3 functionality  \nIt mimics s3 by creating a real s3 bucket, that can mock the s3 functionality in the tested methods\n\nOne thing to note is that you have to use their specific AWS region \\(`us-east-1`\\), since behind the scenes there\u2019s a real bucket that\u2019s created by mock\\_s3 ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1103/_/diff#comment-367055785"}}
{"title": "LDI-184 build l2 model for xb7", "number": 1104, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1104", "body": "added apple watch series 7 2.4ghz l2 fp\nadded apple watch 7, iphone 13 family and galaxy s20 L2 fp\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24737](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24737){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24736](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24736){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24736](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/24736){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1104/_/diff#comment-366462861"}}
{"title": "LDI-368 fixing flake8 configuration", "number": 1105, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1105", "body": "flake8 wasnt check __init__.py files and had duplicate lines in it\nregression: {: data-inline-card='' } \nsmoke: {: data-inline-card='' } \nAPI: "}
{"title": "Add explicit param name", "number": 1106, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1106", "body": "Change save debug path {: data-inline-card='' }"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25418](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25418){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25419](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25419){: data-inline-card='' } \n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25420](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25420){: data-inline-card='' } \n\nThere is some regression in dev, will open separate Bug", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1106/_/diff#comment-367379307"}}
{"title": "LDI-351 result log model tag field is empty", "number": 1107, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1107", "body": "model tag is missing from result log.\nJira: {: data-inline-card='' } \nRegression tests: {: data-inline-card='' } \nAPI tests:{: data-inline-card='' } \nSmoke tests:{: data-inline-card='' }"}
{"title": "lab apple watches and galaxy s20 fe", "number": 1108, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1108", "body": "added fps for\n\nlab apple watches\ngalaxy s20 fe\n\n"}
{"comment": {"body": "API: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25084](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25084){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25085](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25085){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25086](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25086){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1108/_/diff#comment-367081065"}}
{"comment": {"body": "API passed as well  \n\n![](https://bitbucket.org/repo/o5KReBa/images/3212600841-image.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1108/_/diff#comment-367082566"}}
{"title": "Remove file handler to prevent a memory leak", "number": 1109, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1109", "body": "There is a file handle being opened via this line  fh = logging.FileHandler(osp.join(dir_, \"app.log\")), closed (sometimes) by this line  logger.handlers = [h for h in logger.handlers if not isinstance(h, logging.FileHandler)] and since these lines reside in different functions, one may be called and the other not, e.g. in case of an exception in the pipeline. In order to solve this, the gap should be closed.\nIt was decided to remove this feature from the save debug module."}
{"comment": {"body": "Smoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25206](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25206){: data-inline-card='' }   \nAPI passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25207](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25207){: data-inline-card='' }   \nRegression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25208](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25208){: data-inline-card='' }   \n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25260](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25260){: data-inline-card='' }  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1109/_/diff#comment-367300571"}}
{"title": "Snapshot optimization", "number": 111, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/111", "body": "\n\nadded snapshot env vars\nquery kafka for number of partitions\n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} please merge to dev", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/111/_/diff#comment-241254912"}}
{"title": "LDI-405 make test_utils again be able to process ingestio_event.bin files", "number": 1110, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1110", "body": "Recently the automatic recordings of ingestion_event.bin files are not correctly processed inside the classifier. After some trial and error, it seems the issue stems from the event message type being of an incompatible format. This PR updates the format such that the tool works again with ingenstion_event.bin files."}
{"comment": {"body": "API: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25266](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25266){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25267](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25267){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25270](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25270){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1110/_/diff#comment-367304872"}}
{"title": "Bugfix/LDI-409 pre commit fails: isort version", "number": 1111, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1111", "body": "I had some problems yesterday on Sonyas environment (and Ehuds as well) when we tried to commit their work and the pre-commit failed. It turns out that its a known issue that is solved by simply upgrading isort version to 5.12.0 (As explained here:  ).\nNote that this affects only the pre-commit component, and changes only the test-requirements.txt file."}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25531](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25531){: data-inline-card='' }\n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25532](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25532){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25530](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/25530){: data-inline-card='' }  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1111/_/diff#comment-367502629"}}
{"comment": {"body": "no need to pass regression smoke and api in this PR", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1111/_/diff#comment-367503019"}}
{"comment": {"body": "Just saw your comment :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1111/_/diff#comment-367516925"}}
{"title": "LDI-385 xb7 l2 fp new", "number": 1112, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112", "body": "added lab apple watches and galaxy s20 fe\nadded other devices (Arthur's records)\nsorted all file\n\n"}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} @{63b2a7992c70aae1e6faa958} We have a huge diff on the file. Is there any way to reduce that?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112/_/diff#comment-368042788"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} this is because I sorted the all file.  \nthe new fps can be seen in previous commit  \n[https://bitbucket.org/levl/eros-classifier/commits/a17f366d5f0c47816d1f5cb60779778d6c4c77ff](https://bitbucket.org/levl/eros-classifier/commits/a17f366d5f0c47816d1f5cb60779778d6c4c77ff){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112/_/diff#comment-368043781"}}
{"comment": {"body": "Understood. So from now on, we should always keep the file sorted?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112/_/diff#comment-368044807"}}
{"comment": {"body": "Yes, that is the purpose", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112/_/diff#comment-368049023"}}
{"comment": {"body": "sorted by `platform_type, vendor, model, fp`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112/_/diff#comment-368049343"}}
{"comment": {"body": "API: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26224?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26224?launchesParams=page.page%3D1){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26223?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26223?launchesParams=page.page%3D1){: data-inline-card='' }   \nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26225?launchesParams=page.page%3D1](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26225?launchesParams=page.page%3D1){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1112/_/diff#comment-368097208"}}
{"title": "Fix data type", "number": 1113, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1113", "body": "The connected_devices_list is a dictionary and its keys are the desired mac addresses.\nIn the features log it should be a list of mac addresses.\nThe obfuscation logic  from Cujo was added and the one used for MAC addresses was added to the test. \nA new python package was added to the requirements"}
{"title": "Feature/LDI-408 support parsing agent id in coll", "number": 1114, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1114", "body": "LDI-353 fix pipeline duration and move to latest stage before sink\nLDI-353 fix pipeline duration and move to latest stage before sink - fix access to old filed\nLDI-353 fix pipeline duration and move to latest stage before sink - fix order\nLDI-408: add agent id first iteration\n\n"}
{"title": "l2 fp from cujo", "number": 1115, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1115", "body": "l2 fp from cujo"}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26498](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26498){: data-inline-card='' }   \nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26499](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26499){: data-inline-card='' }   \nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26500](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26500){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1115/_/diff#comment-368576310"}}
{"title": "LDI-462 improve check details updated", "number": 1116, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1116", "body": "Currently the check on if identification details were updates basically always returns a True result because there is a comparison between objects of different types and the comparisons themselves are performed before the merger of features.\nThis property must be more informative in order to be able to eliminate unnecessary writes to the dynamo db and reduce costs. \n"}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26628](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26628){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26629](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26629){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26630](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26630){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1116/_/diff#comment-368636837"}}
{"comment": {"body": "who is using `details_updated` ?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1116/_/diff#comment-368638180"}}
{"comment": {"body": "It\u2019s stored in the session object below.\n\nI plan to use it to implement [LDI-358](https://levltech.atlassian.net/browse/LDI-358) and if `details_updated` is false then allow the dynamodb write to be skipped.\n\nI made this smaller PR first because I want to make sure these changes are reasonable before I diverge too much.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1116/_/diff#comment-368639592"}}
{"title": "LDI-426 L2 model xb7 lab", "number": 1117, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1117", "body": "all lab devices from 2023-02-08\n"}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26824](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26824){: data-inline-card='' }   \nSmoke  [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26825](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26825){: data-inline-card='' }   \nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26826](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/26826){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1117/_/diff#comment-368669514"}}
{"title": "LDI-384 - Improve serialization and deserialization of Python dataclasses to improve classifier runtime", "number": 1118, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118", "body": "The serialization from JSON and to JSON of the python dataclasses was a massive performance bottleneck.\nThis was felt massively in two main scenarios:\n\nReading a Device item from the dynamo DB.\nConverting internal structures ( IEEE802CapsFeatureFields to IEEE802CapsFeature)\n\nThe solution was to replace the serialization framework from dataclasses-json to dataclasses-wizard. The serialization process still takes a very significant amount of the total run-time, which will be solved later by reducing the size and the complexity of the objects stored in the DynamoDB.\nTo make the solution work out of the box, it as essential to change of the data structures stored in the DB. Such as getting read of the Version object or defining new dataclass structures for storing certain feature values.\nThe performance was measured by running a set of 500 connection events through the system (on my MacBook Air with M2 chipset, the CPUs in AWS are expected to be 2-3 times stronger)\n| Measure runtime | Before | After | Improvement |\n| --- | --- | --- | --- |\n| Entire pipeline | 137s | 40s | -71% |\n| get_device_from_vault_id | 105s | 17.3s | -84% |\n"}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} Very good! As we have discussed Kinesis json dump lacks of same problems.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369250780"}}
{"comment": {"body": "no patch? \\(as in major.minor.patch\\)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369251629"}}
{"comment": {"body": "@{5f82bf320756940075db755e} The kinesis dump uses something a bit different dataclasses-avro and not dataclasses-json. So we need to look into that as well, but right now that seems to be only 3%-4% of the runtime.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369253985"}}
{"comment": {"body": "Removed this as we don\u2019t have a functionality that actually detects the patch version and even if we did, it\u2019s not returned as part of the result object.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369254385"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} user\\_agent\\_analyzer can detect it, but I guess it is ignored afterwards.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369254477"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} Right now we are not using it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369255828"}}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27460](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27460){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27459](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27459){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27458](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27458){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1118/_/diff#comment-369437318"}}
{"title": "LDI-251 Remove iphone c600 from devices db", "number": 1119, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119", "body": "The Gradiente iPhone C600 is an old Android phone model that only exists in Brazil (not important for LEVL). The system can get confused and report it as a result if a malicious user inserts iphone in the hostname of an Android device and we dont have an L2 match. This PR prevents us to report the iPhone C600 in that case which is reported in LDI-251. More thorough solutions for the underlying issues were detailed in {: data-inline-card='' } but are not currently prioritized."}
{"comment": {"body": "Thanks!\n\nWould it be possible to added this specific scenario of the bug failure to the regression tests to make sure we continuously test this? @{637f5c5e3e79f12e572115d7} @{60506e9606cbba006ae58342} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369241109"}}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27094](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27094){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27093](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27093){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27099](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27099){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369243889"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} @{5a4500fe0cacf235de82a9d4} why deleting the device from devices\\_db is the chosen solution?  \nnext update from the same source will return this device", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369244253"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} the current small fix is agreed that the system will act as if the device doesn\u2019t exist. Do you have a suggestion how to implement it so it will do this behavior ?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369251880"}}
{"comment": {"body": "not for a quick change", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369255272"}}
{"comment": {"body": "@{621df03094f7e20069fd6ab2} @{62e7c839e50f2f2a395430c2} So, how can we solve this? discuss and suggest a solution", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369262713"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} we both agree that this is not a great solution but the only option we have as a quick fix", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369264557"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} I\u2019ll just merge this when a default reviewer approves it.\n\n@{637f5c5e3e79f12e572115d7} @{************************} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369264595"}}
{"comment": {"body": "@{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} I would say the great solution would be point \\(2\\) from the LDI-251 discussion mail:\n\n`Nuriel to add backlog task to filter device DB - Filter Device DB only to use devices that are well used ( from data statistics ) or new devices`\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369264711"}}
{"comment": {"body": "Added as LDI-504 since there are no subtasks to bugs.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1119/_/diff#comment-369725833"}}
{"title": "Snapshot optimization", "number": 112, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/112", "body": "\n\nadded Ron deployment\nadded env vars to snapshot batch\nadded logs to snapshot batch\nremoved dask and topic creation from snapshot batch\nquery kafka for numbmer of partitions \n\n"}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} the pull requests goes to \u2018dev\u2019 first and only later merged to \u2018master\u2019", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/112/_/diff#comment-241254800"}}
{"title": "LDI-334 implement the random mac more than 2 antennas rule", "number": 1120, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1120", "body": "Implementation of the multi-antenna and random mac typing rule.\nAdded a new parsed field to the HT caps and changed tests accordingly\nAdded logic at features/src/typing_features/multi_ant_rand_mac_rule/src/multi_ant_rand_mac_rule.py"}
{"title": "LDI-449 avoid dynamodb write if no new information", "number": 1121, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121", "body": "In order to reduce write operations to the DynamoDB, a check was added on if the Device object meaningfully changed as a result of the new session. The DynamodDB is updated only if there was a meaningful change. There should be no impact on classifier results.\nAPI: {: data-inline-card='' } \nSmoke: {: data-inline-card='' } \nRegression: {: data-inline-card='' }"}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} Do you have any way to measure whether, for example on the regression tests, less writes were executed to the DynamoDB?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369262530"}}
{"comment": {"body": "I think without adding feature specific behavior it won\u2019t make a real effect. Because many of the identification features are changed each connection - for the DHCP transaction ID which is different on every connection so it the identification details will always change. It looks like the behavior should be define feature-by-feature.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369362829"}}
{"comment": {"body": "It is a good question to what extent we want to compromise on efficacy in order to reduce the db usage. This PR does not compromise at all. I\u2019ll work on a metric of db writes so we have something to benchmark against. cc @{5d74d49897d8980d8eacd7f8} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369505728"}}
{"comment": {"body": "In dev when running the regressions there are a total of 2782 writes to the dynamodb.\n\nOn this branch running the same complete regression suite there are 1682 writes to the dynamodb.\n\nThis is a decrease of 39.5% in dynamodb writes without any compromise to efficacy.\n\nDo we have a specific goal in mind @{5a4500fe0cacf235de82a9d4} ?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369744554"}}
{"comment": {"body": "I measured this by adding the following code to the upsert\\_device function:\n\n```\n    with open('/home/<USER>/counter.txt', 'r+') as f:\n        c = int(f.read().strip())\n        c += 1\n\n    with open('/home/<USER>/counter.txt', 'w') as f:\n        f.write(str(c))\n```\n\nWhere `/home/<USER>/counter.txt` is a text file that I manually initialized each time with the digit \u201c0\u201d. The number at the end of the regression run each time is what I posted.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369745077"}}
{"comment": {"body": "| **Event type** | **Number of DB writes in dev branch** | **Number of DB writes in LDI-449 branch** | **Change %** |\n| --- | --- | --- | --- |\n| CONNECTED | 1672 | 1656 | -1% |\n| ONGOING | 92 | 25 | -73% |\n| DISCONNECTED | 1018 | 0 | -100% |\n| Total | 2782 | 1681 | -39.6% |\n\n@{5a4500fe0cacf235de82a9d4} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369803000"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2}  As we expected it doesn\u2019t really help with the connection events. The disconnection events are completely redundant at this point.\n\nThe ongoing reduction is good stuff. It will help much more than that on real-life data.\n\nI don\u2019t want to make this change to the disconnection events, if we decide not to process them, I would make the system just ignore them. Maybe for now we can just apply this to only ONGOING events and handle the CONNECTED events later?\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369821439"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} To clarify, you want the disconnected events to still write to the DynamoDB even if they don\u2019t have any new information?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369823327"}}
{"comment": {"body": "Yes.\n\nEssentially this makes disconnection events disabled and if I want to disable them I would disable them in a much more explicit way.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369828208"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} added ongoing event to the conditions", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1121/_/diff#comment-369829597"}}
{"title": "Bugfix/LDI-285 windows 11 os detection", "number": 1122, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1122", "body": "DHCP fingerprints for Windows 11 and fixed UT\n"}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/27190](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/27190){: data-inline-card='' } \n\nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/27192](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/27192){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/27191](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/27191){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1122/_/diff#comment-369255059"}}
{"title": "LDI-2 - add HEALTH_CHECK_ENABLED ff", "number": 1123, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1123", "body": "Add a feature flag to enable \\ disable health check  \nthis is needed for the scale tests, as the health check is hardcoded to a specific port which causes a collision when running multiple containers in the same task"}
{"title": "mDNS parsing optimization", "number": 1124, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1124", "body": "Now using dpkt code to parse the mDNS packets instead of the slower dns library we had. The default parsing code in dpkt wasnt able to parse the interesting mDNS packets (it was throwing an exception on unknown field). I have imported the relevant code in dpkt and with some minor fixes to make it work properly.\nBefore:\n\nAfter:\n\n"}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27784](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27784){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27778](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27778){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27779](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/27779){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1124/_/diff#comment-369782457"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} let\u2019s add Jira issue link for the Bug or Story/Sub-task", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1124/_/diff#comment-369810574"}}
{"comment": {"body": "where is this code from?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1124/_/diff#comment-369903128"}}
{"comment": {"body": "From the dpkt library", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1124/_/diff#comment-369956748"}}
{"comment": {"body": "[https://github.com/kbandla/dpkt](https://github.com/kbandla/dpkt){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1124/_/diff#comment-369957832"}}
{"title": "Add the error traceback to the log", "number": 1125, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1125", "body": "The motivation is that without the traceback it is unclear what exactly happened to cause the error and the investigation is harder."}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28364](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28364){: data-inline-card='' }   \nAPI passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28362](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28362){: data-inline-card='' }   \nSmoke passed:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28361](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28361){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1125/_/diff#comment-370138865"}}
{"title": "LDI-448 skip dynamo db calls for low info ongoing events", "number": 1126, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1126", "body": "In this PR, DynamoDB interaction is completely disabled (read and write) for ongoing events that dont have at least device family information or at least main OS version information. The reason for this change is to reduce costs that are caused by excess interaction with the DynamoDB. Some unit tests required modification to pass as intended with the new flow.\nAPI: {: data-inline-card='' } \nSmoke: {: data-inline-card='' } \nRegression against same branch name: {: data-inline-card='' }"}
{"comment": {"body": "please add metric here", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1126/_/diff#comment-371335463"}}
{"comment": {"body": "@{5f82bf320756940075db755e} what do you mean by metric in this context?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1126/_/diff#comment-371471534"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} we are throwing events without any feedback to datalake or customer, so it's critical to add metrics:\n\n1. counter of thrown event per event type and label reason\n2. total events counter\n3. total results counter\n\nthey should sum up if all good\n\nIt\u2019s pretty straightforward, you can use any example in metrics.py file. Please tell if it\u2019s a problem \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1126/_/diff#comment-371515220"}}
{"title": "Bugfix/LDI-327 deduplicated devices do not keep all features (prev_rand_macs bug)", "number": 1127, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1127", "body": "This PR fixes the bug we had in the deduplication scenario of merging multiple sessions together. We used to choose the features from the duped devices OR from the current device, instead of merging them all together.\n\nOverall, the merging logic can be described as follows:\n1. Iterate over all matching features\n        1.1. Merge the features from dedup_devices and current device\n2. Iterate over evt_features\n        2.1. Merge evt_features into device\nIn that way, we can make sure were collecting all the features from the different sources.\nA relevant test (that is described as the bug scenario\\) was added in test_pipeline_device_classification_2.py.\n\nThere are two main logic changes here:\n\nWhen we return the deduped devices, we need to return them ordered by their time of creation. Some features are bounded with the amount of data that they store, and we need to make sure that when we merge all the features together, we do it logically by the order of sessions arrival.\nWhen we merge all the deduped devices together, we need to merge together features. In order to do so, were using a new method called get_merged_result that is implemented for each feature.\n\n"}
{"comment": {"body": "I went over all the feature merges and all looks very good, but those from my understand are just the identity features.\n\nHow are the typing features merged in this case? Is there already a separate logic for that and the only issue was with the identity ones?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1127/_/diff#comment-371459216"}}
{"comment": {"body": "After further thinking - you\u2019re correct. The typing features are still a problem, and might need more code work if we don\u2019t want the matchmaker to handle the typing features as well. As I understand, the identification problem is a bigger problem in terms of the system when handling this. If we don\u2019t mind the matchmaker to touch the typing features too, I can implement a similar merging logic for each of the typing features too", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1127/_/diff#comment-371486393"}}
{"comment": {"body": "I suggest to dedicate another story for the typing features as this will hold up this PR, and will deepen the deviation from matchmaker\u2019s original role.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1127/_/diff#comment-371633942"}}
{"comment": {"body": "Yes. Let\u2019s open a new story.  \nWe just need to understand though what is the current behavior of the system. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1127/_/diff#comment-371643738"}}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30672](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30672){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30670](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30670){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30669](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30669){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1127/_/diff#comment-371826689"}}
{"title": "added new l2 fps from GDE env since 01/02/2023", "number": 1128, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1128", "body": "added new l2 fps from GDE env since 01/02/2023, include test devices"}
{"comment": {"body": "**with** iphone 14 family - all passed  \nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28073](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28073){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28074](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28074){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28075](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28075){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1128/_/diff#comment-369847154"}}
{"comment": {"body": "**without** iphone 14 family - all passed  \nA [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28096](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28096){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28097](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28097){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28099](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/28099){: data-inline-card='' }  \nThere are no relevant tests for iPhone 13/14 conflict in XB7", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1128/_/diff#comment-369865997"}}
{"title": "Configure logging using yaml file and not coding.", "number": 1129, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129", "body": "Configure logging using yaml file and not coding.\nModel loader - replace some log messages from \"into\" to \"debug\" as those create a lot of noise in the log.\n\n"}
{"comment": {"body": "If we are going to do work around controlling the logging mechanism we need to do in a way that we can use while the system is live in production, for example changing logging level while the actual solution is deployed. \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129/_/diff#comment-370171896"}}
{"comment": {"body": "The mechanism of logging was not changed, it is the \u201clogging\u201d module of the standard library. Instead of code only configuration it is yaml \\+ code overrides for root level.\n\nSupport of dynamic log level for specific loggers can be implemented by listening on a shared file \\(on NFS or S3\\) and then updating log levels for some logggers.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129/_/diff#comment-370195557"}}
{"comment": {"body": "what are those files?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129/_/diff#comment-370359490"}}
{"comment": {"body": "@{63e37cfc8978d7a4353ce97c} Please use Jira issues linked, @{637f5c5e3e79f12e572115d7} advise how we do it.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129/_/diff#comment-370359546"}}
{"comment": {"body": "@{63e37cfc8978d7a4353ce97c} great to see you starting contributing to the code, couple questions:\n\n1. How is this stands with reference to the reporter logs? `Reporter.fatal`\n2. Does this means you will need to replace all logging calls in the code now? if so, let\u2019s think of a way of formatting also the minimal logs structure for example, with tenant\\_name, worker\\_id, vault\\_id\u2026\n3. Today we can change TF params to control log level, and not to build new tag image, that will be required in your proposal\n\n@{637f5c5e3e79f12e572115d7} @{5fd5d5149edf2800759cc96d} please share some scope on the task.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129/_/diff#comment-370361231"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} models loader in such case might not be the correct approach, as we will need to change logging for specific instances of some portion.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1129/_/diff#comment-370361394"}}
{"title": "Feature/prev dhcp transaction id", "number": 113, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/113", "body": "Now matching by previous dhcp transaction id as well.\nI updated the DB with the new columns, so this PR is ready to be merged"}
{"title": "Bump model version", "number": 1130, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1130", "body": "Bump model version and starting to use date-based versions"}
{"title": "LDI-2", "number": 1131, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1131", "body": "worker_id - add uuid suffix to worker_id label\n\nadd unique (uuid) suffix to worker_id label - this is needed when running multiple containers on the same task so we can distinguish workers in metrics\n\n\n\nadd healthcheck port parameter\n\nthis is needed to avoid port collision between containers running on the same task\n\n\n\n"}
{"comment": {"body": "@{6370afc2f48fbd9b62d2ceb9} is this related to the health check port configuration?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1131/_/diff#comment-370170531"}}
{"title": "LDI-501 eliminate non popular devices from the device db", "number": 1132, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132", "body": "This is a temporary change. Please refer to {: data-inline-card='' }"}
{"comment": {"body": "Passed same branch regression: \\(updated\\)\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/29373](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/29373){: data-inline-card='' } \n\nSmoke\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/29371](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/29371){: data-inline-card='' }\n\nAPI\n\n [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/29372](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/29372){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132/_/diff#comment-370227134"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} can you update the regression tests? are they after the latest update?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132/_/diff#comment-370543774"}}
{"comment": {"body": "@{5d74d49897d8980d8eacd7f8} It looks like you have some UT failure.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132/_/diff#comment-370543969"}}
{"comment": {"body": "I see, It\u2019s a problem because the hostname module uses the devices from the devices DB for matching non-unique devices.  \nI\u2019ll change it so it will use the full devices DB table", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132/_/diff#comment-370553523"}}
{"comment": {"body": "will it affect performance? @{5d74d49897d8980d8eacd7f8} @{5a4500fe0cacf235de82a9d4} @{5dbeb866c424110de52552cc} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132/_/diff#comment-370563868"}}
{"comment": {"body": "Shouldn\u2019t affect performance", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1132/_/diff#comment-370576357"}}
{"title": "Protect the save debug data from missing key", "number": 1133, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1133", "body": "Make the save debug  skip the save part if a key is missing in the metadata from the agent and add a unit test.  \nThere is an issue though, because this case is not caught in the convert_ingestion_event_to_connection_event in the pipeline\nbecause the default values of empty strings are inserted.  Effectively the event is processed without any error being raised\neven if the data for  WirelessInfo is missing.\nIs this the desired behavior? @{5f82bf320756940075db755e} @{5a4500fe0cacf235de82a9d4} @{5dbeb866c424110de52552cc}   \ndef convert_event_msg(msg: EventMsg) - Optional[ConnectionResult]:\n    start_time = time.time()\n    session_bytes_size = len(msg)\n    try:\n        if msg.metadata.msg_type == MessageTypes.CONNECTION:\n            deserialized_session = IngestionEvent().parse(msg.data)\n            connection_event = convert_ingestion_event_to_connection_event(event=deserialized_session)\n\ndef set_wireless_info(session: ConnectionEvent, iface_info: ingestion_event_WirelessInfo) - ConnectionEvent:\n    info = WirelessInfo(\n        if_name=iface_info.if_name.decode(\"utf-8\"),\n        vap_name=iface_info.vap_name,\n        bssid=convert_ingestion_event_bssid(iface_info.bssid),\n        essid=iface_info.essid.decode(\"utf-8\"),\n        mode=iface_info.mode,\n        channel=iface_info.channel,\n"}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29253?launchesParams=page.page%3D2](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29253?launchesParams=page.page%3D2){: data-inline-card='' }   \nAPI passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29252?launchesParams=page.page%3D2](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29252?launchesParams=page.page%3D2){: data-inline-card='' }   \nSmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29251?launchesParams=page.page%3D2](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29251?launchesParams=page.page%3D2){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1133/_/diff#comment-371235843"}}
{"title": "Fail pipeline fast on lint and not after waiting 15 minutes", "number": 1134, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1134", "body": ""}
{"comment": {"body": "Good idea, but flake8 requires container built, no?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1134/_/diff#comment-370284372"}}
{"comment": {"body": "Yes. You are right. There isn\u2019t a real reason though, it just runs on the code.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1134/_/diff#comment-370284769"}}
{"comment": {"body": "one can use pre-commit so he/she shouldn\u2019t need to wait to the pipeline to finish", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1134/_/diff#comment-370285474"}}
{"title": "Quick lookup of features in models to improve runtime of classifier", "number": 1135, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135", "body": "Using python dictionaries instead of dataframes to lookup features in the different typing models around the system. The lookup dictionaries are generated during the loading of the different typing models.\nThis results in a major improvement of the total classifier runtime of ingesting events with more than 10% improvement on the scale test data set.\nBefore:  \n\nAfter:\n\n"}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29036](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29036){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29034](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29034){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29035](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29035){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370333330"}}
{"comment": {"body": "You are changing interface here and in other list to string conversions", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370336405"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} please link to Jira issue and share more details on the changes. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370336559"}}
{"comment": {"body": "It\u2019s not interfaces. Those are all internal structures.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370336748"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} not exactly they used in datacatalog if I recall", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370336916"}}
{"comment": {"body": "@{5f82bf320756940075db755e} The data is exported through the data catalog in the string format.\n\nThose changes did not break the data catalog as otherwise tests were to fail.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370338984"}}
{"comment": {"body": "why removing try except?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370341227"}}
{"comment": {"body": "It's not throwing an exception anymore as the db is a default dict.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-370345201"}}
{"comment": {"body": "Don\u2019t we need to make a change in device\\_class.py as well regarding the type of the dhcpv4\\_lookup field?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-371247128"}}
{"comment": {"body": "@{************************} No. Currently only the DHCP lookup result is stored in the DB so there isn\u2019t any change here. However, with the task you are currently working on \\([LDI-354](https://levltech.atlassian.net/browse/LDI-354)\\) there would be changes required once you merge with my changes.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1135/_/diff#comment-371248709"}}
{"title": "Fix skip condition after parsing mDNS packets", "number": 1136, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1136", "body": "Casting the mDNS packet structure to boolean causes an explicit cast to bytes which is a very heavy operation that totally amounts to 3% (!!) of the total classifier runtime.\nA simple comparison to None does the trick."}
{"comment": {"body": "Great catch, but please work with JIRA!!!!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1136/_/diff#comment-370336749"}}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29059](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29059){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29057](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29057){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29058](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29058){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1136/_/diff#comment-370339281"}}
{"title": "LDI-457 Fix CVE-2019-19814 in classifier", "number": 1137, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137", "body": ""}
{"comment": {"body": "Thos fixes the ecr issue? Very good!\n\n@{637a0dbba593cb822e9385aa} please see if this change affects your work, I recall you added this for M2 local work\u2026", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370488358"}}
{"comment": {"body": "Yes. The manual build of the librdkafka is to support kafka libraries on macbooks with Apple silicon M1/M2.  \nOtherwise you can\u2019t run any kafka library which is required to run the classifier locally.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370500724"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} so we should find another way, for example similar to testdocker, have lockaldocker. I less prefer it, but production should be clean from security vulrnerabilities. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370506208"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} can you try to support the local together with @{637a0dbba593cb822e9385aa} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370506518"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Of course we shouldn\u2019t have vulnerabilities in production. Hence I approved the commit :slight_smile: \n\nWe will figure out the local runners.  ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370507317"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} i know, my reply was also to explain for other m1/m2 user why we should merge this \ud83d\ude02", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370508804"}}
{"comment": {"body": "@{5f82bf320756940075db755e} Yes I will work with @{637a0dbba593cb822e9385aa} on this. A high level idea is to use this production image as a base image and rebuild `librdkafka` for local.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370529990"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} Sounds good", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-370530284"}}
{"comment": {"body": "@{634e54561db4d2ebcf611e5a} please share to engineering and all to use this command for local work on M1/M2", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1137/_/diff#comment-371252533"}}
{"title": "Remove usage of full object copy (deepcopy) in typing merge", "number": 1138, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1138", "body": "There is no actual need to copy feature objects (that might be large in some of the features) to do the typing merge functionality.\nThis shaves off additional 6% from the runtime of the system.\n"}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29190](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29190){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29189](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29189){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29188](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29188){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1138/_/diff#comment-370465605"}}
{"title": "LDI-493 update 16 3 update", "number": 1139, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1139", "body": "\n\nadd wispr& rpvr for 16.3 and 16.3.1\n{: data-inline-card='' }  API\n{: data-inline-card='' } Regression\n\n{: data-inline-card='' } Smoke"}
{"title": "Feature/wps uid", "number": 114, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/114", "body": "Now matching by wps uid as well.\nThis PR is for review, please dont merge until Ill update both eros-dev and eros-classifier tables with the new column."}
{"title": "Bugfix/LDI-520 ipad pro 5th l2 fp", "number": 1140, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1140", "body": "added missing l2 fp in XB7 for:\n\nipad pro 5th\nipad 9th\npixel 3\ngalaxy s10\ngalaxy s20\n\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29766](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29766){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29765](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29765){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29769](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/29769){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1140/_/diff#comment-371252860"}}
{"title": "Feature/LDI-536 pickle to incoded", "number": 1141, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1141", "body": "fixed UT and deleted pickle files\n"}
{"comment": {"body": "Regression is not relevant.  \nOnly made changes at UT", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1141/_/diff#comment-371442824"}}
{"title": "Remove unused code from obfuscation utils", "number": 1142, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1142", "body": ""}
{"comment": {"body": "One thing, can we add the original file code somewhere? if need to use later some of it ..\n\nIt didn\u2019t need to run or anything.. like resource file..", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1142/_/diff#comment-371436383"}}
{"comment": {"body": "Then what is the purpose of removing all the unused code?  \nWouldn\u2019t this be more confusing, to have 2 files, one with partial code and one with all the code but not used?  \nShould we just add a comment with the commit\u2019s sha from Cujo\u2019s repository?  \nI received this file in slack, I don\u2019t know it\u2019s origin details. Do we have the commit\u2019s sha?@{606d973d3e6ea000685ed65f} \n\n![](https://bitbucket.org/repo/o5KReBa/images/4188566626-Screenshot%20from%202023-02-20%2015-42-18.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1142/_/diff#comment-371456432"}}
{"comment": {"body": "I\u2019ll add it as a resource in the test utils", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1142/_/diff#comment-371481822"}}
{"comment": {"body": "Regression passed:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30331](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30331){: data-inline-card='' }   \nAPI passed:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30329](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30329){: data-inline-card='' }   \nSmoke passed:[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30330](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30330){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1142/_/diff#comment-371696141"}}
{"title": "scale test deploy", "number": 1143, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1143", "body": ""}
{"title": "Make some debug artifacts that depend on LOCAL_DEBUG flag depend on general DEBUG flag", "number": 1144, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1144", "body": "kafka_record.json and pcaps extracted from old events are saved in save debug only when the EROS_DEBUG_LOCAL flag is true. There is no reason for these specific artifacts to be dependant on local export, they can be exported to S3 only."}
{"title": "LDI-517 implement devices db statistical filtering", "number": 1145, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145", "body": "This script filters out devices from the devices db according to rules set by the product department. It is not intended to be used by any current automated process. In the future, we may want to be able to run it periodically based on changes the product department made for the rules and on the availability of new statistical data.\nThe files:\neros_pipelines/devices_db_statistical_filter/prevalence_thresholds.csv\neros_pipelines/devices_db_statistical_filter/hard_inclusions.csv\neros_pipelines/devices_db_statistical_filter/temporal_thresholds.json\nAre designed to be easily controlled by the product department and dictate the way the devices_db_tracked.csv table is filtered.\nCurrently there are just arbitrary rules I made up in those files as an example until product make their definitions. Once product make their rules, a second PR here will integrate the result of this script as a csv model into the classifier itself where all devices_db pointers will point to it."}
{"comment": {"body": "Let\u2019s discuss how product department can access production code, what was the design approach?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371765816"}}
{"comment": {"body": "The idea was that the production department is technical enough to change the rules in csv/json files. Any other ideas are welcome @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} @{5a4500fe0cacf235de82a9d4} ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371768001"}}
{"comment": {"body": "This logic should be performed in DWH as it relies on the data from production and devices and not production real data to build the devices db. ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371771006"}}
{"comment": {"body": "Got it with @{5a4500fe0cacf235de82a9d4} . it should be in sparrow\\_pipelines, not eros models. \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371771945"}}
{"comment": {"body": "@{5f82bf320756940075db755e} moved", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371774903"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} as we discussed, let\u2019s move to sparrow\\_pipelines", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371777554"}}
{"comment": {"body": "@{5f82bf320756940075db755e} I moved the code. These specific files however of device statistics are going to be used also by the new prior logic inside the classifier \\(LDI-32\\). It\u2019s just that it will take more time for Ehud to make the PR and merge the code for that. I put it in the same location he put it so that we don\u2019t duplicate the data.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371780591"}}
{"comment": {"body": "all csv/json files in `models` dir should use the LDNI infrastructure. consult @{6335b848a84c7f79c387915c} please", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371782924"}}
{"comment": {"body": "This is true for usage of models inside the classifier, so we can update them on the fly through our backdoor. This script will not run in eros. @{6335b848a84c7f79c387915c} please correct me if I\u2019m wrong", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371783634"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} I guess it\u2019s good for a starting points to use absolute counts, but wouldn\u2019t be better for the long run to use something like fractional counts / percentages to be easier to maintain for the long run, specifically if we would manage to incorporate multiple data sources into our statistics?\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371784717"}}
{"comment": {"body": "@{63b6c1aaf3e7004f77ff2273} I remember this was also suggested by @{63b2a7992c70aae1e6faa958} . Please comment on why this format was chosen in the end for the prior logic.\n\nAt any case, the code will work the same if this is switched to fractional counts, since the normalization will just come back to the same numbers.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371785376"}}
{"comment": {"body": "what is the propose of `specific_name_prob...csv` ? it seems like it is a duplicate of devices\\_db", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371791222"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} I\u2019ve put counts since in the prior logic I compute conditional probabilities out of the candidates.   \nThus I anyway need to compute probabilities online.  \nBut it is possible to add percentages as well.  \nAlso in the future we may add count depended confidence intervals to address cases of small samples.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371791668"}}
{"comment": {"body": "It has prevalence counts for every unique specific name that was found in the wild \\(so a subset of specific\\_names from the devices\\_db\\). Based on these statistics \\(generated by analyst team\\), the filtering of the actual devices db is performed.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371791804"}}
{"comment": {"body": "@{62e7c839e50f2f2a395430c2} I know it would be the same. \n\nI\u2019m just asking for the long run, as when the solution would be in production, it would be harder to make changes due to the need to maintain backward / forward compatibility between models and classifier versions.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371792135"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} I need the counts for the prior logic due to the reasons above, but we can add fractions or percentages as well for the device db statistical filtering.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371793405"}}
{"comment": {"body": "@{63b6c1aaf3e7004f77ff2273} No need to duplicate the same data :slight_smile: \n\nBut it looks like the absolute counts make it easier for the conditional probabilities, so lets keep it as is.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371798694"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} I confirmed with @{6335b848a84c7f79c387915c} , within this script there is nothing to be changed in the way the script currently loads files from the `models` directory. The output of the script will however need to be integrated into the LDNI paradigm, which is part of a future task LDI-547.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-371984152"}}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31016](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31016){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31017](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31017){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31018](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31018){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1145/_/diff#comment-372049238"}}
{"title": "multiply apple watches fps", "number": 1146, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1146", "body": ""}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30729](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30729){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30730](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30730){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30731](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30731){: data-inline-card='' } \n\nall passed", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1146/_/diff#comment-371805436"}}
{"comment": {"body": "passed local regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1146/_/diff#comment-371825144"}}
{"title": "LDI-110 implement classifier prior logic", "number": 1147, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1147", "body": "Hi,\nI send this pull request of the online code, but this would be merged only after the thresholds would be tuned according to the simulation results. \nPlease ignore prior_logic_simulation.py - it is not completed yet.\n{: data-inline-card='' }"}
{"comment": {"body": "most quotation marks are redundant", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1147/_/diff#comment-373551963"}}
{"comment": {"body": "@{63b6c1aaf3e7004f77ff2273} can you please link the design page to this PR?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1147/_/diff#comment-373922905"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1147/_/diff#comment-373929856"}}
{"comment": {"body": "is the simulation ready, I would like to push the code to the system", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1147/_/diff#comment-379553084"}}
{"comment": {"body": "Yes.  \nThe simulation code is in: prior\\_logic\\_simulation.py  \nThe simulation report is in: conflicts\\_simulation\\_report.json", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1147/_/diff#comment-379555486"}}
{"title": "LDI-507 Disable mac vendor feature for vendors that produce wifi chips for devices with other brands", "number": 1148, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1148", "body": "Some brands like Intel and Motorola are both brands of some devices in the devices db and also produce WiFi chips with their mac vendor prefix for other brands. Specifically we have seen Intel mac vendor on Lenovo laptops and Motorola mac vendor in Lenovo cellphones. This PR introduces a blacklist of vendors for which we cant trust that they are the device brand even if we get a match."}
{"comment": {"body": "Note to self @{557058:34460b9c-e072-4356-be8b-f73d5f8f675d} : We should added a task to the backlog to find more of those problematic vendors in the CUJO production data.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1148/_/diff#comment-371803179"}}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30769](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30769){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30770](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30770){: data-inline-card='' } \n\nRegression against same branch: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30775](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30775){: data-inline-card='' }  \\(only change in regression is that a Lenovo phone that used to be detected as a Motorola device is now a generic Android Mobile device.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1148/_/diff#comment-371965521"}}
{"title": "LDI-3344444 stam", "number": 1149, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1149", "body": "scale test deploy\nlndi version\n\n"}
{"comment": {"body": "It\u2019s not stam :upside_down: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1149/_/diff#comment-371800810"}}
{"title": "Bugfix/dhcp fingerprinting", "number": 115, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/115", "body": "Our device type identification used the wrong keyword for the dhcp fingerprinting, as @{5f82bf320756940075db755e} noticed yesterday. In the parser, we parse the following fields from DHCP packets:\n['dhcp_message_type', 'DHCPFP', 'dhcp_vendor', 'dhcp_hostname', 'dhcp_requested_ip', 'dhcp_server_identifier', 'dhcp_transaction_id', 'dhcp_lease_time', 'dhcp_renewal_time', 'dhcp_rebind_time', 'dhcp_client_mac_addr']\nbut in the device_type_identification component, we searched for a dhcp_fingerprint key, which didnt exist.\nThis PR fixes this name-mismatching."}
{"title": "added missing L2 fps for 5GHz", "number": 1150, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1150", "body": "added missing L2 fps for 5GHz for:\n\niPhone 7\niPad 9th gen\nPixel 2\nPixel 3\nGalaxy A21\n\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30948](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30948){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30949](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30949){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30951](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/30951){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1150/_/diff#comment-372007056"}}
{"comment": {"body": "Passed local regression. There are no changes in regression tests", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1150/_/diff#comment-372025494"}}
{"title": "LDI-567: Support classifier stop on init if schema registry init fails", "number": 1151, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1151", "body": "We have bug in our init process in classifier, which continues to work even if schema registry that enabled not registered for some reason. Now we would exit in case of such init with appropriate log and EKS/ECS will try reload till the service fixed or up.\nExample of use case can be seen in the bug itself"}
{"comment": {"body": "Shouldn\u2019t it be fatal error as well?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1151/_/diff#comment-372298436"}}
{"comment": {"body": "Fatal here as well?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1151/_/diff#comment-372298473"}}
{"comment": {"body": "![](https://bitbucket.org/repo/o5KReBa/images/3777784922-Screenshot%202023-02-23%20at%2013.00.02.png)\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1151/_/diff#comment-372355631"}}
{"title": "Conditional enablement of per-function profiling metrics. Disabled by default", "number": 1152, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1152", "body": "This reduces massively the amount of metrics the telemetry system have to process and send out. High loads on the system can cause the telemetry system queue to explode and flood the memory.\nSide effect of this change is also a reduction of the runtime of the classifier by ~10%."}
{"comment": {"body": "1. let\u2019s add in the values.yaml for deployment configuration and in the teraform. \n2. let\u2019s make sure it doesn\u2019t affect our grafana dashboard\n\n\u200c\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1152/_/diff#comment-372279373"}}
{"comment": {"body": "1. It\u2019s getting too much work to maintain all the different configurations of all the debug features we have in the system. I would really think we should just deleted this metric collection instead of maintaining it.\n2. Of course it\u2019s going to break the relevant panels in the dashboards.\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1152/_/diff#comment-372283205"}}
{"comment": {"body": "R: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31357](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31357){: data-inline-card='' }   \nA: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31356](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31356){: data-inline-card='' }   \nS: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31355](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31355){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1152/_/diff#comment-372340085"}}
{"title": "LDI-547 submit statistically filtered db", "number": 1153, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1153", "body": "Filter the full devices db according to statistics and general rules set by the product department."}
{"comment": {"body": "API: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31473](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31473){: data-inline-card='' } \n\nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31474](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31474){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31475](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31475){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1153/_/diff#comment-372437213"}}
{"comment": {"body": "don\u2019t forget to update version.yaml for the model tag", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1153/_/diff#comment-372446464"}}
{"comment": {"body": "updated", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1153/_/diff#comment-372463745"}}
{"title": "LDI-576 model version", "number": 1154, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1154", "body": "model version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\n\n"}
{"title": "LDI-576 model version", "number": 1155, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1155", "body": "Changes in model version\nAdded model version change step\nadded step for model version change\nadded step for model version change\nadded step for model version change\nadded step for model version change\nadded step for model version change\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\nmodel version test\n\n"}
{"title": "update model version", "number": 1156, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1156", "body": ""}
{"title": "My test branch", "number": 1157, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1157", "body": "update model version\nupdate model version\nupdate model version\n\n"}
{"title": "My test branch", "number": 1158, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1158", "body": "update model version\nupdate model version\nupdate model version\nupdate model version\n\n"}
{"title": "My test branch", "number": 1159, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1159", "body": "model test\nupdate model version\nupdate model version\nupdate model version\nupdate model version\ntest\n\n"}
{"title": "Fix dhcpfp comparison", "number": 116, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/116", "body": "Fixed the dhcp fingerprinting comparison - we didnt compare the right type (list to str of list) and one of the fingerprinting lists was wrong.\n"}
{"title": "My test branch", "number": 1160, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1160", "body": "model test\nupdate model version\nupdate model version\nupdate model version\nupdate model version\ntest\ntest\n\n"}
{"title": "test model version", "number": 1161, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1161", "body": ""}
{"title": "My test branch", "number": 1162, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1162", "body": "test model version\ntest model version\n\n"}
{"title": "spellcheck", "number": 1163, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1163", "body": "api: {: data-inline-card='' } \nsmoke: {: data-inline-card='' } \nregression: {: data-inline-card='' }"}
{"title": "reducing max-cognitive-complexity", "number": 1164, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1164", "body": "forcing simpler functions.\nregression:\n{: data-inline-card='' } \napi:\n{: data-inline-card='' } \nsmoke:\n"}
{"title": "test", "number": 1165, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1165", "body": ""}
{"title": "test", "number": 1166, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1166", "body": ""}
{"title": "LDI-594 xb7 l2 fp", "number": 1167, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1167", "body": "added l2 fp from cujo\nwithout iphone 14\n\n"}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} is a blocker.  \n**DON\u2019T MERGE THIS BRANCH WITHOUT HIS APPROVAL**\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1167/_/diff#comment-373113463"}}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31749](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31749){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31748](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31748){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31750](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31750){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1167/_/diff#comment-373114546"}}
{"title": "test", "number": 1168, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1168", "body": ""}
{"title": "Bugfix/LDI-508 apple watch resolution 1", "number": 1169, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169", "body": "changed general_os to Apple OS in devices DB for Apple devices and dealt with the consequences.\nmain changes:\n\nchanged get_row_numbers(db, DevicesDBSchema.general_os, device_os.family) to get_row_numbers(db, DevicesDBSchema.specific_os, device_os.family) in rpvr, wispr and dhcpv4\n\nchanged order of the typing revolver flow\n\ngenerally speaking, the prior logic should happen before finally deciding on the os\n\n\n\n\nThis change will be properly completed after the new prior logic will be merged aka {: data-inline-card='' }"}
{"comment": {"body": "Are there any regression changes?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373119848"}}
{"comment": {"body": "Yes, there is one change\n\n[https://bitbucket.org/levl/eros-automation/pull-requests/491](https://bitbucket.org/levl/eros-automation/pull-requests/491){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373121011"}}
{"comment": {"body": "degradation?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373237335"}}
{"comment": {"body": "move to a function please. this makes the code more encapsulated and readable", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373237975"}}
{"comment": {"body": "This is how it was in the code,I only changed the order.\n\nThe change you ask will happen by Ehud\u2019s code for LDI-32", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373243177"}}
{"comment": {"body": "No, just the correct answer according to the features available ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373243437"}}
{"comment": {"body": "Please choose a better name for this variable other than \u201cdataframe\u201d", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373243887"}}
{"comment": {"body": "This was the name used for all the functions in this file.\n\nThe change you ask will happen by Ehud\u2019s code for [LDI-32](https://levltech.atlassian.net/browse/LDI-32)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373245693"}}
{"comment": {"body": "Stuck on regression runs", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1169/_/diff#comment-373276015"}}
{"title": "DHCP parser to return dataclasses", "number": 117, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/117", "body": "Phase 1 of improving our code and reducing the chance of code errors with keys mismatching. Instead of returning raw dictionaries from the dhcp parser, from now on well return dataclasses, and getting and setting values of those dataclasses will be unified all over the code (no more strings errors)"}
{"title": "Minor Efficacy adjustments 26/02/2023", "number": 1170, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1170", "body": "Add watch UAs to blacklist as they are sent by iPhones\nAdd iPad 8th gen FP\nRemoved prior rule for iPods, as those are after the inclusion of LDI-79\n\n"}
{"comment": {"body": "R: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31881](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31881){: data-inline-card='' }   \nA: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31870](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31870){: data-inline-card='' }   \nS: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31871](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/31871){: data-inline-card='' }\n\n\u200c\n\nAutomation PR here: [https://bitbucket.org/levl/eros-automation/pull-requests/490](https://bitbucket.org/levl/eros-automation/pull-requests/490){: data-inline-card='' }", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1170/_/diff#comment-373120697"}}
{"comment": {"body": "regex applies also to other vendors watches other than Apple?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1170/_/diff#comment-373238701"}}
{"comment": {"body": "Yes. It probably would apply as well.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1170/_/diff#comment-373250096"}}
{"comment": {"body": "@{5a4500fe0cacf235de82a9d4} is it the requested behaviour? ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1170/_/diff#comment-373250598"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} For now, yes.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1170/_/diff#comment-373251453"}}
{"title": "Feature/LDI-284 db device schema compatibility", "number": 1171, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1171", "body": "Add the base for schema versioning logic to support forward/backward compatibility\n\nAdd version field to the class\nChange certain fields to be mandatory, those that relate to connection counts and time.\nAdd a global variable as a placeholder that denotes the current schema version. This should be replaced with a full injection of schema version when the process of this update will be defined.\nMake the serialization and de-serialization part of the Device class API. Prepare an error that will be raised if the de-serialization fails. This is on order to encapsulate the way the device is de-serialized from the rest of the system.\nMake the transition from db record to Device object and vice versa part of the DB module's API.\nAdd a column of schema version as an attribute when inserting a device into the DB. This will enable to detect the schema version before de-serializing if we wish to do so.\nAdd unit test to the device class and DB module.\n\n\n\n"}
{"title": "test model version", "number": 1172, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1172", "body": ""}
{"title": "test model version", "number": 1173, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1173", "body": ""}
{"title": "LDI-602 use correct field in user agent validity check", "number": 1174, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1174", "body": ""}
{"comment": {"body": "Smoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32210](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32210){: data-inline-card='' } \n\nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32211](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32211){: data-inline-card='' } \n\nRegression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32212](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32212){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1174/_/diff#comment-373885798"}}
{"title": "My test branch", "number": 1175, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1175", "body": "test model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\ntest model version\n\n"}
{"title": "test model version", "number": 1176, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1176", "body": ""}
{"title": "My test branch", "number": 1177, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1177", "body": "test model version\ntest\n\n"}
{"title": "test model version", "number": 1178, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1178", "body": ""}
{"title": "Model test branch", "number": 1179, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1179", "body": "test changes\ntest changes\ntest changes\n\n"}
{"title": "Bugfix/wps parsing", "number": 118, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/118", "body": "Fixed wps parsing and serialization. Fixed the parsing of icmp mclr"}
{"title": "test changes", "number": 1180, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1180", "body": ""}
{"title": "My test branch", "number": 1181, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1181", "body": "test changes\ntest changes\n\n"}
{"title": "update models latest version", "number": 1182, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1182", "body": ""}
{"title": "Fixes", "number": 1183, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1183", "body": ""}
{"title": "Model test branch", "number": 1184, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1184", "body": "Fixes\nFixes\nChanges\nUpdate version to 2023_03_01_14:21-753a366 [skip ci]\nModel Version Changes\n\n"}
{"title": "Model Version Changes", "number": 1185, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1185", "body": ""}
{"title": "Model Version Changes", "number": 1186, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1186", "body": ""}
{"title": "Final Model Version Changes", "number": 1187, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1187", "body": ""}
{"title": "Final Model Version Changes", "number": 1188, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1188", "body": ""}
{"title": "LDI-576 Model Version", "number": 1189, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1189", "body": "Final Model Version Changes\nTest\nTest\nFinal Model Version Changes\nUpdate version to 2023_03_01_18:06-f74cf6d [skip ci]\nFinal Model Version Changes\n\n"}
{"title": "Feature/code coverage", "number": 119, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/119", "body": "add report portal for development ut - parser test\nsupport pytest code coverage with html reports\n\n"}
{"comment": {"body": "shouldn\u2019t file be named `conftest.py`?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/119/_/diff#comment-242994054"}}
{"title": "Test", "number": 1190, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1190", "body": ""}
{"title": "Test", "number": 1191, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1191", "body": ""}
{"title": "Testing Merge Commit Changes", "number": 1192, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1192", "body": ""}
{"title": "Testing Merge Commit Changes", "number": 1193, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1193", "body": ""}
{"title": "LDI-780 update models", "number": 1194, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1194", "body": "ios 16.4 models from cujo's data + l2 fp\nLDI-782 l2 fp\n\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32544](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32544){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32545](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32545){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32546](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/32546){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1194/_/diff#comment-374908900"}}
{"title": "Feature/LDI-794 disable ongoing events processing", "number": 1195, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1195", "body": "Add the ability to drop ongoing events without processing them\nThe messages that are pulled from the Kafka consumer queue are being inspected and the message type is determined by the metadata.\nIf the configuration indicates to drop ongoing events, the processing will not continue any further after this part for ongoing events.\nA dedicated metric to count how often this occurs was added.\n"}
{"title": "update mac_vendor model", "number": 1196, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1196", "body": ""}
{"comment": {"body": "The vendors which in devices db need to be in th same name here\n\nfor example, `Microsoft Corporation` needs to be `Microsoft`\n\nand `C2AB0,\"Beijing Xiaomi Electronics Co.,Ltd\"`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1196/_/diff#comment-376322228"}}
{"comment": {"body": "and need to update the model version `models/src/v1/models/version.yaml`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1196/_/diff#comment-376327740"}}
{"comment": {"body": "Done", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1196/_/diff#comment-376367895"}}
{"comment": {"body": "it was just updated which made a conflict", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1196/_/diff#comment-376368010"}}
{"comment": {"body": "all passed  \n[Regression\\_prod:automation:24736-dev-ldi-782-update-mac-vendox-classifier::LDI-782\\_update\\_mac\\_vendor](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/33312)  \n[API:automation:11039-dev-ldi-782-update-mac-vendox-classifier::LDI-782\\_update\\_mac\\_vendor](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/33311)  \n[Smoke:automation:27802-dev-ldi-782-update-mac-vendox-classifier::LDI-782\\_update\\_mac\\_vendor](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/-1/33310)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1196/_/diff#comment-376407190"}}
{"title": "Feature/LDI-354 Reduce Device Record Sizes", "number": 1197, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1197", "body": "This PR introduces a new phase in our efforts to reduce the size of our device records in the DB. A detailed description can be reviewed here.\nWe have two new methods that help up to achieve that goal - feature_to_db() and feature_from_db(), which are now a part of our base class of MainFeature. The logic described in them help us to write the minimal amount of data we can to each of our typing features, and read it back up from the DB and restore the feature data and result.\nSome new tests were added, along with changes in the DB schema to much more simpler types (since were trying to keep the bare minimum of data). For example, instead of storing the following value in our WisprAgent typing feature:\n\\\"wispr_agent\\\": [{\\\"vendor\\\": \\\"Apple\\\", \\\"os\\\": {\\\"family\\\": \\\"iOS\\\", 2\\\"start_os\\\": {\\\"version\\\": \\\"15.6\\\"}, \\\"end_os\\\": {\\\"version\\\": \\\"15.6\\\"}, 3\\\"ui\\\": \\\"iOS 15.6\\\"}}, {\\\"vendor\\\": \\\"Apple\\\", \\\"os\\\": {\\\"family\\\": \\\"watchOS\\\", 4\\\"start_os\\\": {\\\"version\\\": \\\"8.7\\\"}, \\\"end_os\\\": {\\\"version\\\": \\\"8.7\\\"}, \\\"ui\\\": 5\\\"watchOS 8.7\\\"}}, {\\\"vendor\\\": \\\"Apple\\\", \\\"os\\\": {\\\"family\\\": \\\"iOS\\\", \\\"start_os\\\": 6{\\\"version\\\": \\\"15.7\\\"}, \\\"end_os\\\": {\\\"version\\\": \\\"15.7\\\"}, \\\"ui\\\": \\\"iOS 15.7\\\"}}, 7{\\\"vendor\\\": \\\"Apple\\\", \\\"os\\\": {\\\"family\\\": \\\"iPadOS\\\", \\\"start_os\\\": {\\\"version\\\": 8\\\"15.7\\\"}, \\\"end_os\\\": {\\\"version\\\": \\\"15.7\\\"}, \\\"ui\\\": \\\"iPadOS 15.7\\\"}}, 9{\\\"vendor\\\": \\\"Apple\\\", \\\"os\\\": {\\\"family\\\": \\\"iPadOS\\\", \\\"start_os\\\": {\\\"version\\\": 10\\\"15.6\\\"}, \\\"end_os\\\": {\\\"version\\\": \\\"15.6\\\"}, \\\"ui\\\": \\\"iPadOS 15.6\\\"}}]\nWell store just the value itself of \\\"wispr_agent\\\": \\443.40.1\\."}
{"title": "LDI-594 new l2 fp from lab", "number": 1198, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1198", "body": "added missing macbook to devices db\nadded l2 fps from ios 16.3 as planned in the story\n\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33135](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33135){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33136](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33136){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33137](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33137){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1198/_/diff#comment-376324466"}}
{"comment": {"body": "FYI @{5a4500fe0cacf235de82a9d4}   \nWe didn\u2019t see new fps in the watches with the newest watchOS version \\(9.3.1\\).  \nCheck on Watch SE and Watch 7.\n\n@{63b2a7992c70aae1e6faa958}", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1198/_/diff#comment-376324926"}}
{"comment": {"body": "Thanks for the info!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1198/_/diff#comment-376326563"}}
{"title": "LDI-807 Add Askey Wifi6 platform type", "number": 1199, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1199", "body": "Support for Askey Wifi6 in the systems platform types.\n\n"}
{"comment": {"body": "Reverted the default UNKNOWN and will include it in a separate PR where all of the consequences will be examined", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1199/_/diff#comment-376437606"}}
{"comment": {"body": "Regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33460](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33460){: data-inline-card='' }   \nSmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33458](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33458){: data-inline-card='' }   \nAPI: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33457](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33457){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1199/_/diff#comment-376452832"}}
{"title": "Features leftover", "number": 12, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/12", "body": "Extend device type identification with dhcpv4 identification\nUpdate matchmaker interface to append to previous output\nfix formatting with black\n\n"}
{"title": "Bugfix/DHCP hostname extraction", "number": 120, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/120", "body": "Even though this branch initially intended to fix the none-ipv4-value of the connected devices, turns out the problem was at the agent side and indeed was fixes there in PR-19.\nAfter solving the problem in the agent, we started parsing DHCP ACK packets, which we didnt do before, and this PR makes sure we only try to extract the DHCP hostname from the DHCP Request packets, and not the ACK packets, since that fields doesnt appear there."}
{"title": "LDI-806", "number": 1200, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1200", "body": "scale test deploy\nfix Raw events byte size metric calculations are wrong\nLDI-806: fix log on exception\n\n"}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33415](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33415){: data-inline-card='' }  - Smoke\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33417](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33417){: data-inline-card='' }  - API\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33419](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33419){: data-inline-card='' }  - Regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1200/_/diff#comment-376435648"}}
{"title": "added latest to test req and advanced versions", "number": 1201, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1201", "body": "regression: {: data-inline-card='' }\nsmoke: {: data-inline-card='' }\napi: {: data-inline-card='' }\ncodecov is unnecessary and deprecated"}
{"comment": {"body": "@{6265307b185ac200692f9bd9} This is not compatible with `eros-classifier/api/setup.py`\n\n```python\n\"packaging~=21.3\",\n```\n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1201/_/diff#comment-387706286"}}
{"comment": {"body": "do you see any problem between the versions?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1201/_/diff#comment-387749529"}}
{"comment": {"body": "@{6265307b185ac200692f9bd9} no, but the `make install` command will not install `packaging==23.0`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1201/_/diff#comment-387762145"}}
{"title": "reducing cognitive complexity", "number": 1202, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1202", "body": "I will wait with the merge of this until the tag will be released.\nregression: {: data-inline-card='' } \napi:\nsmoke:{: data-inline-card='' }"}
{"title": "LDI-819 flake8 boolean trap", "number": 1203, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1203", "body": "I recommend you to watch {: data-inline-card='' }\ntl;dr\nThis PR makes the code more readable and less error-prone.\nusing *, in the list of arguments to a function, it says that all arguments after that should be called with their name.\nI will wait with the merge of this until the tag will be released.\napi: {: data-inline-card='' } \nsmoke: {: data-inline-card='' } \nregression: "}
{"title": "LDI-820 askey cujo l2", "number": 1204, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1204", "body": "l2 fp from askey cujo\n"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33684](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33684){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33683](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33683){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33685](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33685){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1204/_/diff#comment-377339078"}}
{"title": "LDI-820 askey cujo l2", "number": 1205, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1205", "body": "updated model version\n"}
{"comment": {"body": "regression isn\u2019t relevant", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1205/_/diff#comment-377340956"}}
{"title": "Bugfix/LDI-805 Bonjour and DUID as mac addresses", "number": 1206, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1206", "body": "The system extracts the MDNS_BONJOUR_UID and DHCPV6_DUID_MAC_ADDR values not as MAC addresses and therefore cause the full data to be obfuscated instead of just the low octets of the MAC address.\nFor example,\n| Datatype | Current | Desired Behaviour |\n| --- | --- | --- |\n| MDNS_BONJOUR_UID | \"2a77f1766556\" | \"2a:77:f1:76:65:56\" |\n| DHCPV6_DUID_MAC_ADDR | \"2877f1766556\" | \"28:77:f1:76:65:56\" |\nThis PR fixes that problem to make sure that the data catalog gets the data properly. It requires some changes in the parsing and minor modifications in the matching procedure."}
{"comment": {"body": "Regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33893](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33893){: data-inline-card='' } \n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33892](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33892){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33891](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/33891){: data-inline-card='' } \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1206/_/diff#comment-377527230"}}
{"comment": {"body": "typing hint is incorrect. consider `if mac else \u201d\u201d` \\(recommended\\) or `Optional[str]`", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1206/_/diff#comment-377566924"}}
{"comment": {"body": "Fixed :slight_smile: ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1206/_/diff#comment-377604661"}}
{"title": "add MDNS_INFO data to feature log", "number": 1207, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1207", "body": "Regression Tests: Link\nSmoke Tests: Link\nAPI Tests: Link"}
{"comment": {"body": "@{6335b848a84c7f79c387915c} you meant feature log :wink:. nice job", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1207/_/diff#comment-377659183"}}
{"comment": {"body": "Please add it here with the appropriate OBFS - [https://getcujo.atlassian.net/wiki/spaces/LEVL/pages/3886645261/LEVL+-+Governance+Process+-+Data+Models+Features+log](https://getcujo.atlassian.net/wiki/spaces/LEVL/pages/3886645261/LEVL+-+Governance+Process+-+Data+Models+Features+log){: data-inline-card='' }   \nAlso add it here and create a PR - [https://github.com/getCUJO/labs-data-documentation/blob/master/docsrc/schemas/levl\\_result\\_log.json](https://github.com/getCUJO/labs-data-documentation/blob/master/docsrc/schemas/levl_result_log.json) \n\nAdvise Vladimir from Cujo regarding Cujo labs and LENS part.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1207/_/diff#comment-377793255"}}
{"title": "LDI-810 Fixed the mdns dict parsing", "number": 1208, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1208", "body": "The MDNS dict data didnt contain proper values in the data catalog in the info field (its always empty). For example:\n{'type': '16', 'name': 'iPhone-GQVRW92JCW._device-info._tcp.local', 'info': \"b''\"}\nThis PR fixes that problem - when looking at the sessions and the data, it turned out that we need to look at the rdata field of the AN part of the packet, instead of the data field."}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34060](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34060){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34059](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34059){: data-inline-card='' } \n\nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34061](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34061){: data-inline-card='' }   ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1208/_/diff#comment-377645605"}}
{"title": "LDI-748 Place EventMsg save before potential fail", "number": 1209, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1209", "body": "Since there is no way to use the to_dict() function from the betterprotto library with an ingestion event that holds an unknown enum as one of its messages, and the save debug data module is wrapped with an exception handler to prevent the pipeline from failing to proceed, the fix is to move the logic that comes after the to_dict() call to be executed before that might happen, in order to ensure they are executed."}
{"comment": {"body": "move this line one up", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1209/_/diff#comment-377802256"}}
{"comment": {"body": "OK", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1209/_/diff#comment-377804343"}}
{"comment": {"body": "regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34380](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34380){: data-inline-card='' }   \napi passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34378](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34378){: data-inline-card='' }   \nsmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34377](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34377){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1209/_/diff#comment-377863170"}}
{"title": "Bugfix/icmp mclr comparing", "number": 121, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/121", "body": "Fixed the icmpv6_mclr addresses extracting and comparison mechanism in our code - the addresses are chopped (as in our pilot system) and now used as a feature when a new device is connected."}
{"title": "LDI-780 askey cujo l2 2", "number": 1210, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1210", "body": "misisng l2 fp from today Cujos recordings"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34355](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34355){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34354](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34354){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34357](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34357){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1210/_/diff#comment-377841949"}}
{"title": "LDI-822 fix produce key according agent id", "number": 1211, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1211", "body": "Our Architecture defines the partitioning according household, vauld_id. Cujo STOMP edge sends us events to Kafka topic with agent_id partitioning key. We are extracting vault_id from agent_id today and once returning results we should also produce back to Kafka results topics partitioned by agent_id."}
{"comment": {"body": "[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34547](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34547){: data-inline-card='' }  - API\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34548](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34548){: data-inline-card='' }  - Smoke\n\n[http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34549](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34549){: data-inline-card='' }  - Regression", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1211/_/diff#comment-378201810"}}
{"comment": {"body": "Is there any test for that?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1211/_/diff#comment-378318846"}}
{"title": "Feature/LDI-794", "number": 1212, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1212", "body": "update models latest version\nLDI-794: add to hel charts and bitbucket pipelines for cujo-stage\n\n"}
{"title": "Add the ability to disable the match making service call", "number": 1213, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1213", "body": "Add a flag that will make the classifier skip the match making for every connection event. \n  Additional functionality that will be skipped is the de-duplication process. The 2 cases that are being taken care of are:\n  1. The device is recognized by its MAC address -> do everything in the pipeline except de-duplicating\n  2. The device's MAC address doesn't exist in the vault -> the event is as good as dropped, since there is no device \n      entity to connect any data or logic to.\nAdd unit tests with env file and call in make file\nAdd a script that enables to write devices that are the results of event injection to the system to json files. It can be used in conjunction with the DB module in order to populate the DB without the need to trigger all the pipeline.\n\n"}
{"comment": {"body": "Regression passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34968](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34968){: data-inline-card='' }   \nAPI passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34966](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34966){: data-inline-card='' }   \nSmoke passed: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34967](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34967){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1213/_/diff#comment-378754180"}}
{"comment": {"body": "why do we need to run this script?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1213/_/diff#comment-378758905"}}
{"comment": {"body": "In order to check the new feature, a state where the DB holds 2 devices was required.  \nThe thing is, that this state could not be created by calling the pipeline logic, because the   \nfeature that is tested is the disabling of the match making which creates the devices.  \nThe solution was to create device records that can be read an inserted directly to the DB. It should be noted that the records are not trivial because they create 2 models for the same device.  \n@{5dbeb866c424110de52552cc} asked that this scrip is kept as a tool, in the case that these records need to change for some reason.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1213/_/diff#comment-378820623"}}
{"comment": {"body": "I approve this message", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1213/_/diff#comment-378905965"}}
{"title": "LDI-855 Fixed LDI-169 test and added it to our pipeline", "number": 1214, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1214", "body": "@{5fd5d5149edf2800759cc96d} came across the fact that one of our tests failed in our dev branch. It turned out that this test was not a part of our pipeline, and you could see the fail only if you tried to run all the tests locally.\nAlthough that test did fail, it wasnt a bug in our code, just some adjustments we needed to do in the test itself since the structure of our device class was changed a few times recently in our last PRs.\nThis PR fixes the test by matching the structure to as it should be, and adds the test to our pipeline."}
{"comment": {"body": "Regression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34951](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34951){: data-inline-card='' } \n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34949](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34949){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34950](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/34950){: data-inline-card='' }  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1214/_/diff#comment-378745044"}}
{"title": "LDI-812 bugfix", "number": 1215, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1215", "body": "filter in portion of the data and filter out black listed vaults and partners\nadd tests\n\n"}
{"comment": {"body": "If you don\u2019t mind, please convert this complicated list comprehension to a normal for. this will make the code more readable easier to debug in terms of breakpoints.", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1215/_/diff#comment-379547781"}}
{"comment": {"body": "regression: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35223/9047435](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35223/9047435){: data-inline-card='' } \n\napi: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35222](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35222){: data-inline-card='' } \n\nsmoke: [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35221](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35221){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1215/_/diff#comment-379551613"}}
{"title": "add comcast & charter pipelines", "number": 1216, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1216", "body": ""}
{"title": "adding missing L2 fp of XB7", "number": 1217, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1217", "body": "adding missing L2 fp of XB7"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35536](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35536){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35537](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35537){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35540](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35540){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1217/_/diff#comment-378931105"}}
{"title": "Add configuration variable & change chart version", "number": 1218, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1218", "body": ""}
{"title": "merge_commit_check.sh edited online with Bitbucket", "number": 1219, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1219", "body": "merge_commit_check.sh edited online with Bitbucket"}
{"title": "Bugfix/eros-82 hw mac update", "number": 122, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/122", "body": "Scenario: Connecting a device with a rand mac, and later on connecting it again with hw mac.\nThe hw mac was not updated in the DB and stayed null.\n\nOn the way, this PR is fixing some python type hints and removing unused code :)"}
{"title": "Update both matching & ongoing control flags after validation", "number": 1220, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1220", "body": "During a validation session with @{6252eba45d1e700069ad0104} and @{606d973d3e6ea000685ed65f} we found a typo in configmap.yaml and bitbucket-pipelines.yml regarding the new flags added recently. A dry-run + quick deployment test was done to verify that the variables are being propogated properly."}
{"title": "Update darwin model", "number": 1221, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1221", "body": "(operating_system)#Darwin_20_onwards{: data-inline-card='' }"}
{"comment": {"body": "Regression passed against same branch name [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35675?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35675?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' }\n\nAPI [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35674?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35674?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35673?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/35673?item0Params=filter.eq.hasStats%3Dtrue%26filter.eq.hasChildren%3Dfalse%26filter.in.type%3DSTEP%26filter.in.status%3DPASSED){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1221/_/diff#comment-379702826"}}
{"comment": {"body": "I think it\u2019s a good idea to add watchOS and tvOS support for the next update", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1221/_/diff#comment-379791979"}}
{"comment": {"body": "Does the classifier knows to check and use the watchOS and tvOS?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1221/_/diff#comment-379798787"}}
{"comment": {"body": "Yes it can read the column names arbitrarily", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1221/_/diff#comment-379805845"}}
{"title": "test", "number": 1222, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1222", "body": ""}
{"title": "Small Fix", "number": 1223, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1223", "body": ""}
{"title": "Set MDNS_INFO feature's category to MODEL", "number": 1224, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1224", "body": ""}
{"title": "duplicated apple watches 2.4GHz to all watches", "number": 1225, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1225", "body": "L2 fps are duplicated to all of all apple watches"}
{"comment": {"body": "A [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36080](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36080){: data-inline-card='' }   \nS [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36079](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36079){: data-inline-card='' }   \nR [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36081](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/36081){: data-inline-card='' } ", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1225/_/diff#comment-379769620"}}
{"title": "Set mdns info category to model", "number": 1226, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1226", "body": "[skip ci] bumped eros-chart new version 0.87.0, build 12112\nSet MDNS_INFO feature's category to MODEL\n\n"}
{"title": "LDI-576 Model Version Test 1", "number": 1227, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1227", "body": "test\ntest\n\n"}
{"title": "test", "number": 1228, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1228", "body": ""}
{"title": "Minor Change", "number": 1229, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1229", "body": ""}
{"title": "Feature/netbiod transaction ID", "number": 123, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/123", "body": "Now matching devices by their netbios transaction ID values, as we did in our pilot server.\n"}
{"title": "LDI-858 Fixed the prior logic indication in the decision log", "number": 1230, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1230", "body": "Good job @{63b2a7992c70aae1e6faa958} for finding this bug!\nIt turns out that by mistake we always set the prior logic indication to False even we actually did use that logic.\nThis PR adds fixes that indication assignment and adds a relevant test"}
{"comment": {"body": "Do you need to compare the varaible to some result?  \nfor example, True?\n\nor because it\u2019s boolean it works?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1230/_/diff#comment-380131115"}}
{"comment": {"body": "I don\u2019t think this assert is relevant for the test", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1230/_/diff#comment-380131261"}}
{"comment": {"body": "`device_intelligence_log.context_details_was_prior_logic_used` is a boolean value, so asserting it means making sure it\u2019s True", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1230/_/diff#comment-380131530"}}
{"comment": {"body": "It is, because the prior\\_flag can be retrieved from the DB after a matching, and I want to make sure the flag was affected by this solo connection and this only", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1230/_/diff#comment-380152992"}}
{"comment": {"body": "API [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/965](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/965){: data-inline-card='' } \n\nSmoke [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/964](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/964){: data-inline-card='' } \n\nRegression [http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/966](http://reportportal.automation.vpc.eros-prod.levltech.io/ui/#automation/launches/all/966){: data-inline-card='' }  \n\n\u200c", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1230/_/diff#comment-382778751"}}
{"title": "Set cujo regression pipeline", "number": 1231, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1231", "body": ""}
{"title": "Set cujo regression pipeline", "number": 1232, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1232", "body": ""}
{"title": "LDI-849", "number": 1233, "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233", "body": "As part of {: data-inline-card='' } \nwe wanted to:\n\nRemove metrics thread\n\nReduce I/O - by metrics batching \n\nUse BufferdList class to act as the metrics client internal buffer.\nUse micro batches to optimize shipped metrics batch size.\nadd new and more in-depth tests  \n\n\n\n"}
{"comment": {"body": "why only 100? do we have some suggested number like in influxdb batch size?\n\n\u200c\n\n5000 lines\n\nBatch writes\n\nWrite data in batches to minimize network overhead when writing data to InfluxDB. The optimal batch size is **5000 lines of line protocol**.\n\n[https://docs.influxdata.com/influxdb/v2.6/write-data/best-practices/optimize-writes/#:~:text=InfluxDB scrapers-,Batch writes,5000 lines of line protocol.](https://docs.influxdata.com/influxdb/v2.6/write-data/best-practices/optimize-writes/#:~:text=InfluxDB%20scrapers-,Batch%20writes,5000%20lines%20of%20line%20protocol.)", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233/_/diff#comment-380117793"}}
{"comment": {"body": "@{606d973d3e6ea000685ed65f} please redirect to release/phase\\_1 branch", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233/_/diff#comment-380134532"}}
{"comment": {"body": "this is just the assignment for the tests env var, no need to be bigger than that for the ut", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233/_/diff#comment-380149103"}}
{"comment": {"body": "very nice!", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233/_/diff#comment-380390528"}}
{"comment": {"body": "is this belong to this PR?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233/_/diff#comment-380391212"}}
{"comment": {"body": "?", "htmlUrl": "https://bitbucket.org/levl/eros-classifier/pull-requests/1233/_/diff#comment-380391275"}}
