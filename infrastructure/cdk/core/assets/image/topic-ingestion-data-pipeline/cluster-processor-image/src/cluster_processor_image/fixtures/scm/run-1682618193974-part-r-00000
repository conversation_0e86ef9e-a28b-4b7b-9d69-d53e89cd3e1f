{"title": "Everyone needs a little magic", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1"}
{"comment": {"body": "Are you sure about that?\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=6d11c1af-17f1-44f6-b9ac-ffde5b5aa12e).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r894846423"}}
{"comment": {"body": "Why can\u2019t you send a duck to space? Because the bill would be astronomical.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=c56e71ed-7821-4bea-a013-89b88f8e128d).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r894846685"}}
{"comment": {"body": "Hi Tim :) \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=1d727403-f647-4685-b0a7-6c0530804e68).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r894846834"}}
{"comment": {"body": "Tough crowd...\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=40e5df4a-af22-462b-80eb-4380b02bc6ee).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r894848378"}}
{"comment": {"body": "Tim says you're a shiny rock in a sea of mud! :)\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=7ab5dfa8-a972-4fb1-9848-35752c6e308c).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r894848551"}}
{"comment": {"body": "Can't wait to incorporate all this relevant feedback \n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=003051cd-ce3d-4cfc-9006-1974be225cb1).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r894849159"}}
{"comment": {"body": "What happens if someone doesn't believe in magic? How do we handle that case?", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r884975623"}}
{"comment": {"body": "I'll add a Muggle case\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=be2ccf34-a73e-4a86-9d57-b7bacd1dc9bf).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r885001396"}}
{"comment": {"body": "A Muggle is a person who lacks any sort of magical ability and was not born in a magical family. Muggles can also be described as people who do not have any magical blood inside them. It differs from the term Squib, which refers to a person with one or more magical parents yet without any magical power or ability.\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=6ce8aa86-301f-4573-9894-cc187ef1140c).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r886942178"}}
{"comment": {"body": "Good suggestion, and Spell needs a null case for both\n\n###### <a href=\"https://getunblocked.com\"><img src=\"https://avatars.githubusercontent.com/in/166219\" width=20 align=\"center\" /></a>&nbsp;&nbsp;This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/64450e97-0148-4ca2-95cc-636eadce8a10?message=32568f63-85c0-429a-b71d-13e70f7ce90a).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/1#discussion_r886990712"}}
{"title": "Update Main App Styling", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2", "body": "Increases base button size & adds some margin to the top."}
{"comment": {"body": "This is a pretty big bump - why 80px?", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2#discussion_r885027110"}}
{"comment": {"body": "Can you attach a before / after screen shot to compare these diffs?", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2#discussion_r885027243"}}
{"comment": {"body": "Prefer to use Winston logger:\r\nhttps://geshan.com.np/blog/2021/01/nodejs-logging-library/#winston", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2#discussion_r885136159"}}
{"comment": {"body": "Before:\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/170d9722-b650-44e6-b0c8-bf0f6c8802cf)\n\nAfter:\n\n![](https://getunblocked.com/assets/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/08c3fdc9-1737-4100-8b5c-3064552ad9a8)\n\n\n\n\n\n###### This comment was created from [Unblocked](https://getunblocked.com/dashboard/team/2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417/thread/2e59f052-5b29-43f3-946b-ce4fda9e25c8?message=0646094b-d678-4ac7-88ae-4758a695ed28).", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2#discussion_r885889924"}}
{"comment": {"body": "We wanted a larger hitbox for mobile device users. The buttons were a bit too small for touch screens. ", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2#discussion_r886939420"}}
{"comment": {"body": "But mobile sucks!", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/2#discussion_r887197127"}}
{"title": "Inline color and bgColor values", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/3", "body": "Creating variables here is unnecessary."}
{"comment": {"body": "Nice refactor, but please add tests that align with our Testing Strategy policy doc here: https://www.notion.so/acme-org/Testing-Strategy-3c29651e7a70426099dc26f44e3218b8\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/3#discussion_r885135540"}}
{"title": "Main backup", "number": 4, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/4"}
{"comment": {"body": "This is interesting...", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/4#discussion_r885162328"}}
{"title": "Updating the title", "number": 5, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/5", "body": "There was an extra unneeded space that should be removed - this does that."}
{"comment": {"body": "Looks good, thanks for taking care of this!", "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/5#discussion_r894824685"}}
{"title": "Updating the game title", "number": 6, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/6"}
{"title": "Update index.html", "number": 7, "htmlUrl": "https://github.com/NextChapterSoftware/2048/pull/7"}
{"title": "move dir", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/WebRTC-Builder/pull/1"}
{"title": "Add assets", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/1"}
{"title": "Add colorized assets for gitlab and bitbucket", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/2"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/2#pullrequestreview-1382069410", "body": ""}
{"title": "Update organization of brand assets", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/3", "body": "put external brands into brands/ folder\nadd github asset"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/3#pullrequestreview-1382189518", "body": ""}
{"title": "Add slack", "number": 4, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/4"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/4#pullrequestreview-1382264455", "body": ""}
{"title": "Add masked unblocked icon", "number": 5, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/5", "body": "This image is suitable when requiring alpha-masked imagery"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked-client-assets/pull/5#pullrequestreview-1390519325", "body": ""}
{"title": "Close app when last window is closed", "number": 10, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/10", "body": "Fixes #6"}
{"title": "Handle permissions relaunch", "number": 11, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/11", "body": "\nWhen the screen recording permission is granted, the system relaunches the app. This relaunch doesn't include the command line parameters that allow the app to know how to connect back to VS Code.\nIn this PR, I store the launch args in UserDefaults, and pull them back out in the case that the app is launched with no args. \nThis fixes #4"}
{"title": "Move to Twilio's Network Traversal Service", "number": 16, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/16", "body": "Updates code-signing to build with DeveloperId cert, which solves constant permissions requests when developing. Ask me for the cert if you need it. \nUpdates the app to expect STUN/TURN servers to be received over the VS Code web socket, fixing #14 \nAdds a handy mock web socket server to allow easy dev-side testing without having to coordinate VS Code for STUN/TURN (see IDERemote/README.md)"}
{"title": "Don't send screenshare stream from audience", "number": 17, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/17", "body": "App was sharing screen share streams from both ends, but there's no need to for the audience side. Fixes #15"}
{"title": "Fix issue where host window would draw in the the wrong place", "number": 18, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/18", "body": "Fixes "}
{"title": "Add optional record file argument to app", "number": 20, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/20", "body": "@matthewjamesadam will be using this argument to save the file to (if provided)."}
{"title": "Fixes quality issues with screen share", "number": 21, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/21", "body": "Fixes  \nImproves quality of screen share\nFeeds WebRTC framework logs to OSLog so its easy to see them\n\nThe framework was forcing use of a h.264 profile which was incompatible with the resolution of video we were sending. The hardware encoder was erroring, then the framework would notice no frames and flail into a lower quality mode... then it would restore and try again. \nI updated the framework to use a better profile here: "}
{"title": "Make WebRTC logs viewable outside debugging", "number": 25, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/25", "body": "Fixes #22"}
{"title": "Screen share recording", "number": 26, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/26", "body": "Record screen share session:\n\nVideo is taken from the existing ScreenShareCapturer session\nAudio is taken from a ReplayKit instance, which captures both the mic and app audio\nAll three tracks are sent to the Recorder class, which manages recording to a video file"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/26#issuecomment-978449548", "body": "I think I've resolved most/all of these issues"}
{"comment": {"body": "Could remove the unwrap for `microphoneCapturer` since it's not used.", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/26#discussion_r756457602"}}
{"comment": {"body": "Since there's an escaping completion handler, this could end up releasing the recorder before it has returned. nil it out in the handler instead? ", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/26#discussion_r756458204"}}
{"comment": {"body": "Yeah I wanted to keep the enabling/disabling of each or these components to a single line if possible so there weren't a bunch of disabled bits of code sprinkled around the codebase... I don't feel too strongly about it though", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/26#discussion_r756463862"}}
{"comment": {"body": "Is this a problem (in terms of execution of the `stop` method)?  I kind of did this intentionally so that the recorder state would synchronously be cleared...", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/26#discussion_r756465338"}}
{"title": "Alert user if they don't have screen share permission", "number": 27, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/27", "body": ""}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/27#issuecomment-978214823", "body": "This will only show if the person has not checked the app in security settings right?\n\nYeah.. but this made me realize a different case  where they launched for the first time, they need to get the system dialogue first, or else the app won't be in System Prefs to grant. Looking into that now."}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/27#issuecomment-978514671", "body": "I ended up making a bunch more updates to this and I'm much happier with it now. I came up with a somewhat janky way to detect if the system auth dialogue is up or not, and the logic works much better now."}
{"title": "Only create recorder objects on host", "number": 28, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/28", "body": "Fixes "}
{"title": "Send file recording signal when recording complete", "number": 29, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/29", "body": "Send signal to the extension when recording is complete.  This is done as a socket message before we shut down.\nFixes ."}
{"comment": {"body": "It's unclear if we should be waiting here until we get an ack back from the extension... it seems to work for now though", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/29#discussion_r757169132"}}
{"comment": {"body": "I removed the ack callback here on the `shutdown` signal (where the extension is telling the app to shutdown).  The problem is that this caused a deadlock between the two apps, since the extension was waiting for the ack to be received, while the app was trying to send the \"recording completed\" signal.\r\n\r\nSo now this is simply a \"request shutdown\" signal", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/29#discussion_r757169373"}}
{"comment": {"body": "If it works this is fine!", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/29#discussion_r757644153"}}
{"comment": {"body": "At some point we should move this to the enum as a `var asString : String` ", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/29#discussion_r757644424"}}
{"title": "Correctly place host window", "number": 30, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/30", "body": "Fixes \n(I think)\nThis doesn't seem right to me but it seems to work.  NSScreen's Y-axis is zero is the bottom of the screen, and Y increases going up the screen.  So the code as it was before looks correct, but I'm guessing NSWindow inverts this somehow?"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/30#issuecomment-981884396", "body": "Merging -- this will now work fine for Monterrey but not Big Sur.  Will continue investigating."}
{"title": "Audience app shuts down when hosts disconnects", "number": 31, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/31", "body": "Fixes \nWhen the host app shuts down, it sends a signal to peers that it is quitting.  Audience apps can then shut down."}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/31#issuecomment-982158062", "body": "@PadraigK I've changed this PR.  Now the host sends a command over the WebRTC data channel, informing peers that it is shutting down permanently.  When audience apps see this command, they shut down."}
{"title": "Add recording of remote audio stream", "number": 32, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/32"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/32#issuecomment-980480033", "body": "I'll reconcile this with my other one later"}
{"title": "Adds recording of remote audio stream", "number": 33, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/33"}
{"title": "Move recording onto a single queue and tidy interfaces", "number": 34, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/34", "body": "This PR got a bit messy with swift format and lots of refactoring, but the main change is just to use one queue for all recording."}
{"title": "Use a new recording file each time", "number": 36, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/36", "body": "We were always using out.mp4 which meant it got overwritten each time.  Instead use a random file name."}
{"title": "Merging tracks into output video", "number": 37, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37", "body": "Writes each track as a separate asset\nMerges them together after they're done recording.\n\nWill follow up with clean-up of logging and 'processing' state handling."}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/37#issuecomment-985171379", "body": "Most of my responses to the feedback are in a new PR that I'll post in a few mins. In the mean time I'm going to merge this much."}
{"comment": {"body": "Get rid of this stuff?", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761560732"}}
{"comment": {"body": "And this stuff too?", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761560820"}}
{"comment": {"body": "m4a?\r\n\r\nMaybe we should name these consistently so the file names are unambiguous?  `localAudio.m4a`, `remoteAudio.m4a`, `localScreen.mp4` ?", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761565058"}}
{"comment": {"body": "It feels strange that we're treating the remote audio stream so differently then the other tracks.  Shouldn't the local video and audio tracks be queued in the same way?", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761567982"}}
{"comment": {"body": "Can we type sampleBuffer to `Array<CMSampleBuffer>` so that we don't need `!`?", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761568134"}}
{"comment": {"body": "Do we need to have a separate creation codepath for remote input like this?  We're effectively doing the same thing as for the local tracks (waiting until we have a valid frame to start with)...", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761568883"}}
{"comment": {"body": "I might use some of it to experiment with audio quality stuff soon so I want to leave it here for now", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761608674"}}
{"comment": {"body": "Refactored this on another branch", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761608753"}}
{"comment": {"body": "Yes, it's quite hacky now, the recorder can be simplified a lot because the three recordings are independent now. I'll refactor this soon.", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761609020"}}
{"comment": {"body": "Weird... `Array<CMSampleBuffer>` works, but `[CMSampleBuffer]` was generating a type error", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761609811"}}
{"comment": {"body": "Yes, I want to pass the CMSampleBuffer's formatDescription into `AVAssetWriterInput`'s initializer, so it has to be created here instead of in the main initializer. As I mentioned above, I want to break the recorder up a bit since it's no longer recording one file.", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/37#discussion_r761611799"}}
{"title": "Add end call button", "number": 38, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/38", "body": "Adds end call button\nReworks a bunch of the flow so that the window can disappear without quitting the app, while it's still processing\nRefactors the Recorder; it now manages 3 separate TrackRecorders which manage each track.\nBroadcasts signals to the IDE\nMaintains size of window"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/38#issuecomment-985795400", "body": "Merging this now, but would still appreciate a review when you have time @matthewjamesadam"}
{"comment": {"body": "This is a new message that tells the IDE extension that the call has ended. This should trigger the \"processing\" state in the IDE. ", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/38#discussion_r762151686"}}
{"title": "Fix audio corruption issues", "number": 40, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/40", "body": "Make sure to copy the audio buffer on the caller's thread, or else the memory gets modified between the queueing and our processing."}
{"title": "Mutate the timestamp on the same thread to solve re-entracy issues", "number": 41, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/41", "body": "If the renderFrame method was called twice before the queue processed the block, then the timestamp would have been incorrect."}
{"title": "Hide the viewer thumbnail window when session ends", "number": 44, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/44", "body": "Fixes: "}
{"title": "Add readme", "number": 45, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/45"}
{"title": "Switch to Next Chapter team and new Bundle Id", "number": 46, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/46", "body": "This will cause macOS to prompt for mic, video and screen recording permissions again. To reduce confusion, clear the previous app out of System Preferences by running this:\ntccutil reset All org.padraig.codeswell\nThe new Developer ID cert is in 1Password, you'll need to add this to your local keychain to build and run. \nThis PR also updates CI"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/46#issuecomment-988341825", "body": "I've updated the secrets on GitHub Actions so once this goes in everything should build with the new app."}
{"title": "Adds alert asking users if they'd like to make the video available to the team", "number": 49, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/49", "body": "Also adds an app icon\nNote that if you run both ends of the chat on your local machine, you'll get prompted once for each  it's not a bug! \nRepo name in message is hard coded to chapter2inc/2048"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/49#issuecomment-989207551", "body": "fixes #48"}
{"html_url": "https://github.com/NextChapterSoftware/BotherFace/pull/49#issuecomment-989207954", "body": "I realize I had slightly different description text in the dark mode variant in Figma. It should read \"All Codeswell users that are members of chapter2inc/octoberdemo will be able to watch this video.\""}
{"title": "Fixes", "number": 5, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/5", "body": "Handles the following issues. \n\nFixes #3 \nAn Alert is popped up instead of crashing\nFixes #1 \nThe app will now re-activate VS Code after launch, when in Host mode.\nFixes #2 \nThe events remoteConnected and remoteDisconnected are sent back to VS Code. Note that disconnection takes a few seconds to be detected, because it makes efforts to reconnect first. This will get snappier for most cases in a future PR"}
{"title": "Updates to Permission Alert", "number": 50, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/50", "body": "Re-activates app when session ended from VSCode, so that the dialogue appears infront of other windows.\nNo longer asks the audience for permission to share, requirement is now that the host gets to decide overall.\nWithholds sending the videoReady message until the user has responded to the dialogue. \n\nBonus:\n* Copies each build into the vscode/screenshareapp folder for easier local iteration."}
{"comment": {"body": "This effectively finds `vscode/screenshareapp/Codeswell.zip` anywhere on the drive, right?  There would be some failure modes (multiple repo checkouts, or the extension actually installed, or spotlight is borked), I'm not sure if any of that matters right now.  This is maybe more indication that a monorepo for this would be useful...", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/50#discussion_r769880694"}}
{"comment": {"body": "Should we move this `group.enter()` above the block above (`self.assetMerger?.mergeTracks { blah }`)?  I think the assumption in the code is that the mergeTracks block will execute asynchronously, but if it calls the block synchronously I think the logic of the DispatchGroup will fail, because we will run the `group.leave()` before the second `group.enter()` here is called?", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/50#discussion_r769916284"}}
{"comment": {"body": "Yeah, it's definitely not perfect but seemed good enough to me. A lot of small requests coming back lately have been because I was testing exclusively in Xcode, so this really helps the workflow of testing it alongside the VS Code integration.", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/50#discussion_r769918456"}}
{"comment": {"body": "Yep, good catch, thanks! This method is super messy overall, so I'll also think about refactoring it soon. Xcode 13.2 was just released and now includes `async / await` support back to macOS 10.15, so I might try that out. ", "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/50#discussion_r769921129"}}
{"title": "Tidy up unused code", "number": 51, "htmlUrl": "https://github.com/NextChapterSoftware/BotherFace/pull/51"}
{"title": "Add screenshots", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/1"}
{"title": "Add thread participants and snippets", "number": 10, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/10"}
{"title": "Remove common whitespace from snippets", "number": 11, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/11"}
{"title": "Fix crash on empty snippets", "number": 12, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/12"}
{"title": "Command to decode base64 compressed payloads", "number": 13, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/13", "body": "sourcemark snippets\nmessage queue events\n\n"}
{"title": "Leniently decode base64 strings", "number": 14, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/14"}
{"title": "Add Copy JSON action to decode command", "number": 15, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/15"}
{"title": "Fix lint", "number": 16, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/16"}
{"title": "Add CI and CD", "number": 17, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/17"}
{"title": "Fix publish step by making non-interactive", "number": 18, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/18"}
{"title": "Adds script commands to start/stop GE instance", "number": 19, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/19"}
{"title": "No longer need this, since it hits the API directly", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/2"}
{"title": "Add list sections for \"Unread\" and \"Read\"", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/3"}
{"title": "Add repo dropdown", "number": 4, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/4"}
{"title": "Open files with VSCode", "number": 5, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/5"}
{"title": "Reacts to search query changes and debounces/throttles", "number": 6, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/6"}
{"title": "Intent from Clipboard or Selected text", "number": 7, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/7"}
{"title": "The getSelectedText API seems quite unreliable, retry it 5 times", "number": 8, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/8"}
{"title": "Add experts to find results", "number": 9, "htmlUrl": "https://github.com/NextChapterSoftware/raycast/pull/9"}
{"title": "Update gitignore", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/kube-job-queue-controller/pull/1"}
{"title": "Basic VSCode extension", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/1"}
{"title": "Add avatars to 'Ask a Question' UI", "number": 10, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/10", "body": "Add GithubUserCache, a simple client cache for storing information on Github users\nAdd some extra logic when resolving Github users from email addresses.  We now first try to fetch the user by their email address (user.search call), but often this fails because users don't publish their email addresses.  We fail back to getting the information for a commit that we know is associated with the user (commit.search) -- since we know this commit was made by the user in question, that's can give us the email -> GitHub user mapping.\nAdd more styling to the question UI"}
{"title": "Add timestamp updates on message sends", "number": 101, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/101"}
{"comment": {"body": "I don't know where this Bother originates from, but you're mutating its internals here -- you may want to update BotherUtils's cloneBother, and add an 'update' option to update the lastCharAt / lastUrgentCharAt / title. ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/101#discussion_r745190772"}}
{"comment": {"body": "Fair point...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/101#discussion_r745190926"}}
{"title": "Add ability to clone with arbitrary updates of bother", "number": 102, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/102", "body": "I've made the clone operation a bit more generic.\nWe are now using override properties and PartialDeep to ensure correctly typed updates to a cloned object."}
{"comment": {"body": "Very cool!  `type-fest` is pretty neat.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/102#discussion_r745284654"}}
{"title": "Fix race with loading webiew ui and refreshing state", "number": 104, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104", "body": "I was able to validate that Dennis' bother was a legitimate mongo entry and this was a render issue.\nThe current theory is we were hitting a possible race in this scenario:\n\nWe call this.webviewController.loadUI();\nWebview is still loading but not active/visible to properly receive properties\nthis.refresh() which attempts to load state on a webview whose event handler has yet to initialize to receive state.\n\nWe are now moving to load state once webview is visible and active.\nAdded some debug logging to see if this resolves Dennis' issue."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104#issuecomment-964430576", "body": "Is this something that should ideally live in the WebviewController, so that it knows to refresh the props once the UI is loaded?  It probably doesn't matter much for now, but I'm thinking that any other client using WebviewController is going to have to duplicate the same logic that you've put in the ChatWebviewController.\nWe probably didn't run into this in the AskQuestion UI because there's a bunch of inherent asynchronicity and we always refresh the UI after a short while..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104#issuecomment-964431253", "body": "(Also I made sure that the AskQuestion UI would still render if it had no props, it just doesn't show anything terribly interesting -- that's another approach)"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104#issuecomment-964431695", "body": "ething that should ideally live in the WebviewController, so that it knows to refresh the props once the UI is loaded? It probably doesn't matter much for now, but I'm thinking that any other client using WebviewController is going to have to duplicate the same logic that you've put in the ChatWebviewController.\nWe probably didn't run into this in the AskQuestion UI because there's a bunch of inherent asynchronicity and we always refresh the UI after a short while...\n\nYeah, I was going to mention that we do a double setProps in AskQuestionCommand.\n`  \n    controller.setProps({\n            candidates: suggestions,\n     });\nconst avatarPromises = suggestions.map((suggestion) = {\n    const userSha = peopleSha.get(suggestion.email);\n    return GithubUserCache.getUserFromEmail(suggestion.email, userSha)\n        .then((user) = {\n            suggestion.id = user.id;\n            suggestion.avatarUrl = user.avatarUrl;\n        })\n        .catch((err) = {\n            // Drop errors when fetching avatars\n        });\n});\n\nawait Promise.all(avatarPromises);\n\nconst fontFamily = vscode.workspace.getConfiguration().get('editor.fontFamily');\nconst fontSize = vscode.workspace.getConfiguration().get('editor.fontSize');\n\nconst codeHtml = await CodeToHtmlRenderer.renderCodeToHtml(content, document.languageId);\n\ncontroller.setProps({ candidates: suggestions, codeHtml, fontFamily, fontSize });`"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104#issuecomment-964432904", "body": "I'm not entirely sure if this will resolve the problem, but we'll see.. :)"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104#issuecomment-964433144", "body": "Yeah, I was going to mention that we do a double setProps in AskQuestionCommand.\n\n\nThat was intentional -- there's an initial set of props to get the UI rendering with some data.  After that first render, we load more data (some potentially expensive calls to GitHub), after which we refresh the UI again.  I think I might change the UI to display a \"Loading\" spinner on the first render, actually..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/104#issuecomment-964434195", "body": "Yeah, I was going to mention that we do a double setProps in AskQuestionCommand.\n\nThat was intentional -- there's an initial set of props to get the UI rendering with some data. After that first render, we load more data (some potentially expensive calls to GitHub), after which we refresh the UI again. I think I might change the UI to display a \"Loading\" spinner on the first render, actually...\n\nUnderstood.\nI was just saying that I believe that might be the reason why the AskQuestionCommand webview doesn't have this problem.\nThe second call to setProps gets around this issue.\nWho knows. :)\nWe'll see...\n-rashin"}
{"title": "Add upload build artifact", "number": 105, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/105", "body": "Dennis brought this up and I thought why not...\n"}
{"title": "Notifications", "number": 106, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/106", "body": "\n\nAdd status bar item that displays when there are any urgent unread messages\nAdd dot beside tree items that displays when there are any unread messages\n\nThis involved a lot more work then I expected.  Here are some things I had to do:\n\nI had to add a class (WorkspaceDataProvider) that synchronizes the Github login state and the workspace root git URL state.  This feeds both the tree and status bars, and ensures that both UIs only activate once both of those data elements exist, because we need to know both the root git URL and your GitHub user to display those UIs.  This fixes a bunch of tree bugs we had before, where you could open the tree before login was complete and break the tree display.\nI added a welcome UI (displays on the left-hand UI) that displays when you aren't logged in, prompting the user to try logging in again.\nWe store the last-read timestamp for every chat in local storage.  This gets updated every second on a timer while the UI is alive.  I added 5 seconds to the timestamp to prevent time/UI jitter.\n\nFixes #100 \nFixes #57"}
{"title": "Add checkbox to messageinput", "number": 107, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/107", "body": "The styling needs work, but we now have the ability to add action bar siblings to the message input action bar."}
{"comment": {"body": "On a similar note to this, in React there's a concept of 'Controlled Components', which means the parent view controls and supplies the current value (usually as one of its own state variables): https://reactjs.org/docs/forms.html.  Controlled components are the idiomatic way of doing this kind of thing, it lets the parent supply default values, do validation and change values due to business logic.  We do the same thing in the MessageInput component, the input's `content` is passed in as a value by the parent.  In this case we could provide a boolean value property.\r\n\r\nOne other thing you could do is use the `children` property instead of `label`:\r\n\r\n```\r\nexport interface CheckboxProperties {\r\n   children: React.ReactNode\r\n  ...\r\n}\r\n```\r\n\r\nThis lets you use the nice JSX syntax:\r\n\r\n```<Checkbox>This is my label</Checkbox>```\r\n\r\nincluding any renderable object:\r\n\r\n```<Checkbox><SomeOtherComponent><div>thing</div></SomeOtherComponent></Checkbox>```", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/107#discussion_r746080301"}}
{"comment": {"body": "Thank you for this info.\r\nI wasn't aware of the children feature....\r\n\r\nThat's pretty neat.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/107#discussion_r746088321"}}
{"title": "Show unread message notifications in status bar", "number": 108, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/108", "body": "\n\nWe can't update badge counts as we'd hoped to indicate unread messages, so we're going to show unread in the status bar too, for now."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/108#issuecomment-*********", "body": "Merging this in, let me know if you have any comments"}
{"title": "Clean up font usage across a few places", "number": 109, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/109", "body": "Fixing fonts and colors Matt noticed..."}
{"title": "Add paths to pull request", "number": 11, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/11"}
{"title": "Initial Screensharing work", "number": 110, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/110", "body": "Gets the initial pieces in places for launching and controlling a separate screenshare app.\nThis won't work on anyone else's machine yet, because I need to sort out signing and permissions for my screenshare app but I wanted to get the pieces in place.\nNote: the last two files in the diff are the interesting typescript ones"}
{"comment": {"body": "I'm guessing this is for testing for now?  Eventually we'd have buttons or other UI triggering this behaviour?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/110#discussion_r746803248"}}
{"comment": {"body": "Yep, exactly, I just wanted to prove to myself that VS Code could control the app. ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/110#discussion_r746806186"}}
{"comment": {"body": "Do we need to handle/check when ports is empty?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/110#discussion_r746810114"}}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/110#discussion_r749583945"}}
{"comment": {"body": "Can yo move away form hardcoding this and move it to the set of enum values in Common.ts?\r\nWe tend to re-use command names.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/110#discussion_r749592496"}}
{"title": "Fix sizing issues in viewport", "number": 111, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/111", "body": "Make sure header is omnipresent over text.\nMake sure the content properly is sized in viewport."}
{"comment": {"body": "I'm not sure why we need a global variable here, set to a fixed height?  If we just set `height: 100%` here (or even remove this?) I think that would work?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/111#discussion_r746901506"}}
{"comment": {"body": "To be honest, I've been having a hell of a time with 100% and overflow scrolling in vscode windows.\r\nSpecifically I want to ensure that there's no content out of the viewport as much as possible.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/111#discussion_r746926052"}}
{"title": "Fix code block styling for chat", "number": 112, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/112", "body": ""}
{"comment": {"body": "We should probably use `import` everywhere?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/112#discussion_r746965944"}}
{"comment": {"body": "This one can't be imported sadly :(", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/112#discussion_r746966162"}}
{"comment": {"body": "I might look for another library. Kind of shitty...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/112#discussion_r746966744"}}
{"title": "Some tweaks to the title editor", "number": 113, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/113", "body": "\n\n\nSome tweaks to the title editor:\n* Hide the pencil while editing\n* When pencil is clicked, select content and start editing\n* Size the width of the input to content or placeholder\n* Use blue border styling"}
{"title": "Added initial signalling server to Docker", "number": 114, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/114", "body": "Docker works locally, but I have no idea if I got the compose stuff right..."}
{"comment": {"body": "You should check in the package-lock file too so the resolved package set is consistent...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/114#discussion_r747056302"}}
{"comment": {"body": "Should probably consider doing this implementation via typescript in the long run, but this is fine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/114#discussion_r747063385"}}
{"comment": {"body": "Definitely \u2014\u00a0I need to iterate a bunch on this, but I wanted to get it hosting properly first. ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/114#discussion_r747076026"}}
{"title": "Tweak chat layout", "number": 115, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/115", "body": "Remove the global sizing variables, and allow the header to size naturally and the message content fills the rest.  This fixes bugs that sometimes resulted in there being two sets of scrollbars.\nThere are still some layout bugs that happen when a chat UI is sized very small, but those should be easier to find and fix now."}
{"title": "Tidy up some signalling server stuff", "number": 116, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/116", "body": "Gets live reloading working"}
{"comment": {"body": "question. does signalling actually have a src directory?\r\nI noticed that the index.js is located at root of folder.\r\n\r\n-Rashin", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/116#discussion_r747124355"}}
{"title": "Add urgent checkbox to Ask a Question UI", "number": 117, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/117", "body": "We added the checkbox to the chat UI but not the initial Ask a Question UI.\nI moved the Checkbox into the MessageInput since the two message input UIs are the same now.  I also made the checkbox a controlled component."}
{"comment": {"body": "Just FYI the reason for this is that if a checkbox `input` is within a `label`, you can toggle the checkbox by clicking anywhere in the `label`.  If not, you have to click on just the little checkbox box.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/117#discussion_r747132057"}}
{"comment": {"body": "Woah...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/117#discussion_r747217317"}}
{"title": "Add signal service pipeline", "number": 118, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/118", "body": "This pr does the following:\n1. Add signal action workflow to deploy to docker repository on aws\n2. Modify Cloud Development kit scripts to create new service and other details."}
{"title": "TestWebChanges", "number": 12, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/12"}
{"title": "Pin overlay to left side", "number": 120, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/120", "body": "This is pretty hacky but it seems to work.  It only affects the overlay positioning so the actual rendered code in the 'ask a question' and chat UI still render with character precision.\nPadriag if you can think of a better way to do this I'm all ears.  I'm sure there's some way to get the end of a line in the document but since this works I figured it was probably cheaper anyways.\nFixes #61"}
{"title": "Show errors when git information cannot be determined", "number": 121, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/121", "body": "Fixes #44 \nFixes #49"}
{"comment": {"body": "We should probably start heading in the direction of custom errors that extend Error type for better error handling.\r\nFor now, this is okay.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/121#discussion_r748541434"}}
{"comment": {"body": "Yeah I started doing that, but my custom errors did nothing other then set the message, and Typescript/JS's support for dealing with custom types is so weak that I didn't see the point.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/121#discussion_r748577834"}}
{"title": "Fix candidate selection", "number": 122, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/122", "body": "Fixes a number of small quality-of-life issues with the Ask a Question UI:\n* Show a loading spinner while we are resolving Github users from their email addresses, as this can take awhile if the cache isn't primed\n* Only display resolved Github users.  There are some conditions where we can't resolve a GitHub user from a commit, so we will elide those users from the candidate list.\n* Show a warning if we have no candidates.  This should never happen in the demo as we will be cooking the repo.\nFixes #50 ."}
{"title": "Make sure redis hostname is linked to container", "number": 123, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/123", "body": "When running from within a docker-compose container, we need to explicltly provide a linked service name (localhost does not work)"}
{"title": "Ensure chat is opened only in one tab", "number": 124, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/124", "body": "The canonical way of ensuring that a command re-uses the same tab for its webview is to essentially use a singleton model for a command.\nWe are now using a single instance of ChatCommand to maintain a single instance of a WebviewController which will update and possible re-initialize a webview panel if it's been disposed.\nThere are other changes in this pr:\n1. We are now correctly disposing of event handlers for the WebviewContainer\n2. We are now ensuring bother command names are shared.\nTESTING:\n1. Lots of manual testing. In all cases, validated that the correct tab was being used for rendering Chat."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/124#issuecomment-969159831", "body": "\n\nThere is something incredibly gratifying about getting this from you Matt.\nThank you."}
{"comment": {"body": "This won't move an existing webview to a new column if the `viewColumn` changes, right?  I guess we probably don't care about that...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/124#discussion_r749536986"}}
{"comment": {"body": "That's correct.\r\nIt's easy enough to allow for that if we want.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/124#discussion_r749538104"}}
{"title": "Remove getstream chat code", "number": 125, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/125", "body": "Haven't noticed any problems with redis so it's good for now.\nAs discussed with the inimitable Matthew James Adam, this is not a problem."}
{"title": "'Questions I've Asked' should come before 'Questions for Me'", "number": 128, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/128", "body": "Part 1 of \n"}
{"title": "Extract input box into a separate component", "number": 13, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/13", "body": "Extract the input box into a separate component so we can reuse it in the chat UI."}
{"title": "Unread items should appear at the top of the list", "number": 130, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/130", "body": "Part 2 of \nBefore:\n\nAfter:\n"}
{"title": "Fix bother clones and make chat rendering more defensive", "number": 131, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/131", "body": "We were using .assign which does not work with child subproperties. .merge correctly merges child subproperties in.\ni.e \nconst obj1 = { child: { a: \"a\", b : \"b\" } };\nconst obj2 = { child: { a: \"c\" } };\n\nconst badClonedObj = .assign({}, obj1, obj2); //       { child: { a: \"c\" } }\nconst goodClonedObj = .merge({}, obj1, obj2); //     { child: { a : \"c\", b: \"b\" }\n\nWe are making bother pointOfInterest requests more defensive so the Chat at least renders if something is bonkos."}
{"title": "Update MessageInput.scss", "number": 133, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/133", "body": "Updating the padding that surrounds the \"send\" button in the message input box."}
{"title": "Updates to signalling server", "number": 134, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/134", "body": "Tidying and fixes to support bother screensharing. Also updated to use typescript"}
{"comment": {"body": "const :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/134#discussion_r749833593"}}
{"title": "Bother Title should be optional", "number": 135, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/135", "body": "The Bother title should not be initially set.\nIt should be set only on user input via chat window."}
{"title": "Fix titles and icons of tabs", "number": 137, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/137", "body": "This pr does the following:\n1. Add a static class that contains icons and other information for various modules.\n2. Add options to WebviewContainer to specify light/dark icons for webviews and titles.\nAnnoyingly, vscode does NOT allow us currently to specify ThemeIcons for webview tab icons.\nThere's an ongoing issue related to this:\n"}
{"title": "WIP: ScreenShare VSCode UI", "number": 138, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/138", "body": "This is a WIP please do not look through this yet."}
{"title": "VSCode ScreenShare UI", "number": 139, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139", "body": "Add VSCode UI elements for screen sharing.  Note that the app itself is not committed in the repo yet, so starting a screen share right now will just result in an error.  This is still a WIP, I need to get this basic stuff in so I can begin testing.\nSome work I did in this PR:\n* Add streaming status banner and connect/disconnect buttons to the chat UI\n* Add an API for clients to note when they are offering a screen share in a bother\n* Add a class (ScreenShare) for managing local screen share app state\n* Add state tracking in the chat controllers: monitor the local screen share app state, plus the remote offered streams, to determine what the UI state should be.\nThings left to do:\n* Plenty of UI fixups.  The banner is pretty poor at this point, the chat header buttons are inconsistent.\n* Add expiry to stream offers so that they don't last forever\n* Lots of bug fixing, no doubt."}
{"comment": {"body": "This API is meant to represent an offer for a screen share -- one person commits an offer for a screen share to the service, and other parties can query the API to determine whether any screen share offers exist for a given bother.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139#discussion_r750688140"}}
{"comment": {"body": "A lot of these changes are Prettier updates, which sadly makes it hard to tell what I actually changed.  Rashin enable Prettier please!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139#discussion_r750688768"}}
{"comment": {"body": "I made HeaderButton to hold the styling for these buttons in one place, I will be replacing the PointOfInterest and References components below with it in a future commit.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139#discussion_r750691820"}}
{"comment": {"body": "Hmm maybe to make this clearer the models and API here should be named ScreenShareOffer ?  Since that is what is being mutated?  I'm not sure if it matters or not for the demo.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139#discussion_r750694878"}}
{"comment": {"body": "Why port 0?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139#discussion_r750706783"}}
{"comment": {"body": "Oh if you tell express to use port zero it will pick a free port.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/139#discussion_r750709624"}}
{"title": "Add openapi typescript server implementation", "number": 14, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/14", "body": "Due to Matt's awesome work, we now have server stubs for api..."}
{"comment": {"body": "We need to move away from this to a per environment config.\r\nIn another PR I'll try to figure this out...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/14#discussion_r734999697"}}
{"comment": {"body": "We should be able to add multiple servers to the spec file so that the base urls are defined in a single place instead of in each client.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/14#discussion_r735000170"}}
{"comment": {"body": "It looks like this isn't even used anywhere... which I guess makes sense on the server.  This does matter for client bindings though.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/14#discussion_r735174693"}}
{"title": "Add coturn service", "number": 140, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/140"}
{"title": "Updat HeaderButton.scss", "number": 141, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/141", "body": "Updating the header button default and hover styles."}
{"title": "Some sharing/header UI fixes", "number": 142, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/142", "body": "All of the header buttons (screen share, POI, references) all use the same common component (HeaderButton) now, so they look and act the same.\nPass --host arg into the screen sharing app if you are hosting the screen share"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/142#issuecomment-971008467", "body": "Wow! You're quick man. I was going to do this after seeing your header button component. Thank you"}
{"title": "Ensure bothers are opened in correct tabs", "number": 143, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/143", "body": "We are now opening chat and source files using the following parameters:\n1. Chat uses ViewColumn.Besides\n2. Source uses ViewColumn.Besides and preview:true"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/143#issuecomment-971793284", "body": "This looks good, I think the only issue I'm seeing behaviourally is that the chat UI should open in the current column, ie the chat UI shouldn't result in a new window split."}
{"title": "Ensure chat window is in active column", "number": 148, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/148", "body": "As Matt correctly identified, the Chat window should be in active column and not start a split."}
{"title": "Fix tab focus on open", "number": 149, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/149", "body": "Most of this is prettier fixes.  The only real change is preserveFocus: true.\nWhen opening a Bother, the tab focus flickers between the newly-opened associated text editor and the chat tab.  This prevents that flicker."}
{"comment": {"body": "This is the only actual change.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/149#discussion_r751521701"}}
{"title": "Add mongo and libraries to api", "number": 15, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/15", "body": "We are moving in the direction of using mongodb and mongoose for schema management.\nValidated this with local stack testing.\n\nAdded docker images and configuration files\nAdded mongoose models and schemas\nAdd db connection management\nAdded a test for sanity"}
{"comment": {"body": "using embedded types in mongoose...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/15#discussion_r735049976"}}
{"comment": {"body": "This is a temporary demo hack, which is totally fine, I'm finding it hard to think about what things like this we should care about or not -- how long will the demo live for?  Should we be making GitHub issues for things like this (security or other high-risk things) or just not bother?  I'm interested in your thoughts.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/15#discussion_r735757433"}}
{"comment": {"body": "Just FYI the newest versions of node let you write async/await code at the top level.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/15#discussion_r735798387"}}
{"title": "Suppress tooltip on hover over link", "number": 150, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/150", "body": "Fixes . The tooltip now won't appear when the mouse hovers over the link.\nBefore:\n\nAfter:\n"}
{"title": "Serialize Chat Webviews", "number": 152, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/152", "body": "Serialize chat webview data on editor restarts\nThis is phase 1 of serializing webview data for chat.\n\nWe save state of our react properties with render call.\nFor chat, we've now encapsulated the management of chat webviews in its own class.\nWe have created an encompassing ChatState type so we can pass it around.\nWe are now providing an optional webviewPanel to WebviewContainer.\nWe add a custom WebviewPanelSerializer for chat webviews.\n\nValidation:\n1. Opened chat\n2. Closed editor window\n3. Reopened workspace\n4. Confirmed chat window pops up correctly.\nVscode is a bit slow invoking the serializer...SHRUGS...."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/152#issuecomment-972213510", "body": "I've got a little more to do, but this is good for now..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/152#issuecomment-972248432", "body": "If it makes it any easier on the rest of your work, you can always just serialize the object ID (and re-fetch after deserializing) instead of storing the entire data structure.  For cloud services you generally need to re-fetch after deserializing anyways to make sure the data is up to date (and that's what's happening here) so the benefit of storing everything is not too significant."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/152#issuecomment-972253206", "body": "Yeah, I was considering that.\nGoing to have to sidestep the BotherCache as it is entirely dependent on repoUrl and there's a bit of a race when the repoUrl becomes available (especially at editor startup).\nEither that, or I'm going to have to figure out how to fix that race..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/152#issuecomment-972259956", "body": "Yeah, I was considering that.\nGoing to have to sidestep the BotherCache as it is entirely dependent on repoUrl and there's a bit of a race when the repoUrl becomes available (especially at editor startup).\nEither that, or I'm going to have to figure out how to fix that race...\n\nI'd just ignore the bother cache and fetch the items you need directly using the API.  You are already doing something similar in RedisChatChannelController ~line 67:\nconst channelState = await this.client.getState({ channelId: channel.id });\nYou could either add the Bother onto the return value for this call, or fetch it with a separate API call afterward?"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/152#issuecomment-972265118", "body": "Indeed. Just trying to minimize requests and use common patterns.\nThis time might as well just do a client request I suppose. ;)\n-Rashin"}
{"title": "Sharing UI fixes", "number": 154, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/154", "body": "Fix a number of UI issues with the sharing/chat UI:\n\nVSCode was adding padding around our webview body elements, which means that elements like the sharing banner couldn't span the entire width of the UI.  Remove this padding (in our template HTML) and set explicit padding where we need it.\nFix some associated padding/layout issues in the chat header UI that you can see when the window is space-constrained\nUse the correct screen share icon\nUpdate the screen share state correctly when the user closes the streaming app"}
{"comment": {"body": "This file is almost entirely prettier changes", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/154#discussion_r751686686"}}
{"comment": {"body": "This is the only real change to this file", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/154#discussion_r751686774"}}
{"title": "Fix screen sharing state", "number": 155, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/155", "body": "Last commit broke this by changing the name of the property to 'screenShareStatus'."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/155#issuecomment-972279543", "body": "Mental note: for V1 we need to have the typing system work across the extension/webview divide."}
{"title": "Mac App Launch Tweaks", "number": 156, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/156", "body": "Adds listeners for connect/disconnect events from Mac App\nAdds open -g for launching app in background in hosting case\nFixes some var that I shouldn't have used."}
{"title": "Do client request for bother to get latest bother", "number": 157, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/157", "body": "We update client apis for bother to handle single bother request by id (this should be different than bothers request)\nUpdate chat serializer to do an explicit bother request and use that if the query is successful rather than state bother."}
{"title": "Move to logger", "number": 158, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/158"}
{"title": "here comes mr app", "number": 159, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/159", "body": "Adds the app bundle to the VS Code extension."}
{"title": "Package ScreenShare app as a zip", "number": 163, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/163", "body": "Fixes #160 \nThe Visual Studio packager (vsce) does not correctly package symlinks ().  This is a workaround: we will package up the app as a zip file, and at runtime unzip the app and run it."}
{"comment": {"body": "Is there a possibility we want to overwrite the contents? What if the app is stale?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/163#discussion_r752504144"}}
{"comment": {"body": "Yes for engineers this is a problem -- I'll fix it in a future PR.  But every time you install the extension it installs it from an empty folder so for actual end users this shouldn't be an issue.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/163#discussion_r752512190"}}
{"title": "Scroll the chat window to the bottom only on load and new message", "number": 165, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/165", "body": "Addresses . The chat window will scroll to the bottom when it first loads and when a new message is added after that. It currently scrolls to the bottom any time message editor text changes.\nI'm a react noob so I'm not 100% sure this is the best approach (despite the fact that it seems to work)!\nBefore:\n\nAfter:\n"}
{"title": "Add hooks for background", "number": 166, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/166", "body": "We are adding hooks so that ben can do styling stuff. :)"}
{"comment": {"body": "Dummy values... :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/166#discussion_r752569029"}}
{"title": "Ensure we apply code styling at load time to code block snippets", "number": 167, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/167", "body": "We do not want to render code block html until user actually loads the sucker.\nInstead we save the text and languageid as attachment only..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/167#issuecomment-*********", "body": "Good question.\nIt's meant to be backwards compatible.\nNothing has to change."}
{"comment": {"body": "We now load the html block dynamically before we sent message out to webview.\r\nPossibly hacky, but whatever...\r\nDemo worthy :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/167#discussion_r752665097"}}
{"title": "Add bother routes", "number": 17, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/17", "body": "Adding routing that connect dbs models with responses.\nAdded tests to validate behaviour."}
{"comment": {"body": "I'm guessing we can get rid of these lines...?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/17#discussion_r735940820"}}
{"comment": {"body": "Yeah, it was just for testing. :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/17#discussion_r735962316"}}
{"title": "Fixes for screen share UI", "number": 170, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/170", "body": "Update VSCode's state to be 'Connected' once another party connects to the stream (previously we would be considered 'Connected' once the screen share app connected back to VSCode)\nUpdate VSCode's state to be 'Disconnected' once the other party disconnects from the stream\nDisable the 'Start a screen share' button when the screen share app is already active."}
{"title": "Add ability to debug api service from vscode", "number": 171, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/171", "body": "Assuming you do an npm install from web package, you should now be able to debug api service."}
{"title": "Screen share state update fixes", "number": 172, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/172", "body": "A few fixes to do with screen share state updating:\n\nFix a bug in the typescript server openapi templates (adopting this change: https://github.com/matthewjamesadam/openapi-typescript-server/pull/4). This fixes a bug where we were storing an invalid screen share model ({}) in redis, instead of deleting the redis document, which broke loading the initial screen share state when opening a Bother.\nFix another bug where we weren't actually using the screen share state we fetch from the API -- we would always set the state to 'idle'."}
{"comment": {"body": "This is the generated change from the new API template -- if a request body is optional in the API, and is not sent in the request, return `undefined` instead of `{}`.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/172#discussion_r753398520"}}
{"title": "Clean up some code", "number": 173, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/173", "body": "Remove ContextPanel code.\nClean up css for other vs user"}
{"title": "Create plain object from vscode.Range when creating a new Bother", "number": 175, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/175", "body": "Fixes . \nCurrently, when we create a new bother in CreateBother, the vscode.Range object passed to CreateBother is used when creating the PointOfInterest. vscode.Range conforms to the Range interface in Range.ts, however _.merge doesn't correct merge it with a PartialDeepBother where pointOfInterest.location is a plain javascript object. \nSince we use vscode.Range for new Bothers (and I think not for those restored from the API) the location isn't updated for new Bothers, at least not until VS Code is restarted.\nI think a better approach would be to convert the Bother in cloneBother to a plain javascript object before merging, but I didn't find helper function that would allow this. Also, I'm not sure if we plan on continuing using lodash beyond the demo :).\nNew Bother (with vscode.Range in location property):\n\nExisting Bother:\n"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/175#issuecomment-974320467", "body": "Yeah, we're not using lodash anymore.\nThis is fucked... :)"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/175#issuecomment-974320519", "body": "Also, I'm not sure if we plan on continuing using lodash beyond the demo :).\n\nMy vote would be 'no' on this, maybe going so far as a linter rule prohibiting it.  Lodash is really meant for older-style javascript, and if you're using typescript it can cause a lot of issues (like this one!)"}
{"title": "Fix empty line selection for ask a question", "number": 177, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/177", "body": "We should be showing the line selected in AskAQuestion\nthis is an ode to @matthewjamesadam"}
{"comment": {"body": "isEmpty for range indicates the selection is empty, but it does have line information.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/177#discussion_r753455864"}}
{"title": "Fix race condition in GutterService when multiple editors are open for the same file", "number": 178, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178", "body": "Fixes .\nThe bug was happening because the forEach loop in updateDocumentMap is running logic asynchronously, and this logic looks up for an existing gutterDocumentInfo from documentUriToEditors. \nWhen two editors are opened for the same file on VS Code launch, there is a race condition when looking up an existing gutterDocumentInfo. The solution here is to group the editors by the key used to look up the gutterDocumentInfo so that it is looked up by key only once."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#issuecomment-974557305", "body": "The fact that you used reduce gets a plus one from me."}
{"comment": {"body": "Just FYI, `!` probably shouldn't ever be used...\r\n\r\nand I think I already wrote a version of this algorithm in `BotherCache.arrayToMapArray`, you could extract both into a utility fn...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754481402"}}
{"comment": {"body": "Lint rule baby.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754482345"}}
{"comment": {"body": "You can avoid the `!`s everywhere here by checking for null....\r\n\r\n`if (!gutterDocumentInfo) { log && continue; }`\r\n\r\nor\r\n\r\n`if (gutterDocumentInfo && gutterDocumentInfo.editors.has() && ...`\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754482651"}}
{"comment": {"body": "Thanks for the tip, will fix \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754483356"}}
{"comment": {"body": "Yeah setting up eslint and prettier correctly in CI and in the IDE is probably the first thing to do when we set up the vscode project for V1...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754486145"}}
{"comment": {"body": "@davidkwlam sorry I missed the review on this earlier!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754486412"}}
{"comment": {"body": "All good! Will handle comments in https://github.com/Chapter2Inc/OctoberDemo/pull/181", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/178#discussion_r754493602"}}
{"title": "Update conversation styling", "number": 179, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/179", "body": "Main Conversation Styling\nMoved from an open thread-like appearance to conversation bubbles. 'My' messages are styled differently than messages from others. Also tweaked some of the default margins and padding to make this happen.\n\n\nCode Block Styling and Scroll Behaviour\nCode blocks now don't wrap when the code container shrinks. Removed line padding so it affords a horizontal scroll without the default thick scrollbar. Side note: I tried modifying the default scrollbar but it doesn't work.\n\nTitle Styling\nIncreased the title font size, tweaked button height and adjusted the surrounding padding.\n"}
{"title": "Integrate proper endpoint handling for client apis", "number": 18, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/18", "body": "There are several things happening in this pr.\n1. We are adding jest support for testing.\n2. Upgrading openapitools that fixes a bug with typescript generation in runtime.ts.\n3. Adding initial config management and endpoint generation for client.\n4. Passing custom config overrides when instantiating openspec generated clients. Look at \"client.ts\" and \"client.test.ts\" for what's happening there...\n5. Adding tests to validate all this behaviour.\n6. Updating docker-compose so it mounts local code on container so that it can restart api service on demand within container.\nValidated client to server communication with tests.\nI'll be giving more instructions tomorrow, but this should be a start."}
{"comment": {"body": "We should maybe move this into another folder, so `/vscode/src/api` is purely generated code, otherwise we're mixing generated and test code?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/18#discussion_r736044695"}}
{"comment": {"body": "Not sure. Another pr if this proves to be confusing.\r\nI do like the test files being next to the files they're testing, but I do get generated code not being next to our code.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/18#discussion_r736046971"}}
{"comment": {"body": "Sure -- I have a preference to keep generated code separate, even if it means having a separate `test` folder living alongside `api`, but it's not a big deal for now.\r\n\r\nJust as a FYI, you can test this without needing an actual endpoint to hit by mocking the fetch module itself -- I did this here: https://github.com/matthewjamesadam/epyc/blob/2839c10cac990fa7cf146b489f4f0967999f8e31/server/GameManager.test.ts#L414. It's kind of roundabout but means the test doesn't depend on any services.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/18#discussion_r736050360"}}
{"comment": {"body": "Roger that.\r\nI will say that this was more of an integration test than a unit test (hence the fetch bullshit).\r\nI didn't want to say things were working tomorrow without confirming end - to -end.\r\n\r\nthanks for the example!!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/18#discussion_r736052006"}}
{"title": "Move screenshare to its own controller", "number": 180, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/180", "body": "Separate some code out so the basic ChatWebviewController is a little bit easier to read."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/180#issuecomment-974733947", "body": "Nothing really big in this pr, just getting it in post some more testing..."}
{"title": "Use existing logic to transform array to map", "number": 181, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/181", "body": "...and also don't use ! for nullables."}
{"title": "AddScreenShareFileAndChat", "number": 182, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/182", "body": "PHASE 1:\nThis pr adds the ability to view screen share recordings from chat UI for the audience member.\nTo do this:\nEXTENSION:\n1. We add a recFile to ScreenShareState. \n2. We populate recFile for the audience member at screen share start.\n3. On screenShareStop if the recFile exists, we add a ScreenShare attachment type that holds a reference to the local recorded file.\nWEBVIEW:\n1. We clean up how we handle attachments and generate a screen share recording message node.\n2. We add a playScreenShare handler to invoke a shell open on the extension side.\nTESTING:\n1. Hardcoded a dummy video file and confirmed that message opened the file...\nTODO:\n1. We need to add a flag to provide the recFile to app."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/182#issuecomment-975891655", "body": "Merging. Going to do Matt's suggestions in another pr."}
{"comment": {"body": "I think this is the wrong event to trigger this on -- this will only trigger when the Stop button is pressed.  There are other conditions where streaming can stop that aren't triggered by the Stop button (quitting the streaming app, the host shutting down the session, etc).  For the demo I'm not sure if this matters, but a couple ideas:\r\n\r\n* We could monitor the `ScreenShare.onStateChanged` event, as this tells you when the local screen sharing state changes.  When it changes from `destStreaming` to anything else, the stream should be done.\r\n* We could add a second event to `ScreenShare` that indicates when a new recording is available.  This would keep the state monitoring within that class.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/182#discussion_r754604420"}}
{"comment": {"body": "Roger...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/182#discussion_r754606715"}}
{"comment": {"body": "I'll attempt to address this in another pr so it's more clear...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/182#discussion_r754608734"}}
{"title": "Matt/cmd enter send message", "number": 188, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/188", "body": "Fixes #183"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/188#issuecomment-975914201", "body": "good change, thank you..."}
{"title": "Make recording handle more edge cases", "number": 189, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/189", "body": "As discussed with Matt, we need to be smarter on how we handle local session ends.\nTo that end:\n1. We move to a model where state change events now hold previous state as well as current state.\n2. The ChatWebviewController now correctly handles state changes when record session ends using previous and current state."}
{"title": "Client bother cache", "number": 19, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/19"}
{"comment": {"body": "Should this use the `Disposable` stuff in VS Code, rather than needing to be `removeSubscription`'d?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/19#discussion_r736772844"}}
{"comment": {"body": "I think this will update bothers, and add new ones, but won't remove bothers that have been deleted from the server side.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/19#discussion_r736776000"}}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/19#discussion_r736963936"}}
{"comment": {"body": "Done", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/19#discussion_r736964041"}}
{"title": "Sort bothers by lastChatAt", "number": 191, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/191", "body": "Addresses .\nWe're currently showing new bothers at the top of the list\n\nBother.lastChatAt gets updated whenever a message is received/sent. When this happens, the bother will bubble up to the  top of the list. Also now when a bother is read it won't jump to the bottom of the list."}
{"title": "Adds sending Twilio TURN/STUN server info", "number": 192, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/192", "body": "We send this over web socket to the screen share app. Also adds an updated Mac app that makes use of this"}
{"title": "Try demo", "number": 193, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/193"}
{"title": "Version plugin we upload to s3 and push to github", "number": 194, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/194", "body": "Example:\n"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/194#issuecomment-976920826", "body": "Oh never mind I see you did a test run"}
{"title": "Package with build number", "number": 195, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/195", "body": "The build number corresponds to the GitHub action workflow run number... v0.0.num"}
{"title": "Add delete bother operation", "number": 196, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/196", "body": "Part one of . Implements the server side logic for deleting a bother.\nI can update to make this a soft delete if we want, so that an accidental delete can be recovered."}
{"comment": {"body": "Our codegen is actually generating this as `this.router._delete` so I manually removed the underscore. Not sure why this is happening, anyone have any ideas?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/196#discussion_r755408998"}}
{"comment": {"body": "This will be an OpenAPI issue of some kind.. let me look into this", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/196#discussion_r755419014"}}
{"comment": {"body": "I fixed this in a hacky way (https://github.com/Chapter2Inc/OctoberDemo/pull/196/commits/990be99f280ebdf134e3f72f5f9e46143a9bf8da) -- since `delete` is, in some contexts, a reserved symbol in javascript, the opanAPI generator rewrites any usage of it as `_delete`, but in this context (as a method call on a class) it's fine.  I'm not sure if there's a better/more-general fix but this is fine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/196#discussion_r755427780"}}
{"comment": {"body": "thanks!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/196#discussion_r755428026"}}
{"title": "Add launch arguments for recorded file", "number": 197, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/197", "body": "Move to latest Codeswell app with recFile argument.\nPass in argument."}
{"title": "Use findOneAndDelete", "number": 198, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/198"}
{"title": "Add ability to delete bother from tree view", "number": 199, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/199", "body": "\n"}
{"comment": {"body": "Yeah, iterating through all repoCaches is a little gross", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/199#discussion_r755522325"}}
{"comment": {"body": "Off the top of my head, one way of doing this possibly is doing this:\r\n\r\nconst bother = await ClientBotherApi.getBother({ botherId: botherId });\r\nconst cache = this.repoCaches.get(bother.repoId);\r\nif (cache) {\r\n...\r\n}\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/199#discussion_r755563958"}}
{"comment": {"body": "Thanks Rashin. I'm tempted to leave it as is as I think it'll be cheaper to just iterate through the repo caches vs hitting the API to get the repo url (but will do what you suggest if I'm wrong).", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/199#discussion_r755567657"}}
{"title": "Determine person to ask question w/git", "number": 2, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/2"}
{"title": "Add mongo cluster support", "number": 20, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/20"}
{"title": "Update screen share app", "number": 200, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/200", "body": "Adds changes from https://github.com/Chapter2Inc/BotherFace/pull/21"}
{"title": "Select and center range for bother", "number": 202, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/202", "body": "As requested."}
{"title": "Make sure we select the bother in the tree view", "number": 203, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/203", "body": "This pr addresses a Dennis' request to ensure that the tree view is opened and the bother is correctly selected.\nTo do this:\n1. We move away from using registerTreeDataProvider to register the BotherTreeProvider as the system holds a private instance of the TreeView that we cannot touch nor open programmatically.\nInstead, from the BotherTreeProvider, we explicitly instantiate a TreeView (and register it with the view subsystem) so that we can manage the view ourselves. \nThis is the recommended (and only) approach in the docs to programmatically open and manage the Treeview.\n2. We modify the BotherTreeProvider to use BotherTreeNode to describe the tree hierarchy. We also define the getParent interface method which is necessary if we want to programatically open the tree view.\n3. We add a reveal function to the BotherTreeProvider that correctly opens and selects the associated tree view element in bother tree view.\nTESTING:\n1. Validated action panel works.\n2. Validated bother is correctly selected in tree view when I open a gutter link.\n3. Validated bother is correctly selected in tree view when I open from status bar."}
{"title": "Remove old bothers when polling the API", "number": 204, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/204", "body": "When a bother is deleted, it will be removed from the tree view and the gutters: \n\n"}
{"title": "Fix single-line height for auto-sized text area", "number": 205, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/205", "body": "textarea has a default rows value of 2.  This effectively sets a minimum height for the text area to be two rows.  This PR resets that to one (if it isn't overridden), plus removes a min-height value we had to match previous designs.\n"}
{"title": "Ben/conversation styling", "number": 206, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/206", "body": "Few more visual tweaks after seeing Kay go through the demo.\nTighten and clean up padding in the message bubble\n\nScreen sharing banner foreground text. I'm now using \"statusBarItem-warning-foreground\". This seems to work reasonably well for all default and top themes from the marketplace.\n\nCircular avatars in the question UI. Also aligned the avatar size to what I was using in the conversation UI.\n\nAdjusted surrounding padding so the total height of the footer bar and message input matches. Also reduced left/right padding in the footer so all sides are equal.\n"}
{"title": "Fix inaccurate timestamp for first chat message", "number": 207, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207", "body": "Fixes .\nThe core issue is that the message timestamps in the chat come from the messages stored in redis. We were create the first message in the chat when the chat is opened, so it always says \"a few seconds ago\" even if the bother was created long before.\nThis PR sends the first message after creating the bother so the timestamp accurately reflects when that bother was created."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#issuecomment-978389060", "body": "Going with a different approach."}
{"comment": {"body": "This `if` block is probably not needed, but I'll leave this here in case there was an issue creating the first message when the bother was created.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756438727"}}
{"comment": {"body": "I'd rather have separation of responsibilities here. \r\nI don't believe this should be here.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756441856"}}
{"comment": {"body": "Fair. Maybe we do this on the backend?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756442390"}}
{"comment": {"body": "Or just figure out how to set the timestamp when sending the message", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756443090"}}
{"comment": {"body": "I think that's more appropriate. I currently dynamically set the timestamp at creation time.\r\nWe can optionally provide a timestamp...\r\n\r\n        const createdAt = moment().valueOf();\r\n        message.createdAt = createdAt;\r\n        \r\n        ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756444300"}}
{"comment": {"body": "Probably will need a createdAt on Bothers...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756446098"}}
{"comment": {"body": "I think there is an existing race condition though. If two clients open the same bother at the same time, each client will send the same message to the channel, causing a dupe.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756448758"}}
{"comment": {"body": "There's a possibility, you're correct. Very minimal for the demo.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/207#discussion_r756449761"}}
{"title": "Add createdAt field to Bother", "number": 208, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/208"}
{"title": "Set createdAt for first message in chat", "number": 209, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/209", "body": "Fixes https://github.com/Chapter2Inc/OctoberDemo/issues/190\nTake two of https://github.com/Chapter2Inc/OctoberDemo/pull/207"}
{"title": "Add initial gutter code", "number": 21, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/21", "body": "This gets something showing in the gutter, but not based off POIs yet."}
{"comment": {"body": "Curious, is there a particular reason this is configurable?  Is this overridable in the workspace settings or something?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/21#discussion_r737833048"}}
{"comment": {"body": "Oh, this is left over from Rashin's initial work on the gutter stuff, I didn't dig into it because I ended up just using the editor colors \u2014  @rasharab might know more! ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/21#discussion_r737837478"}}
{"title": "Add context panel to right hand side with references", "number": 215, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/215", "body": "This pr sets up initial references pane on right hand side.\nTo do this:\n1. Added a missing createdAt field for referencees.\n2. Moved to using nested flexbox for ChatPanel to handle side-by-side layout.\n3. Added Context Panel activation from Header Bar."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/215#issuecomment-979371939", "body": "@benedict-jw this is the references panel I'm talking about -- the entire view is effectively a two-column panel, the left column is the main messages view, the right column is the references panel.\n"}
{"comment": {"body": "Oh interesting, didn't know this would work ... the way I've done this is `overflow-x: hidden` which should have the same effect...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/215#discussion_r756506636"}}
{"comment": {"body": "@benedict-jw this sizes the 'references' column to be 1/3rd of the total size of the panel (so messages are 2/3rds) -- do we want this to be a relative size?  Or should it be a fixed-width size?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/215#discussion_r756507255"}}
{"comment": {"body": "How the hell do you know this stuff!\r\nThat definitely works too.\r\n\r\nHad no clue...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/215#discussion_r756507754"}}
{"comment": {"body": "Hmm 1/3 might be a bit generous. I think we should have a max width applied. What is the the underlying structure of the panel? Is it column based or something else? ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/215#discussion_r756540054"}}
{"title": "Update reference panel styling", "number": 216, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/216", "body": "Reference panel width\nReferences panel width set to 30% of editor. It has a max width of 400pts.\n\nReference list items\nFixed position of the icon so it doesn't continue to centre itself text wraps. It previously began to look wonky once it wrapped to multiple lines. Also put a minimum width and height on the icon so it didn't shrink, and adjusted font weight, margins, and padding as well."}
{"title": "Change mouse cursor to 'pointer' for clickable elements", "number": 217, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/217", "body": "VSCode uses cursor: pointer for clickable elements. I added this to all of the clickable elements in our extension with the exception of the gutter icon. Unfortunately we don't control this one.\nConversation title\n\nConversation message input box\n\nAsk a question team members\n\nReference list items"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/217#issuecomment-979488660", "body": "I'm not entirely sure what you mean. Do you mean moving this up? In this example I already see a cursor: default"}
{"title": "Fix for references panel max-width", "number": 218, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/218", "body": "Fix for references panel max-width"}
{"title": "Use test env when running vscode tests", "number": 22, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/22"}
{"title": "Update dark and light gutter icons", "number": 220, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/220", "body": "Just updated the gutter icons to match what we're using everywhere else. Kept the dimensions exactly the same. \n"}
{"title": "Handle recording file signal from the screenshare app", "number": 221, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/221", "body": "Fixes #214 .\nThe screen share app now sends a signal over the web socket when the recording file is ready.   This handles this change.\nAs part of this, I removed the local \"ShareStateChange\" event, and replaced it with a \"RecordingFinished\" event.\nI won't merge this until I have a completed screen share app build available for this."}
{"title": "Clean up text in point of interest button", "number": 223, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/223", "body": "@matthewjamesadam  as per our chat in office, I have cleaned up the text in the point of interest button. I'm noticing though that the lines represented are in accurate."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/223#issuecomment-979703675", "body": "Here's another screenshot showing the inaccuracy in the lines."}
{"title": "Disable send button when no message content exists", "number": 224, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/224", "body": "Fixes #219"}
{"title": "Add new reference type", "number": 228, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/228", "body": "Minor change to allow for audio transcript."}
{"title": "Add event handlers for references being clicked", "number": 229, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/229", "body": "Move reference items to be buttons\nAdd onclick handlers for references"}
{"title": "Fix test", "number": 23, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/23", "body": "The config files were not being bundled up.\nMight change this pattern down the line, but it's working now."}
{"title": "Cleanup chat messaging for screen share recording", "number": 230, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/230", "body": "When the host initiates a chat, there is no notion of an otherParty from the host's perspective (other than possibly the recipient of the question). That field should be optional.\nThere was a bug with the user were using for initializing the message."}
{"comment": {"body": "This is a host broadcast. There is no otherParty.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/230#discussion_r757723116"}}
{"title": "Fix chat recording bubble names", "number": 231, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/231", "body": "Slight hack to determine other user from host. Good for demo, not so good outside of demo.\nFix user being specified for chat recording message.\n\nValidated host was correctly populating bubble."}
{"comment": {"body": "Hack. For demo. The orginal implementation where the otherParty was the current user was wrong to begin with, so this is somewhat better.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/231#discussion_r757725814"}}
{"title": "Screen share app recording", "number": 232, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/232", "body": "Update the screen share app to support video recording"}
{"title": "Add references to completed screen share videos", "number": 233, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/233", "body": "We should be adding refrences when screen share videos are completed.\nAlso, now we can play the damned things. :)\nCurrently just storing local file uri."}
{"title": "Create and endpoint that returns a signed url for uploading an audio file", "number": 234, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/234", "body": "The idea here is to have an endpoint that the client can call to get an URL where an audio file can be uploaded. \nThe plan is to have a separate audio file for each speaker, so this endpoint will need to be called for each file.\nThe URL returned by this endpoint is a signed PUT s3 url that expires after 120 seconds. \nOnce all files for a chat have been uploaded, the client should call an API endpoint to trigger transcription (endpoint will come in another PR).\nThis endpoint has the form /bother/${botherId}/audio-file-upload-url?speakerName=SPEAKER _NAMEscreenRecordingId=SCREEN_RECORDING_ID. The two required parameters are:\n\nspeakerName: name of the speaker in the audio file (used to label the speaker of a block of text in the transcription, so should be unique for each speaker)\nscreenRecordingId: a unique ID that identifies the video being transcribed (the idea here is that this ID uniquely identifies a group of audio files and will later be passed to the API by the client when it wants to trigger transcription after uploading all files). One option is to use the message ID of the video bubble in the chat.\n\nTODO in other PRs:\n- create an endpoint that clients call to signal that transcribing should start\n- notify client that a transcription is complete and available for download"}
{"title": "Create endpoint to trigger transcription", "number": 236, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236", "body": "Creates and endpoint for clients to call when they're finished uploading audio files. Once called it will call the transcription service to trigger transcription. \nTODO in a separate PR:\n- notify the client when transcription is complete\n- provide an endpoint for grabbing the transcription"}
{"comment": {"body": "This policy is needed to allow calling `AWS.TranscribeService.startTranscriptionJob()`", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236#discussion_r759526413"}}
{"comment": {"body": "This is the screen recording ID that is passed as a query param to the  `getAudioFileUploadUrl` operation", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236#discussion_r759527627"}}
{"comment": {"body": "We probably should start putting this all in a centralized config for this stuff?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236#discussion_r759719896"}}
{"comment": {"body": "Good call, will do", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236#discussion_r759722570"}}
{"comment": {"body": "Gonna do this in a separate PR", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236#discussion_r759730358"}}
{"comment": {"body": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n\r\n\r\n<img width=20 src=\"https://raw.githubusercontent.com/Chapter2Inc/BotherFace/fd2d7b8ef890a1e7ba9dce8b730aee17a9750c7c/CodeswellSharing/Stuff/Assets.xcassets/AppIcon.appiconset/icon_16x16%402x%402x.png?token=AANXBSNSQM63Y7CLXUR3OUTBWOYF4\"/>  [Added by CodeSwell](url)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/236#discussion_r766942815"}}
{"title": "Enable HTML in tooltips", "number": 239, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/239", "body": "As requested by Ben.  I had to update the vscode API type definitions since this is a new feature."}
{"title": "Enable incremental builds in VSCode extension", "number": 24, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/24"}
{"title": "Fix bother updates in cache", "number": 242, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/242", "body": "The cache does bother comparisons to update cache.\nThis was not working correctly when we were updating references because we didn't have a utility function for comparing references.\nTo that end:\n1. We add a references array comparison\n2. We add a suite of tests"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/242#issuecomment-983029104", "body": "For V1 we should sidestep all of these kinds of things by having a last-changed UUID or timestamp or something on the object, and comparing those. Comparing an entire object like this is always going to be fragile.\n\n+1000"}
{"title": "Makes sure we wait for updates before refreshing", "number": 244, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/244", "body": "We shouldn't refresh until updates are complete"}
{"comment": {"body": "migh consider linting for this...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/244#discussion_r759696250"}}
{"comment": {"body": "Yeah definitely -- ESlint has a rule for this https://github.com/typescript-eslint/typescript-eslint/blob/main/packages/eslint-plugin/docs/rules/no-floating-promises.md", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/244#discussion_r759697389"}}
{"title": "Truncate webview title", "number": 245, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/245", "body": ""}
{"title": "Move bucket name to config file", "number": 246, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/246"}
{"comment": {"body": "maybe it would be better to pass this into a constructor?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/246#discussion_r759754060"}}
{"title": "Use repo commit API instead of commit search API to prevent Github rate limiting", "number": 248, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/248", "body": "Fixes #243 \nIn order to show avatars and full user names, we need to correlate repo emails to GitHub users.  We've done this by trying two separate approaches:\n1) Use the GitHub search API to search for a user with an email address.  This usually fails, because only users who have made their email address public in the GH settings can be found this way (and most people don't enable this).\n2) Use the GitHub search API to search for a commit (by its hash) that we know that user made.  The returned commit has author information we can use.\nThe problem is that the Search API is heavily rate-limited, so often these calls will fail.\nSo here is the new strategy:\n1) First use the GitHub repos API to directly fetch the commit, via its repo and hash.  This is a different API that has much less strict rate-limiting.\n2) If this fails, use the Search API to find the user by email (though I'd expect (1) will succeed for the demo)"}
{"comment": {"body": "It's a bit goofy that we parse the URL twice, but they give slightly different output.  getGithubUrl returns a decent canonicalized string, while GitUrlParse returns the decomposed URL elements.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/248#discussion_r760398428"}}
{"comment": {"body": "For a non-demo I woudl've asked you to comment this (or create its own function), but this is good enoughf or me. :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/248#discussion_r760416951"}}
{"comment": {"body": "For you, Rashin, I will put this in a comment.\r\n\r\n(I will also put it in a comment because you are right and that is where it belongs)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/248#discussion_r760437632"}}
{"title": "Create operation to get transcript url", "number": 249, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249", "body": "Creates an endpoint to allow the client to download a transcript. This endpoint returns a pre-signed url from where the file can be downloaded. The URL expires after 10 seconds."}
{"comment": {"body": "Endpoint isn't being used yet so this is safe", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249#discussion_r760401913"}}
{"comment": {"body": "(the shape is the same, so I think it would have been safe anyways)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249#discussion_r760402494"}}
{"comment": {"body": "Should the request provide the expiration?\r\nAlso, is 10 seconds enough? \r\nAlso, comments. :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249#discussion_r760435954"}}
{"comment": {"body": "I think 10 seconds should be enough, since I imagine the client will hit this endpoint when the customer clicks a \"Download\" button, and the client then just downloads the file immediately. ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249#discussion_r760437890"}}
{"comment": {"body": "There is the possibility for retries for downloads, but that's not a demo problem. :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249#discussion_r760438694"}}
{"comment": {"body": "Gotcha, yeah we can tune that (or the client can just hit this again on retry to get another 10 seconds)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/249#discussion_r760439357"}}
{"title": "Removing dead code", "number": 25, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/25"}
{"title": "Tweak annotation tooltip styling", "number": 250, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/250", "body": "Based on demo run-through feedback, I have tweaked the styling in this tooltip:\n- Shortened title string and added bold styling\n- Added avatars of conversation participants\n"}
{"title": "Add the notion of private messages for chat bubble", "number": 251, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/251", "body": "I've been asked by a very smart dude to show the chat screen share recording bubble only for the originator of the message.\nTo that end:\n1. We add isPrivate to Message.\n2. We only show message for private messages if user ids match."}
{"title": "Update screenshare app", "number": 252, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/252", "body": "This should bring the screen share app up to this build: https://github.com/Chapter2Inc/BotherFace/actions/runs/1532622785\non this PR: https://github.com/Chapter2Inc/BotherFace/pull/36\non this commit: https://github.com/Chapter2Inc/BotherFace/commit/995330704442b3ec2099709cad81a170d33ccf61"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/252#issuecomment-985050093", "body": "I want to get this in specifically because it adds the feature where audience apps shut down when the host closes, which we want for the demo."}
{"title": "Send pusher event when transcription is available", "number": 253, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/253", "body": "Goal here to signal to the client when the transcript for a screen recording is ready. \nThe idea is to have a handler that listens for a post from our lambda that processes transcriptions and sends a push notification when that request is received after verifying that the transcript indeed exists."}
{"comment": {"body": "The assumption here is the `botherId` === `channelId`", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/253#discussion_r761539113"}}
{"comment": {"body": "This is the endpoint that the [lambda](https://us-west-2.console.aws.amazon.com/lambda/home?region=us-west-2#/functions/process-transcription?tab=code) will call once its done processing the transcriptions (that lambda is a WIP :)).", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/253#discussion_r762120677"}}
{"title": "Make chat bubble visible by audience", "number": 255, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/255"}
{"title": "Add placeholders for screenshare callbacks", "number": 257, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/257", "body": "These will get sent once we merge my BotherFace PR and update the client app in this repo.\nFixes #256"}
{"title": "Make sure we invoke dispose correctly", "number": 258, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/258"}
{"title": "Update the screenshare app", "number": 259, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/259", "body": "Adds a hover button to quit the screen share app, new IDE socket commands, fixes to the audience view window restoration"}
{"title": "Fix fetch", "number": 26, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/26"}
{"title": "Show dot in parent items in the tree if there are unread messages", "number": 260, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/260", "body": "Fixes #240\n"}
{"title": "Update bother when transcription is available", "number": 261, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/261", "body": "We need persist the fact that a transcription is available because if a client starts up for the first time (or is restarted) it needs to know that a transcription exists."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/261#issuecomment-989040780", "body": "Closing in favour of https://github.com/Chapter2Inc/OctoberDemo/pull/264"}
{"comment": {"body": "@rasharab I think we chatted about naming the reference in a way that makes it possible to associate a transcription with the video file, so we'll need to fix this before merging", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/261#discussion_r763340180"}}
{"comment": {"body": "Please hold off on this. I'm taking a lightly different tact.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/261#discussion_r764282573"}}
{"comment": {"body": "https://github.com/Chapter2Inc/OctoberDemo/pull/264", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/261#discussion_r764282761"}}
{"title": "Fix remote audio in screen-share app", "number": 262, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/262", "body": "https://github.com/Chapter2Inc/BotherFace/pull/40"}
{"title": "Add audio transcript and fix up ui to ben's new mockups", "number": 264, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/264", "body": "This pr does the following:\n1. Audio/Video components of screenshare were decoupled which was making it a pain in the ass to manage things.\na. To that end, I've merged them. References now have an optional secondUrl, name, otherPartyName and message attachments have video/audio attributes.\n2. Add loading -> available links in ui for video/audio components of screenshare.\n3. Use socketId to reference screen shares\n4. Add referenceId to references so we can upsert to them as audio/video components become available.\n5. Screenshare message updates (video processing/ video available) are deduped at load time via the controller. Messages were designed to be immutable, so I just choose the last update.\n6. Fix bug where pusher channels were not being unsubscribed when chats are closed."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/264#issuecomment-988424437", "body": "Going to merge this change soon. Need to test some server side stuff with production server."}
{"title": "Update screen share app", "number": 265, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/265", "body": "Moves to using Next Chapter Software's developer Id\nNew BundleId too  unfortunately, this will mean that you'll have to give the app Mic, Camera and Screen Recording permissions again. To reduce confusion, first clear out all the old permissions by running tccutil reset All org.padraig.codeswell \nAlso includes https://github.com/Chapter2Inc/BotherFace/pull/44"}
{"title": "[RFC] Have Dennis's name show up in the transcript", "number": 266, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/266", "body": "Yes, this is naughty."}
{"comment": {"body": "Let me double check this does what I expect...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/266#discussion_r766239476"}}
{"comment": {"body": "Yep this resolves to the string literal", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/266#discussion_r766243437"}}
{"title": "Update app: Adds alert asking users if they'd like to make the video available to the team", "number": 267, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/267", "body": "Details here: https://github.com/Chapter2Inc/BotherFace/pull/49"}
{"title": "Introduce admin web service", "number": 268, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/268", "body": "Separate service initially intended to be used for backfilling DB with PR comments."}
{"comment": {"body": "This is basically the only change. Almost everything else is generated.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/268#discussion_r766278644"}}
{"title": "Backfill CodeSwell conversations with Github PR comments", "number": 269, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269", "body": "runs at startup in web service\nlimited to recent PR comments\ndoes not rerun ever\nHACKS: uses richie's personal access token. don't care\n\n\n\n\n"}
{"comment": {"body": "For conversations that only have one participant.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768249341"}}
{"comment": {"body": "For conversations that only have one participant.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768249375"}}
{"comment": {"body": "I don't know if running these serially was intentional or not, but you can run these in parallel with something like:\r\n\r\n```\r\nawait Promise.all(repoIngestRequests.map(request => ingestRepo(item)))\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768927134"}}
{"comment": {"body": "Ah I would imagine that running these in parallel might trigger GitHub API rate limiting...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768928839"}}
{"comment": {"body": "unnecessary todo. This stuff is going to be dead in a few weeks. :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768928848"}}
{"comment": {"body": "Doesn't matter for the demo at all, but just FYI we've found lodash can cause problems with typescript.  Some of the algorithms don't actually do what they say they do type-wise.  In the VSCode extension you can see how we did this in BotherUtils (arrayToMapArray)...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768932605"}}
{"comment": {"body": "exactly, this is intentional as I was hitting rate limits very quickly.\r\n\r\nGitHub has two rate limits as it applies to Personal Access Tokens:\r\n1. the **primary** rate limit is pretty transparent to the user where it lets you know in the response header exactly how many remaining request slots you can make, and when those slots will be refilled.\r\n2. the **secondary** rate limit is hidden from the user. It's based on a combination of signals (probably ML). It is especially sensitive to concurrent requests, even running a handful of concurrent requests over a the course of a minute will get the account throttled to zero for a while.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768943794"}}
{"comment": {"body": "Yeah, I did notice. Looking forward to working with Kotlin :)\r\nNot going to change anything. Not worth it.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/269#discussion_r768948489"}}
{"title": "Asking a question creates and saves a Bother", "number": 27, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/27", "body": "Work done in this PR:\n- Wire up message handling so a Webview can send eventing messages back to the extension\n- Use this so that when the Send button is clicked, the extension submits a new Bother to the service.\n- Update some bits of the BotherCache to allow adding/updating Bothers"}
{"comment": {"body": "Hah, I was just doing this right now!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/27#discussion_r737841483"}}
{"title": "Add api service", "number": 270, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/270", "body": "This is a test. \nNot meant to be reviewed."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/270#issuecomment-993938786", "body": "What you think about using Bazel? Benefit is long term leads to dramatically faster build/deploy times.\n"}
{"title": "Fix: Ingest doesn't run if there are manual bothers", "number": 271, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/271", "body": "slightly modify ingest to run iff there are no PrConversation bothers."}
{"title": "Add ability to ask OpenAI to explain a code snippet", "number": 272, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/272", "body": "Simple first pass at integrating OpenAI to check out its capabilities. \nSend please explain this to the chat and the code snippet will be sent to OpenAI to ask it to explain the code ().\nRequires passing an api key as an environment variable (OPENAI_API_KEY) to the web service. You can get one by creating an account at ."}
{"comment": {"body": "I think we make this assumption everywhere", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/272#discussion_r769126581"}}
{"comment": {"body": "yep", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/272#discussion_r769131060"}}
{"comment": {"body": "Ignore this comment :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/272#discussion_r783461828"}}
{"title": "Deduplicate participants based on ID", "number": 273, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/273", "body": "we were using name , which is unreliable since name is often not avilable and we have to fallback to username.\ninstead deduplicate using id, which is username under the hood.\n\n\nBefore\n\n\nAfter\n"}
{"title": "Update Screenshare App", "number": 274, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/274", "body": "Changes from: https://github.com/Chapter2Inc/BotherFace/pull/50"}
{"title": "Fix some minor db problems", "number": 275, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/275"}
{"title": "Introduce SourceMark, SourceVector, SourcePoint models", "number": 276, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/276", "body": ""}
{"title": "DeployKotlinService", "number": 277, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/277", "body": "Fix settings\nAdd shadow jar\nFix shadowjar\nExperiment with deployments\nExperiemtn with deployment"}
{"title": "Fixing webhook handler", "number": 278, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/278"}
{"title": "Move to kotlin service", "number": 279, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/279", "body": "This pr transitions us away from the nodejs web service.\nThe Kotlin service is feature parity with the web service.\nI've tested: \n1. Chat\n2. Audio Transcription\n3. Video\nDeployment happens here:\n\nCool thing is we halved the deployment time. Apparently, building gradle is faster than node thanks to caching dependencies...."}
{"title": "Fix bother api server generation", "number": 28, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/28", "body": "Moving server generation code to latest fix I submitted for json conversion for server types.\nhttps://github.com/matthewjamesadam/openapi-typescript-server/pull/1/files"}
{"title": "Delete", "number": 280, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/280"}
{"title": "E;liminate web deps", "number": 281, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/281"}
{"title": "AddDockerTests", "number": 282, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/282", "body": "Add makefile and stuff\nAdd test dependencies\nAnother change"}
{"title": "Add Git utilities and Git diff hunk parser", "number": 283, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/283", "body": "Parser is based on:\n\nTODO\n\n[ ] None of this is hooked up to basic unit tests yet"}
{"title": "Add commitSha and fileHash to legacy Bother", "number": 284, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/284", "body": "This change just allows the client to send/receive bothers with extra info needed for reliable SourceMark tracking (commitSha, fileHash), but they are currently unused.\nUsing these fields will come in a subsequent change.\nDoing this as a temporary proof-of-concept before implementing totally new models as proposed in #276.\n\nREVIEWER NOTE: easier to review by commit"}
{"title": "Add Git hash object method for generating file hashes", "number": 285, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/285", "body": "unused currently; will be hooked up to bother creation in subsequent change\nalso GitDiff class refactor to separate file"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/285#issuecomment-999176357", "body": "just going to merge this simple one, as I need it for follow on change"}
{"title": "Persist SourceMark properties with legacy Bother model in VSCode", "number": 286, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/286", "body": "save SourceMark stuff on server, but unused currently\nfollow up change will use the SourceMark properties to compute diff later"}
{"title": "AddKtorOpenSpec", "number": 287, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/287", "body": "This pr adds a ktor generator that allows for delegate implementation.\nThe pattern is this:\n1. Generate basic ktor routes \n2. The ktor routes take in a delegate which we define.\nThis piggy backs on an existing openapi generator known as kotlin-server.\nWe downloaded the kotlin-server template (in full) and we customized it to meet our needs (delegate pattern).\nWe are using ktor locations for typed input.\nGenerated a sample package to indicate how it works. \n"}
{"comment": {"body": "Add our custom openapi routes here :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/287#discussion_r773546954"}}
{"comment": {"body": "rename `Params`?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/287#discussion_r773640161"}}
{"comment": {"body": "This file is used to exclude crap generated by base kotlin-server template.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/287#discussion_r773714787"}}
{"title": "Minor change", "number": 288, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/288"}
{"title": "MoveToKtor", "number": 289, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/289", "body": "This pr does a lot of things:\n1. Fixes up a lot of problems with openapi generator for ktor (handling null values, post bodes). \na. Apparently, Ktor locations only deserializes path parameters, we have to handle bodies.\n2. Move all code to ktors and update all tests.\n3. Move all code to Kmongo + Lettuce (for redis)\nTesting:\n1.  Have a whole suite of unit tests that caught a few issues.\n2. Ran extension and validated chat/bothers/video worked."}
{"title": "Build VSCode extension in CI", "number": 29, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/29", "body": "For now this is just building, make sure nobody screws up the API or types anywhere.  We can add deployment whenever we figure out where it should go to.\nTesting is commented out because it seemed to block, I'll figure that out later."}
{"title": "Disable datas as timestamps for serialization", "number": 290, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/290", "body": "Need to ensure that dates serialized back to client are not turned into weird timestmaps."}
{"title": "Remove unnecessary locations", "number": 291, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/291"}
{"title": "Clean up Amazon Transcribe logic", "number": 292, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/292", "body": "Clean up the code and adding some tests. This is in prep to move over to AssemblyAI for transcriptions, since a lot of the S3 calls we'll still need for AssemblyAI."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/292#issuecomment-1000543927", "body": "I suck at naming, @rasharab please rename anything that looks weird after I merge"}
{"title": "Update Amazon Transcribe webhook processing", "number": 293, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/293", "body": "Updates our amazon transcribe web hook handler to receive web hooks from a simpler lambda (amazon-transcribe-webhook). This is so that we can remove the process-transcription lambda, which is untested and brittle."}
{"title": "Check if not null or blank", "number": 294, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/294"}
{"title": "Dont create transcript if audio files have not been transcribed", "number": 295, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/295", "body": "The hook here is sent when transcription fails, so we should only create a transcript if at least one succeeds."}
{"title": "Fix audio transcription", "number": 296, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/296", "body": "Fix audio transcription (wrong logic for checking files)\nAdd logging\nMinor code gen improvement"}
{"comment": {"body": "Just merged in a fix haha", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/296#discussion_r776566971"}}
{"comment": {"body": "Yeah, you got to me first mate. :) Oh well. I still think logging might help so I'll push it.\r\nthanks for the approval!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/296#discussion_r776568097"}}
{"title": "Prepare to use repo identifier", "number": 298, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/298", "body": "This is a refactoring change in preparation for transitioning to the repo root\ncommit as the ID for a repo in CodeSwell.\nI'm staging the PRs to reduce risk. This change does not affect funtionality at all.\nA follow on change will flip a flag to change the behaviour.\nMotivation for the change is here:\n"}
{"comment": {"body": "A follow on change will deal with flipping this flag, after testing thoroughly locally. There may also be some migration work to preserve existing Bothers, which would also be in follow up change.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/298#discussion_r779162340"}}
{"title": "[WIP] Add Git integration tests using testcontainer", "number": 299, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299", "body": "TODO\n- [ ] not clear to me how to run docker in GitHub Actions yet"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#issuecomment-1006878395", "body": "FYI, we're booting up docker services in api actions workflow for testing purposes."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#issuecomment-1006889136", "body": "Can you explain the benefit of using docker here vs just cloning the required repos locally?  I'm not second-guessing, it's just not super clear to me."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#issuecomment-1006917004", "body": "Can you explain the benefit of using docker here vs just cloning the required repos locally? I'm not second-guessing, it's just not super clear to me.\n\n\nstable environment for reproducibility: pinned version of Git, no local git config interfering, precise git repo\n[less important] decouple test fixtures from GitHub remote"}
{"comment": {"body": "I should move this to `package.json` as part of test runner...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#discussion_r779791896"}}
{"comment": {"body": "https://www.testcontainers.org", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#discussion_r779792202"}}
{"comment": {"body": "Everything below this line was just moved (unmodified) from `Git.ts`", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#discussion_r779794020"}}
{"comment": {"body": "Testing, ignore this comment.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/299#discussion_r782561522"}}
{"title": "Add react webview integration", "number": 3, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/3", "body": "Add some .vscode launch tasks etc\nAdd webview controllers and views to extension.\nAdd npm script options to package.json (\"npm run compile\" will build everything)\nAdd \"Open Bother Chat Panel\" command to package.json.\nAdd webview react skeleton\n\nThe way it works (as dicussed).\n1. npm run compile will generate all relevant code (including react components) into dist folder\n2. The extension webview wrapper will take the contents of the dist folder and generate an html that it will load into the panel.\nFolder Hierarchy:\nvscode \n|-extension\n|---controller\n|---webview\n|-webview (react)\nTesting:\nManual"}
{"comment": {"body": "Not using VSCode anymore? :) ", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/3#discussion_r732056337"}}
{"title": "Add basic tree view", "number": 30, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/30", "body": "\nAdd a tree view displaying all Bothers.  The tree view is driven by the BotherCache, and updates when the bother list changes, either from pulling remote data or from updating locally.\nThere's no good label to display for Bothers right now so I'm displaying the ID, I'll fix this in the next PR."}
{"comment": {"body": "Ideally this would be a union type of strings and fixed enums, but that doesn't work for some reason.  Fixed strings are fine for now.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/30#discussion_r737906639"}}
{"title": "Add code coverage with JaCoCo and CodeCov", "number": 300, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/300"}
{"title": "Add badge for Coverage", "number": 301, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/301"}
{"title": "Add Detekt static analysis", "number": 302, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/302"}
{"title": "Config uses HOCON instead of YAML", "number": 303, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/303", "body": "HOCON is from here:\n"}
{"title": "Add ktlint", "number": 304, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/304"}
{"title": "Revert \"Add Detekt static analysis\"", "number": 305, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/305", "body": "Reverts Chapter2Inc/OctoberDemo#302"}
{"title": "Add postgres container", "number": 306, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/306"}
{"comment": {"body": "this is a nice pattern. we should do this for mongo, redis too", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/306#discussion_r782400268"}}
{"title": "DO NOT MERGE. References with dummy data", "number": 307, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/307", "body": "dfsdfdsdsf\nDescribing changes to the code block above."}
{"comment": {"body": "Test comment with test image. ```djkfsjlfks```\r\n![istockphoto-1207862195-1024x1024](https://user-images.githubusercontent.com/13353189/149041298-a1c47e82-80a8-45a6-9e2e-ab0b9c5960a2.jpg)\r\n\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/307#discussion_r782618217"}}
{"title": "Use AssemblyAI for transcription", "number": 308, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/308", "body": "Adds logic to run transcriptions using AssemblyAI instead of Amazon transcribe. I've kept the Amazon Transcribe logic around in case we need to switch back. No tests but I'll add those when we move this logic over to the production version."}
{"comment": {"body": "This is my personal (free) account key. When we port this over to production, we'll create a team account and check that API key into vault.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/308#discussion_r783668548"}}
{"comment": {"body": "ktor rocks :)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/308#discussion_r784098344"}}
{"comment": {"body": "Out of curiosity. Is the path a canoncial file path. I wonder if we can use URI or something to parse these things.\r\nFor now, this is okay.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/308#discussion_r784099374"}}
{"title": "Add logging abilities using logz.io", "number": 309, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/309", "body": "We are moving to using logz.io using their appender"}
{"title": "Update Bother model to have description and Person data", "number": 31, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/31", "body": "Remove separate id parameter on saveBother operation\nAdd description property to Bother -- this will be the initial question the originator asked\nAdd Person model\nAdd originator and recipient Persons to Bothers"}
{"title": "Add more structured logs", "number": 310, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/310"}
{"title": "Reduce presigned url signature duration to 10 minutes", "number": 311, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/311", "body": "120 minutes is overkill"}
{"title": "Update my GitHub Personal Access token", "number": 312, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/312", "body": "this token will expire in 60 days ... :boom:"}
{"title": "Fix bad regex", "number": 313, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/313"}
{"comment": {"body": "This is the only actual change -- the regex is invalid.  The rest of the changes in the file are prettier updates.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/313#discussion_r839801236"}}
{"title": "Configure Renovate", "number": 314, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/314", "body": "Welcome to Renovate! This is an onboarding PR to help you understand and configure settings before regular Pull Requests begin.\n To activate Renovate, merge this Pull Request. To disable Renovate, simply close this Pull Request unmerged.\n\nDetected Package Files\n\ndocker-compose.java.yml (docker-compose)\ndocker-compose.web.yml (docker-compose)\ndocker-compose.yml (docker-compose)\napiservice/Dockerfile (dockerfile)\napiservice/Dockerfile.standalone (dockerfile)\nsignalling/Dockerfile (dockerfile)\nweb/Dockerfile (dockerfile)\n.github/workflows/api.yml (github-actions)\n.github/workflows/signal.yml (github-actions)\n.github/workflows/vscode.yml (github-actions)\n.github/workflows/web.yml (github-actions)\ngradle.properties (gradle)\nsettings.gradle.kts (gradle)\nbuild.gradle.kts (gradle)\napiservice/build.gradle.kts (gradle)\nktortest/gradle.properties (gradle)\nktortest/build.gradle.kts (gradle)\napiservice/gradle/wrapper/gradle-wrapper.properties (gradle-wrapper)\ngradle/wrapper/gradle-wrapper.properties (gradle-wrapper)\nktortest/gradle/wrapper/gradle-wrapper.properties (gradle-wrapper)\napi/package.json (npm)\ndevops/package.json (npm)\nsignalling/package.json (npm)\nvscode/package.json (npm)\nweb/package.json (npm)\n\nConfiguration Summary\nBased on the default config's presets, Renovate will:\n\nStart dependency updates only once this onboarding PR is merged\nEnable Renovate Dependency Dashboard creation.\nIf Renovate detects semantic commits, it will use semantic commit type fix for dependencies and chore for all others.\nIgnore node_modules, bower_components, vendor and various test/tests directories.\nAutomatically detect the best rangeStrategy to use.\nRate limit PR creation to a maximum of two per hour.\nLimit to maximum 10 open PRs at any time.\nGroup known monorepo packages together.\nUse curated list of recommended non-monorepo package groupings.\nA collection of workarounds for known problems with packages.\n\n Would you like to change the way Renovate is upgrading your dependencies? Simply edit the renovate.json in this branch with your custom config and the list of Pull Requests in the \"What to Expect\" section below will be updated the next time Renovate runs.\n\nWhat to Expect\nWith your current configuration, Renovate will create 58 Pull Requests:\n\nUpdate dependency moment to v2.29.4 [SECURITY]\n\n  - Branch name: `renovate/npm-moment-vulnerability`\n  - Merge into: `main`\n  - Upgrade [moment]() to `2.29.4`\n\n\n\n\n\nUpdate dependency nconf to v0.11.4 [SECURITY]\n\n  - Branch name: `renovate/npm-nconf-vulnerability`\n  - Merge into: `main`\n  - Upgrade [nconf]() to `0.11.4`\n\n\n\n\n\nUpdate gradle/wrapper-validation-action digest to 9aa31f2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/gradle-wrapper-validation-action-digest`\n  - Merge into: `main`\n  - Upgrade gradle/wrapper-validation-action to `9aa31f26bc8e536d1faf4b332bb8365350743a18`\n\n\n\n\n\nUpdate dependency @types/node to v10.17.60\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/node-10.x`\n  - Merge into: `main`\n  - Upgrade [@types/node]() to `10.17.60`\n\n\n\n\n\nUpdate dependency com.pusher:pusher-http-java to v1.3.3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.pusher-pusher-http-java-1.x`\n  - Merge into: `main`\n  - Upgrade [com.pusher:pusher-http-java]() to `1.3.3`\n\n\n\n\n\nUpdate dependency io.logz.logback:logzio-logback-appender to v1.0.28\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/io.logz.logback-logzio-logback-appender-1.x`\n  - Merge into: `main`\n  - Upgrade [io.logz.logback:logzio-logback-appender]() to `1.0.28`\n\n\n\n\n\nUpdate dependency org.jetbrains:kotlin-css-jvm to v1.0.0-pre.156-kotlin-1.5.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.jetbrains-kotlin-css-jvm-1.x`\n  - Merge into: `main`\n  - Upgrade [org.jetbrains:kotlin-css-jvm]() to `1.0.0-pre.156-kotlin-1.5.0`\n\n\n\n\n\nUpdate dependency org.mongodb:mongo-java-driver to v3.12.11\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.mongodb-mongo-java-driver-3.x`\n  - Merge into: `main`\n  - Upgrade [org.mongodb:mongo-java-driver]() to `3.12.11`\n\n\n\n\n\nUpdate ktor_version to v1.6.8\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/ktor_version`\n  - Merge into: `main`\n  - Upgrade [io.ktor:ktor-server-tests]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-metrics]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-gson]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-jackson]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-server-netty]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-serialization]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-html-builder]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-client-gson]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-client-apache]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-client-core-jvm]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-client-core]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-locations]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-auth]() to `1.6.8`\n  - Upgrade [io.ktor:ktor-server-core]() to `1.6.8`\n\n\n\n\n\nUpdate plugin com.github.johnrengelman.shadow to v7.1.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.github.johnrengelman.shadow-7.x`\n  - Merge into: `main`\n  - Upgrade com.github.johnrengelman.shadow to `7.1.2`\n\n\n\n\n\nUpdate aws-cdk monorepo to v1.189.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/aws-cdk-monorepo`\n  - Merge into: `main`\n  - Upgrade [@aws-cdk/assert]() to `1.189.0`\n  - Upgrade [@aws-cdk/core]() to `1.189.0`\n  - Upgrade [aws-cdk]() to `1.189.0`\n\n\n\n\n\nUpdate dependency ch.qos.logback:logback-classic to v1.4.5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/logback_version`\n  - Merge into: `main`\n  - Upgrade [ch.qos.logback:logback-classic]() to `1.4.5`\n\n\n\n\n\nUpdate dependency com.fasterxml.jackson.datatype:jackson-datatype-jsr310 to v2.14.1\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/com.fasterxml.jackson.datatype-jackson-datatype-jsr310-2.x`\n  - Merge into: `main`\n  - Upgrade [com.fasterxml.jackson.datatype:jackson-datatype-jsr310]() to `2.14.1`\n\n\n\n\n\nUpdate dependency gradle to v7.6\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/gradle-7.x`\n  - Merge into: `main`\n  - Upgrade [gradle]() to `7.6`\n\n\n\n\n\nUpdate dependency io.dropwizard.metrics:metrics-core to v4.2.15\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/io.dropwizard.metrics-metrics-core-4.x`\n  - Merge into: `main`\n  - Upgrade [io.dropwizard.metrics:metrics-core]() to `4.2.15`\n\n\n\n\n\nUpdate dependency io.lettuce:lettuce-core to v6.2.2.RELEASE\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/io.lettuce-lettuce-core-6.x`\n  - Merge into: `main`\n  - Upgrade [io.lettuce:lettuce-core]() to `6.2.2.RELEASE`\n\n\n\n\n\nUpdate dependency net.logstash.logback:logstash-logback-encoder to v7.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/net.logstash.logback-logstash-logback-encoder-7.x`\n  - Merge into: `main`\n  - Upgrade [net.logstash.logback:logstash-logback-encoder]() to `7.2`\n\n\n\n\n\nUpdate dependency org.assertj:assertj-core to v3.24.2\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.assertj-assertj-core-3.x`\n  - Merge into: `main`\n  - Upgrade [org.assertj:assertj-core]() to `3.24.2`\n\n\n\n\n\nUpdate dependency org.jetbrains.kotlinx:kotlinx-html-jvm to v0.8.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.jetbrains.kotlinx-kotlinx-html-jvm-0.x`\n  - Merge into: `main`\n  - Upgrade [org.jetbrains.kotlinx:kotlinx-html-jvm]() to `0.8.0`\n\n\n\n\n\nUpdate dependency org.litote.kmongo:kmongo to v4.8.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.litote.kmongo-kmongo-4.x`\n  - Merge into: `main`\n  - Upgrade [org.litote.kmongo:kmongo]() to `4.8.0`\n\n\n\n\n\nUpdate dependency software.amazon.awssdk:bom to v2.19.23\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/aws-java-sdk-v2-monorepo`\n  - Merge into: `main`\n  - Upgrade [software.amazon.awssdk:bom]() to `2.19.23`\n\n\n\n\n\nUpdate kotlin monorepo to v1.8.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/kotlin-monorepo`\n  - Merge into: `main`\n  - Upgrade [org.jetbrains.kotlin:kotlin-test-junit]() to `1.8.0`\n  - Upgrade [org.jetbrains.kotlin.plugin.jpa]() to `1.8.0`\n  - Upgrade [org.jetbrains.kotlin.plugin.serialization]() to `1.8.0`\n  - Upgrade [org.jetbrains.kotlin.jvm]() to `1.8.0`\n\n\n\n\n\nUpdate kotlinx_coroutines_version to v1.6.4\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/kotlinx_coroutines_version`\n  - Merge into: `main`\n  - Upgrade [org.jetbrains.kotlinx:kotlinx-coroutines-reactive]() to `1.6.4`\n  - Upgrade [org.jetbrains.kotlinx:kotlinx-coroutines-reactor]() to `1.6.4`\n  - Upgrade [org.jetbrains.kotlinx:kotlinx-coroutines-core]() to `1.6.4`\n\n\n\n\n\nUpdate plugin org.openapi.generator to v5.4.0\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.openapi.generator-5.x`\n  - Merge into: `main`\n  - Upgrade org.openapi.generator to `5.4.0`\n\n\n\n\n\nUpdate Node.js to v19\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/node-19.x`\n  - Merge into: `main`\n  - Upgrade [node]() to `19`\n\n\n\n\n\nUpdate actions/checkout action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-checkout-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/checkout]() to `v3`\n\n\n\n\n\nUpdate actions/download-artifact action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-download-artifact-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/download-artifact]() to `v3`\n\n\n\n\n\nUpdate actions/setup-java action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-setup-java-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/setup-java]() to `v3`\n\n\n\n\n\nUpdate actions/setup-node action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-setup-node-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/setup-node]() to `v3`\n\n\n\n\n\nUpdate actions/upload-artifact action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/actions-upload-artifact-3.x`\n  - Merge into: `main`\n  - Upgrade [actions/upload-artifact]() to `v3`\n\n\n\n\n\nUpdate aws-cdk monorepo to v2 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-aws-cdk-monorepo`\n  - Merge into: `main`\n  - Upgrade [@aws-cdk/assert]() to `2.62.0`\n  - Upgrade [aws-cdk]() to `2.62.0`\n\n\n\n\n\nUpdate codecov/codecov-action action to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/codecov-codecov-action-3.x`\n  - Merge into: `main`\n  - Upgrade [codecov/codecov-action]() to `v3`\n\n\n\n\n\nUpdate dependency @octokit/rest to v19\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/octokit-rest-19.x`\n  - Merge into: `main`\n  - Upgrade [@octokit/rest]() to `^19.0.0`\n\n\n\n\n\nUpdate dependency @octokit/types to v9\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/octokit-types-9.x`\n  - Merge into: `main`\n  - Upgrade [@octokit/types]() to `^9.0.0`\n\n\n\n\n\nUpdate dependency @testing-library/react to v13\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/testing-library-react-13.x`\n  - Merge into: `main`\n  - Upgrade [@testing-library/react]() to `^13.0.0`\n\n\n\n\n\nUpdate dependency @testing-library/user-event to v14\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/testing-library-user-event-14.x`\n  - Merge into: `main`\n  - Upgrade [@testing-library/user-event]() to `^14.0.0`\n\n\n\n\n\nUpdate dependency @types/node to v18\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/node-18.x`\n  - Merge into: `main`\n  - Upgrade [@types/node]() to `^18.0.0`\n  - Upgrade [@types/node]() to `18.11.18`\n\n\n\n\n\nUpdate dependency @types/react to v18\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/react-18.x`\n  - Merge into: `main`\n  - Upgrade [@types/react]() to `^18.0.0`\n\n\n\n\n\nUpdate dependency @types/react-dom to v18\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/react-dom-18.x`\n  - Merge into: `main`\n  - Upgrade [@types/react-dom]() to `^18.0.0`\n\n\n\n\n\nUpdate dependency copy-webpack-plugin to v11\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/copy-webpack-plugin-11.x`\n  - Merge into: `main`\n  - Upgrade [copy-webpack-plugin]() to `^11.0.0`\n\n\n\n\n\nUpdate dependency git-url-parse to v13\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/git-url-parse-13.x`\n  - Merge into: `main`\n  - Upgrade [git-url-parse]() to `^13.0.0`\n\n\n\n\n\nUpdate dependency pusher-js to v8\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/pusher-js-8.x`\n  - Merge into: `main`\n  - Upgrade [pusher-js]() to `^8.0.0`\n\n\n\n\n\nUpdate dependency react-scripts to v5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/react-scripts-5.x`\n  - Merge into: `main`\n  - Upgrade [react-scripts]() to `5.0.1`\n\n\n\n\n\nUpdate dependency sass-loader to v13\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/sass-loader-13.x`\n  - Merge into: `main`\n  - Upgrade [sass-loader]() to `^13.0.0`\n\n\n\n\n\nUpdate dependency stream-chat to v8\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/stream-chat-8.x`\n  - Merge into: `main`\n  - Upgrade [stream-chat]() to `^8.0.0`\n\n\n\n\n\nUpdate dependency ts-node to v10\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/ts-node-10.x`\n  - Merge into: `main`\n  - Upgrade [ts-node]() to `^10.0.0`\n\n\n\n\n\nUpdate dependency twilio to v4\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/twilio-4.x`\n  - Merge into: `main`\n  - Upgrade [twilio]() to `^4.0.0`\n\n\n\n\n\nUpdate dependency type-fest to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/type-fest-3.x`\n  - Merge into: `main`\n  - Upgrade [type-fest]() to `^3.0.0`\n\n\n\n\n\nUpdate dependency typescript to v4\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/typescript-4.x`\n  - Merge into: `main`\n  - Upgrade [typescript]() to `~4.9.0`\n\n\n\n\n\nUpdate dependency uuid to v9\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/uuid-9.x`\n  - Merge into: `main`\n  - Upgrade [uuid]() to `^9.0.0`\n  - Upgrade [@types/uuid]() to `^9.0.0`\n\n\n\n\n\nUpdate dependency web-vitals to v3\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/web-vitals-3.x`\n  - Merge into: `main`\n  - Upgrade [web-vitals]() to `^3.0.0`\n\n\n\n\n\nUpdate dependency webpack-cli to v5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/webpack-cli-5.x`\n  - Merge into: `main`\n  - Upgrade [webpack-cli]() to `^5.0.0`\n\n\n\n\n\nUpdate dependency webpack-manifest-plugin to v5\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/webpack-manifest-plugin-5.x`\n  - Merge into: `main`\n  - Upgrade [webpack-manifest-plugin]() to `^5.0.0`\n\n\n\n\n\nUpdate hoplite_version to v2 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-hoplite_version`\n  - Merge into: `main`\n  - Upgrade [com.sksamuel.hoplite:hoplite-yaml]() to `2.7.0`\n  - Upgrade [com.sksamuel.hoplite:hoplite-core]() to `2.7.0`\n\n\n\n\n\nUpdate jest monorepo to v29 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-jest-monorepo`\n  - Merge into: `main`\n  - Upgrade [@types/jest]() to `^29.0.0`\n  - Upgrade [jest]() to `^29.0.0`\n  - Upgrade [ts-jest]() to `^29.0.0`\n\n\n\n\n\nUpdate ktor_version to v2 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-ktor_version`\n  - Merge into: `main`\n  - Upgrade [io.ktor:ktor-server-tests]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-server-netty]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-serialization]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-client-gson]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-client-apache]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-client-core-jvm]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-client-core]() to `2.2.2`\n  - Upgrade [io.ktor:ktor-server-core]() to `2.2.2`\n\n\n\n\n\nUpdate plugin org.openapi.generator to v6\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/org.openapi.generator-6.x`\n  - Merge into: `main`\n  - Upgrade org.openapi.generator to `6.2.1`\n\n\n\n\n\nUpdate react monorepo to v18 (major)\n\n  - Schedule: [\"at any time\"]\n  - Branch name: `renovate/major-react-monorepo`\n  - Merge into: `main`\n  - Upgrade [react]() to `^18.0.0`\n  - Upgrade [react-dom]() to `^18.0.0`\n\n\n\n\n\n Branch creation will be limited to maximum 2 per hour, so it doesn't swamp any CI resources or overwhelm the project. See docs for prhourlylimit for details.\n\n Got questions? Check out Renovate's Docs, particularly the Getting Started section.\nIf you need any further assistance then you can also request help here.\n\nThis PR has been generated by Mend Renovate. View repository job log here."}
{"title": "InitialChatImplementation", "number": 32, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32", "body": "Add initial implementation\n1. Add dependencies on getstream.io\n2. Add controllers wrapping getstream functionality\n3. Add some helper dependencies just to get things going.\n4. Extension  UI message passing is a bit inefficient, as we're passing a large chunk of the chat state to react.\n5. Use explicit chat interfaces to guarantee contract between extenstion  webview\nTODO:\n1. Optimize message passing.\n2. Make state updates in react more efficient.\n3. Flesh out UI more\n4. Integrate Matt's persons..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#issuecomment-954149460", "body": "Awesome   "}
{"comment": {"body": "getstream chat client does websocket stuff. Needed to remove that for now to unblock.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738631530"}}
{"comment": {"body": "MOCK SHIT!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738631858"}}
{"comment": {"body": "Do you think it's worth making GitHub issues for this kind of thing so we unwind this later if we change the chat implementation?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738653830"}}
{"comment": {"body": "I guess we don't really care about committing this into the repo?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738654946"}}
{"comment": {"body": "This will eventually be taken from the Bother that this chat is for, correct?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738655272"}}
{"comment": {"body": "You can probably use the Person model for this now?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738656716"}}
{"comment": {"body": "Correct.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738659720"}}
{"comment": {"body": "Correct.\r\nThis thing is going to be replaced and it's a preprod environment.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738659997"}}
{"comment": {"body": "Good point!", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738660090"}}
{"comment": {"body": "Yes.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738660385"}}
{"comment": {"body": "https://github.com/Chapter2Inc/OctoberDemo/issues/33", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738664233"}}
{"comment": {"body": "We should probably define failover values, even if they're bad ones? (`userId: message.user?.id || ''` or something?)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738698565"}}
{"comment": {"body": "Probably none of this matters if we're ripping out the chat service code soon, but you can do this type-safe with something like `if (event.message?.user?.id !== user.userId)`", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738701774"}}
{"comment": {"body": "I'd like to figure out a way of typing the properties we hand across the extension <-> react divide so we don't have to do things like this -- I manually checked the property content to make sure it was correct but it's a huge pain to do...", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738702758"}}
{"comment": {"body": "FYI doesn't really matter here, but the recommended way to do things like this is to use `React.useCallback` to memoize the function, so the function is only re-bound whenever relevant values change, instead of on every render.  React hooks is strange.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/32#discussion_r738707098"}}
{"title": "Correctly set originator and receiver data on Bother", "number": 34, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/34", "body": "When we create a new Bother, add the correct originator (from the currently-logged-in GitHub user) and receiver (from whoever they pick in the 'Ask a Question' UI."}
{"title": "Gutter annotations showing based on Points of Interest in bothers", "number": 35, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/35", "body": "This connects the store up to show the gutter, with POIs in any open files\n\nNext up I need to wire in my editor code that updates POIs as the developer types in the editor."}
{"title": "Package and upload VSCode extension", "number": 36, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/36", "body": "Fix dependencies so extension runtime dependencies are packaged correctly\nAdd npm run package command\nAdd packaging metadata"}
{"title": "Connect AskQuestion to Chat", "number": 37, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/37", "body": "We now invoke ChatCommand via AskQuestion View!\n\nEncapsulate user/recipient/originator/channelId into a ChannelMetadata object.\nClean up some code based off Matt's suggestions."}
{"title": "VSCode prod build", "number": 38, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/38"}
{"comment": {"body": "This was the only real change -- local -> production by default", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/38#discussion_r738836187"}}
{"title": "POI locations update locally as user edits the file", "number": 39, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/39"}
{"title": "Prettier settings", "number": 4, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/4", "body": "Set up prettier as a recommended extension and default file formatter\nRemove nested .vscode folder as it doesn't do anything AFAICT"}
{"title": "Add production vscode launch target", "number": 40, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/40"}
{"title": "Add tree provider callbacks", "number": 41, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/41"}
{"title": "Fix Ask a Question UI", "number": 42, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/42", "body": "Grey out 'Send' button until a person is selected and a question is entered\nSelect first person by default\nAdd editor context menu item for 'Ask a Question'"}
{"title": "Fix Hover View + New Gutter Icon", "number": 43, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/43"}
{"title": "Switch tree view label to something more accurate", "number": 46, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/46", "body": "(PS @rasharab install prettier!)"}
{"title": "Make initial load faster", "number": 47, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/47", "body": "Does initial fetch without waiting for poll"}
{"title": "Use webpack instead of CRA", "number": 5, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/5", "body": "Replace create-react-app with webpack.  This also merges all of the webview configuration into the parent vscode folder, so that npm dependencies, typescript configurations, etc can be consistent between the extension and webview code."}
{"title": "Normalize github repo URL", "number": 51, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/51", "body": "Normalize all GitHub repo URLs to something in the form .  This should ensure that people on a team see the same Codeswell data regardless of how they cloned their repo.\nFixes #45"}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/51#issuecomment-955069492", "body": "Nice! "}
{"title": "Move some files so we have fewer index.ts's everywhere", "number": 6, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/6", "body": "@rasharab I moved a couple files up a level -- when every file is an index.ts I find it hard to tell files apart in the editor tabs and debugger.  Let me know if you disagree with this."}
{"title": "Fix light theme message input box border", "number": 60, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/60", "body": "Fixes #48"}
{"comment": {"body": "I also fixed styling basic buttons to support hover state and borders (useful for high-contrast mode)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/60#discussion_r739570697"}}
{"title": "Fix issue where code lines would nudge when gutter decoration is added", "number": 69, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/69", "body": "Makes the gutter icon smaller, which solves an issue where the code line was nudging a few pixels when gutters were added"}
{"title": "Build & display multiple React UIs", "number": 7, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/7", "body": "Allow displaying different React UIs in the extension:\n\nAdd multiple entry points in the webpack config\nWebviewController/WebviewContainer now take as an argument the name of the UI to load\n\nI had to do a little bit of trickery with the extension context/location -- previously you had to pass the ExtensionContext through to the WebviewController, but in some scenarios (like the 'Ask a Question' command) that might be very hard or not possible.  So instead I made a little helper class (ExtensionState) that is assigned the extension location when the extension is activated, and keeps it available for anyone else afterwards.  I'm open to alternatives or reverting this."}
{"comment": {"body": "I renamed your 'index' file to be chat-specific .. maybe should have capitalized it, but I'll leave that up to you", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/7#discussion_r733051258"}}
{"title": "Fix UI bugs in 'Ask a Question' UI", "number": 70, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/70", "body": "Fixes the following:\n\nUse correct mouse cursor for the entirely of the candidate button (#62)\nAdd placeholder in message input (#63) -- note that Chat can hook into this too by providing the destinationName prop to MessageInput)\nFix Webview JS debugging (#68) -- for whatever reason the debugger in VSCode only works with eval-style source maps."}
{"title": "Fixes left alignment on new lines", "number": 71, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/71", "body": "Fixes #52 by regenerating the gutter decorations on every line changing edit."}
{"title": "Add chat right panel", "number": 73, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/73", "body": "This adds chat right panel with a lot of css crap.\nAlso some backend changes to types to ensure we can populate the right panel correctly."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/73#issuecomment-956830567", "body": "Submitting this until next round..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/73#issuecomment-956832106", "body": "Split up the scss files..."}
{"title": "Adding react icons which includes vscode icons", "number": 74, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/74", "body": "Tested and works for what we need."}
{"title": "Remove bad css and clean up some code", "number": 75, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/75"}
{"title": "Code renderer", "number": 76, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/76", "body": "Add rendering for code chunks.  This is wired up in 'Ask a Question', it should be pretty easy to reuse it for the Chat UI.\n\nSome notes:\n\nI used this project for the syntax hilighting: .  It uses the same syntax definitions as VSCode so the hilighting works the same and the same languages are supported.\nI had to write a bunch of code to feed Shiki with the currently-selected VSCode theme JSON.\nShiki has an ugly bug where it doesn't quite parse theme files correctly, so I had to fork it and fix the bug (someone has already filed this fix as a PR, but the author apparently isn't taking PR requests?)\nI had to do a fair bit of fiddly CSS work to get it all to look right\n\nNext steps:\n\nHook this up to the Chat UI\nThe Message input box doesn't quite lay out as I would like.  I know how to fix it but it will involve some work, so for now the input area is fixed-height."}
{"title": "Add new chat UI", "number": 77, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77", "body": "I want to separate the notion of a user-customizable title from the original description that formed the bother.\nHence, adding title field for bother models...\nWe are now using Ben's new mockups to create a top level header for chat panel.\n1. The file button will open up an editor moving to the range within the file that the pointOfInterest represent.\n2. The references button is still a dummy button but we'll fix that.\n3. Clean up a lot of css.\n4. All header components are under a HeaderPanel container.\n5. Moving to vscode theme colors."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#issuecomment-960185124", "body": "Fuck ton of changes.\nGonig to make more fine tuned changes in another pr based off of Matt's recommendations."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#issuecomment-960201621", "body": " good work!"}
{"comment": {"body": "I think we should remove the description field?  I think the title replaces it?  Maybe I'm not understanding the difference between the two?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742304702"}}
{"comment": {"body": "... and should it be optional?  How do we tell if someone has explicitly set a title or not?", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742305522"}}
{"comment": {"body": "FYI `vh` doesn't always do what you'd expect and generally people don't use it.  There are a few ways to do this kind of thing, but if you want to say \"fill the parent space\" normally you could either use flexbox or just `height: 100%`.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742331792"}}
{"comment": {"body": "Since the parent is a flexbox, I'd just set this to `flex: 1`, which would cause this element to grow in height and take up all of the parent's space (excepting the header)", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742337209"}}
{"comment": {"body": "I would either change this to a fixed height (`3em` or something), or just let it size to the header's content, with the padding specified below.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742338451"}}
{"comment": {"body": "Some layout suggestions for this file overall:\r\n\r\n* I'd set `chat_message_panel` to use `flex: 1` instead of setting its width/height/left/bottom -- we want the chat message panel to fill out all space to the left of the asset/resource section, which has a fixed width.\r\n* Move `overflow-y: scroll` from `chat_message_panel_container` to `chat_message_panel` so the message input box scrolls along with the messages\r\n* When you use flexbox, typically you don't need to use width and height much, except for fixed-size items -- you probably could remove a number of the percentage-based widths/heights in here and it wouldn't affect anything.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742344022"}}
{"comment": {"body": "(I can't add a comment to the scss file in this PR...).  If you set `chat_context_panel` to have a fixed width, then I think this will let the MessagePanel's layout work correctly without a percentage width.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742344869"}}
{"comment": {"body": "There are a bunch of fixed colors here that should probably be changed to VSCode theme references -- let me know if you want help tracking down what theme colours should be used.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742345893"}}
{"comment": {"body": "So I know the problem going on here -- I actually have a similar problem with the message input (it's fixed-height, but should size to content).  Solving it this way with `ch` measurement doesn't give a particularly accurate value because it doesn't take into account that 'W' and 'i' have different widths.  I'm going to try to solve this in a PR tomorrow, I'll try to come up with a solution that will help here too.", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/77#discussion_r742349037"}}
{"title": "Use http for fetching github-hosted npm package", "number": 78, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/78", "body": "CI building failed here: , I think because I used ssh+git access for the new NPM package instead of https.\n(It's odd that the Build job succeeded while the Deploy job failed, no idea what's up with that)"}
{"title": "Fix repo ref that causes CI failure", "number": 79, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/79", "body": "CI was failing because of a bug in NPM: \nNPM was translating a public https GitHub repo reference into a git+ssh reference in package-lock.json, which would fail as the CI environment has no ssh keys.  Using this form for the package reference causes NPM to generate the correct reference."}
{"title": "Support sending props to react UIs", "number": 8, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/8", "body": "WebviewController.setProps sends a set of properties through to the react view through the message system.\nA helper (RenderReactUI) handles messages and re-rendering the UI whenever a new set of props is received.\nRight now there's no type checking, ie the extension could send a different set of props to the view then the view is expecting.  I think this can eventually be resolved but it'll take some work so I'm not going to tackle it, instead the top-level react UI will have to verify that the parameters match what it expects, which I've done in the 'Ask Question' UI."}
{"title": "Remove caching of NPM packages in CI", "number": 80, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/80", "body": "I think maybe GitHub Actions cached bad results for the NPM cache.  Seeing if this fixes it."}
{"title": "Test to see if build works", "number": 81, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/81"}
{"title": "Hopefully fix CI", "number": 82, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/82", "body": "The 'Build' job was correctly setting the right version of Node, but the 'Deploy' job wasn't.  I'm guessing the ubuntu image includes some default older version of node, which resolves packages differently."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/82#issuecomment-959942464", "body": "@rasharab I think maybe the /web actions need a similar treatment?"}
{"title": "Add languageid to apis and models as its needed for code styling", "number": 83, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/83"}
{"title": "Map to shiki language IDs", "number": 84, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/84", "body": "For some reason the Shiki devs decided to remap a few language IDs so they aren't consistent with VSCode anymore.  This PR:\n\nIntroduces a mapping table to map VSCode language IDs to Shiki language IDs, for those few languages where it's relevant\nAdds a fallback to plain text rendering if rendering with the language ID fails\n\nThanks to @rasharab for seeing this."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/84#issuecomment-*********", "body": "There is a possibility that we could fetch the language syntax definition files at runtime and feed it to Shiki, but that's a little beyond what I want to tackle right now."}
{"title": "Add UI structure to 'Ask a Question\"", "number": 9, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/9", "body": "Add some UI structure to 'Ask a Question'.  It looks like this now:\n\nI will be adding avatars next."}
{"title": "Add code styling to chat output", "number": 91, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/91", "body": "We are now using Matt's amazing code styling in chat.\nTo do this:\n1. We are using message attachments to add codeHtml for message."}
{"comment": {"body": "I think this is normally actually a number... not 100% sure about that though", "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/91#discussion_r743186581"}}
{"title": "Resize message input height to content", "number": 92, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/92", "body": "This allows small and large messages to be shown as you'd expect:\n\n\nThe implementation of this is pretty complicated, as the textarea element has no support for sizing to content.  You need to monitor two things (the textarea content string, and the textarea's width), and whenever either of these change, you can set the element's height to its scroll size.\n(as a side note, monitoring an element's width is also pretty tricky, so I wrote a hook to make it easier to reuse in the future)"}
{"title": "Click to focus message input", "number": 93, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/93", "body": "Focusing on the textarea element of the MessageInput is a bit tricky, because you have to click only at the top bit of the message input box.  With this PR, clicking anywhere on the MessageInput will focus on the textarea.\nThere was a bit of trickiness about this: the MessageInput has to know the ref for the textarea, but the textarea also needs it, so I had to write a bit of code to help them share the same ref."}
{"title": "Add optional lastChatAt field", "number": 95, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/95", "body": "@matthewjamesadam \nDo we want this to be non-optional down the line? I can do that for the next pr..."}
{"html_url": "https://github.com/NextChapterSoftware/OctoberDemo/pull/95#issuecomment-963431342", "body": "@matthewjamesadam\nDo we want this to be non-optional down the line? I can do that for the next pr...\n\nI'm not sure it matters much to me.  You could make it required and just set it to the creation date initially?"}
{"title": "Adding api/model infor for lastChatAt and lastUrgentChatAt", "number": 96, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/96"}
{"title": "Removing duplicate types", "number": 97, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/97", "body": "Rashin was dumb and, as Matt caught, added a duplicate type.\nMoving everything to user."}
{"title": "Add white-space handling to chat message", "number": 98, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/98", "body": ""}
{"title": "Make sure chat is bottom-aligned", "number": 99, "htmlUrl": "https://github.com/NextChapterSoftware/OctoberDemo/pull/99", "body": ""}
{"title": "Add all my changes to main", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/WebRTC/pull/1", "body": "One big PR so we can understand what I changed clearly\n\n\nFixes a crash that was happening constantly with pixelBufferPool. Apple's docs explicitly state that it's fine to just get the pool whenever you need it, there's no need to keep a reference. \n\n\nMakes macOS use the level 5.2 h.264 profile instead of always using 3.1  this fixes a lot of quality issues with screen sharing higher resolutions."}
{"title": "Update colour and break tests", "number": 1, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1"}
{"title": "Opt in chromatic", "number": 10, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/10"}
{"title": "Add wildcard ssl cert", "number": 100, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/100", "body": "Added CDK code to create wildcard public certificates for environment's main hosted zone (e.g dev.usecodeswell.com for dev)\nAdded the cert to helm chart for API service"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/100#pullrequestreview-860088907", "body": ""}
{"title": "allow all viewer request params to be forwarded.", "number": 1000, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1000", "body": "Replaced the old policy with a more generic one allowing all request params to be forwarded from viewer to origin."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1000#pullrequestreview-952389720", "body": ""}
{"title": "Make sure pre elements in editor and MessageView wraps text", "number": 1001, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1001", "body": "\n"}
{"comment": {"body": "@benedict-jw I think the idea is that we wanted to not word-wrap in code blocks, but allow scrolling?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1001#issuecomment-1109007246"}}
{"comment": {"body": "Yes \u2014\u00a0I find word wrapping code unnatural and difficult to read. I prefer horizontal scrolling for this. Happy to talk more about it if you like. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1001#issuecomment-1109010288"}}
{"comment": {"body": "Updated \r\n<img width=\"791\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/165172888-8f4e4ee8-469c-40c6-bf9d-ff34cef37e9b.png\">\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1001#issuecomment-1109029574"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1001#pullrequestreview-952450041", "body": ""}
{"title": "Add adminweb service", "number": 1002, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002", "body": "Creating internal AdminWeb service\n    - Added ECR repo named adminweb\n    - Added step to and deploy build new adminwebservice\n    - Created Kotlin service project for adminwebservice with a placeholder noop service\n    - Added helm configuration to create an internal ALB\n    - Modified CDK config to create a certificate for adminweb.prod.getunblocked.com as well as the us-west-2 alb address\n    - Removed two old unused CNAME records \n    - Added service account for adminweb service to EKS\nAll infra and EKS changes have been deployed.\n\nNote: Once deployed adminweb service will only be accessible via VPN"}
{"comment": {"body": "Increased service build timeout to 20 mins. Last service build timed out! ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#pullrequestreview-*********", "body": "thanks. one minor thing"}
{"comment": {"body": "fine for now, I'll probably drop most of these once I add implementation ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#discussion_r858030332"}}
{"comment": {"body": "we should refactor these plugins as they are now in 7 places, but another time....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#discussion_r858031155"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#pullrequestreview-952470104", "body": ""}
{"comment": {"body": "This is just a placeholder service. We would trim things as we go along", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#discussion_r858040164"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1002#pullrequestreview-952477664", "body": ""}
{"title": "Remove thread archiving restriction", "number": 1003, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1003", "body": "also update sourcemark icon to reflect thread type"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1003#pullrequestreview-952485139", "body": ""}
{"comment": {"body": "Thanks \ud83d\udc4d ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1003#discussion_r858051713"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1003#pullrequestreview-952485315", "body": ""}
{"title": "add cors workaround to auth lambda edge", "number": 1004, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1004"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1004#pullrequestreview-952532346", "body": ""}
{"title": "fix a copy paste mistake in adminweb service deployment code", "number": 1005, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1005", "body": "Small copy paste mistake in Github action workflow for deploying admin web service."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1005#pullrequestreview-952524071", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1005#pullrequestreview-952532461", "body": ""}
{"title": "Web extension Invite Flow", "number": 1006, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006", "body": "Update extension to allow invite participants to a new discussion.\n\nCaveats\nSince we're in GitHub, we do not have access to Git, specifically blame. This means we are unable to mine the local source file for emails and metadata that would be used to display data such as \"hasMostContributions\".\nWe also only have access to the GH usernames (from DOM). This may cause issues if there are users who have not connected to Unblocked as we no longer have email as a backup."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-952557257", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-952558310", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-953679983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-953680806", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-953791274", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-953792301", "body": ""}
{"comment": {"body": "Should we put this copy in the vscode start discussion form as well? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#discussion_r858996273"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1006#pullrequestreview-953792779", "body": ""}
{"title": "Cleanup cors handling in lambda", "number": 1007, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1007", "body": "Add initial auth for webview\nAdd cloudfront cors tests"}
{"title": "Invite Sheet", "number": 1008, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008", "body": "Summary\nHacky as usual. I considered using a PreferenceKey to signal the parent view to render the invite pane but opted for a callback instead. It's a bit hacky because I couldn't figure out how to set a State property to null without writing a State extension to allow for this, so I used a sentinal instead for now. I'll swoop in to fix that grossness and all the code duplication in another PR once we feel the UX is in a stable place.\nThere's also a lot of styling duplication, but I'm not going to spend any time refactoring this until the patterns are clear and stable.\nGIF\n\nScreenShots\nLight\nThread List\n\nCollapsed\n\nExpanded TeamMember Icons\n\nTeam Member List\n\nInvite Sheet\n\nInvite Sent Banner\n\nDark\nThread List\n\nTeamMember List\n\nInvite Sheet\n\nInvite Success Banner\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-953828262", "body": ""}
{"comment": {"body": "I'm doing this because I want UX state to be reconstructed each time the popover emerges. Not doing so created some strange visual artifacts. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r859021918"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-953829235", "body": ""}
{"comment": {"body": "I (re)-learned a new trick", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r859022557"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956792807", "body": ""}
{"comment": {"body": "These styles are pretty specific to only being used in a menu/popover context (ie, some kind of material background), aren't they?  I'm specifically thinking of the fixed colours (white foreground) and how these buttons won't look correct in light mode in a regular View of any kind.\r\n\r\nShould we name these styles to make this clear? Or put comments?  I'm not sure.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861135571"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956803543", "body": ""}
{"comment": {"body": "Is there a tradeoff between using this vs the `.overlay` decorator?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861142495"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956806844", "body": ""}
{"comment": {"body": "Ahh it's unfortunate that SwiftUI has no real TextEditor placeholder property...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861144642"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956813248", "body": ""}
{"comment": {"body": "Just a general comment (no action needed in this PR) -- as some of these UIs are getting pretty big, we may want to factor bits of them out, either into separate files, or just separate helpers classes within the same file.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861148824"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956823237", "body": ""}
{"comment": {"body": "Ah yeah as you pointed out in the PR description, this pattern could use some work, it's pretty messy and hard to follow.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861156999"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956824639", "body": ""}
{"comment": {"body": "They should be canonized as `ViewModifier` styles with a well named view extension functions like `styleAsPopover()` or something similar", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861158020"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956827384", "body": ""}
{"comment": {"body": "It feels like this pattern (for displaying Toast UIs) is something we could formalize into a reusable decorator at some point as well...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861160012"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956828424", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956830936", "body": ""}
{"comment": {"body": "`ZStack` provides layout semantics, `.overlay` does not. In this case I needed the overlay to be right-aligned. \r\n\r\n`.overlay` also clips to the parent view, so an overlay on the button would only work if the button was sized to the same size as the overlay. \r\n\r\nI think the takeaway is there are many ways to slice this, but no noticeable performance implications one way or the other as far as I know.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861162548"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956834991", "body": ""}
{"comment": {"body": "Very :) \r\n\r\nBut there is actually a way to provide this via some nasty AppKit view bridge hacks. As it stands today, `TextEditor` and `TextView` are backed by an `NSTextView` instance. This means we can punch a hole through and interact with that component via `ViewModifer`s if necessary. See my very disgusting `NSTextView` extension hack to clear the background colour. We probably shouldn't do this generally and especially not through an extension, but for now I think it's ok", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861165522"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956837601", "body": ""}
{"comment": {"body": "Top of mind for me. The strategy I'm employing right now is to cobble it together and keep track of the common patterns and opportunities for modular factoring. If someone else jumps into this codebase I will prioritize that work.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861167375"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956839031", "body": ""}
{"comment": {"body": "After writing this I tried to search around for better patterns and noticed a few people doing something similar. But I think I have a better idea of how to handle this...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861168443"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#pullrequestreview-956840420", "body": ""}
{"comment": {"body": "Agreed. Right now we have 2 toasts (\"Copied!\", \"Invite Sent Successfully\"), but I imagine more will pop in for settings and video", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1008#discussion_r861169473"}}
{"title": "Fix dev config", "number": 101, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-860093101", "body": ""}
{"comment": {"body": "Currently doing this is okay for now. We'll populate this soon.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#discussion_r790040456"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-860093467", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-860093839", "body": ""}
{"comment": {"body": "Not actually a huge fan of POST, idempotent PUTs with client generated IDs is more resilient for retries.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#discussion_r790040988"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-860094283", "body": ""}
{"comment": {"body": "Do we disallow POST? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#discussion_r790041326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-860095006", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Not right now, but that's my suggestion.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/101#discussion_r790078340"}}
{"title": "Introduce Admin Web", "number": 1010, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1010", "body": "The shell is there: routing, tabs, breadcrumbs\nwill add team implementation in next PR\n\n"}
{"comment": {"body": "Thank you for doing this!!!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1010#issuecomment-**********"}}
{"comment": {"body": "Going Skywagon Admin Console style baby, love it :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1010#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1010#pullrequestreview-*********", "body": ""}
{"title": "Add cors response headers and fix stuff", "number": 1011, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1011"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1011#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This was annoying as fuck to figure out...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1011#discussion_r858131212"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1011#pullrequestreview-952586287", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1011#pullrequestreview-954008247", "body": ""}
{"comment": {"body": "hello\n\n<img src=\"https://avatars.githubusercontent.com/u/3806658?v=4&s=16\"/> Added by rasharab", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1011#discussion_r859148675"}}
{"title": "More headers cleanup", "number": 1012, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1012"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1012#pullrequestreview-952591365", "body": ""}
{"title": "Integrate authentication into vscode webview image loads", "number": 1014, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014", "body": "Background:\nIn lieu of that, I had to hack up image downloads to using a browser fetch, which while it might work for images, will probably not work well for videos.\nThis pr does the following things:\n1. Add authentication injections into vscode webview. WebviewContentController subscribes to Authstore events and posts messages to RenderWebview. The RenderWebview will subsequently store token in a zustand store for access anywhere.\n2. Add an AssetDownloader which will take Authentication token from zustand store and attempt to query cloudfront endpoint for asset and generate an object blob url. (Not sure how well this will work for video assets).\nTesting:\nValidated that the asset was correctly being uploaded and the asset download was going through cloudfront."}
{"comment": {"body": "This method should work for videos too! For videos .m3u8 files are only a few hundred bytes long but include signed URLs for video parts which editor can take and handle on its own. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#issuecomment-1109343272"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-952637567", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-952637928", "body": ""}
{"comment": {"body": "Basic hook to get authenticated img blob url.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r858165673"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-952639101", "body": ""}
{"comment": {"body": "Hack. Will have to move this logic to server...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r858166282"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-952647983", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-952649351", "body": ""}
{"comment": {"body": "Using hook to get the actual blob url.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r858174379"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953897964", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953902611", "body": ""}
{"comment": {"body": "I would just make this a single async function that takes in the assetUrl and returns the completed URL...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r859072993"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953903677", "body": ""}
{"comment": {"body": "And at that point you could just integrate it with `useAssetUrl`...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r859074417"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953906673", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953979391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953980120", "body": ""}
{"comment": {"body": "We need this in a shared library.\r\nI want to be able to reference it from shared libraries.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r859127361"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953981955", "body": ""}
{"comment": {"body": "Get authToken straight from auth context.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r859129559"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953983381", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953998823", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-953999359", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954001756", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954007600", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954053902", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954054385", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954079932", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954080486", "body": ""}
{"comment": {"body": "If we leave a webview open long enough that the API token refreshes (which isn't that long), I think this will cause all the images to re-download, since the authToken will change....", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r859203433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954081463", "body": ""}
{"comment": {"body": "Considered that.\r\nI was hping the assetSrc check would prevent that. Not sure if that's the case.\r\n\r\n        if (authToken && !assetSrc) {\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#discussion_r859204168"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1014#pullrequestreview-954081589", "body": ""}
{"title": "Rejig MessageView layout", "number": 1015, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015", "body": "Flatten the MessageView children hierarchy so that we can apply a grid layout onto them\nUse grid layout to present two different layouts in VSCode\nShow title in first message.  Note: with this change re-titling a thread is no longer possible, this will be fixed in a future PR.\n\n(Note, I see a bug in the images below, looks like there is extra spacing in the message views that don't have a title)\nExpanded layout, this is what is used when you have a thread view that spans the whole window (and what would be used in the dashboard):\n\nCompressed layout, this is what is used in a split view:\n"}
{"comment": {"body": "Awesome thanks for doing this!", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015#issuecomment-1109347777"}}
{"comment": {"body": "Curious re: how this looks in the web extension? Is the title still in the header section of the dialog? Is the idea that it\u2019s not editable anymore? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015#issuecomment-1109351932"}}
{"comment": {"body": "> Curious re: how this looks in the web extension? Is the title still in the header section of the dialog? Is the idea that it\u2019s not editable anymore?\r\n\r\nI think the title + description can be edited together. They're created together so that makes sense to me. That's a good point about the Chrome modal. I'll have to see how this may change there.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015#issuecomment-1109358247"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015#pullrequestreview-952753415", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015#pullrequestreview-952755730", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1015#pullrequestreview-953668072", "body": ""}
{"title": "Store access token on Identity model and use for GitHub messages", "number": 1016, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016", "body": "The token is stored unencrypted for now. We will need to encrypt before launch."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#pullrequestreview-953646511", "body": "great work Dave\nI think we can save ourselves some round-trips to GitHub if we persist the token expiry and cache access tokens in memory"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#pullrequestreview-953693911", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#pullrequestreview-953695277", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#pullrequestreview-953764254", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#pullrequestreview-954007752", "body": ""}
{"comment": {"body": "don't need to pass in token here anymore right?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#discussion_r859148337"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#pullrequestreview-954027923", "body": ""}
{"comment": {"body": "We do. From here it'll be stored on the Identity model.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1016#discussion_r859163329"}}
{"title": "Hide SMs of an archived thread in IDE", "number": 1017, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1017"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1017#pullrequestreview-953736847", "body": ""}
{"title": "Remove unnecessary roles", "number": 1018, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1018"}
{"title": "fix the api path", "number": 1019, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1019", "body": "ALB was mapping request paths to / and it was causing 404s! \ne.g adminweb.dev.getunblocked.com/__shallowcheck was being passed to service at /__shallowcheck but the service is expecting it /api/__shallowcheck\nAlso our Admin web pages are at adminweb.{dev, prod}.getunblocked.com"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1019#pullrequestreview-953773618", "body": ""}
{"title": "add replicaset access for deployer user", "number": 102, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/102", "body": "Tiny PR to add replicaset permissions for deploybot user"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/102#pullrequestreview-860117220", "body": ""}
{"title": "Fix baseurl bug in vscode", "number": 1020, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1020", "body": "environment object must be passed into the webview in vscode in order for the proper environment data to be parsed -- update other clients to behave similarly\nslack: "}
{"comment": {"body": "Would it make more sense to build the environment info into the webview bundles as well?  Then the Environment object would work as expected everywhere and we wouldn't need to pass the env around?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1020#issuecomment-1110085309"}}
{"comment": {"body": "* NOTE: Needed to move the Environment.ts file out of the shared/api file to avoid import conflicts in the api index file", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1020#issuecomment-1110203496"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1020#pullrequestreview-953949576", "body": ""}
{"title": "Add adminweb team pages", "number": 1021, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1021"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1021#pullrequestreview-953817894", "body": "Marry me."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1021#pullrequestreview-953818657", "body": ""}
{"title": "Fix shit", "number": 1025, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1025"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1025#pullrequestreview-953846781", "body": "thank you"}
{"title": "fix admin web paths", "number": 1026, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1026", "body": "Turns out there was no VPN issue. We just needed to use https!! \n is working fine now. \n\nModified paths so it doesn't do any prefix matching and forwards everything to admin web service\nUpdated the secondary health probe path to match routes.kt\nUpdated ALB health probe to get rid of those annoying 404s in logs"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1026#pullrequestreview-953900382", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1026#pullrequestreview-953900964", "body": ""}
{"comment": {"body": "adminwebservice (I changed to that)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1026#discussion_r859070908"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1026#pullrequestreview-953901705", "body": ""}
{"comment": {"body": "        route(\"/api/health/adminwebservice\") {\r\n            HealthApi(healthApiDelegateImpl)\r\n        }", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1026#discussion_r859071895"}}
{"title": "fix cname for admin web", "number": 1027, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027", "body": "Renamed CName record to admin.{dev, prod}.getunblocked.com\nRegenerated SSL certs \nModified ALB configs to use new certs \nRemoved old unused wildcard cert"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#pullrequestreview-*********", "body": ""}
{"comment": {"body": "do we need the \"prod\"?\r\n```suggestion\r\n                \"recordName\": \"admin.getunblocked.com\",\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#discussion_r859106639"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Prod dns zone is under our root account. It will make it a pain to do the cross account name setup and SSL stuff. We only do those for things fronted via CloudFront. In other words for customer facing endpoint(s)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#discussion_r859107433"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#pullrequestreview-*********", "body": ""}
{"comment": {"body": "I mean .prod isn't all that bad since this is not a customer facing one. Also we want people to know which env they are targeting (my ops excuse :P )", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#discussion_r859107859"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#pullrequestreview-*********", "body": ""}
{"comment": {"body": "yeah, no biggie. agree", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1027#discussion_r859136063"}}
{"title": "Admin: try to get health checks working", "number": 1028, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1028"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1028#pullrequestreview-953999476", "body": ""}
{"title": "Fix storybook and clean up imports", "number": 1029, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1029", "body": "Attempt at some alias clean up -- @models and @shared-api-models export the same thing, consolidate\nFix API import reference in RestoreThreadDialog to fix the web stories"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1029#pullrequestreview-954055906", "body": ""}
{"title": "Thread summary view", "number": 103, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103", "body": "Implemented thread summary view with rough props.\nUpdated basic headers based on web design doc. Only using h1 - h3. Removed the rest\nThis included updating how the Mixins work and separating into separate files due to circular dependencies.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-860113027", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-860113274", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-860113318", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-860113432", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-860113627", "body": ""}
{"comment": {"body": "Do we need more header sizes defined?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r790056209"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-860113834", "body": ""}
{"comment": {"body": "Currently same as desktop numbers. Should these be updated? @benedict-jw ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r790056364"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861378256", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861378660", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861379246", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861387030", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861387156", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861390348", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861395969", "body": ""}
{"comment": {"body": "Chatted with Ben. Will handle later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r791063839"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-861396064", "body": ""}
{"comment": {"body": "Unnecessary for now.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r791063950"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862744886", "body": ""}
{"comment": {"body": "remove TODO?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r792037382"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862747222", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862748215", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862748707", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862752366", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862754195", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862783743", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862786926", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862788721", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862790560", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862909927", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862920416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-862922988", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-863060322", "body": ""}
{"comment": {"body": "does this still need to be here if we have it in the story? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r792271179"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865160082", "body": ""}
{"comment": {"body": "I have this in here since this exists in the actual bundle and without it, it would just be an empty page.\r\n\r\nCan remove if we're really against having it here but it should be removed when we start implementing the chat feature.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r793807791"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865478121", "body": ""}
{"comment": {"body": "we could probably write a mixin for this ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r794033766"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865478584", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865479649", "body": ""}
{"comment": {"body": "in my head it makes sense for it to be empty until we have actual data (i.e. the mocked version is the in storybook) but I don't feel super strongly either way", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r794034939"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865480654", "body": ""}
{"comment": {"body": "so `Chats` house a list of `ChatSummaryView`s ? \r\n\r\nthere's a part of me that thinks the container should then be called `ChatSummaryViews` ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r794035627"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865480909", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-865481121", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-868147730", "body": ""}
{"comment": {"body": "This page is at the \"View\" level so I didn't think of it as a 1-1 relationship to its child components, more to the actual page/route. \r\n\r\nDon't have strong opinions on this so open to changing it if it seems more clear.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#discussion_r795875747"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/103#pullrequestreview-868170250", "body": ""}
{"title": "Streamline web extension build pipeline", "number": 1030, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1030", "body": "Update build pipeline to support multiple browsers from a single src.\nXcode project working but code signing failing...\nerror: Signing for \"Unblocked for Safari (iOS)\" requires a development team. Select a development team in the Signing  Capabilities editor. (in target 'Unblocked for Safari (iOS)' from project 'Unblocked for Safari')\nerror: Signing for \"Unblocked for Safari Extension (iOS)\" requires a development team. Select a development team in the Signing  Capabilities editor. (in target 'Unblocked for Safari Extension (iOS)' from project 'Unblocked for Safari')\n** BUILD FAILED **\nWill look into how to handle this in CI.\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1030#pullrequestreview-954076523", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1030#pullrequestreview-954078279", "body": ""}
{"title": "Update signature for PR comments created and edited from unblocked", "number": 1031, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1031", "body": "Adding an avatar and link to come later\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1031#pullrequestreview-956799732", "body": ""}
{"title": "Hack fix", "number": 1032, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1032", "body": "Will have to move awy from this hack soon."}
{"title": "Remove admin webroot prefix path", "number": 1033, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1033", "body": "Removes /api/... prefix which is no longer necessary"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1033#pullrequestreview-954131333", "body": ""}
{"title": "Run adminweb service using docker locally", "number": 1034, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1034"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1034#pullrequestreview-954130826", "body": ""}
{"title": "Add adminweb team pages", "number": 1035, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1035"}
{"title": "Add selected state to treeviews", "number": 1036, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036", "body": "\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-1059565143", "body": ""}
{"comment": {"body": "I think we had a discussion about enabling this? IIRC it was difficult to maintain the MessageEditor content when switching between tabs and we decided to enable this to get around that on the interim.\n\n--\n\nComment posted using [Unblocked](https://dev.getunblocked.com/dashboard/team/9c34b1d4-d8a9-43eb-8cd4-b86c8ab31361/thread/29420320-48eb-4419-bd1e-1b7da06a8e7f?message=f2d766e1-5010-4157-938d-2f5d2f74394c).", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r936132545"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955165725", "body": ""}
{"comment": {"body": "So a WebviewEventHandler instance allows communication between the plugin and webview without going through the props?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859981789"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955165727", "body": ""}
{"comment": {"body": "So a WebviewEventHandler instance allows communication between the plugin and webview without going through the props?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859981793"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955166038", "body": ""}
{"comment": {"body": "Is this two way or just from webview to plugin?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859982033"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955166039", "body": ""}
{"comment": {"body": "Is this two way or just from webview to plugin?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859982034"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955168581", "body": ""}
{"comment": {"body": "One caveat of having this state live locally here is that external events that open discussions will *not* highlight the sidebar.\r\n\r\nFor example, notifications or permalinks that are open a discussion in VSCode won't highlight the sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859983643"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955168582", "body": ""}
{"comment": {"body": "One caveat of having this state live locally here is that external events that open discussions will *not* highlight the sidebar.\r\n\r\nFor example, notifications or permalinks that are open a discussion in VSCode won't highlight the sidebar.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859983644"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955170257", "body": ""}
{"comment": {"body": "We will eventually need to pass this selected state into the sidebar. I think we can get this in for now though.\r\nAdd a TODO / GH Issue for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859984715"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955170261", "body": ""}
{"comment": {"body": "We will eventually need to pass this selected state into the sidebar. I think we can get this in for now though.\r\nAdd a TODO / GH Issue for this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r859984717"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955172771", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955374340", "body": ""}
{"comment": {"body": "Will the unblocked discussion be a sufficient enough TODO? \ud83e\uddd0 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r860130770"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955375148", "body": ""}
{"comment": {"body": "I don't think so, at least right now it's just extension code (DiscussionThreadCommand) communicating with extension code (SidebarWebviewProvider)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r860131326"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-955381034", "body": ""}
{"comment": {"body": "The issue is that without explicitly looking back at this discussion, the TODO could be lost. IMO, I don't think a discussion / unblocked is a replacement for issue tracking.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r860135456"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#pullrequestreview-956776527", "body": ""}
{"comment": {"body": "@kaych is there a particular reason you enabled this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1036#discussion_r861124467"}}
{"title": "Improved SM icon behaviour in VSCode text editor", "number": 1037, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1037", "body": "Whenever any edit occurs, re-render the sourcemark icons immediately, with a temporarily-calculated set of SM locations.  This causes SM icons to update immediately to a reasonable place, and re-renders the spacing on all lines, which fixes pretty much all of the editor jank.\nAs before, debounce a run of the SM engine -- this gets applied afterwards.  The end result is that the SM icons update quickly, then \"snap\" to a final location if the edit is complicated and the SM algorithm came to a different conclusion."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1037#pullrequestreview-954173995", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1037#pullrequestreview-955196391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1037#pullrequestreview-955480277", "body": ""}
{"title": "Add new admin web pages", "number": 1038, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038", "body": "This:\n- Route cleanup\n- Search page\n- Repos and Repo pages\n- Members and Member pages\n- Users and User pages\n- Minimal Threads page\nRemaining:\n- Message pages\n- SourceMark page\n- Unreads page\n- Video pages\n\n\n\n"}
{"comment": {"body": "This looks so familiar \ud83d\ude02", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#issuecomment-1111336595"}}
{"comment": {"body": "Absolutely. Beautiful.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#issuecomment-1111337664"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#pullrequestreview-955359105", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#pullrequestreview-955364431", "body": ""}
{"comment": {"body": "Rad", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#discussion_r860124138"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#pullrequestreview-955370135", "body": ""}
{"comment": {"body": "We going to page this eventually?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#discussion_r860127659"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#pullrequestreview-955370920", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#pullrequestreview-955388240", "body": ""}
{"comment": {"body": "yup", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1038#discussion_r860140449"}}
{"title": "Add authorized asset support to web", "number": 1039, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039", "body": "We need authorized asset support for web.\nTo that end;\n1. Add cors support for AssetService\n2. Add authorized context for base level web app.\n3. Generalize asset creation."}
{"comment": {"body": "I'm not 100% following what this is doing in the local case -- for local env is file uploading disabled?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#issuecomment-1111341676"}}
{"comment": {"body": "> I'm not 100% following what this is doing in the local case -- for local env is file uploading disabled?\r\n\r\nFor local, it's uploading to s3 like other environments.\r\nBut for downloads, we can't use any of the cloudfront endpoints, we're just using the s3 presigned url.\r\nThere is no authenticated asset download for local, as all our cloudfront endpoints are coupled to either dev/prod services.\r\n\r\n**UPLOADS**\r\nFor local/dev/prod:\r\n1. We get an s3 presigned url to upload.\r\n2. We upload to s3 presigned url.\r\n\r\n**DOWNLOADS**\r\nFor local: \r\n1. We get an s3 presigned download url to download and use that (requires no authorization)\r\n\r\nFor dev/prod:\r\n1. We use a static cloudfront url that in the backend will generate a temporary s3 presigned url and it will transparently forward the asset after authenticating the user.\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#issuecomment-1111342883"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#pullrequestreview-955237654", "body": ""}
{"comment": {"body": "cors support needed for asset service...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#discussion_r860033478"}}
{"comment": {"body": "Thank you @matthewjamesadam  :)", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#discussion_r860034723"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#pullrequestreview-955295897", "body": ""}
{"comment": {"body": "for local, we don't use cloudfront, so let's up the download expiry to 7 days for local testing.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#discussion_r*********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1039#pullrequestreview-*********", "body": ""}
{"title": "Fix naming of tags to take into account runs...", "number": 104, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/104"}
{"title": "Add notification svc logic", "number": 1040, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040", "body": "Added AWS LocalStack with S3, SQS and SES services to docker compose templates \nAdded SES and SQS provider classes to AWS lib similar to what we have for S3. \nCreated lock provider for email notifications in Redis client lib \nCreated MockAWSProvider and a couple of helper classes for SES and SQS \nAdded the actual business logic to Email job to process messages from SQS and send emails using AWS SES \nReverted a few accidental changes pushed in from my local stash\n\nTests use localstack's SQS and SES services. SES service in localstack cannot send emails but we can inspect sent emails by looking at the filesystem inside the docker container. For the purpose of tests it is enough to check API response. \nThis is currently working but still needs some more work around email templates. I'll sync with Kay to figure out how we would like to approach email rendering. We might still stick to sending RAW emails and take care of html content generation in our code instead of using SES built-in functions."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955284096", "body": ""}
{"comment": {"body": "Had a chat with Rashin about this. We will figure out a better to handle Dev profiles soon. It should not cause any problems for now. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#discussion_r860067265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955294416", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955295654", "body": "minor comments - nice work!"}
{"comment": {"body": "what does the \"Ob\" suffix mean? use words please to help readability", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#discussion_r860080310"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955296709", "body": ""}
{"comment": {"body": "I figured we will have numerous message types for queues. Whatever needs to interact with queue should have access to message classes for serialization/deserialization so I moved this to here. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#discussion_r860078148"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955297391", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955297813", "body": ""}
{"comment": {"body": "Forces localstack to verify an email address so we could use it for testing ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#discussion_r860078996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955305780", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955339824", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955380302", "body": ""}
{"comment": {"body": "Stands for the object. These are exactly are they were in AWS doc examples. Actual content are wrapped in objects before we can pass them to request builders ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#discussion_r860134919"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955380433", "body": ""}
{"comment": {"body": "Should I still change them ?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#discussion_r860135018"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955381430", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1040#pullrequestreview-955402953", "body": ""}
{"title": "Fix sidebar data loading", "number": 1042, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042", "body": "If you try to render to a Webview while it is not visible, nothing happens.  The rendered state will be lost.  This was happening on startup when the sidebar view was loading, because we would force it to display, and then be hidden.\nWith this PR, when we have new data to render, if the webview is hidden, we will not render, but instead we will hold onto the props.  When the webview is made visible, we will render with those props we held onto."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042#pullrequestreview-955595241", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042#pullrequestreview-955595589", "body": ""}
{"comment": {"body": "So this webviewcontent controller will stay around, even if not visible, and keep this pendingProps in memory?\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042#discussion_r860286458"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042#pullrequestreview-955595630", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042#pullrequestreview-955618018", "body": ""}
{"comment": {"body": "Yep, VSCode keeps the view and provider alive indefinitely.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1042#discussion_r860304190"}}
{"title": "Ingest threads from all pull requests", "number": 1043, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043", "body": "Currently, we only ingest threads from merged pull requests. We want to start ingesting threads from open pull requests.\nFor threads from open PRs, source points will be created with the hash of the original commit on the PR branch (vs threads from a merged PR, where we use the merge commit hash). \nAs open PRs are merged, we need to update the sourcemarks by adding a new original source point with the merge commit hash. We'll only do this for non-outdated threads (threads referring to code that hasn't changed).\nThis means that Sourcemarks can have multiple original source points (one for the pr branch, one for the main branch). This is blocked on the sourcemark agent being updated to handle this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#pullrequestreview-956812807", "body": ""}
{"comment": {"body": "just fyi: we have `Message` data class now if you want to swap out the DAO at this layer", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#discussion_r861244085"}}
{"comment": {"body": "where is this caught?\r\n\r\nseem like this is called in a loop of threads, meaning that we would abort _all_ threads instead of just this one.\r\n\r\nmaybe swallow exception here and log error, allowing the remaining threads to be ingested?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#discussion_r861262431"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#pullrequestreview-957028064", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#pullrequestreview-957084582", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#pullrequestreview-957097709", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#pullrequestreview-957396370", "body": ""}
{"comment": {"body": "Thanks, I think I need to keep these as DAOs for now since I'm updating records below", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#discussion_r861563259"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1043#pullrequestreview-958086785", "body": ""}
{"title": "Set up separate webpack caches for VSCode bundles", "number": 1044, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1044", "body": "The webpack persistent build cache wasn't really working for VSCode builds, because we do two builds at the same time and apparently webpack's cache doesn't deal with that gracefully.  Giving each build a separate cache name fixes this."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1044#pullrequestreview-956867776", "body": ""}
{"title": "Styling fixes", "number": 1046, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046", "body": "Message layout fixes\n\n\n\nAdd icon badge for not a member (replace text) \n\nInvolves refactoring the StatusIcon component into shared/"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#pullrequestreview-955654567", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#pullrequestreview-955657221", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#pullrequestreview-956750009", "body": ""}
{"comment": {"body": "Odd that this didn't get tracked in git as a file move?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#discussion_r861105726"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Hmm. I did add a type on line 14 so maybe that was enough for git to think it was a brand new file?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#discussion_r861107808"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#pullrequestreview-*********", "body": ""}
{"comment": {"body": "This only really happens when a user is removed from the team, right?  Slack labels this \"Deactivated account\", does that accurately describe the situation that causes this?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#discussion_r861116981"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1046#pullrequestreview-*********", "body": ""}
{"title": "Admin thread detail page", "number": 1047, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1047", "body": "add thread details page\nlot's of refactoring\n\n"}
{"comment": {"body": "@matthewjamesadam @jeffrey-ng @kaych I've seen similar to that third message where some messages from clients have a malformed message content (`Message missing required fields...`). I'm able to repro", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1047#issuecomment-**********"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1047#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1047#pullrequestreview-956778203", "body": ""}
{"title": "VSCode: Snippet not displayed for mark when a point cannot be computed at the latest commit", "number": 1048, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048", "body": "The problem can happen when:\n1. None of the points of the mark exist in the tree, because (i) the mark was added on commit ahead of the local Git repo, or (ii) the mark was added on a commit on a feature branch that was not merged.\n2. The latest point could not be calculated, because (i) the mark range was deleted, or (ii) the mark range was modified significantly, or (iii) due to an engine bug.\nThis change allows the client to get the original point, so that the original point can be displayed in the discussion view.\nAlso related to this "}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#pullrequestreview-955777634", "body": ""}
{"comment": {"body": "@davidkwlam relevant our conversation earlier today. I _think_ in this case, we should iterate over each of the original point commits, and prune the ones that do not exist in the current tree (branch). After filtering we would expect at most one original point; but even if there was more than one, it would be safe to just choose the first one. That make sense to you?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#discussion_r860429652"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#pullrequestreview-956724364", "body": ""}
{"comment": {"body": "I'll fix this once this PR is merged.  The unsaved files are being tracked but it will require a bit of rearchitecting to get the data available here.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#discussion_r861087587"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#pullrequestreview-956790527", "body": ""}
{"comment": {"body": "Will improve in follow up...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#discussion_r861134110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1048#pullrequestreview-956830137", "body": ""}
{"title": "Gradle should build TS protos", "number": 1049, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049", "body": "Seems to be working. Second time is cached.\n\n"}
{"comment": {"body": "Thanks for looking into this!  Combined with this PR: https://github.com/NextChapterSoftware/unblocked/pull/1044, incremental VSCode builds drop from ~20 seconds to ~2 seconds.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#issuecomment-1112395002"}}
{"comment": {"body": "@rasharab I think you're the gradle expert that will need to review this stuff \ud83d\ude06 ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#issuecomment-1112407408"}}
{"comment": {"body": "> This looks fine. I still thnk dependency should be move to root, up to you.\r\n\r\nOk, I'll this out and see if it works...", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#issuecomment-1112442996"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#pullrequestreview-956701708", "body": ""}
{"comment": {"body": "Is there a particular reason you put this dependency in a subfolder?  We've been trying to keep dependencies at the root because it helps keep dependencies (and transitive dependencies) consistent, and once you start nesting NPM dependencies it gets very hard to understand what version of each library is actually being used.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#discussion_r861071665"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#pullrequestreview-956703005", "body": ""}
{"comment": {"body": "Yeah let's talk about this at some point, the structure we have now doesn't make sense.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#discussion_r861072482"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#pullrequestreview-956706485", "body": ""}
{"comment": {"body": "Agreed here with Matt.\r\nPlease put it at root.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#discussion_r861074849"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#pullrequestreview-956727138", "body": ""}
{"comment": {"body": "The reason is because the common proto task depends on the npm dependency. The dependency needs to be explicit in the common gradle sub-project, which you can see on the gradle file. Without this explicit dependency the proto task will fail.\r\n\r\nAn alternative would be for the common gradle sub-project task to depend on a root npm install, but that means moving the entire npm toolchain into gradle which you probably don\u2019t want to do.\r\n\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#discussion_r861089490"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#pullrequestreview-*********", "body": "This looks fine. I still thnk dependency should be move to root, up to you."}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#pullrequestreview-*********", "body": ""}
{"comment": {"body": "I\u2019ll do it in a follow up PR", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1049#discussion_r861089934"}}
{"title": "Refactor identity models", "number": 105, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105", "body": "Changes\n\naccount for anonymous identities, those who have never signed in\nadd concept of a deactivated members, members removed from the team\nrename User to Person to avoid ambiguity (eg: a GitHub user or a Codeswell user)\na team member no longer maps to a user, it maps to an identity\n\nHack\n\nAdded this hack  to remove an old model. Without this hack all your tests will fail on merge. I can revert the hack commit after some time.\n\nDiagram\n"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-*********", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-*********", "body": ""}
{"comment": {"body": "Or set their own?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790065831"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-*********", "body": ""}
{"comment": {"body": "What does this look like when a user has multiple identities? How are they represented in a chat context?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790066412"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860127411", "body": ""}
{"comment": {"body": "I think we need lastSeenAt somewhere in the model to track engagement?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790066951"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860128348", "body": ""}
{"comment": {"body": "Do we need to worry about the larger team concept right now, where a \"team\" is really a codeswell entity that wraps potentially multiple providers?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790067728"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860128449", "body": ""}
{"comment": {"body": "yeah, not needed right now. will add later instead", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790067803"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860128732", "body": ""}
{"comment": {"body": "Why do we have to throw @JvmStatic at this? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790068006"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860129032", "body": ""}
{"comment": {"body": "Chat will relate to TeamMember (an Identity), and not a User.\r\n\r\nSo the ChatMembers will always be Identities within a single Team (and Provider type), so it's not actually possible for a user with multiple identities to participate in a chat. If that doesn't make sense call me.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790068252"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860129294", "body": ""}
{"comment": {"body": "Possibly in future, but that involves us hosting the image. Simpler for now to just choose, and layer on uploading their own one later.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790068464"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860130945", "body": ""}
{"comment": {"body": "Do all PRs run tests against the same database? If so, are the tables in unique schemas? Just wondering if there is any race condition here that we need to worry about.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790069733"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860131805", "body": ""}
{"comment": {"body": "From: https://stackoverflow.com/questions/35554076/how-do-i-manage-unit-test-resources-in-kotlin-such-as-starting-stopping-a-datab\r\n\r\n>@JvmStatic - an annotation that turns a companion object method into a static method on the outer class for Java interop\r\n", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790070445"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860134033", "body": ""}
{"comment": {"body": "Ah this must be a temp change to dump the no-longer-needed tables and columns in the test db?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790072249"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860139047", "body": ""}
{"comment": {"body": "Yes, but only for Billing purposes. This concept can be safely deferred for some time.\r\n\r\nAdded some notes here:\r\nhttps://www.notion.so/nextchaptersoftware/How-SCM-Orgs-Interact-With-Teams-d83d3b8c444e44baa0542f3f2c44c0ce#8c4191e6fc9745739fb9252bdc6f8f09", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790076367"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860139915", "body": ""}
{"comment": {"body": "> Do all PRs run tests against the same database?\r\n\r\nyes\r\n\r\n> If so, are the tables in unique schemas? Just wondering if there is any race condition here that we need to worry about.\r\n\r\nyes, same shared schema. The reason that there is no race is because by default junit does not run parallel tests. So if we were to run parallel tests, this will be a problem. I'm hoping that the destructive nature of our work will settle down, and then we can remove these destructive schema operations entirely. Then we can run parallel tests. Tests run pretty fast right now (~10s), so no incentive to address this right now anyway.\r\n\r\n> Ah this must be a temp change to dump the no-longer-needed tables and columns in the test db?\r\n\r\nexactly. a better pattern would be to run some schema migration logic, but I was lazy.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790077110"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860140743", "body": ""}
{"comment": {"body": "Should we use \"Jupiter\" instead?\r\n```\r\nimport org.junit.jupiter.api.BeforeAll\r\nimport org.junit.jupiter.api.BeforeEach\r\n```", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790077866"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860142868", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860143099", "body": ""}
{"comment": {"body": "Worth having a closer look. When I played around with it, I noticed it was still calling the setup method multiple times (once per test function) but maybe I configured it incorrectly", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790079861"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#pullrequestreview-860143144", "body": ""}
{"comment": {"body": "it === BeforeAll", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/105#discussion_r790079905"}}
{"title": "Legacy proto generation cleanup", "number": 1050, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1050"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1050#pullrequestreview-956782157", "body": ""}
{"title": "First pass at creating a queue produce and consumer for posting PR comments", "number": 1051, "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051", "body": "The eventual goal here is to offload creating, updating, and deleting GitHub comments from unblocked to a queue consumer. \nThis PR sets up a skeleton consumer and producer just to test that this works. We'll fire a message to the queue when messages are created, updated, and deleted, and the consumer will just log it out. Next PR will all proper logic.\nTODO\n- [x] Create a standard SQS queue called unblocked_pr_comments\n- [x] Update global.conf and prod.conf with the queue url"}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-956797788", "body": ""}
{"comment": {"body": "@mahdi-torabi I notice that this field is not currently used and that the email config has its own region. Should I use this region or define a new config that has its own region?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r861138994"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-956842068", "body": ""}
{"comment": {"body": "The email one was a bit tricky. Ideally I would have used region specified in the config for each but that was adding a bunch of extra code. Since email deals with SES I took that one over SQS. \r\n\r\nIn your case I'd say go with what's in QueueConfig for region.\r\n\r\nI'll cleanup the email service to avoid confusion in future. ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r861170594"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-956842577", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-956857004", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-956887154", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-956888143", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957009347", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957025664", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957150669", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957159575", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957204206", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957204725", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957240340", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957240678", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957255757", "body": ""}
{"comment": {"body": "As promised:   https://github.com/NextChapterSoftware/unblocked/pull/1075", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r861465383"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957256378", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957256571", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957256813", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957267634", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-957270106", "body": ""}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958047137", "body": ""}
{"comment": {"body": "This needs to be added to `local.conf` as well otherwise local services will fail ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r862011987"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958049129", "body": ""}
{"comment": {"body": "@rasharab is there a downside to passing an actual client (e.g S3 or sqs) to the class instead of providers and factory classes ? I was thinking of doing it for notification service but held back. Would that cause a concurrency issue ? ", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r862013447"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958050435", "body": ""}
{"comment": {"body": "Only downside is mocking possibly for tests.\r\nBut we can mock the actual clients as well.\r\nWe can move away form this pattern if you have a good example, not set in stone.", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r862014456"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958054368", "body": ""}
{"comment": {"body": "interesting, so it doesn't inherit from global.conf?", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r862017265"}}
{"htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#pullrequestreview-958054886", "body": ""}
{"comment": {"body": "oh I guess we're overriding the region, so we override the whole object", "htmlUrl": "https://github.com/NextChapterSoftware/unblocked/pull/1051#discussion_r862017624"}}
