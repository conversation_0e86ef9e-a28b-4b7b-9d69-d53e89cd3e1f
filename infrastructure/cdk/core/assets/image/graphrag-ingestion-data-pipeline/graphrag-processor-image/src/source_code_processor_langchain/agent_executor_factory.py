from abc import ABC, abstractmethod

from langchain.agents import AgentExecutor, initialize_agent, Too<PERSON>, AgentType
from langchain.chains import RetrievalQA
from langchain_core.language_models import BaseLanguageModel
from langchain_neo4j import GraphCypherQAChain
from langchain_neo4j.graphs.graph_store import GraphStore


class AgentExecutorFactoryInterface(ABC):
    """
    Interface for creating LangChain AgentExecutor instances.
    """

    @abstractmethod
    def create_agent_executor(self) -> AgentExecutor:
        """
        Creates and returns a LangChain AgentExecutor.

        Returns:
            AgentExecutor: The constructed AgentExecutor.
        """
        pass


# https://neo4j.com/developer-blog/knowledge-graph-rag-application/
class Neo4jAgentExecutorFactory(AgentExecutorFactoryInterface):
    """
    Factory for creating an AgentExecutor using pre-initialized Neo4j graph and vector stores.
    """

    def __init__(
        self,
        graph_store: GraphStore,
        vector_store,
        llm: BaseLanguageModel,
    ):
        """
        Initializes the factory with Neo4j stores and LangChain configuration.

        Args:
            graph_store: Pre-initialized Neo4jGraph for graph-based querying.
            vector_store: Pre-initialized Neo4jVector for vector-based querying.
            llm: The llm
        """
        self.graph_store = graph_store
        self.vector_store = vector_store
        self.llm = llm

    def create_agent_executor(self) -> AgentExecutor:
        """
        Creates and returns an AgentExecutor using Neo4j tools and LangChain RAG setup.

        Returns:
            AgentExecutor: The constructed AgentExecutor.
        """
        # Cypher QA Chain
        cypher_chain = GraphCypherQAChain.from_llm(
            llm=self.llm,
            graph=self.graph_store,
            allow_dangerous_requests=True,
        )

        # Vector QA Chain
        vector_qa = RetrievalQA.from_chain_type(
            llm=self.llm,
            chain_type="stuff",
            retriever=self.vector_store.as_retriever(),
        )

        # Tools for the agent
        tools = [
            Tool(
                name="graph_cypher_tool",
                func=cypher_chain.run,
                description=(
                    "Query the knowledge graph using Cypher. Use this tool when the question involves structured data, "
                    "hierarchies, relationships, or requires graph-level reasoning."
                ),
            ),
            Tool(
                name="vector_qa_tool",
                func=vector_qa.run,
                description=(
                    "Retrieve semantically similar information from the knowledge graph using vector embeddings. "
                    "Use this tool when the question involves unstructured data or natural language matching."
                ),
            ),
        ]

        # Initialize Agent
        agent_executor = initialize_agent(
            tools=tools,
            llm=self.llm,
            agent=AgentType.OPENAI_FUNCTIONS,
            verbose=True,
        )

        return agent_executor
