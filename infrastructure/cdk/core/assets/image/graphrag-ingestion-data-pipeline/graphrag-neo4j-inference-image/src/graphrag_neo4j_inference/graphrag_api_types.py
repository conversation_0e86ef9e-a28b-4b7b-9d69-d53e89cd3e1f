from typing import Optional

from pydantic import BaseModel


class GraphRagParameters(BaseModel):
    max_tokens: Optional[int] = None
    top_k: Optional[int] = None
    vector_index_name: Optional[str] = None
    fulltext_index_name: Optional[str] = None


class GraphRagRequest(BaseModel):
    query: str
    parameters: Optional[GraphRagParameters] = None


class GraphRagResponse(BaseModel):
    response: str
