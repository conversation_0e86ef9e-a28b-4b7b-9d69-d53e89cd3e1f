{"state":"Merged","mergedAt":"2023-11-19T03:30:51Z","number":9296,"body":"chnages\n- copy when different\n- make non-null","mergeCommitSha":"0939cf27d0082526f15d52fec922fd42aba7cd7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9296","title":"Migrate isUserSelected by backfilling from isScmConnected","createdAt":"2023-11-19T03:09:03Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:43:07Z","number":9297,"body":"Only cleanup deselectd repos when actually deselected, otherwise the cleanup is far too noisy.","mergeCommitSha":"aa9e57577d26cc8fe5807ae43a05082791374542","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9297","title":"More precise repo cleanup","createdAt":"2023-11-20T00:08:28Z"}
{"state":"Closed","mergedAt":null,"number":9298,"body":"GitHub repo selection UI","mergeCommitSha":"b7142c49e21f6ccfbf557e1700c564b0af25e66c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9298","title":"GitHub repo selection UI","createdAt":"2023-11-20T00:08:32Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:52:55Z","number":9299,"mergeCommitSha":"9c267ff7bd5acb54eff36376fe456df1982a20b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9299","title":"Use new repo APIs","createdAt":"2023-11-20T00:08:35Z"}
{"state":"Closed","mergedAt":null,"number":93,"mergeCommitSha":"f3dc612a957556cf22aadacbf9e1a49f4b202519","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/93","title":"[WIP] Setup CORS and port handling","createdAt":"2022-01-21T01:45:06Z"}
{"state":"Merged","mergedAt":"2022-04-19T20:40:47Z","number":930,"mergeCommitSha":"96e2bfd642d49d9e69fd224b1f17e9fa1ef0747c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/930","title":"TextEditor line range off by one","createdAt":"2022-04-19T20:26:32Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:50:39Z","number":9300,"mergeCommitSha":"ee031b244295de2193a6ea39b1445a5e6d7dac66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9300","title":"Fix github repo installation webhook","createdAt":"2023-11-20T00:14:17Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:46:45Z","number":9301,"body":"Use new repo UI for GitHub onboarding and settings\r\n\r\n![CleanShot 2023-11-19 at 17 02 40@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/af4d816e-c62c-40d9-9304-c4ff89cf718c)\r\n\r\n* Add `GitHubRepoSelectionNeeded` -- view for when we need to set up GitHub because explicit repo selection is required\r\n* Update `Tabs` styling so they work better in general usage\r\n* During onboarding, when we are in the pending state, display the repo-selection UI as needed","mergeCommitSha":"62cca0fcbd85e8df9f7aa7341190e5aa2cf3d2c0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9301","title":"GitHub repo selection","createdAt":"2023-11-20T01:06:23Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:53:36Z","number":9302,"mergeCommitSha":"155f700d15ca30cd0e7c56bf1d3e27cbe7032439","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9302","title":"Adjust return type of getTeamScmInstallationReposV2","createdAt":"2023-11-20T02:10:25Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:54:45Z","number":9303,"mergeCommitSha":"8db39da86bb4535b124be3d2a4c4f2cb683c2f6b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9303","title":"Re-include forks in repo selection (but not archived/disabled repos)","createdAt":"2023-11-20T02:59:32Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:55:51Z","number":9304,"body":"Motivation: By far the most interesting un-selected repos are the ones that changed recently.\r\n\r\n# Old: Sort by name\r\nTop most repos are not actually that interesting, such as repos from 2022.\r\n<img width=\"800\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/4dc495ef-31c2-4f9d-8d23-c73664a593e3\">\r\n\r\n\r\n# Proposed: Sort by recency\r\nCan see the most interesting repos at top, such as `unblocked`, `machine-learning`, etc.\r\n\r\n<img width=\"800\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/68510f8d-b227-4188-8cc1-7932c2ac83d2\">\r\n","mergeCommitSha":"979a99ed193385476fb6f42864412ef338629362","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9304","title":"By default sort repos by most recently active","createdAt":"2023-11-20T03:13:53Z"}
{"state":"Merged","mergedAt":"2023-11-20T03:55:07Z","number":9305,"mergeCommitSha":"b78700493616d04790abb6fcc6d3ed54b475501e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9305","title":"Surface regression testing in admin console","createdAt":"2023-11-20T03:21:43Z"}
{"state":"Merged","mergedAt":"2023-11-20T07:07:18Z","number":9306,"body":"Is this correct?","mergeCommitSha":"b4a4e0d5e22789a21ed92c05a593bba643182d8f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9306","title":"Cascade deletes on ProviderAuthenticationStateModel","createdAt":"2023-11-20T04:11:26Z"}
{"state":"Merged","mergedAt":"2023-11-20T05:41:20Z","number":9307,"body":"Tests don't detect this issue because they're all wrapped in transaction blocks","mergeCommitSha":"fd6dfc30a7a8fad261351f62fdb3b70bcf18b488","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9307","title":"Broken transaction logic on data model conversions","createdAt":"2023-11-20T05:17:30Z"}
{"state":"Closed","mergedAt":null,"number":9308,"body":"Adjust the root dom items and nav stack so that the window object (body dom element) is the primary scrolling element, instead of a descendant.\r\n\r\nThis should make scroll position restoration easier, as most libraries seem to assume that the window is the primary scroll object.\r\n\r\nAs part of this, removed the `ScrollContext`, as we can just scroll the window","mergeCommitSha":"8b17b58d03b0f425ae080b4b72c1c1bf25eeaede","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9308","title":"Use window object for scrolling in dashboard","createdAt":"2023-11-20T05:20:25Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:58:57Z","number":9309,"body":"Note that there is no max number of repos, for now.","mergeCommitSha":"68098d642abc644e6dfdbe1b039c51a349d4e8a7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9309","title":"Pre-select repos from the last 6 months when onboarding teams","createdAt":"2023-11-20T05:32:40Z"}
{"state":"Merged","mergedAt":"2022-04-19T20:44:17Z","number":931,"body":"Dumb syntax mistake.  I'm surprised but there doesn't seem to be an eslint rule for this.","mergeCommitSha":"cad0b8595a156ef560a9be94ddb63c4fac7cad42","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/931","title":"Fix gutter rendering","createdAt":"2022-04-19T20:36:30Z"}
{"state":"Merged","mergedAt":"2023-11-20T07:00:26Z","number":9310,"mergeCommitSha":"67a3c219bfc23d2823593bdaf2633f398a3cf94f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9310","title":"Fix NaN exception","createdAt":"2023-11-20T06:29:48Z"}
{"state":"Merged","mergedAt":"2023-11-20T19:49:37Z","number":9311,"body":"More work to help with scroll restoration -- if the stream used in `useStream` has a remembered value, we will return that synchronously on first render.  This should help prevent rendering `loading` states unnecessarily.\r\n\r\nLots of wonky hook logic here, I tried to comment and test it.","mergeCommitSha":"b16edd87adef1e133a18b76ceaa943324fc88d6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9311","title":"`useStream` hook returns remembered value on first render","createdAt":"2023-11-20T07:03:33Z"}
{"state":"Merged","mergedAt":"2023-11-20T08:23:55Z","number":9312,"mergeCommitSha":"5110adb1cb5674be47d2c647c4b9ab6f0dd83779","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9312","title":"Add delete test button","createdAt":"2023-11-20T08:01:42Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:57:23Z","number":9313,"body":"Is this correct?","mergeCommitSha":"3d1b1a56e864035287133c61c6f25b41adaed6b6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9313","title":"Cascade deletes on ProviderAuthenticationStateModel","createdAt":"2023-11-20T08:23:39Z"}
{"state":"Merged","mergedAt":"2023-11-20T21:00:08Z","number":9314,"mergeCommitSha":"ce9a05f0b67d051779bfbe717be91a276c4216fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9314","title":"Hack using getTeam API because the client team store is not updating properly","createdAt":"2023-11-20T08:23:47Z"}
{"state":"Merged","mergedAt":"2023-11-20T21:01:43Z","number":9315,"mergeCommitSha":"89a42f02b9fa08280a807141e1417fa4a9b7e684","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9315","title":"Support GitHub in listScmInstallationRepos API","createdAt":"2023-11-20T08:23:51Z"}
{"state":"Merged","mergedAt":"2023-11-20T21:03:13Z","number":9316,"mergeCommitSha":"6480f4d512e12075ccb0a138c9ffc7ded80eb3ba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9316","title":"Persist fork trait on RepoModel and use to filter out from onboarding pre-selection","createdAt":"2023-11-20T08:46:11Z"}
{"state":"Merged","mergedAt":"2023-11-20T17:30:25Z","number":9317,"mergeCommitSha":"c4b85e18b33f704387dcb8151ce44d2ec6afcda2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9317","title":"Using the wrong ID for deletes","createdAt":"2023-11-20T17:21:02Z"}
{"state":"Merged","mergedAt":"2023-11-20T19:58:56Z","number":9318,"mergeCommitSha":"bf1cffa25b14376ba80ff15f68b0f163d14185ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9318","title":"Channel ingestion on changing slack channels","createdAt":"2023-11-20T19:36:51Z"}
{"state":"Merged","mergedAt":"2023-11-20T19:50:07Z","number":9319,"mergeCommitSha":"e35a94f0f782ba9511cc8f8de0dd33853ea47dd4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9319","title":"Add eval logging","createdAt":"2023-11-20T19:38:58Z"}
{"state":"Merged","mergedAt":"2022-04-19T21:38:08Z","number":932,"body":"- Modified Lambda Lookup function to handle requests for streaming (agora) assets\r\n- I have added temporary support for special path handling to help us test this while we figure out how to register agora assets. Right now we can address an asset with .m3u8 extension directly. Eventually all assets will be accessed only using UUIDs\r\n- Added support to handle streaming assets similar to regular assets (for later once we have agora asset registration working!)\r\n- We no parse and modify playlist files on the fly to replace each part with a signed url\r\n- Added a separate config file to help with managing streaming configs\r\n- Added integration tests to infra deployment. These tests make sure Cloudfront in Dev is functional before allowing promotion to next env\r\n- Made infra deploys sequential","mergeCommitSha":"edba4936c97dc9dff80709dd8dcdfd80a5dee97f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/932","title":"Adding support for secure HLS streaming","createdAt":"2022-04-19T21:14:33Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:08:24Z","number":9320,"body":"![CleanShot 2023-11-20 at 11 56 32@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/6e2e1501-5929-415a-ae02-8ccf47bb27fc)\r\n![CleanShot 2023-11-20 at 11 56 40@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/4abdd0c3-3311-4b86-aabc-b2f8510e2626)\r\n","mergeCommitSha":"f667002e32a9f3738e45bb927754ff22e6c7cc57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9320","title":"Fix Connect post-button spacing","createdAt":"2023-11-20T19:57:18Z"}
{"state":"Merged","mergedAt":"2023-11-20T20:35:31Z","number":9321,"mergeCommitSha":"54355d184407d463b80dfe22e10b841030cd9d74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9321","title":"Move machine learning tabs around","createdAt":"2023-11-20T20:01:58Z"}
{"state":"Merged","mergedAt":"2023-11-20T21:11:59Z","number":9322,"body":"As part of strategy to consolidate the SCM install APIs, the `connectScmInstallation` and `patchInstallation` are now completely identical.\n\nNow, both always create the team and both always select the specified repos.","mergeCommitSha":"07a02cdbff7edf5a000a2c7b140da5764219c9c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9322","title":"Rework SCM install APIs","createdAt":"2023-11-20T20:14:45Z"}
{"state":"Merged","mergedAt":"2023-11-20T23:48:40Z","number":9323,"body":"Small feedback bits in previous PR","mergeCommitSha":"6a6b6c88e3956044b7bd0359996473c335c25f27","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9323","title":"Some cleanup","createdAt":"2023-11-20T21:10:59Z"}
{"state":"Merged","mergedAt":"2023-11-20T21:37:33Z","number":9324,"mergeCommitSha":"3b1190b10e5a3d53872906f2b0ee9ee960689bf1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9324","title":"Add global examples page","createdAt":"2023-11-20T21:16:15Z"}
{"state":"Merged","mergedAt":"2023-11-20T23:13:29Z","number":9325,"mergeCommitSha":"cb91e10f6bf11803dd4f339529b81bb5039b2f95","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9325","title":"Move to upsert","createdAt":"2023-11-20T22:06:11Z"}
{"state":"Merged","mergedAt":"2023-11-20T22:31:04Z","number":9326,"mergeCommitSha":"e91e42945508bee74d84a6ada3ced88e94807cb5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9326","title":"More logging","createdAt":"2023-11-20T22:10:11Z"}
{"state":"Merged","mergedAt":"2023-11-20T22:43:14Z","number":9327,"body":"If you filter down the set of repos, and click the search bar \"select all\" / \"clear all\" checkbox, we will only select/clear the filtered/visible items","mergeCommitSha":"1ffa3d7902974170c93da7e2db3a1c2d66283ccf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9327","title":"Only select filtered/visible items in the repo table","createdAt":"2023-11-20T22:32:06Z"}
{"state":"Merged","mergedAt":"2023-11-20T23:37:36Z","number":9328,"mergeCommitSha":"fa498e61a05b35b54c43c3033cef21ca236869fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9328","title":"Add regression test details page","createdAt":"2023-11-20T23:05:52Z"}
{"state":"Merged","mergedAt":"2023-11-20T23:40:07Z","number":9329,"mergeCommitSha":"6ce59b7ca860d7f5cba660fce15cd7d1ef14145b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9329","title":"Fix topic ingestion pipeline","createdAt":"2023-11-20T23:39:59Z"}
{"state":"Merged","mergedAt":"2022-04-19T22:04:09Z","number":933,"body":"See this for more details: https://github.com/microsoft/vscode/issues/146330\r\n\r\nThe main downside of this, as far as I can tell, is that we always jump back to the explorer view afterwards.  This makes sense on startup (because that's what is displayed by default), but when you're installing the extension VSIX it means we jump from the extension view to the explorer.  I can't find any way to determine which view the user is currently on, but I'll keep digging.","mergeCommitSha":"d2daf8bc983991755f2b005eec5629afa66bc915","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/933","title":"Force display the sidebar for a split second on startup, to allow badges to display","createdAt":"2022-04-19T21:52:41Z"}
{"state":"Merged","mergedAt":"2023-11-20T23:57:10Z","number":9330,"body":"![CleanShot 2023-11-20 at 15 45 27@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/9e97f4df-57f1-41bc-81fc-0a72935d8030)\r\n","mergeCommitSha":"7c1dff2aa6b3cb0e649aa0ef42fb01bb06eebf69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9330","title":"Change 'Grant Access' label","createdAt":"2023-11-20T23:45:37Z"}
{"state":"Merged","mergedAt":"2023-11-20T23:54:35Z","number":9331,"body":"https://chapter2global.slack.com/archives/C066FE1417V/p1700519984542259","mergeCommitSha":"299db61123d6bcbed593d731d6d0a27968371094","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9331","title":"Bitbucket onboarding repo selection uses the getTeamScmInstallationReposV2 once a team has been created","createdAt":"2023-11-20T23:52:06Z"}
{"state":"Merged","mergedAt":"2023-11-21T00:03:24Z","number":9332,"mergeCommitSha":"ac9dc3c80e2b8e39f421fdd7d9f52464d8352e6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9332","title":"Fix lint","createdAt":"2023-11-21T00:02:46Z"}
{"state":"Merged","mergedAt":"2023-11-21T00:12:58Z","number":9333,"mergeCommitSha":"50566ca667197bfecfe19a056f65a70acf61ea39","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9333","title":"Always show repo that was selcted","createdAt":"2023-11-21T00:12:38Z"}
{"state":"Merged","mergedAt":"2023-11-22T21:17:03Z","number":9334,"body":"Migrate dashboard routes to route objects for new react router.","mergeCommitSha":"6369727b739fb463c579896e5b47433bbbbf9766","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9334","title":"Migrate dashboard routes","createdAt":"2023-11-21T00:14:46Z"}
{"state":"Merged","mergedAt":"2023-11-21T00:42:52Z","number":9335,"mergeCommitSha":"10a2a4ab0add777508e329dc6d48cfcea2eefddd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9335","title":"Add regression eval details page","createdAt":"2023-11-21T00:29:17Z"}
{"state":"Merged","mergedAt":"2023-11-21T01:02:46Z","number":9336,"mergeCommitSha":"4ff61a2aac9dc8b2729ff9d6fdc6543b6fea9c3b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9336","title":"Fix execution state update bug on regression test object","createdAt":"2023-11-21T00:40:49Z"}
{"state":"Merged","mergedAt":"2023-11-21T00:51:33Z","number":9337,"mergeCommitSha":"3ebce4c33660a41f4080eea9342497ae04ec6628","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9337","title":"Ability to trigger web CI manually","createdAt":"2023-11-21T00:50:53Z"}
{"state":"Merged","mergedAt":"2023-11-21T06:54:35Z","number":9338,"body":"## Background\nCurrently the user profile menu depends on TeamMember API model information for rendering the user display name and avatar.\n\n## Motivation\nBefore the user has created a team, there is no team member, therefore we cannot render the user profile menu.\n\nSee https://chapter2global.slack.com/archives/C066FE1417V/p1700519726652459.\n\n## Changes\nThis change adds name and avatar to Person API model.","mergeCommitSha":"8237565fe13db1e20c1b481972017dec91e8b8b2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9338","title":"Add name and avatar to Person API model","createdAt":"2023-11-21T01:03:43Z"}
{"state":"Closed","mergedAt":null,"number":9339,"mergeCommitSha":"cf1e39776d16197d9964c3a6b7a9ddf6fae0a01a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9339","title":"Fix the Manage existing team repos during the Bitbucket onboarding flow","createdAt":"2023-11-21T01:25:08Z"}
{"state":"Merged","mergedAt":"2022-04-19T22:37:19Z","number":934,"body":"Handy for me when clearing points.","mergeCommitSha":"32b47f1eb3f9b11ae4a3eec5fbc70541ac6e5e1e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/934","title":"Added debug information when clearing calculated source points","createdAt":"2022-04-19T22:23:11Z"}
{"state":"Merged","mergedAt":"2023-11-22T21:18:20Z","number":9340,"body":"When onboarding and we pre-select some repos, show this header:\r\n\r\n![CleanShot 2023-11-20 at 17 30 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/75b36337-c5f1-4549-afde-97a9d84810ba)\r\n\r\nWhen onboarding and no repos are selected, or when going to the dashboard when you have not yet selected repos, but need to, you see this (same as the settings UI):\r\n\r\n![CleanShot 2023-11-20 at 17 34 55@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b5ecb2cb-56bc-4234-be00-089ff1bae837)\r\n","mergeCommitSha":"30f63ad9406db31a3f3741ed14172384b998ab66","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9340","title":"Use correct GitHub repo header","createdAt":"2023-11-21T01:37:40Z"}
{"state":"Merged","mergedAt":"2023-11-21T02:15:46Z","number":9341,"mergeCommitSha":"6f3d1f82fb54443baab45fe2b27096b28faf400f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9341","title":"Fix variable naming","createdAt":"2023-11-21T02:15:37Z"}
{"state":"Merged","mergedAt":"2023-11-21T03:07:21Z","number":9342,"mergeCommitSha":"df8f2ee5810ba3395efb39d1117bc4272bedf9a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9342","title":"Fix title","createdAt":"2023-11-21T03:07:14Z"}
{"state":"Merged","mergedAt":"2023-11-21T04:08:15Z","number":9343,"mergeCommitSha":"7f3ccbc0f18eed4a5af267d0446f701f34139570","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9343","title":"Add pass/fail field to regression tests","createdAt":"2023-11-21T03:43:35Z"}
{"state":"Merged","mergedAt":"2023-11-21T05:24:56Z","number":9344,"mergeCommitSha":"b7d9fa15a3d8dfff99d04c3e250998c0a7b4cdd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9344","title":"Naughty logging fix","createdAt":"2023-11-21T04:27:17Z"}
{"state":"Merged","mergedAt":"2023-11-21T07:06:29Z","number":9345,"mergeCommitSha":"8ada6bd4ad40666adcd7714df9a1bc009347bdd7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9345","title":"Run regression tests every hour","createdAt":"2023-11-21T06:43:03Z"}
{"state":"Merged","mergedAt":"2023-11-21T07:34:44Z","number":9346,"mergeCommitSha":"f9638a77ea6e070807a3805ca5973a2d09c5efd6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9346","title":"Don't create regression test if there are no examples to test","createdAt":"2023-11-21T07:14:40Z"}
{"state":"Merged","mergedAt":"2023-11-21T07:45:27Z","number":9347,"mergeCommitSha":"e6c63d6b70cf59f32b9c72723bb9bfe5e60d2dd1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9347","title":"Clean up score rendering","createdAt":"2023-11-21T07:31:57Z"}
{"state":"Merged","mergedAt":"2023-11-22T01:15:36Z","number":9348,"body":"This allows forks to be eligible for selection, where previously they were not.\r\n\r\nIt does not pre-select them during onboarding, even if they are recent.\r\n\r\nFixes https://app.intercom.com/a/inbox/crhakcyc/inbox/admin/5621013/conversation/904","mergeCommitSha":"308009be98c90797ec153830accad1d45892e72e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9348","title":"Adds fork information into Repo and ScmRepo APIs","createdAt":"2023-11-21T07:51:07Z"}
{"state":"Merged","mergedAt":"2023-11-21T21:49:10Z","number":9349,"body":"Add settings row for pr conversations in GH that isn't configurable (disabled). Shows the user the full list of sources that we are pulling.\r\n\r\n<img width=\"703\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/8fa60218-c38a-4132-ab24-ec974ce915a3\">\r\n","mergeCommitSha":"2e3166836857403043a1528e695a9e67c3871b36","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9349","title":"Add pull request toggle row to GH settings","createdAt":"2023-11-21T18:18:47Z"}
{"state":"Merged","mergedAt":"2022-04-19T22:48:47Z","number":935,"body":"The extra output in diffs is because last deploy failed. Those same changes should go out with this PR ","mergeCommitSha":"cf720dbbcec58a655009b9cd21c5676bc45d399d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/935","title":"fix path prefix in cloudfront tests","createdAt":"2022-04-19T22:36:26Z"}
{"state":"Merged","mergedAt":"2023-11-21T20:46:23Z","number":9350,"mergeCommitSha":"3c49ee5044c1adaa135ae96c859ee65c28a9bb23","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9350","title":"Enable chat compression for slack threads","createdAt":"2023-11-21T18:46:57Z"}
{"state":"Merged","mergedAt":"2023-11-21T21:18:25Z","number":9351,"mergeCommitSha":"72e59286cf2861f4966775d0d65c323a7420721c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9351","title":"Limit historical messages for Unblocked and slack bot queries","createdAt":"2023-11-21T19:02:05Z"}
{"state":"Merged","mergedAt":"2023-11-21T19:38:14Z","number":9352,"mergeCommitSha":"11fc52300204135799e148a9a7ae463426950fa5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9352","title":"Fix excxeption handling for slack","createdAt":"2023-11-21T19:37:23Z"}
{"state":"Merged","mergedAt":"2023-11-21T21:40:16Z","number":9353,"body":"* Upon first onboarding, prior to team creation, we weren't able to display the user menu because we relied on team member data to populate the UI\r\n* Fix: If there is no team member, fall back to using the Person displayName/avatarUrl ","mergeCommitSha":"f8f5ef820bbd9155c56d99cbb6f6f72d51cde52e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9353","title":"Address missing person menu","createdAt":"2023-11-21T20:16:19Z"}
{"state":"Merged","mergedAt":"2023-11-21T21:22:10Z","number":9354,"mergeCommitSha":"3a2db3b861426e472b5a88261a9d9353f0556ce3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9354","title":"Improve notion ingestion logging","createdAt":"2023-11-21T20:46:05Z"}
{"state":"Merged","mergedAt":"2023-11-21T22:21:56Z","number":9355,"body":"<img width=\"724\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/2fefb8e7-6302-4c8c-ba40-eb2f09ee153e\">\r\n","mergeCommitSha":"1c7717e2a9fc2cbab0e47717832c35d951b9a061","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9355","title":"Move background","createdAt":"2023-11-21T22:09:54Z"}
{"state":"Merged","mergedAt":"2023-11-22T19:14:18Z","number":9356,"mergeCommitSha":"3dabaa74d08d0a06d9d7ae2aa62de2327010433e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9356","title":"HACK: clean up UAT docs for Drata","createdAt":"2023-11-21T22:28:19Z"}
{"state":"Merged","mergedAt":"2023-11-21T23:28:38Z","number":9357,"mergeCommitSha":"65306841407b2a5d959e02f72bf131612f3eec2d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9357","title":"Enable regressoin testing in prod","createdAt":"2023-11-21T22:56:15Z"}
{"state":"Merged","mergedAt":"2023-11-21T23:29:39Z","number":9358,"mergeCommitSha":"04fec11bd111b67de9b7e3408b66718d9a383292","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9358","title":"Fix admin web repo cleanup routes","createdAt":"2023-11-21T23:16:19Z"}
{"state":"Merged","mergedAt":"2023-11-22T01:14:07Z","number":9359,"body":"Fixes issue that someone from gitkraken was running into: https://chapter2global.slack.com/archives/C063TMQQD6K/p1699654711179619\r\n\r\nWe issue a separate `findRepo` query for each team, for each repo, for each remote (\uD83E\uDD2A).  Unfortunately when any of these queries failed with a `404`, we would give up on the entire set of queries for the repo.  The end result is if you have multiple remotes, and we couldn't map any of the remotes, we would fail to map the repo entirely.\r\n\r\nWith this fix, we skip `404` results on every `findRepo` invocation -- the first repo/remote we can match will be used.","mergeCommitSha":"258e8f21c4817ad35334dbf38a70a579c4864500","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9359","title":"IDE fix for multi-remote repos","createdAt":"2023-11-21T23:23:35Z"}
{"state":"Merged","mergedAt":"2022-04-19T23:18:25Z","number":936,"mergeCommitSha":"5afcf31f49171ba0b4ecff0c8e08aa08d895218d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/936","title":"unblock infra deployments while I figure this out","createdAt":"2022-04-19T23:06:35Z"}
{"state":"Merged","mergedAt":"2023-11-22T01:22:20Z","number":9360,"body":"- Fix excxeption handling\r\n- Move to a standardized model for handling slack block texts\r\n","mergeCommitSha":"d5d8e7a380c0b4adec219774ba8b8a72a3e6051d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9360","title":"FixSlackErrors","createdAt":"2023-11-21T23:28:05Z"}
{"state":"Merged","mergedAt":"2023-11-22T00:58:55Z","number":9361,"mergeCommitSha":"7d230d77c99d023c346016ca00141213854179ee","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9361","title":"Post to slack for regression failures","createdAt":"2023-11-22T00:05:10Z"}
{"state":"Merged","mergedAt":"2023-11-22T00:53:10Z","number":9362,"mergeCommitSha":"a25ca106e66830f2a88d026d0fe85780bc64eaaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9362","title":"Regression pass criteria tweak","createdAt":"2023-11-22T00:40:33Z"}
{"state":"Merged","mergedAt":"2023-11-22T00:50:30Z","number":9363,"mergeCommitSha":"98f1a5ba1256f7169bad6b2be755ce6f17ee367a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9363","title":"[SKIP TESTS] reduce max scale to reduce pressure on activemq","createdAt":"2023-11-22T00:42:11Z"}
{"state":"Merged","mergedAt":"2023-11-22T02:00:43Z","number":9364,"body":"Addresses a possible, but very unusual, bug where the user tried to install the GitHub App for an org, but somehow there were more than 100 installations of our App.","mergeCommitSha":"8113f4de98db98ad5e02c2e32145d6cc552457eb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9364","title":"Stream GitHub App installations accessible to the current user","createdAt":"2023-11-22T01:03:34Z"}
{"state":"Merged","mergedAt":"2023-11-22T01:46:20Z","number":9365,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1700592769587839","mergeCommitSha":"95afaaf2457cdf21878cf1e0e2ad9aa9c96d2c48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9365","title":"Filter out Terraform Atlantis PR commands from messages","createdAt":"2023-11-22T01:35:03Z"}
{"state":"Merged","mergedAt":"2023-11-22T20:22:43Z","number":9366,"body":"Adds a source_classifier to the unblocked_file_util lib + use this new lib version in the code-ingestion.\r\n\r\nThis isn't the ideal place for it, but that is where the we are doing the filtering today (in file_traversal.py)","mergeCommitSha":"b7f1c6383428a4f5846522154b5b9bad95260756","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9366","title":"Add source code classifier","createdAt":"2023-11-22T02:09:44Z"}
{"state":"Closed","mergedAt":null,"number":9367,"mergeCommitSha":"d6e9d6f39802381154f34f792092a4cc2a99093a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9367","title":"update poetry.lock file to use new sig","createdAt":"2023-11-22T04:06:06Z"}
{"state":"Merged","mergedAt":"2023-11-22T06:24:44Z","number":9368,"mergeCommitSha":"9ac6f25c80aaf5a83f62b4b96b4709593a72c91c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9368","title":"Limit doc size in prompts to 6k","createdAt":"2023-11-22T06:09:53Z"}
{"state":"Merged","mergedAt":"2023-11-22T10:19:27Z","number":9369,"body":"- works when a thread contains a mix of bot messages and low value messages\n- excludes threads that have more than 15 words in them if the those words are spread across all messages","mergeCommitSha":"9810a37433fec3e7f2e4a0e034706d156e9b9b50","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9369","title":"Rework thread comment classification","createdAt":"2023-11-22T09:54:32Z"}
{"state":"Merged","mergedAt":"2022-04-20T00:35:40Z","number":937,"body":"Saves us having to convert","mergeCommitSha":"8752995a1ca4d4ef105f23ab898d4d7bb9c394bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/937","title":"Dont use OffSetDateTime","createdAt":"2022-04-19T23:32:03Z"}
{"state":"Merged","mergedAt":"2023-11-22T10:30:51Z","number":9370,"body":"Changes\n- scm maintenance uses exclusive lock to prevent multiple concurrent service instances from simultaneously refreshing team resources leaving to conflicts\n- disable repo cleanup; turns out that scheduling jobs to run in the future is incompatible with auto-scaling based on queue size, because when even a single item is scheduled in the queue for a far future date, then the auto scaler will scale up repeatedly to drain the queue, but this will fails, leading to more instances, that each invoke more cleanup, and generate more future jobs, and leads to a fork bomb.","mergeCommitSha":"bdd168b65015d6aca12e03bc0612ebc0f43457b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9370","title":"SCM maintenance uses exclusive lock and disable repo cleanup","createdAt":"2023-11-22T10:16:48Z"}
{"state":"Merged","mergedAt":"2023-11-22T18:03:57Z","number":9371,"mergeCommitSha":"0f0230fb77731dbc1c117639e90099bad33a4724","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9371","title":"removing the nat gateway to save us some money","createdAt":"2023-11-22T18:03:14Z"}
{"state":"Merged","mergedAt":"2023-11-22T18:52:17Z","number":9372,"mergeCommitSha":"9747d82a2a9dc45ff0740ac1ef4ddbf95860540c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9372","title":"Trust that the installer sets the correct ownership","createdAt":"2023-11-22T18:46:21Z"}
{"state":"Merged","mergedAt":"2023-11-23T19:47:51Z","number":9373,"mergeCommitSha":"cb7be540ca8492249b7096e476ff0a3501f5a765","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9373","title":"Bypass Notion API call if uat filter condition is true","createdAt":"2023-11-22T22:01:43Z"}
{"state":"Merged","mergedAt":"2023-11-22T23:07:30Z","number":9374,"body":"Possible fix for dashboard token removal on logout -- one time we saw the refresh token live on after logout.  AFAICT I don't think this would have actually caused the bug, as the end API call is synchronous, but it's worth doing this anyways.","mergeCommitSha":"997063c2e9f766b17d7d32a30cf104323c920b9f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9374","title":"Dashboard await on token removal on logout","createdAt":"2023-11-22T22:55:55Z"}
{"state":"Merged","mergedAt":"2023-11-23T00:33:57Z","number":9375,"body":"When a user disconnects or deselects a repo we cleanup code vectors and incremental state.\r\n\r\nHowever, we must make sure that we also clear the `codeIngestLastStartAt` timestamp, so that if the user subsequently _re-connects_ or _re-selects_ the repo, then code ingestion is immediately re-scheduled. Otherwise, code ingestion will incorrectly assume that the repo has ben code ingested, and will re-run only after an extended period of time has passed to refresh the code.\r\n\r\nhttps://chapter2global.slack.com/archives/C045VGYML95/p1700550115162029?thread_ts=1700549627.797039&cid=C045VGYML95","mergeCommitSha":"c8258f7d14e1406ff91c55e0d9eca5912624490b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9375","title":"Reset code ingest run status when a repo is disconnected or deselected","createdAt":"2023-11-22T23:13:58Z"}
{"state":"Merged","mergedAt":"2023-11-23T00:34:49Z","number":9376,"body":"This does exactly the same jobs as:\n```kotlin\nisScmConnected && isUserSelected\n```","mergeCommitSha":"d5e47d5cba7667c6d3608371137648a01cffaaba","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9376","title":"Remove unnecessary repo column codeIngestEnabled","createdAt":"2023-11-22T23:30:29Z"}
{"state":"Merged","mergedAt":"2023-11-23T00:35:39Z","number":9377,"body":"Replace with `Repo.isActive`.","mergeCommitSha":"99d030a0c62f69e2673073459e496991537d81d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9377","title":"Cleanup pullRequestIngestEnabled and topicIngestEnabled","createdAt":"2023-11-22T23:58:57Z"}
{"state":"Merged","mergedAt":"2023-11-23T01:06:01Z","number":9378,"body":"This is affecting the stability of the service.\n\nWe're going to pause cleanup until we find a better approach.\n\nOne option is to leverage the DB as a queue, and select exactly one repo to cleanup, but at most once every 5 minutes.","mergeCommitSha":"d31363bae10b3ba595710493677b03061f408881","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9378","title":"Disable repo cleanup because it's causing too many events to be enqueued","createdAt":"2023-11-23T00:47:12Z"}
{"state":"Merged","mergedAt":"2023-11-23T05:47:33Z","number":9379,"body":"Fix correctness issue introduced in #9221.\r\n\r\nAddresses this bug:\r\nhttps://chapter2global.slack.com/archives/C05D7H25PV5/p1700597339508959","mergeCommitSha":"700d72d59a9d7c4ff4c43ccdd29dd549a999847f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9379","title":"Prune documents from retrieval set if the repos are not active","createdAt":"2023-11-23T01:22:10Z"}
{"state":"Merged","mergedAt":"2022-04-21T21:09:12Z","number":938,"body":"Whenever the web extension's service worker is refreshed by the `KeepAlive` helper (~ 5 minutes), the data kept in memory is cleaned up. New streams are then created but left in an uninitialized state with no data. \r\n\r\nThis PR brings a persistence layer where the new streams can grab cached data and start in an initialized state.\r\nCurrently using web extension storage (https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions/API/storage) to store the data.\r\n\r\n","mergeCommitSha":"1923663bb883c3e948b2d55c087d257ae9f052a5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/938","title":"Persistence layer for web extension","createdAt":"2022-04-19T23:43:13Z"}
{"state":"Merged","mergedAt":"2023-11-23T01:32:56Z","number":9380,"body":"![CleanShot 2023-11-22 at 17 21 54@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b83ef192-1291-4735-a8cb-d075727007bc)\r\n","mergeCommitSha":"70fcb4b32caf24ed9d850b3efa62d9be8e1b4544","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9380","title":"Update text in the BB headers","createdAt":"2023-11-23T01:22:18Z"}
{"state":"Merged","mergedAt":"2023-11-23T01:31:09Z","number":9381,"mergeCommitSha":"6a762f8117b7e2f5d187ab5630af6d3ad53e91a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9381","title":"Upgrade python depds","createdAt":"2023-11-23T01:24:47Z"}
{"state":"Merged","mergedAt":"2023-11-23T02:16:13Z","number":9382,"mergeCommitSha":"72b220fd54534140073b58ebae92ae8981eea75b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9382","title":"bad comments","createdAt":"2023-11-23T02:16:07Z"}
{"state":"Merged","mergedAt":"2023-11-23T02:28:13Z","number":9383,"mergeCommitSha":"3d5f979a059d59254ca2d7a0cb3482254baf0e01","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9383","title":"Keep rate limit rules disabled for now","createdAt":"2023-11-23T02:27:28Z"}
{"state":"Merged","mergedAt":"2023-11-28T02:42:57Z","number":9384,"body":"When onboarding GitHub, after installing the GH app, if there are *any* repo-based failures (GH app not installed, no valid repos selected, repo selection required), go to the UI to resolve that state immediately, instead of going to the identities/integrations UI.\r\n\r\nAlso, remove the identities UI from onboarding","mergeCommitSha":"61d5c7b8916121aad7a47e4c3b913cfd542b9c33","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9384","title":"GH onboarding: show repo UI immediately after connection","createdAt":"2023-11-23T02:56:52Z"}
{"state":"Merged","mergedAt":"2023-11-23T05:00:05Z","number":9385,"body":"https://chapter2global.slack.com/archives/C045VGYML95/p1700708618065249?thread_ts=1700706772.300789&cid=C045VGYML95\r\n\r\nFixed:\r\n<img width=\"833\" alt=\"Screenshot 2023-11-22 at 21 00 21\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/170886fe-7fee-4439-8d8a-f189cbd53abe\">\r\n","mergeCommitSha":"49333ff074f221bacd83ca4fbde25b2b09927c37","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9385","title":"Fix repo count in Repo Banner in web","createdAt":"2023-11-23T03:29:53Z"}
{"state":"Merged","mergedAt":"2023-11-23T05:59:07Z","number":9386,"mergeCommitSha":"103410cb4b814fc71e9b33c14019f94458d2ffce","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9386","title":"Render Semantic Search page response in markdown","createdAt":"2023-11-23T05:56:37Z"}
{"state":"Merged","mergedAt":"2023-11-24T01:33:27Z","number":9387,"body":"Retrieves shared team questions based threads.\n- The thread must be created by someone other than the current user.\n- The thread must have at least one bot participant.\n- The thread must be accessible to the user.\n- The thread must be active.\n\nSupports cursor-based keyset pagination to fetch previous and next pages.","mergeCommitSha":"e9d8eb846d74de4242db172e77c014face80396d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9387","title":"Team questions API and server implementation","createdAt":"2023-11-23T07:19:05Z"}
{"state":"Merged","mergedAt":"2023-11-23T18:35:11Z","number":9388,"body":"Adding a new VPC to be used for EKS deployment via CDK. \r\n\r\n Currently this is only enabled for Dev us-west-2.","mergeCommitSha":"e9295ecdb79ab1db419b2d69204cecd240ee99e1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9388","title":"Adding new cdk stack for EKS vpc","createdAt":"2023-11-23T18:28:15Z"}
{"state":"Merged","mergedAt":"2023-11-23T20:11:55Z","number":9389,"mergeCommitSha":"5407c160c2b8d8b13fd8052dd35732d2609534c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9389","title":"gradle","createdAt":"2023-11-23T18:31:25Z"}
{"state":"Merged","mergedAt":"2022-04-20T01:16:18Z","number":939,"body":"Intention is that the team runs in assertion mode.\r\n\r\nFound an issue right away:\r\n```js\r\n{\r\n  context: 'SourceMarkStore',\r\n  service: 'vscode',\r\n  environment: 'local',\r\n  type: 'nodejs',\r\n  process: 'extension',\r\n  marksWithNoOriginal: [\r\n    '7e5e908f-3497-4b7f-81fe-21c26ecab3fa',\r\n    '658a3edc-4d84-415f-a038-6944dee94c6f',\r\n    'f72294b2-de61-4392-bb1f-064adc735ad6',\r\n    '4dab14a9-f9fe-4bb8-9078-f333e2b00383',\r\n    'f056a74a-6bea-4852-b997-88ba4a2fdbdd',\r\n    'ae6f9f9d-99f2-47ea-8179-004f42b943a2',\r\n    '30df9b1c-993d-4890-8c53-492fd807fbd1',\r\n    'cab06b7c-d841-4008-a447-de00187e39bc'\r\n  ],\r\n  level: 'error',\r\n  message: 'found mark(s) with no original point',\r\n  timestamp: '2022-04-20T00:18:34.246Z'\r\n}\r\n```","mergeCommitSha":"abc618d701a8eae0478e7b4c543a393286e37a1f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/939","title":"Sourcemark debug assertions mode","createdAt":"2022-04-20T00:19:06Z"}
{"state":"Merged","mergedAt":"2023-11-23T18:39:17Z","number":9390,"body":"```\nc.n.a.RestrictedAccessException: User Access denied: 'Bitbucket/712020:c2330d23-69ff-45e7-8015-f6044164224e'\n\tat c.n.a.RestrictedAccessServiceInterface$DefaultImpls.checkUserAccess(RestrictedAccessServiceInterface.kt:18)\n\tat c.n.a.RestrictedAccessService.checkUserAccess(RestrictedAccessService.kt:5)\n\tat c.n.a.s.a.i.ScmIdentityAuthExchangeService.exchangeAuthCodeForIdentity(ScmIdentityAuthExchangeService.kt:49)\n\tat c.n.a.s.a.i.ScmIdentityAuthExchangeService$exchangeAuthCodeForIdentity$1.invokeSuspend(ScmIdentityAuthExchangeService.kt)\n\tat k.c.j.i.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)\n\tat k.c.DispatchedTask.run(DispatchedTask.kt:108)\n\tat k.c.EventLoop.processUnconfinedEvent(EventLoop.common.kt:68)\n\tat k.c.i.DispatchedContinuation.resumeWith(DispatchedContinuation.kt:347)\n\tat i.k.u.p.SuspendFunctionGun.resumeRootWith(SuspendFunctionGun.kt:135)\n\tat i.k.u.p.SuspendFunctionGun.loop(SuspendFunctionGun.kt:109)\n\tat i.k.u.p.SuspendFunctionGun.access$loop(SuspendFunctionGun.kt:11)\n\tat i.k.u.p.SuspendFunctionGun$continuation$1.resumeWith(SuspendFunctionGun.kt:59)\n\tat k.c.j.i.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:46)\n\tat k.c.DispatchedTask.run(DispatchedTask.kt:108)\n\tat i.n.u.c.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174)\n\tat i.n.u.c.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java:167)\n\tat i.n.u.c.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470)\n\tat i.n.c.e.EpollEventLoop.run(EpollEventLoop.java:403)\n\tat i.n.u.c.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\n\tat i.n.u.i.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n\tat i.k.s.n.EventLoopGroupProxy$Companion.create$lambda$1$lambda$0(NettyApplicationEngine.kt:296)\n\tat i.n.u.c.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n\tat java.lang.Thread.run(Thread.java:1583)\n```","mergeCommitSha":"661874bbb6672547a7c75a2bc5c22454be17877b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9390","title":"Allow Ben's new Bitbucket account in DEV","createdAt":"2023-11-23T18:37:49Z"}
{"state":"Merged","mergedAt":"2023-11-23T19:24:48Z","number":9391,"mergeCommitSha":"7ee8b4fb87c636b2dd6e9acba228acb2a68dd068","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9391","title":"Fix build","createdAt":"2023-11-23T18:50:32Z"}
{"state":"Merged","mergedAt":"2023-11-23T19:51:14Z","number":9392,"mergeCommitSha":"32b8841c00777305ac2a0c9882f70abcbc05596c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9392","title":"Admin readability","createdAt":"2023-11-23T19:12:09Z"}
{"state":"Merged","mergedAt":"2023-11-23T20:08:55Z","number":9393,"mergeCommitSha":"022baa821cc3e1a3f41f05493ad14b7ee721c446","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9393","title":"Show teams using template","createdAt":"2023-11-23T19:30:35Z"}
{"state":"Merged","mergedAt":"2023-11-23T20:37:13Z","number":9394,"mergeCommitSha":"da2db2e689cfa90dbf8a934b36de53639c3010c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9394","title":"REmove folder data","createdAt":"2023-11-23T20:01:16Z"}
{"state":"Merged","mergedAt":"2023-11-23T21:26:58Z","number":9395,"mergeCommitSha":"187752d361532579dfda38a141bca611bebceeb6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9395","title":"Remove old topic pipelines","createdAt":"2023-11-23T20:07:16Z"}
{"state":"Merged","mergedAt":"2023-11-24T00:55:33Z","number":9396,"mergeCommitSha":"d04a5ca91546912a73e67c6c8602f233fda2ed2f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9396","title":"Always include the summaries in the prompt","createdAt":"2023-11-23T20:55:22Z"}
{"state":"Merged","mergedAt":"2023-11-23T21:15:10Z","number":9397,"mergeCommitSha":"b637684f292b7d4bc908510f2f4759774fea25ae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9397","title":"Move teams to top of template definition","createdAt":"2023-11-23T21:05:45Z"}
{"state":"Merged","mergedAt":"2023-11-23T21:32:35Z","number":9398,"mergeCommitSha":"dd46a62656eca32593d810e37490d105c66ee439","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9398","title":"Remove old Topics","createdAt":"2023-11-23T21:19:22Z"}
{"state":"Merged","mergedAt":"2023-11-23T22:24:53Z","number":9399,"mergeCommitSha":"ed29dea8e687699677c974d6065310d023a384a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9399","title":"Only block if no channels AND patterns","createdAt":"2023-11-23T22:13:09Z"}
{"state":"Merged","mergedAt":"2022-01-21T16:10:01Z","number":94,"body":":) ","mergeCommitSha":"bb2d6dd7214dce4b4c1685c94394c5074470323c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/94","title":"Remove duplicated line","createdAt":"2022-01-21T05:41:35Z"}
{"state":"Merged","mergedAt":"2022-04-20T01:03:49Z","number":940,"body":"For now, this is opening it up to everyone. We can clean this up later.","mergeCommitSha":"cbaf8097ec8b2001613c3c49c78f43148654e0de","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/940","title":"Add cors","createdAt":"2022-04-20T00:42:18Z"}
{"state":"Merged","mergedAt":"2023-11-23T22:45:36Z","number":9400,"mergeCommitSha":"dec78bb2381feaf8248355acb2a6921e02b86d03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9400","title":"Admin spacing on ML Templates pages","createdAt":"2023-11-23T22:28:33Z"}
{"state":"Merged","mergedAt":"2023-11-23T23:17:50Z","number":9401,"body":"<img width=\"727\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/7b3bd883-9c4e-41d1-8b21-725da5ff85ed\">\r\n","mergeCommitSha":"06dca2697643eaaa8e0d6fb088981ae9bd02ca09","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9401","title":"Add remove repo dialog to GitHub settings","createdAt":"2023-11-23T23:00:10Z"}
{"state":"Merged","mergedAt":"2023-11-24T01:30:06Z","number":9402,"body":"* We were explicitly assuming certain path forms in the SM engine, git, and associated repo management code -- fix this using platform APIs\r\n* Re-focusing VSCode involved a Mac-specific call -- not sure how to do this in Windows, but for now we will skip it when it fails","mergeCommitSha":"934a1c768d22f63628a8f8c72bf390d481f7cbfc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9402","title":"Get VSCode extension working on Windows","createdAt":"2023-11-24T01:15:45Z"}
{"state":"Merged","mergedAt":"2023-11-24T20:25:01Z","number":9403,"body":"The `Manage` button in this UI, for BB, takes you to a UI that only has an installation ID -- but most of the time (AFAICT) the team is constructed and usable, and we actually need to edit the team settings.  This PR changes this UI to query the installation to see if a team is already installed, and if it is, use the EnabledRepoStore via the teamId.\r\n\r\nI wonder if we even need this route and UI, or maybe if the button here should take you to the settings UI instead?\r\n\r\n![CleanShot 2023-11-23 at 19 14 31@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/e02a58c6-8916-406d-b587-c592b0f4f89a)\r\n","mergeCommitSha":"bfc5f9b8f40df8b2a2b1e2493ee1abc2ced001c2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9403","title":"Use correct installation or team ID","createdAt":"2023-11-24T03:16:30Z"}
{"state":"Merged","mergedAt":"2023-11-24T19:09:17Z","number":9404,"body":"Slack message sent to `#machine-learning` channel to alert the team.","mergeCommitSha":"3cd9138487409d1eca3acf4ba515a24dcc1578ec","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9404","title":"Alert when bot fails to answer a question","createdAt":"2023-11-24T17:11:28Z"}
{"state":"Merged","mergedAt":"2023-11-24T19:19:19Z","number":9405,"mergeCommitSha":"b65a374969be7114221c4110c4538892454be17d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9405","title":"Team questions API tests","createdAt":"2023-11-24T19:18:59Z"}
{"state":"Merged","mergedAt":"2023-11-24T22:49:05Z","number":9406,"body":"<img width=\"1240\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/13431372/2a7a20dd-7e4b-473e-affd-bf98003b41ad\">\r\n","mergeCommitSha":"22c5f767f3e57a5f171f43bd7c5f7a9402c435c6","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9406","title":"Team Questions UI","createdAt":"2023-11-24T21:21:22Z"}
{"state":"Merged","mergedAt":"2023-11-24T21:49:02Z","number":9407,"mergeCommitSha":"37f1b44a37c3372c1da2c858425a9919f3163dae","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9407","title":"My questions API","createdAt":"2023-11-24T21:26:43Z"}
{"state":"Merged","mergedAt":"2023-11-24T23:23:09Z","number":9408,"body":"Not good:\r\n```kotlin\r\nthreads.map {\r\n  val team = teamStore.findById(teamId)\r\n  ...\r\n}\r\n```\r\n\r\nMost of the thread listing API runtime was dominated by fetching the same team 50 times.\r\nhttps://ui.honeycomb.io/unblocked/environments/development/datasets/apiservice/result/3Te2QdcM7Ba/trace/8QTTZwMSnzH?fields[]=s_name&fields[]=s_serviceName&span=9f59a8a3d18852c7","mergeCommitSha":"c2f48fb6772efbb55f130ebbe531351d00776a2b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9408","title":"Do not lookup team from DB in a loop","createdAt":"2023-11-24T23:09:12Z"}
{"state":"Merged","mergedAt":"2023-11-25T05:48:14Z","number":9409,"mergeCommitSha":"9c8160ad11572f26365f117e32bf40165d78faff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9409","title":"Improve Q&A retry logic and messaging","createdAt":"2023-11-24T23:38:18Z"}
{"state":"Merged","mergedAt":"2022-04-20T01:00:58Z","number":941,"body":"Looks like it needs a bit more time to initialize the webview.","mergeCommitSha":"3fcdf0b2f51207704bfd91a14dcb30ee9b7c37d9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/941","title":"Fix sidebar initialization","createdAt":"2022-04-20T00:49:20Z"}
{"state":"Merged","mergedAt":"2023-11-25T00:21:33Z","number":9410,"mergeCommitSha":"3558f27f6ce69a4e3694a72c2e5bb1197c43ec91","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9410","title":"No team questions in standard mode","createdAt":"2023-11-24T23:56:19Z"}
{"state":"Merged","mergedAt":"2023-11-25T02:57:13Z","number":9411,"mergeCommitSha":"68ec5b4bb067ec8687a0f4a7e28aee70a85d9a89","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9411","title":"No team questions is no longer mode-dependent","createdAt":"2023-11-25T00:59:06Z"}
{"state":"Merged","mergedAt":"2023-11-25T01:27:57Z","number":9412,"mergeCommitSha":"5780c3b2c136ed28e287c9eabe639f49891de76e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9412","title":"Fixes local doc duplication bug","createdAt":"2023-11-25T01:07:06Z"}
{"state":"Merged","mergedAt":"2023-11-25T06:42:12Z","number":9413,"mergeCommitSha":"4ba3fe6138709d21e6eeba544cdc9af580a7e7ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9413","title":"Maintain loading state while retrying","createdAt":"2023-11-25T06:28:38Z"}
{"state":"Merged","mergedAt":"2023-11-25T17:10:55Z","number":9414,"body":"Not just participation (@-mention or invite) but an actual message response.","mergeCommitSha":"a80119dcf72a44b42363d8c1a68496d69f52311c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9414","title":"My and Team Question list views must have bot answers","createdAt":"2023-11-25T07:23:27Z"}
{"state":"Merged","mergedAt":"2023-11-25T17:16:01Z","number":9415,"mergeCommitSha":"b2a4f252f4a51aaf12d45fc24ec0de0c9cc68e48","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9415","title":"Remove pointless interface","createdAt":"2023-11-25T07:26:39Z"}
{"state":"Merged","mergedAt":"2023-11-25T07:43:28Z","number":9416,"mergeCommitSha":"8022d26decf06a72167a2ee4bbae106de333b596","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9416","title":"Update text while loading","createdAt":"2023-11-25T07:30:52Z"}
{"state":"Merged","mergedAt":"2023-11-25T19:55:36Z","number":9417,"body":"Actually includes any from of bot that we could introduce, like GitHub bot, Linear bot, Jira, etc.\n\nCriteria for listing a thread as a question:\n\n1. the bot must have authored at least one message in thread (meaning that it answered)\n2. my questions: the human must have mentioned the bot in a message in the thread\n2. team questions: the human must NOT have mentioned the bot in a message in the thread\n\nIn the case where many human mentioned the bot in a thread, then the thread will appear in My Questions if the human has asked at least one question, and not appear in Team Questions, even if other humans have asked questions that same thread. In other words, you \"own\" the thread as soon as you ask a bot question in it.","mergeCommitSha":"67c4d82015db9120b7500b9463878b2dcdd627ca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9417","title":"My/Team questions include slackbot questions","createdAt":"2023-11-25T18:57:11Z"}
{"state":"Merged","mergedAt":"2023-11-26T02:12:01Z","number":9418,"body":"Questions are already sorted by the API by created at; resorting is unnecessary since we are not communicating partial sets of threads.\n\nRe-sorting is incompatible with paging, which will be coming next.\n\nNo need to clip, which makes no sense. Just show all the data that was fetched.","mergeCommitSha":"9cee9edebde25ed428e65b15253ded90e43c570f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9418","title":"Do not re-sort and clip question API streams","createdAt":"2023-11-25T18:57:30Z"}
{"state":"Merged","mergedAt":"2023-11-28T03:59:44Z","number":9419,"body":"Interpolated MessageView rendering, for streamed message content.\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/2133518/3ec8c22e-1beb-4f99-b087-c4ea8e662b75\r\n\r\n","mergeCommitSha":"3f22b61a3d3dc3afadf39a8f1cdd505982b929b4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9419","title":"Interpolated message rendering","createdAt":"2023-11-26T05:57:43Z"}
{"state":"Merged","mergedAt":"2022-04-20T01:07:50Z","number":942,"mergeCommitSha":"353a5cf436fe19a5f36384d691dfe1d4fa367bbe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/942","title":"Lint","createdAt":"2022-04-20T01:04:26Z"}
{"state":"Merged","mergedAt":"2023-11-26T06:24:44Z","number":9420,"body":"Will be used temporarily while this feature is under construction","mergeCommitSha":"2d70c3f21009dfca60938f3f8a559b577b626566","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9420","title":"Add feature flag for interpolated message view rendering","createdAt":"2023-11-26T06:01:52Z"}
{"state":"Merged","mergedAt":"2023-11-26T18:22:40Z","number":9421,"mergeCommitSha":"75bac27d1cbeeb55e6e0c73ae6b666315c6620f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9421","title":"Update JIRA icon in admin web to Atlassian's modern icon matching web service","createdAt":"2023-11-26T18:01:31Z"}
{"state":"Merged","mergedAt":"2023-11-27T17:17:00Z","number":9422,"body":"Clean up the message rendering code.  I've extracted this out into a separate PR so the interpolation work is easier to read and evaluate on its own.\r\n\r\n* Instead of using raw functions to render each part of the message, use React JSX.  This is more consistent with the rest of our UI, and allows using hooks and other React features when needed.\r\n* The message rendering parameters (videoMetadata, textOnly, etc) are now stored in a `MessageBlockRendererContext`, which any part of the message rendering can inspect as needed.  This means we don't have to pass all the properties all the way down through the entire rendering tree.\r\n","mergeCommitSha":"9de5a8c62c9bc008f270904a00bcf5c029b8163d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9422","title":"Cleanup message rendering code","createdAt":"2023-11-27T00:38:09Z"}
{"state":"Merged","mergedAt":"2023-11-27T03:14:56Z","number":9423,"mergeCommitSha":"1ce6e158afdb98e8dcb4d49265bed3315c5ccef4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9423","title":"Record a trace of the chain execution steps in the inference example","createdAt":"2023-11-27T02:52:08Z"}
{"state":"Merged","mergedAt":"2023-11-27T05:24:22Z","number":9424,"mergeCommitSha":"e00b00c382d9422526a2fccbbf94e914f9f3bfc8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9424","title":"Add full stack engineer share image to client assets repo","createdAt":"2023-11-27T05:12:28Z"}
{"state":"Closed","mergedAt":null,"number":9425,"body":"Threads where the current user has been notified.\r\n\r\nOrdered by Unread, then last message received at.\r\n\r\nSupports cursor-based keyset pagination to fetch previous and next pages.\r\n\r\nTODO\r\n- [ ] tests\r\n- [ ] pusher","mergeCommitSha":"8ab10d8020bfca76548f7ae05e7b7f8669bd23e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9425","title":"Introduce inbox API","createdAt":"2023-11-27T06:16:56Z"}
{"state":"Merged","mergedAt":"2023-11-27T18:56:59Z","number":9426,"body":"Removal of community manager job posting, and addition of full-stack web engineer.\r\n\r\nNeed to test locally.. putting this up for the first pass of feedback","mergeCommitSha":"567a66637775c9cefb72a1915491acb45eb139fe","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9426","title":"Remove community manager, add full stack engineer","createdAt":"2023-11-27T15:08:43Z"}
{"state":"Merged","mergedAt":"2023-11-30T17:19:25Z","number":9427,"mergeCommitSha":"9984e6da401d081210847d39561d17b696e98912","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9427","title":"Replace topic-based onboarding questions","createdAt":"2023-11-27T15:59:23Z"}
{"state":"Merged","mergedAt":"2023-11-27T18:18:33Z","number":9428,"mergeCommitSha":"a8c096f3cbbb9a91b7bf86b067c3e0f04b148776","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9428","title":"Fix type on slack landing page","createdAt":"2023-11-27T18:13:42Z"}
{"state":"Merged","mergedAt":"2023-11-27T18:34:31Z","number":9429,"mergeCommitSha":"98960127582bda7163cf9f11d371dedad0304ab4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9429","title":"Explict wording for deleting data in privacy policy","createdAt":"2023-11-27T18:20:30Z"}
{"state":"Merged","mergedAt":"2022-04-20T01:09:43Z","number":943,"mergeCommitSha":"07064b77837e6d1b9a97a9fff4a263bc2e65cf59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/943","title":"Fix","createdAt":"2022-04-20T01:09:12Z"}
{"state":"Merged","mergedAt":"2023-11-27T18:48:41Z","number":9430,"mergeCommitSha":"eb4264dd137833382010114468c556096c7106e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9430","title":"Fix lint","createdAt":"2023-11-27T18:48:29Z"}
{"state":"Merged","mergedAt":"2023-11-27T20:23:19Z","number":9432,"mergeCommitSha":"377c2d697e85280a250a1f56699215553b3ea253","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9432","title":"Remove quote","createdAt":"2023-11-27T19:39:07Z"}
{"state":"Merged","mergedAt":"2023-11-27T20:55:17Z","number":9433,"body":"Team Questions depends on there being at least two user accounts in the team.\r\n\r\nWe calculate this in the client, but the problem is that it is blocked on the `getTeamMembersV2` call which is not paginated.\r\n\r\nBetter fix is to paginate `getTeamMembersV2` so that the stream that drives the Team Questions section updates quickly.\r\n\r\nThis change approximates this for now, by assuming that all Full-mode teams have at least two users with an account.\r\n\r\nSee related changes:\r\n - #9410\r\n - #9411","mergeCommitSha":"746dac722fec922d280a29a7cebafb8b7716b36b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9433","title":"Fix Team Questions menu item flicker","createdAt":"2023-11-27T19:44:42Z"}
{"state":"Merged","mergedAt":"2023-11-27T21:13:39Z","number":9434,"mergeCommitSha":"82d5a381da3f7d5796e78bf0930ba4c890962e7f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9434","title":"[SKIP TESTS] Fix JIRA icon typo in adminweb","createdAt":"2023-11-27T21:13:16Z"}
{"state":"Closed","mergedAt":null,"number":9435,"body":"https://github.com/NextChapterSoftware/unblocked/pull/10672\r\n\r\nThings went sideways and I am not good at git. Had to abandon this branch and merge it in a separate PR. Closing this one. ","mergeCommitSha":"f51b5de3ad2218f6e74596f41dca689abaf5dda5","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9435","title":"Add eks cluster stack","createdAt":"2023-11-27T21:44:27Z"}
{"state":"Merged","mergedAt":"2023-11-27T22:50:25Z","number":9436,"mergeCommitSha":"b4a6bdb61e3e51b675dbbcfdbe28ea2767353591","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9436","title":"Include GitHub hidden directory","createdAt":"2023-11-27T22:05:13Z"}
{"state":"Merged","mergedAt":"2023-11-27T22:39:10Z","number":9437,"body":"Largely reverts the work in this PR: https://github.com/NextChapterSoftware/unblocked/pull/9311\r\n\r\nThe change does mostly seem to work, but causes bugs in strict mode.  I'll figure out another way later.","mergeCommitSha":"d0ae761b245c870caa2748480e6345f4b3714a8d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9437","title":"Fix stream bug","createdAt":"2023-11-27T22:26:53Z"}
{"state":"Merged","mergedAt":"2023-11-27T22:50:42Z","number":9438,"mergeCommitSha":"d48e0ddf0c1302951593be31bb0851b8dc3833b3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9438","title":"Clip dashboard thread display to 10 experts","createdAt":"2023-11-27T22:39:08Z"}
{"state":"Merged","mergedAt":"2023-11-28T00:06:45Z","number":9439,"mergeCommitSha":"d5040ce91bb610930d29fc386105784328ba2b25","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9439","title":"Bring back OpenAI response streaming","createdAt":"2023-11-27T23:35:26Z"}
{"state":"Merged","mergedAt":"2022-04-20T04:09:35Z","number":944,"body":"Addresses issue found in https://github.com/NextChapterSoftware/unblocked/pull/939","mergeCommitSha":"a3b1e5e1edb1a34f235de594fe1e7284f8404541","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/944","title":"Merge modified server partial sourcemarks with locally cached marks","createdAt":"2022-04-20T01:17:26Z"}
{"state":"Merged","mergedAt":"2023-11-28T00:23:05Z","number":9440,"mergeCommitSha":"8a1e2c1bcb573a75389441bb4b37f09169a6ffb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9440","title":"Fix thread table in admin when repo is null","createdAt":"2023-11-28T00:22:40Z"}
{"state":"Merged","mergedAt":"2023-11-28T00:45:39Z","number":9441,"mergeCommitSha":"ce85f6d107e4c11cec3584167cabc37809aa7eaa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9441","title":"DataService logging sensitive data","createdAt":"2023-11-28T00:30:26Z"}
{"state":"Merged","mergedAt":"2023-11-28T02:10:57Z","number":9442,"mergeCommitSha":"89617b747b8246781e64f8914da9737d726e0fe0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9442","title":"adds `get_assets_directory` to get the correct path for the assets","createdAt":"2023-11-28T01:13:52Z"}
{"state":"Merged","mergedAt":"2023-11-28T02:34:56Z","number":9443,"mergeCommitSha":"1c639337d4f54e4fda3e4334a0d1701b0f151d5c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9443","title":"[SKIP TESTS] - Add flow logging","createdAt":"2023-11-28T02:34:11Z"}
{"state":"Merged","mergedAt":"2023-11-28T03:07:23Z","number":9444,"mergeCommitSha":"1bcd701c8720c08049dc57636d0327bf5dfb6159","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9444","title":"Update kotlin openai lib to stable release","createdAt":"2023-11-28T02:54:28Z"}
{"state":"Merged","mergedAt":"2023-11-28T03:23:20Z","number":9445,"mergeCommitSha":"06574facd5b0f068f7955d78ef417d52e79025d2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9445","title":"Kotlin flows are the worst","createdAt":"2023-11-28T03:20:17Z"}
{"state":"Merged","mergedAt":"2023-11-29T20:54:36Z","number":9446,"mergeCommitSha":"4517d4a27025c2600778dcbdbc86b51f7d7013b0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9446","title":"Use merged config so we get the benefit of individual streaming","createdAt":"2023-11-28T04:08:01Z"}
{"state":"Merged","mergedAt":"2023-11-28T06:01:53Z","number":9447,"mergeCommitSha":"2c19ab0506c3d8305a064038ef56419eb7c85c04","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9447","title":"Remove aggressive flow logging for completions","createdAt":"2023-11-28T05:48:11Z"}
{"state":"Merged","mergedAt":"2023-11-28T06:51:29Z","number":9448,"body":"https://chapter2global.slack.com/archives/C02US6PHTHR/p1701149979551079?thread_ts=1701145535.826419&cid=C02US6PHTHR\r\n\r\n<img width=\"677\" alt=\"Screenshot 2023-11-27 at 23 18 03\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/a2743976-c176-4fed-88c9-2567cdabe621\">\r\n\r\n","mergeCommitSha":"23af41cbf9c01872ea069d75796a33323b13928f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9448","title":"Remove duplicate references from Slack bot answers","createdAt":"2023-11-28T06:38:07Z"}
{"state":"Merged","mergedAt":"2023-11-28T07:27:18Z","number":9449,"body":"…ackages for child packages\r\n\r\nthere maybe a way to make this work using the package feature, but i want to get this out before we leak more json files","mergeCommitSha":"637159889732f396e46c5d5198661f9ff4726995","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9449","title":"It appears the poetry does not install included directories to site_p…","createdAt":"2023-11-28T07:03:25Z"}
{"state":"Merged","mergedAt":"2022-04-20T21:30:43Z","number":945,"body":"Adds logic to ingest comments from newly merged PRs as well as updating already ingested PR comments.","mergeCommitSha":"2a737ac743d30b2c132f3b66450b08bcb04e0b32","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/945","title":"Ingest recently merged PRs and updated comments for previously merged PRs","createdAt":"2022-04-20T15:23:08Z"}
{"state":"Merged","mergedAt":"2023-11-28T07:26:50Z","number":9450,"body":"changes:\r\n- bold correctly rendered\r\n- links correctly rendered\r\n\r\n---\r\n\r\n### Bold issue\r\n<img width=\"559\" alt=\"Screenshot 2023-11-27 at 23 19 06\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/999c8c5b-4853-4915-b746-b7cac52424f9\">\r\n\r\n---\r\n\r\n### Link issue\r\n<img width=\"688\" alt=\"Screenshot 2023-11-27 at 23 20 07\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/e9bb6eec-a9f4-4848-a20f-279394dc33e8\">\r\n","mergeCommitSha":"182d1e3f17a49617baa0f18e20adde14d1d5005c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9450","title":"Fix formatting links and bold text in Slack bot messages","createdAt":"2023-11-28T07:12:07Z"}
{"state":"Merged","mergedAt":"2023-11-28T17:26:00Z","number":9451,"mergeCommitSha":"34e156f8de41bf36e8fb1c301b1e1788c8233d85","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9451","title":"Add Linear logging","createdAt":"2023-11-28T17:03:14Z"}
{"state":"Merged","mergedAt":"2023-11-28T18:49:01Z","number":9452,"mergeCommitSha":"e55db670d1ff12d9e00904fea57f77021af52ada","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9452","title":"Add more linear logging","createdAt":"2023-11-28T18:37:03Z"}
{"state":"Merged","mergedAt":"2023-11-28T18:57:22Z","number":9453,"body":"1. We’ve been on 1.8.0 tree-sitter for poetry for a while.\r\n2. We no longer need to install torch manually as it’s been fixed in 2.1.1\r\n\r\nhttps://github.com/pytorch/pytorch/issues/100974\r\n","mergeCommitSha":"3ec74da7831873f77bb526bad95744d493fc950f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9453","title":"Fix poetry dependencies","createdAt":"2023-11-28T18:47:38Z"}
{"state":"Merged","mergedAt":"2023-11-28T19:25:26Z","number":9454,"mergeCommitSha":"befee75d9bb918689e3c340fe2f5d54d515e4e56","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9454","title":"Log should be info not error","createdAt":"2023-11-28T19:25:06Z"}
{"state":"Merged","mergedAt":"2023-11-28T20:05:44Z","number":9455,"mergeCommitSha":"b8eff665339fdefcbd8733eb6dbe68ff048ddc73","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9455","title":"Remove dead s3 buckets","createdAt":"2023-11-28T19:59:30Z"}
{"state":"Merged","mergedAt":"2023-11-28T20:22:52Z","number":9456,"mergeCommitSha":"c282c67eb607d9013ea5ca9192208856590c15fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9456","title":"More linear logging","createdAt":"2023-11-28T20:11:30Z"}
{"state":"Merged","mergedAt":"2023-11-28T20:43:02Z","number":9457,"mergeCommitSha":"6e563323645f082249ea93026aeab24f38941b9e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9457","title":"Re enable pusher and logs rate limit","createdAt":"2023-11-28T20:42:13Z"}
{"state":"Merged","mergedAt":"2023-11-28T22:41:13Z","number":9458,"body":"This ensures that we check the pending state first, before we check the integrations state.\r\n\r\nAlso, fix the TeamStore so that it returns the latest-fetched value (`TeamStore.teams`) correctly.  This never worked before!  We no longer actively use `ValueCacheStream`, so I replaced it with a `ValueStream`.  I'll completely remove `ValueCacheStream` in a subsequent PR.","mergeCommitSha":"d8e8e661235191e9a303ce3db21d37215838d665","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9458","title":"Move pending check into OnboardingOutlet","createdAt":"2023-11-28T20:46:37Z"}
{"state":"Merged","mergedAt":"2023-11-28T21:56:45Z","number":9459,"mergeCommitSha":"f354b2ad336ee49f694d6fc4a3b70aea75432f7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9459","title":"Torch 2.1.1 installation required for cuda","createdAt":"2023-11-28T21:56:25Z"}
{"state":"Merged","mergedAt":"2022-04-20T17:58:01Z","number":946,"body":"Seems stable enough now. People can still turn it off in settings if something goes wrong.\r\n\r\n<img width=\"732\" alt=\"Screen Shot 2022-04-20 at 09 25 08\" src=\"https://user-images.githubusercontent.com/1798345/164278016-26033645-3f9c-45c9-a30f-7405099d23d0.png\">","mergeCommitSha":"1f4576d1aa647e676689056965f3e5490b4f9d44","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/946","title":"Enable SourceMark engine by default","createdAt":"2022-04-20T16:09:14Z"}
{"state":"Merged","mergedAt":"2023-11-28T23:12:26Z","number":9460,"mergeCommitSha":"5d343d374d7190ed440694eaa120b443173f099b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9460","title":"Render repo name in retrieved document tables","createdAt":"2023-11-28T22:50:18Z"}
{"state":"Merged","mergedAt":"2023-11-28T23:08:27Z","number":9461,"body":"This reverts commit d8e8e661235191e9a303ce3db21d37215838d665.","mergeCommitSha":"36bc173d95be549bebe85c0c434420674e62423a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9461","title":"Revert \"Move pending check into OnboardingOutlet (#9458)\"","createdAt":"2023-11-28T23:08:18Z"}
{"state":"Merged","mergedAt":"2023-11-30T17:19:36Z","number":9462,"body":"The token refresh token would always first check to see if the token was expired before refreshing it. But in the case where we receive a 401 back from the service, we should just refresh it immediately so we don't get into death spirals where we retry continuously. ","mergeCommitSha":"c12d6a350460f4dbafd92985de05145a277bac20","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9462","title":"Force token refresh on 401","createdAt":"2023-11-28T23:23:45Z"}
{"state":"Merged","mergedAt":"2023-11-28T23:45:29Z","number":9463,"body":"Sometimes I hate computers....","mergeCommitSha":"403aab2a89e3e343626818bc8d30931e02d598ad","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9463","title":"fix api log rate limit rule","createdAt":"2023-11-28T23:44:44Z"}
{"state":"Merged","mergedAt":"2023-11-29T00:07:44Z","number":9464,"mergeCommitSha":"3935af1f98d5c84ec7746e44c00558c0273b642b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9464","title":"Refactor LinearIngestionService","createdAt":"2023-11-28T23:44:52Z"}
{"state":"Merged","mergedAt":"2023-11-29T00:07:40Z","number":9465,"mergeCommitSha":"6f6aacc82d5354efe3ddcd103cd1e8d908d6d654","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9465","title":"AWS gpu does not support 2.1.1","createdAt":"2023-11-29T00:07:18Z"}
{"state":"Merged","mergedAt":"2023-11-29T00:31:05Z","number":9466,"body":"Re-try this PR with bug fixes: https://github.com/NextChapterSoftware/unblocked/pull/9458","mergeCommitSha":"e5214bbac21f795fa0844574964f04ae95ef1f3e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9466","title":"Move pending check into OnboardingOutlet","createdAt":"2023-11-29T00:16:58Z"}
{"state":"Merged","mergedAt":"2023-11-29T01:17:49Z","number":9467,"mergeCommitSha":"e6a4d48bd99dc1f7403645a03ad700d885723332","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9467","title":"Fix logging issues","createdAt":"2023-11-29T01:05:49Z"}
{"state":"Merged","mergedAt":"2023-11-29T03:04:50Z","number":9468,"mergeCommitSha":"dd98f770b591c2860741f8e9a7f33422abe0a36f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9468","title":"Revert","createdAt":"2023-11-29T03:04:42Z"}
{"state":"Merged","mergedAt":"2023-11-29T03:57:18Z","number":9469,"mergeCommitSha":"3876f2a57da0799479b8fa65fc63cc921a7cb1fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9469","title":"Only show removal dialog when necessary","createdAt":"2023-11-29T03:45:01Z"}
{"state":"Merged","mergedAt":"2022-04-21T23:06:59Z","number":947,"body":"* Add participant ids to the CreateThreadRequest body\r\n* Insert ThreadParticipantModels on thread creation\r\n* Create new ThreadUnreads for each participant team member \r\n\r\nNOTE: Right now we only pass through team member ids -- next bit of work is handling what we do with users that have not authed/signed up with Unblocked yet (right now we do nothing)","mergeCommitSha":"689fd0fcb0852d33f8d0808a1ac162ce29ea0f13","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/947","title":"Funnel through participants on thread creation","createdAt":"2022-04-20T16:30:36Z"}
{"state":"Merged","mergedAt":"2023-11-29T16:42:43Z","number":9470,"mergeCommitSha":"b8147ed990453478d8ff43d6ece42e648fb4cace","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9470","title":"Log out the number issues retrieved per page","createdAt":"2023-11-29T05:36:51Z"}
{"state":"Merged","mergedAt":"2023-11-29T16:58:49Z","number":9471,"body":"Calculates which repos a team member focuses their attention.","mergeCommitSha":"4fb91e0348106794819d4e8f185ee7e0a6b2b16d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9471","title":"Introduce team member repo focus","createdAt":"2023-11-29T07:36:10Z"}
{"state":"Merged","mergedAt":"2023-11-29T18:33:34Z","number":9472,"mergeCommitSha":"5da1e61f45c42986e95463bf27d49bc02087f910","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9472","title":"Add flag for Inbox and My Questions","createdAt":"2023-11-29T18:08:35Z"}
{"state":"Merged","mergedAt":"2023-11-29T18:39:19Z","number":9473,"body":"Widens the net of threads used to calculate focus, for example, to include Slack threads that have a repo association.","mergeCommitSha":"5d3dbcbd0ae28c10ac26450738ee855756af0369","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9473","title":"Repo focus joins on associated members","createdAt":"2023-11-29T18:10:44Z"}
{"state":"Merged","mergedAt":"2023-11-29T20:48:22Z","number":9474,"mergeCommitSha":"94c8250ac5cf2d8d11f4c43ac41fe77c2c13262c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9474","title":"Add is streaming flag to message object","createdAt":"2023-11-29T19:07:31Z"}
{"state":"Merged","mergedAt":"2023-11-29T20:07:20Z","number":9475,"mergeCommitSha":"8c41544d904461329a993cf41047f995862365b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9475","title":"Disable two azure endpoints from round robin","createdAt":"2023-11-29T20:01:03Z"}
{"state":"Merged","mergedAt":"2023-11-29T23:02:01Z","number":9476,"mergeCommitSha":"72f035cb19ef905b190e1847a406349098aafbb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9476","title":"Implements the isStreaming flag and its uses","createdAt":"2023-11-29T20:51:03Z"}
{"state":"Merged","mergedAt":"2023-12-01T01:03:46Z","number":9477,"mergeCommitSha":"18d4b549252b82c88d855b3812449de7d25bd569","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9477","title":"Cleanup column usage for dataframe","createdAt":"2023-11-29T21:09:54Z"}
{"state":"Merged","mergedAt":"2023-11-30T00:50:24Z","number":9478,"body":"* Add My Questions route and threads to dashboard -- this is hidden under the `FeatureNewInbox` feature flag \r\n* Add support for infinite scroll pagination to the questions routes \r\n    * This involved a fairly sizable restructuring of some of the code in the ThreadListStore and warrants closer scrutiny to the code changes \r\n* Fix bug with dashboard search view routing ","mergeCommitSha":"d62869ecae2992fc619ba9b99b540f110636c71c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9478","title":"My Questions UI/Pagination","createdAt":"2023-11-29T22:37:38Z"}
{"state":"Merged","mergedAt":"2023-12-04T20:01:08Z","number":9479,"body":"Will be used for embedding websites","mergeCommitSha":"bbce0ef550cb9977a36a5509911a7031ebad2d46","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9479","title":"Add Provider.Web","createdAt":"2023-11-30T00:50:17Z"}
{"state":"Merged","mergedAt":"2022-04-20T17:08:58Z","number":948,"body":"I forgot to add the build step (which bundles NPM modules) to deployment jobs. We need this for both test and deploy jobs but we were only running this in the test jobs!","mergeCommitSha":"a3bca7b82c0d3c75bfedad3a13004f6c5d1bb317","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/948","title":"forgot to add build step before deployments","createdAt":"2022-04-20T16:57:50Z"}
{"state":"Merged","mergedAt":"2023-11-30T05:41:32Z","number":9480,"body":"Bug in the keys of the sections. Also updates the unblocked nav icons.","mergeCommitSha":"f626e8f3b2a611733b15289b71d6e1b523a719d3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9480","title":"Fix scrolling behaviour","createdAt":"2023-11-30T01:44:14Z"}
{"state":"Merged","mergedAt":"2023-11-30T05:05:20Z","number":9481,"body":"* Respect `isStreaming` flag, so we show the initial \"Loading\" UI, and move into streaming mode after\r\n* Vary the interpolation velocity depending on how many characters we have left to render\r\n* Fix a bunch of bugs\r\n* Interpolate link nodes\r\n* Add a bunch of logging so I can track down any remaining bugs","mergeCommitSha":"fa89ab608de506bd7b4791a4f07aa82be9f7b1b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9481","title":"Improved message rendering interpolation","createdAt":"2023-11-30T04:41:39Z"}
{"state":"Merged","mergedAt":"2023-11-30T06:20:54Z","number":9482,"body":"We invoke repo summary generation whenever we: select, deselect, connect, disconnect repos.\n\nHowever, the problem was that we never generated repo summaries during onboarding.\n\nBit of a PITA to keep this updated in so many places, and the same will be true for all summaries.\n\nThere is a better way, at least in this case:\nhttps://chapter2global.slack.com/archives/C045VGYML95/p1701320752569289?thread_ts=1700795800.131489&cid=C045VGYML95","mergeCommitSha":"678fd73b7c55ae3113ec0cd276f97719e9412e06","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9482","title":"Invoke repo summary generation from SCM API","createdAt":"2023-11-30T05:53:13Z"}
{"state":"Merged","mergedAt":"2023-11-30T06:50:45Z","number":9483,"body":"The bug resulted in content sometimes being clipped off the end of the document","mergeCommitSha":"e5ece98e627546f0fb4b2e2673a6c6895ad28bc9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9483","title":"Fix another interpolation bug","createdAt":"2023-11-30T06:38:52Z"}
{"state":"Closed","mergedAt":null,"number":9484,"mergeCommitSha":"e94ec43c928e6baf608c6d1ff58fa48a051bebcb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9484","title":"Don't render feedback and suggestions while streaming","createdAt":"2023-11-30T07:09:07Z"}
{"state":"Merged","mergedAt":"2023-11-30T19:48:57Z","number":9485,"mergeCommitSha":"cd7792be726846aff20e0b2c90dd1e491b2438c3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9485","title":"Add IngestionEvent.WebIngestionEvent","createdAt":"2023-11-30T19:35:14Z"}
{"state":"Merged","mergedAt":"2023-11-30T21:38:47Z","number":9486,"body":"Add incognito mode button into IDE explorer panel.  Fix some bugs so the incognito mode banner shows up correctly (and on top of the header, now).\r\n\r\n![CleanShot 2023-11-30 at 12 35 18@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/51373ce5-a064-45aa-8bca-e25fc161b2c3)\r\n![CleanShot 2023-11-30 at 12 35 22@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/afa2b871-f114-41b1-a4e9-2248587d03fd)\r\n![CleanShot 2023-11-30 at 12 35 29@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/9be01374-09d0-4e37-aa1a-933062f0d1a6)\r\n![CleanShot 2023-11-30 at 12 35 33@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/116968a0-22dd-4491-9e2b-4ecd17c03a88)\r\n![CleanShot 2023-11-30 at 12 37 16@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/75d97412-4397-4e9c-a6ef-0280c9715378)\r\n![CleanShot 2023-11-30 at 12 37 18@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/b62b541c-ae3d-4a53-b254-565a642ed6ea)\r\n![CleanShot 2023-11-30 at 12 37 22@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/d7189921-d5ec-4485-837c-acb28f5c9e02)\r\n![CleanShot 2023-11-30 at 12 37 26@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/a87a16a7-18b3-4af6-9ac6-beb867dc7d39)\r\n","mergeCommitSha":"c0f73329a59cb76f2eaac8332ba21e1d6c7f24ac","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9486","title":"IDE incognito mode","createdAt":"2023-11-30T20:39:48Z"}
{"state":"Merged","mergedAt":"2023-11-30T21:23:04Z","number":9487,"mergeCommitSha":"bada01841e6e4eac9f89cb19d50d8dbceea7a3b7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9487","title":"Round robin flow retries","createdAt":"2023-11-30T20:55:53Z"}
{"state":"Merged","mergedAt":"2023-11-30T21:22:15Z","number":9488,"mergeCommitSha":"ea3da3078d58cafcd8dff63b36dfaaeb3399c923","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9488","title":"Fix one small potential interpolation glitch","createdAt":"2023-11-30T21:08:45Z"}
{"state":"Merged","mergedAt":"2023-12-01T01:21:26Z","number":9489,"body":"Paragraphs inside list elements are kinda weird anyways... not sure if this breaks anything, will test","mergeCommitSha":"13d1124a8c9aabae8e35672d6901beefd594dba8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9489","title":"Fix interpolated list rendering in safari","createdAt":"2023-11-30T22:11:33Z"}
{"state":"Merged","mergedAt":"2022-04-20T17:54:38Z","number":949,"body":"<img width=\"211\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/164292174-0fbf35b9-6a20-44e8-b0a7-fcfaacc93ee5.png\">\r\n","mergeCommitSha":"d9dc5c6c125bd9e20b0c2446251e9dad74a945a1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/949","title":"Fix sidebar overlay","createdAt":"2022-04-20T17:49:01Z"}
{"state":"Merged","mergedAt":"2023-11-30T23:46:10Z","number":9490,"body":"Motivation:\nhttps://chapter2global.slack.com/archives/C0678GN0H6W/p1701382676702009","mergeCommitSha":"d02b3efa351506d78be84d4c4e70e89b4ddb0ced","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9490","title":"[BREAKS API ON MAIN] Remove getQuestionsForMe","createdAt":"2023-11-30T22:23:27Z"}
{"state":"Merged","mergedAt":"2023-11-30T22:40:18Z","number":9491,"body":"When we crawl a site, we visit a page and inspect it for links to other pages to ingest. If those other pages link back to previously ingested pages, this could cause us to revisit previously ingested page. This PR adds a cache to help us skip past over previously ingested pages.\r\n\r\nThe cache has a TTL for each entry so that we can reingest a page after a set time.","mergeCommitSha":"2bdd227b00de4f931dbf697c982e366d1ffb59e0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9491","title":"[Web Site Ingestion] Add a cache to handle circular links","createdAt":"2023-11-30T22:23:58Z"}
{"state":"Closed","mergedAt":null,"number":9492,"body":"Motivation:\nhttps://chapter2global.slack.com/archives/C0678GN0H6W/p1701382676702009","mergeCommitSha":"c087efe20bf6c26eacbc560c90f7ba6a18741d84","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9492","title":"Deprecate FeatureNewInbox feature flag","createdAt":"2023-11-30T22:25:25Z"}
{"state":"Merged","mergedAt":"2023-12-01T21:27:26Z","number":9493,"body":"Default is all providers.","mergeCommitSha":"480f2b5c818e4417a7e53c13687c2616c653f909","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9493","title":"Add provider filter to getThreadsForMe","createdAt":"2023-11-30T22:31:24Z"}
{"state":"Merged","mergedAt":"2023-11-30T23:18:12Z","number":9494,"mergeCommitSha":"406a088f674cde508f111fd6aca30ff2ff0b966d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9494","title":"Remove My Questions from UI","createdAt":"2023-11-30T23:01:24Z"}
{"state":"Merged","mergedAt":"2023-12-01T00:27:03Z","number":9495,"mergeCommitSha":"3812b8d2ba0b1c17a2fcba80e70220469cff6454","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9495","title":"[Web Site Ingestion] Emit embedding event in the WebIngestionEventHandler","createdAt":"2023-11-30T23:48:29Z"}
{"state":"Merged","mergedAt":"2023-12-01T00:34:09Z","number":9496,"body":"Turns out, depending on your environment, timer values could be numeric, so doing a falsy check here means you could clear the wrong timer.  The timer APIs are old and poor.\r\n\r\nThe theory is that we were, in fact, clearing a random `PromiseUtils.wait` timer on first render, which broke loads of other things.\r\n\r\nThis is different per-browser (the return value is basically opaque), which explains why this behaviour was only seen in node environments, not in browser environments.","mergeCommitSha":"d26177fbb86018672af64ff889e7bfe7f60ce867","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9496","title":"Fix bug that caused interpolation to occasionally go slooooow","createdAt":"2023-12-01T00:32:33Z"}
{"state":"Merged","mergedAt":"2023-12-01T01:32:55Z","number":9497,"mergeCommitSha":"844dc67ed4db2a31f7a11fb1e0616d639ce083da","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9497","title":"Update dev team site ingestion config","createdAt":"2023-12-01T01:13:48Z"}
{"state":"Merged","mergedAt":"2023-12-01T02:53:39Z","number":9498,"body":"So gross","mergeCommitSha":"00d3fd510bfe0761adbb70dd48b05fa42eb83c99","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9498","title":"More bad reference cleanup","createdAt":"2023-12-01T02:41:01Z"}
{"state":"Merged","mergedAt":"2023-12-01T05:41:15Z","number":9499,"body":"An off-by-one error that could occasionally cause rendering to render a bit chunky.","mergeCommitSha":"43fa9881b72a17f07c3d17ef3f24f3d228fa5a6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9499","title":"Fix interpolated rendering chunking bug","createdAt":"2023-12-01T05:29:42Z"}
{"state":"Merged","mergedAt":"2022-01-21T21:09:47Z","number":95,"body":"This pr does several things:\r\n1. Associates a serviceAccount with our services deployed via helm.\r\n2. Fixes liveness probes for services deployed via helml.\r\n3. Add database config that allows us to authenticate db access against sts. \r\n\r\nTesting:\r\n1. Deployed here https://github.com/Chapter2Inc/codeswell/actions/runs/**********\r\n2. Validated pod is running and connected to db!\r\n```\r\n20220121T203954Z\r\n20220121/us-west-2/rds-db/aws4_request\r\n549ac458bbbd793998d92a35faebe39f6b858847c10332b1f22bf6b3fc8089d1\r\n2022-01-21 20:39:55.035 [main] DEBUG Exposed - CREATE EXTENSION IF NOT EXISTS pg_trgm\r\n2022-01-21 20:39:55.169 [main] TRACE s.a.awssdk.auth.signer.Aws4Signer - AWS4 Canonical Request: GET\r\n/\r\nAction=connect&DBUser=postgres&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAR4KJXTWR4JCJFZEE%2F20220121%2Fus-west-2%2Frds-db%2Faws4_request&X-Amz-Date=20220121T203955Z&X-Amz-Expires=900&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEA0aCXVzLXdlc3QtMiJIMEYCIQCDGiOr09aOScd3Y05Y8DWMN0DFlJ%2BGIcwOIrkvBoKjlgIhAIsxrP8k%2BpdrtusjJPyg11K%2FE012Qc67uuyU9BwBxijmKoAFCDYQABoMMTI5NTQwNTI5NTcxIgw3ytN5Ch%2FWRQ4qWQQq3QTgsSBZqvDEJVzw9ascxRz4riJeb3gSU3RyKK5oZA%2FndByaycnHIPGfR4kXpGVnBu8aaunJQEFRKimK%2FDG4mLL83FQIIslh%2B3jKC5OAKBl2pf%2Fc3XLLkTx8sWY9toqxhF9MKdnh5a8Sh732JHvfOlslw274SDpOIdFJK3zON48dlZk2MD8n6RZ94p6D3QJXa5k1Xdz74UK%2BVr90s9HL7eXiqCBc9G5dDTxCzzcwKQ0ta9Zu0kQvRZ2IaDk7vK6aGaBPxrN87LJP%2BhfBrW9%2F696iDumUmq7CISJwjqKgR1rH%2FcyX8a3daOgstIQoC6limT%2FISGYzThSMi2I4mmMLi5bglwI3jrk1kv3NLZbHU5JN88PwbL%2B2Vfzi87sw8ELCVw7LHnNnpST4AovjFIRW3lIYprGU2p41V2JmF6rKXygfo6CotztMqPtQfSR4tnL77jpG%2FS0YwHYvjSaNtglzG6QQ4M%2Fyc0Elt5QdDZdjKPeZE2Hr96uasl8Mt%2FFtkhOWBnUXm1GJLfh12YJn9wTw%2B78m9wsB8jP6%2B7PQqTFoDhSo9CWxg42WOAJGA2UWHtvQXMQDAs8C%2B6iPM4sBAbf62kUMSHGLEvX0D2RWffJNCe8wll5whNtMyk3eYPYVVe1XhN6e3kde49dMkhYAsliyotHrH9%2FxLIz8EhCN0dhWKR5z73hJyJ5W2yDtw%2BVdJUp9ws1EEJRSmzgKdUUkTmdWn3zM6hVOr2f16X0KbndBu1G71j8v0rePH5gvgqWmmjdLMzt2Uv79KgIM6ELhmD3Yc3YdJBvvkm3t8GF%2FfPCxuDCatKyPBjqZAVsaW3TbfjAHLKHJSTsyjt7my4NB7m95%2FR2tdFRStjPpCt2cuDRSslchN75QehXTiQKiq1IGEMs6A4U8XOZ728kY%2BpIVDbEt7R9w0IGJXhzAzqjW6YEyPcwIrR1jiQy4lx42i55i78t%2BSA7X%2BwdG3iNt3AVw8x6A6uNpM3vqSrkCWJeGOOJ8njbT5iZzhxGxYKvfNERDAHOwQQ%3D%3D&X-Amz-SignedHeaders=host\r\nhost:databasestack-mainapp32bc6243-1vvgj496lx136.cluster-csga8shziqqm.us-west-2.rds.amazonaws.com:5432\r\n\r\nhost\r\ne3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\r\n2022-01-21 20:39:55.170 [main] DEBUG s.a.awssdk.auth.signer.Aws4Signer - AWS4 String to sign: AWS4-HMAC-SHA256\r\n20220121T203955Z\r\n20220121/us-west-2/rds-db/aws4_request\r\n5275846048d8619af3c28e7037330122d210cd002954fd8d7287b9020eb05731\r\n2022-01-21 20:39:55.267 [main] DEBUG Exposed - SELECT busy.busy FROM busy FOR UPDATE\r\n2022-01-21 20:39:55.280 [main] DEBUG Exposed - INSERT INTO busy (busy) VALUES (TRUE)\r\n2022-01-21 20:39:55.291 [main] INFO  Exposed - Preparing create tables statements took 10ms\r\n2022-01-21 20:39:55.297 [main] INFO  Exposed - Executing create tables statements took 5ms\r\n2022-01-21 20:39:55.316 [main] INFO  Exposed - Extracting table columns took 19ms\r\n2022-01-21 20:39:55.373 [main] INFO  Exposed - Extracting column constraints took 56ms\r\n2022-01-21 20:39:55.373 [main] INFO  Exposed - Preparing alter table statements took 76ms\r\n2022-01-21 20:39:55.376 [main] INFO  Exposed - Executing alter table statements took 3ms\r\n2022-01-21 20:39:55.419 [main] INFO  Exposed - Checking mapping consistence took 43ms\r\n2022-01-21 20:39:55.423 [main] DEBUG Exposed - DELETE FROM busy\r\n2022-01-21 20:39:55.521 [main] INFO  ktor.application - Autoreload is disabled because the development mode is off.\r\n2022-01-21 20:39:56.014 [main] INFO  ktor.application - Application auto-reloaded in 0.493 seconds.\r\n2022-01-21 20:39:56.015 [main] DEBUG ktor.application - Application started: io.ktor.server.application.Application@7aac8884\r\n2022-01-21 20:39:56.210 [DefaultDispatcher-worker-2] INFO  ktor.application - Responding at http://0.0.0.0:8080\r\n2022-01-21 20:41:53.905 [idle-connection-reaper] DEBUG s.a.a.h.a.internal.net.SdkSslSocket - shutting down output of sts.us-west-2.amazonaws.com/54.240.252.193:443\r\n2022-01-21 20:41:53.906 [idle-connection-reaper] DEBUG s.a.a.h.a.internal.net.SdkSslSocket - closing sts.us-west-2.amazonaws.com/54.240.252.193:443\r\n2022-01-21 20:46:21.325 [eventLoopGroupProxy-4-1] DEBUG ktor.application - Path: /, Status: 404 Not Found, HTTP method: GET, User agent: curl/7.68.0, Parameters: []\r\n2022-01-21 20:46:24.173 [eventLoopGroupProxy-4-2] DEBUG ktor.application - Path: /, Status: 404 Not Found, HTTP method: GET, User agent: curl/7.68.0, Parameters: []\r\n\r\n\r\n```","mergeCommitSha":"928055dacf9fca28dda474760304f9db59e11a5e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/95","title":"Fix DB access in development environment","createdAt":"2022-01-21T05:42:12Z"}
{"state":"Merged","mergedAt":"2022-04-21T18:10:55Z","number":950,"body":"* Rename and move component into the shared directory so it can be used in multiple clients \r\n* Add stories for testing\r\n* No logic change, just moving the location of the files","mergeCommitSha":"b405df6c420945544a0137054ae3d36a3a505ad7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/950","title":"Refactor out the ContributorsList for reuse","createdAt":"2022-04-20T19:28:45Z"}
{"state":"Merged","mergedAt":"2023-12-01T16:44:43Z","number":9500,"mergeCommitSha":"4ce1a3af70c848163a0b16fcda9fa9e0a64aeb30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9500","title":"Only log reference cleanup when there are actually things to clean up","createdAt":"2023-12-01T16:30:43Z"}
{"state":"Merged","mergedAt":"2023-12-01T19:31:19Z","number":9501,"mergeCommitSha":"ca49b5a9aea2498afd6466fc70f0f19e9fa83949","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9501","title":"Dont enqueue a web ingestion event if already handled","createdAt":"2023-12-01T18:58:12Z"}
{"state":"Closed","mergedAt":null,"number":9502,"body":"Changes:\n- rely only on the presence or otherwise of the cursor, and not on the item count\n- removed the concept of fetching one more item than is displayed, which makes no sense\n- lower page size to 25 to improce performance. each page will load faster","mergeCommitSha":"98ed7e218cd77cb89f53ec8b32e9444b4b3628a3","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9502","title":"Fetch 25 items per page and fix next page pagination to use cursor","createdAt":"2023-12-01T19:02:57Z"}
{"state":"Merged","mergedAt":"2023-12-01T23:31:28Z","number":9503,"body":"Should fix https://chapter2global.slack.com/archives/C05D7H25PV5/p1701458328621889\r\n","mergeCommitSha":"382c1c0117fb771d95dd6d4331f65879d098d554","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9503","title":"Include repo ID in query context for repo-focussed sample question","createdAt":"2023-12-01T21:21:28Z"}
{"state":"Merged","mergedAt":"2023-12-01T22:07:07Z","number":9504,"mergeCommitSha":"1e34886a95af076718eabe97a46766ab37a3824a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9504","title":"Do not allow installation of slack workspaces if owned by another team","createdAt":"2023-12-01T21:37:54Z"}
{"state":"Merged","mergedAt":"2023-12-02T00:04:00Z","number":9505,"body":"- addresses duplicate functionality\n- makes the result of semantic search linkable","mergeCommitSha":"68b9e3bacb26e9e404e509cd204c88039089a2bf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9505","title":"Semantic search open infererence page","createdAt":"2023-12-01T23:54:47Z"}
{"state":"Merged","mergedAt":"2023-12-04T17:44:22Z","number":9506,"mergeCommitSha":"270d4a274acbb4ffb37e59f230987dde0c086b74","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9506","title":"Replace sample question","createdAt":"2023-12-02T00:23:51Z"}
{"state":"Merged","mergedAt":"2023-12-06T05:04:52Z","number":9507,"body":"<img width=\"789\" alt=\"image\" src=\"https://github.com/NextChapterSoftware/unblocked/assets/1798345/e5a96396-12c3-4e2c-af3c-a4a10fda89dc\">\r\n","mergeCommitSha":"cc58c0f451c8859370eb7ec5639a3e42fe4ef6cf","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9507","title":"Vend thread links","createdAt":"2023-12-02T00:40:41Z"}
{"state":"Closed","mergedAt":null,"number":9508,"mergeCommitSha":"3012efc3aa692903be7d8069169357d8b4dffde7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9508","title":"[DO NOT MERGE] - testing FA token in actions","createdAt":"2023-12-02T00:44:24Z"}
{"state":"Merged","mergedAt":"2023-12-02T02:36:32Z","number":9509,"mergeCommitSha":"739350bde5b31f01064b93ed60fdf86321386520","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9509","title":"Increaese token limit","createdAt":"2023-12-02T02:36:22Z"}
{"state":"Merged","mergedAt":"2022-04-21T17:28:42Z","number":951,"body":"This pr enables using asset service to retrieve s3 presigned urls for uploads/downloads.\r\n\r\nKeep in mind several things:\r\n1. Webview tells extension to call AssetService for presigned urls.\r\n2. We use a very stupid request/response model to update the webview when urls are available for upload to s3.\r\n3. The Webview then does an asychronous fetch request to upload to s3. **Only** the Webview can do this as the underlying file stream is local to the Webview instance. (File Paths are not exposed in most browsers for security reasons).\r\n\r\nOther notes:\r\n1. We transparently switch the image src from local data uris to s3 urls when the asset has been officially uploaded.\r\n2. No idea why we moved away from Image Blocks, but there's no reason we would consider using inline images rather than image blocks for representing this stuff.\r\n\r\nTesting:\r\n1. Validated s3 urls were correctly being retrieved and uploads of images was working.\r\n![CleanShot 2022-04-20 at 12 28 48](https://user-images.githubusercontent.com/3806658/164309425-33e23e6e-de7c-400b-8cac-79ecc6cc4e93.gif)\r\n\r\n","mergeCommitSha":"9082347e92945ac1f9e49fb1fcbd423e2b5a710f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/951","title":"Create and populate asset urls in message","createdAt":"2022-04-20T19:33:10Z"}
{"state":"Merged","mergedAt":"2023-12-02T06:19:23Z","number":9510,"mergeCommitSha":"ad84c5da0ffc04819de4a661c5d403c77642a1e7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9510","title":"Log failed writes in installer","createdAt":"2023-12-02T06:04:07Z"}
{"state":"Merged","mergedAt":"2023-12-02T06:27:49Z","number":9511,"mergeCommitSha":"f39b14a4789908452b02b4ce5015ff5768b08ddb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9511","title":"Small bump for new build","createdAt":"2023-12-02T06:27:18Z"}
{"state":"Merged","mergedAt":"2023-12-02T07:20:45Z","number":9512,"mergeCommitSha":"35653eaa5ac4ff3d1e18456defddc55f5badcd60","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9512","title":"Unredact error logs in installer","createdAt":"2023-12-02T07:20:15Z"}
{"state":"Merged","mergedAt":"2023-12-02T07:23:15Z","number":9513,"mergeCommitSha":"f5e519cb3ee630dbb72d8c1616ae2954b3368417","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9513","title":"Bump for installer","createdAt":"2023-12-02T07:22:57Z"}
{"state":"Open","mergedAt":null,"number":9514,"body":"References fontawesome via a submodule.  I've dumped the resolved FA npm packages here:  https://github.com/NextChapterSoftware/fontawesome\r\n\r\nThe submodule lives in `/shared/fontawesome`.  I set up aliasing to point `@fortawesome` to this folder, so everything should build the same as it ever has.\r\n\r\nThis is one possible fix for flaky FA hosting.  Also means devs don't need to set up FA tokens, and FA will stop harassing us about the amount of data we pull from them.\r\n\r\nThe repo is ~250MB... but in theory we were pulling this much down in every CI build, and GitHub should do a better job of caching and transferring this since it's now residing in a GitHub repo?","mergeCommitSha":"3232ea4937beaf10e89c54992a3327c4448853f9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9514","title":"Use local fontawesome","createdAt":"2023-12-02T20:59:15Z"}
{"state":"Merged","mergedAt":"2023-12-04T14:12:11Z","number":9515,"mergeCommitSha":"f83b777c95dc7d745747ac31825f928dc52124b9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9515","title":"Minimum number of documents for clustering","createdAt":"2023-12-04T14:09:03Z"}
{"state":"Merged","mergedAt":"2023-12-04T18:45:03Z","number":9516,"body":"Step 1 to address the MDM issue. We'll need to disable releases for teams under this configuration.","mergeCommitSha":"1eeaf76ffa6eaddba05b1131bd79b53f16eb5c41","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9516","title":"Add disabled release channel to team settings","createdAt":"2023-12-04T16:36:54Z"}
{"state":"Merged","mergedAt":"2023-12-04T18:39:52Z","number":9517,"mergeCommitSha":"f6710704a3d843a6199b839e99334aa0e55f298b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9517","title":"Allow Claire to access adminweb and onboard in web DEV environment","createdAt":"2023-12-04T18:37:48Z"}
{"state":"Merged","mergedAt":"2023-12-04T19:13:54Z","number":9518,"mergeCommitSha":"7811d53ab123a8babc7877686118effec042837a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9518","title":"Add ability to delete Jira installation from admin console","createdAt":"2023-12-04T18:56:55Z"}
{"state":"Merged","mergedAt":"2023-12-04T21:45:03Z","number":9519,"mergeCommitSha":"e9b741642d24c5ab66892d34e88212c7c69f7dcc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9519","title":"Set file ownership to logged in user","createdAt":"2023-12-04T21:19:24Z"}
{"state":"Merged","mergedAt":"2023-12-05T19:09:52Z","number":9520,"mergeCommitSha":"9e91719da8175d295d5150c11e06f89556b45068","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9520","title":"Fix sagemaker endpoint names","createdAt":"2023-12-04T22:03:48Z"}
{"state":"Merged","mergedAt":"2023-12-04T22:48:54Z","number":9521,"mergeCommitSha":"deb521e9d9c8cd7100e3279df6bef9db6da18e03","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9521","title":"Add logging to AtlassianOauthTokenProvider","createdAt":"2023-12-04T22:34:09Z"}
{"state":"Merged","mergedAt":"2023-12-05T01:24:34Z","number":9522,"mergeCommitSha":"a2d6c77bdf11c7067f05ff25b0bbf9e2aef50e9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9522","title":"Prevent running updates when executing process doesn't own the files","createdAt":"2023-12-04T22:35:06Z"}
{"state":"Merged","mergedAt":"2023-12-04T22:47:30Z","number":9523,"mergeCommitSha":"46d2023abec50aa07015938063ab51db161b7b0f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9523","title":"Bump for new installer","createdAt":"2023-12-04T22:47:15Z"}
{"state":"Merged","mergedAt":"2023-12-05T01:02:18Z","number":9524,"mergeCommitSha":"516add614cf6208c78221920ef6cd43905b7d4d8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9524","title":"Try other Jira auth tokens if the first one doesnt work","createdAt":"2023-12-05T00:45:42Z"}
{"state":"Merged","mergedAt":"2023-12-05T02:26:51Z","number":9525,"mergeCommitSha":"7550419a388bbcbb95499eb23bf7575264805ff8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9525","title":"Bump for new installer","createdAt":"2023-12-05T02:26:35Z"}
{"state":"Merged","mergedAt":"2023-12-05T05:54:49Z","number":9526,"mergeCommitSha":"56c9f40922cf62c482373c3141eda152f6bda143","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9526","title":"Return empty versions if user is not logged in or doesn't belong to a team","createdAt":"2023-12-05T05:43:49Z"}
{"state":"Merged","mergedAt":"2023-12-05T09:09:23Z","number":9527,"mergeCommitSha":"cc2fe66ac0b5c183f538916eac66891a82ea162a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9527","title":"Add person level release channel override","createdAt":"2023-12-05T06:28:16Z"}
{"state":"Merged","mergedAt":"2023-12-05T10:37:22Z","number":9528,"mergeCommitSha":"ce9287a714ca21eca713d2c9bcf88e4deb57d17f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9528","title":"pulled wrong person id","createdAt":"2023-12-05T10:15:37Z"}
{"state":"Merged","mergedAt":"2023-12-05T18:12:37Z","number":9529,"body":"Richard Bresnan indicated that my logic was foolhardy and not in sync with what we need to be doing when using slack threads.","mergeCommitSha":"d7edb80c145c78ee50150fd13aed173c06f0d57b","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9529","title":"Should use isCurrentMember for determining if author is legit","createdAt":"2023-12-05T18:08:02Z"}
{"state":"Merged","mergedAt":"2022-04-21T17:09:49Z","number":953,"mergeCommitSha":"0b5d6018ce38274759d205ef49dd42d5e03d1dc4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/953","title":"Track changed files and write out to tmp directory as needed","createdAt":"2022-04-20T20:55:44Z"}
{"state":"Merged","mergedAt":"2023-12-05T18:23:30Z","number":9530,"mergeCommitSha":"6eaf13dd0439325a354153f6d2337eec21fb9761","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9530","title":"Add mistral to dev","createdAt":"2023-12-05T18:23:21Z"}
{"state":"Merged","mergedAt":"2023-12-05T19:21:39Z","number":9531,"body":"Fixes all of the `Failed to store sensitive fields.` errors","mergeCommitSha":"623e7af50c08f7b8aa3ea31cd4acef56b78fa3d0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9531","title":"Give the linear service permission to write to the dynamo logContentCache","createdAt":"2023-12-05T19:05:03Z"}
{"state":"Merged","mergedAt":"2023-12-05T19:21:08Z","number":9532,"body":"see related #9517 ","mergeCommitSha":"9edf27b9f5630c458273701fb9f56342207284c4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9532","title":"Give Claire access to DEV","createdAt":"2023-12-05T19:20:19Z"}
{"state":"Merged","mergedAt":"2023-12-05T20:03:21Z","number":9533,"mergeCommitSha":"2dd6d27fb70f6fbc354e5a30d1722dca251915fd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9533","title":"Dont retry ingesting a notion page on 404","createdAt":"2023-12-05T19:43:31Z"}
{"state":"Merged","mergedAt":"2023-12-05T20:30:19Z","number":9534,"body":"New API for public download","mergeCommitSha":"770a100ceb64565beb87e53e86d44c65ce5fe792","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9534","title":"New API for public download","createdAt":"2023-12-05T19:53:45Z"}
{"state":"Merged","mergedAt":"2023-12-05T21:05:20Z","number":9535,"mergeCommitSha":"c43f73a082df65a34773bbd7543c2b007da9abb2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9535","title":"Update ts clients to use the public installer API for download links","createdAt":"2023-12-05T20:19:42Z"}
{"state":"Merged","mergedAt":"2023-12-05T21:43:57Z","number":9536,"mergeCommitSha":"499f45744b78bfd90e69af30dd883ee4f53d6983","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9536","title":"Assert that we have at least 1 live version for API compat check","createdAt":"2023-12-05T21:20:54Z"}
{"state":"Merged","mergedAt":"2023-12-05T21:58:56Z","number":9537,"body":"https://chapter2global.slack.com/archives/C02GEN8LFGT/p1701812529627209","mergeCommitSha":"85ec71c4a9d0c24c87ca23b7187da3999179295f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9537","title":"Allow archive/restore of slack threads","createdAt":"2023-12-05T21:47:53Z"}
{"state":"Merged","mergedAt":"2023-12-05T22:48:21Z","number":9538,"body":"support for:\r\n\r\n```kotlin\r\nfirstStep.thenAsync(\r\n    listOf(\r\n         documentRetrievalStep1,\r\n         documentRetrievalStep2,\r\n         documentRetrievalStep3,\r\n     )\r\n)\r\n```\r\n\r\nProduces a homogeneous list of generically defined outputs. Needed to do arbitrary numbers of async document retrieval, classification, etc. \r\n\r\nBasically to support async branching ","mergeCommitSha":"c73696a113f70e9991ffbc5777789163b02c606f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9538","title":"Add multi-chain support to async chaining","createdAt":"2023-12-05T21:51:44Z"}
{"state":"Merged","mergedAt":"2023-12-05T23:36:40Z","number":9539,"mergeCommitSha":"572cba18c2d2ecc08cbb179d1cbff3a0f42d3d3f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9539","title":"Add python libraries for pinecone and llm","createdAt":"2023-12-05T22:41:50Z"}
{"state":"Merged","mergedAt":"2022-04-21T17:11:30Z","number":954,"body":"Just getting rid of test code we aren't using.","mergeCommitSha":"d8ee78d259074b6e6869a2b64d5028bffb2e3d6e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/954","title":"Remove MessageEditorPlayground dead code","createdAt":"2022-04-20T21:09:03Z"}
{"state":"Merged","mergedAt":"2023-12-06T20:04:33Z","number":9540,"body":"* Fetch from all three 'mine' sources (All, SCM Provider, and Unblocked).  This data gets held in memory\r\n* Add `FilterBar` component, acts like a Picker, looks like a bunch of pill buttons\r\n* Render from the correct source based on the picker selection\r\n\r\n![CleanShot 2023-12-05 at 15 13 06@2x](https://github.com/NextChapterSoftware/unblocked/assets/2133518/d438cbd1-66df-4554-823c-135d756a04ed)\r\n","mergeCommitSha":"d1aa033aee8abcac1dbc446ba288f5b44662ad62","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9540","title":"Display All/Unblocked/SCM discussions in hub","createdAt":"2023-12-05T23:14:20Z"}
{"state":"Merged","mergedAt":"2023-12-05T23:54:03Z","number":9541,"mergeCommitSha":"0dbfb0cfe57fc6db038a0590a63387cfd0343276","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9541","title":"Use user tokens for pulling in confluence spaces","createdAt":"2023-12-05T23:34:50Z"}
{"state":"Merged","mergedAt":"2023-12-06T00:35:18Z","number":9542,"body":"This is not an Unblocked error","mergeCommitSha":"9563800335a45e9338e82692d0d6076f4e2a26a0","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9542","title":"Dont error if Jira site is tenant restricted","createdAt":"2023-12-06T00:06:46Z"}
{"state":"Merged","mergedAt":"2023-12-06T01:04:15Z","number":9543,"body":"So that we don't re-ingest the same page","mergeCommitSha":"16b229f14260c29e914b740adeb439ac50f8edf4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9543","title":"Strip anchors from paths scraped from a page","createdAt":"2023-12-06T00:55:24Z"}
{"state":"Merged","mergedAt":"2023-12-06T05:50:45Z","number":9544,"body":"https://chapter2global.slack.com/archives/C02HEVCCJA3/p1701801392794319","mergeCommitSha":"b92b9eb0da4e5ad24a4108eb0a9ce524e773e03d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9544","title":"Users can delete bot messages","createdAt":"2023-12-06T05:28:36Z"}
{"state":"Merged","mergedAt":"2023-12-06T07:08:37Z","number":9545,"mergeCommitSha":"562dcb431a3b6f56a47a565a687680d0a43e0cdd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9545","title":"Fix JIRA comment ID and comment URL backfill","createdAt":"2023-12-06T05:57:00Z"}
{"state":"Merged","mergedAt":"2023-12-06T07:08:19Z","number":9546,"body":"HTTP response header `X-Unblocked-Last-Modified` is not correct for pusher, when the stream is paginated.\n\nInstead the clients must use the MAX of the modified timestamps over each of the threads in the collection; the _entire_ collection across many pages.\n\nThis change exposes the `modifiedAt` of each `ThreadInfo` for the clients.","mergeCommitSha":"5fb29a0cec2812ef483f34d5a2ec3952d59ca388","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9546","title":"Drive 'My Discussions' paginated polling with ThreadInfo modifiedAt","createdAt":"2023-12-06T06:29:49Z"}
{"state":"Merged","mergedAt":"2023-12-06T08:16:54Z","number":9547,"mergeCommitSha":"1611c8fd5ef019306666c535c65fd4000dacbc94","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9547","title":"Populate GitHub Issue comment URL in ThreadInfo API","createdAt":"2023-12-06T07:20:59Z"}
{"state":"Merged","mergedAt":"2023-12-06T09:10:48Z","number":9548,"mergeCommitSha":"d5404addf6c5247b6beb8f8581b265680675b6b8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9548","title":"Fix Github issue comment typo","createdAt":"2023-12-06T08:49:41Z"}
{"state":"Closed","mergedAt":null,"number":9549,"mergeCommitSha":"7178af6a0f14696fe15f12ab539602ee55b16e43","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9549","title":"Add sagemaker llm endpoint","createdAt":"2023-12-06T17:47:51Z"}
{"state":"Merged","mergedAt":"2022-04-22T21:41:41Z","number":955,"body":"The secret sauce was to create a Protocol with associated types over the concrete type, then make clever use of builders. The resulting API feels very SwiftUI-like. Ignore some of the styling things for now, I'm still a n00b and was playing around with inner drop shadows and stuff.\r\n\r\nI'd appreciate any advice on how to adopt platform colours that will update between light/dark mode for macOS 11 without resorting to Environment hacks. Trying to get to this:\r\n<img width=\"486\" alt=\"CleanShot 2022-04-20 at 14 17 29@2x\" src=\"https://user-images.githubusercontent.com/858772/164324526-f5b38a9a-1af7-4ef0-8181-3ecb841549ed.png\">\r\n\r\n<img width=\"524\" alt=\"CleanShot 2022-04-20 at 14 18 04@2x\" src=\"https://user-images.githubusercontent.com/858772/164324610-9d3c4043-990c-4778-af34-b29567a85731.png\">\r\n\r\n","mergeCommitSha":"cabd010f271ccf25c2a68506f88777bcca5c481c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/955","title":"Custom tabview","createdAt":"2022-04-20T21:18:31Z"}
{"state":"Merged","mergedAt":"2023-12-06T20:42:54Z","number":9550,"mergeCommitSha":"37ffe87037a844cc2a33ec0e9960553278b08829","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9550","title":"Add pinecone integration","createdAt":"2023-12-06T19:31:32Z"}
{"state":"Merged","mergedAt":"2023-12-06T20:05:27Z","number":9551,"mergeCommitSha":"cf2454fdd342a496c4dc528b4c84514d3726cb72","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9551","title":"Move jobs back to Jira Service","createdAt":"2023-12-06T19:44:44Z"}
{"state":"Merged","mergedAt":"2023-12-06T21:34:27Z","number":9552,"body":"Making Linear service resource allocations match Jira service","mergeCommitSha":"c096281908bf205f45164d1e0246726bc9a3f10e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9552","title":"[SKIP TESTS] Increase CPU and memory limits for linear service","createdAt":"2023-12-06T21:25:56Z"}
{"state":"Merged","mergedAt":"2023-12-06T21:56:45Z","number":9553,"mergeCommitSha":"689cb9fed0711833facaf32dec46a6bcc4a57b4d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9553","title":"Revert Move jobs back to Jira Service (#9551)","createdAt":"2023-12-06T21:30:26Z"}
{"state":"Merged","mergedAt":"2023-12-06T23:49:09Z","number":9554,"mergeCommitSha":"e89470171ce99bf98a50b47364ec63c10b73409a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9554","title":"Add topic mappings image","createdAt":"2023-12-06T23:04:53Z"}
{"state":"Merged","mergedAt":"2023-12-06T23:51:37Z","number":9555,"body":"To set some sensible limits. We can always make this configurable at a team level.","mergeCommitSha":"8f1c597064a16952ae258a6a29988da8b762f686","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9555","title":"[Web Ingestion] Limit crawl depth","createdAt":"2023-12-06T23:15:13Z"}
{"state":"Merged","mergedAt":"2023-12-06T23:23:52Z","number":9556,"mergeCommitSha":"8ed56da88f95c8ce77dd5bc28e9d1ea6b91ebd51","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9556","title":"Fix permissions for ml models","createdAt":"2023-12-06T23:23:19Z"}
{"state":"Merged","mergedAt":"2023-12-07T19:06:08Z","number":9557,"body":"- Adding Claire to the \"about\" page\r\n- Updating Peter's photo\r\n- Submodule bump for the above assets \r\n\r\n![CleanShot 2023-12-06 at 15 55 41@2x](https://github.com/NextChapterSoftware/unblocked/assets/13353189/d3acd412-8713-48c6-8ac1-de63963154c7)\r\n","mergeCommitSha":"695b2de564dbac69e31f0c254b77e2164bb6386d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9557","title":"Adding Claire to about page","createdAt":"2023-12-06T23:58:27Z"}
{"state":"Merged","mergedAt":"2023-12-07T22:30:48Z","number":9558,"mergeCommitSha":"42a5d25b012dc512c982532a1ae2b249c5bde1a2","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9558","title":"Add personal repo focus to queries","createdAt":"2023-12-07T00:44:45Z"}
{"state":"Merged","mergedAt":"2023-12-07T01:18:11Z","number":9559,"mergeCommitSha":"a258dc225ace2c87fe10f1b60cd94cdc035c1597","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9559","title":"topic mapping endpoint","createdAt":"2023-12-07T01:17:55Z"}
{"state":"Merged","mergedAt":"2022-04-21T03:07:56Z","number":956,"body":"At the moment we are only planning to add email notification logic to this. Further down the road we might add slack and other notification types to it as well. I'll be adding service accounts and ECR repos in a separate PR\r\n- Setup a new gradle project\r\n- Added source code for an empty app with health probes\r\n- Generated deployment helm charts\r\n- Added the new service to Github actions workflow to have it deployed\r\n- Tiny change in .gitignore to hide all those annoying `bin` directories","mergeCommitSha":"734a1b7346a8d8fa7bd55016ca9fccee928630a9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/956","title":"Spinning up Notification service","createdAt":"2022-04-20T21:41:39Z"}
{"state":"Merged","mergedAt":"2023-12-07T04:34:34Z","number":9560,"body":"#### Motivation\r\nhttps://chapter2global.slack.com/archives/C0678GN0H6W/p1701914584901739?thread_ts=**********.505049&cid=C0678GN0H6W\r\n\r\n#### Type converts to\r\n- Kotlin: `Long` ✅\r\n- Swift: `Int64` ✅\r\n- JS: `number` ☑️ \r\n  - can only represent integers accurately up to ﻿`Number.MAX_SAFE_INTEGER`, which is ﻿****************\r\n  - but that's enough for microsecond timestamps","mergeCommitSha":"f87d54408b1deb5b221c669b9768e573bf14c333","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9560","title":"[BREAKS API ON MAIN] Use ThreadInfo timestamp instead of Date to workaround JS limitation","createdAt":"2023-12-07T02:28:10Z"}
{"state":"Merged","mergedAt":"2023-12-08T07:31:11Z","number":9561,"body":"There are 46 queue consumers on each SCM service instance. That means, in the worst case, there is 46 jobs worth of memory contributing to peak memory. The simplest solution here is to just lower the consumer counts, which will cause auto-scalar to ramp up more instances.\n\n- 6 x pr_summary_ingestion\n- 6 x pr_summaries_ingestion_completion\n- 6 x pr_archive\n- 6 x scm_events\n- 6 x hooks_scm\n- 6 x unblocked_pr_comment_reactions\n- 6 x unblocked_pr_comments\n- 4 x pr_ingestion\n\nSee more here:\nhttps://chapter2global.slack.com/archives/C02HEVCCJA3/p1701287151284689?thread_ts=1700155393.798489&cid=C02HEVCCJA3","mergeCommitSha":"5570d1330b2f76fc7be1dd500f71ac2e2a7b8ac9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9561","title":"[RFC] Reduce concurrent SCM queue consumers to reduce peak memory","createdAt":"2023-12-07T03:03:36Z"}
{"state":"Merged","mergedAt":"2023-12-08T01:38:50Z","number":9562,"body":"new source code classifier in a new library. testing and adding to source ingestion shortly.","mergeCommitSha":"ca339969d341b2c6cd9e4c5f4a96c3bbb76268c7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9562","title":"classifier-utils: a new llm source code classifier in a new library, used by file-utils, used by source code processor image","createdAt":"2023-12-07T04:08:14Z"}
{"state":"Merged","mergedAt":"2023-12-11T18:55:20Z","number":9563,"mergeCommitSha":"8112e61ee635d6dfaf79022ff9255e76c61c3711","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9563","title":"[Web Ingestion] Add web integration","createdAt":"2023-12-07T05:51:08Z"}
{"state":"Merged","mergedAt":"2023-12-07T06:46:17Z","number":9564,"mergeCommitSha":"72053a278c0728871629cca5365ba9e39d5da1d4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9564","title":"Fix MessageModel.getExternalCommentUrl","createdAt":"2023-12-07T06:23:32Z"}
{"state":"Merged","mergedAt":"2023-12-07T06:42:51Z","number":9565,"mergeCommitSha":"b9ca79d264644f60f20f9e196ceca4578fda2229","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9565","title":"Actually expose the Instant as microseconds","createdAt":"2023-12-07T06:40:14Z"}
{"state":"Merged","mergedAt":"2023-12-07T07:12:18Z","number":9566,"mergeCommitSha":"ca17c6bcdef57251168e1e8b8f05020e15e934dc","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9566","title":"Fix MessageStore.upsertByLinearCommentId","createdAt":"2023-12-07T06:51:40Z"}
{"state":"Merged","mergedAt":"2023-12-08T07:30:42Z","number":9567,"body":"Been deprecated for a long time.","mergeCommitSha":"d20b7393dc166787f9c16810f9c07fddcd357b8a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9567","title":"Cleanup legacy sourcemark in ThreadInfo API","createdAt":"2023-12-07T06:56:42Z"}
{"state":"Merged","mergedAt":"2023-12-07T07:24:03Z","number":9568,"mergeCommitSha":"b8c64e32a3b9129d0a3f7aac981568f52397fe16","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9568","title":"Fix linear thread url","createdAt":"2023-12-07T07:02:58Z"}
{"state":"Merged","mergedAt":"2023-12-07T07:37:28Z","number":9569,"mergeCommitSha":"4bc202b316c330480010e59612756a60bfc2665e","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9569","title":"Fix LinearIngestionStore.upsert","createdAt":"2023-12-07T07:14:32Z"}
{"state":"Merged","mergedAt":"2022-04-20T21:55:50Z","number":957,"body":"- Added a notification repo to ECR\r\n- Added a new service account for  notification service","mergeCommitSha":"b57912ae98e686bc5c558e380c491052db56ae57","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/957","title":"Adding service acccount and ECR repo for new service","createdAt":"2022-04-20T21:49:42Z"}
{"state":"Merged","mergedAt":"2023-12-07T12:26:51Z","number":9571,"mergeCommitSha":"6618fed20837d827b2b2e2534310d2b4e0a71f59","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9571","title":"Add kotlin layer for topic mapping","createdAt":"2023-12-07T12:02:20Z"}
{"state":"Closed","mergedAt":null,"number":9572,"mergeCommitSha":"0942eb16c6df6c8357e0ebdbef6c241ad5c3de7c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9572","title":"Temporary","createdAt":"2023-12-07T18:08:42Z"}
{"state":"Merged","mergedAt":"2023-12-07T18:21:29Z","number":9573,"mergeCommitSha":"7ed1101ed12c8bc09b22d045e8ab8c1124932fbd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9573","title":"IMprove topic cluster error handling","createdAt":"2023-12-07T18:17:34Z"}
{"state":"Merged","mergedAt":"2023-12-07T18:40:38Z","number":9574,"body":"Motivation:\nhttps://chapter2global.slack.com/archives/C0678GN0H6W/p1701973237229879?thread_ts=1701969783.517729&cid=C0678GN0H6W\n\nPartial revert of #2347.","mergeCommitSha":"e3de39a294dfb0bb6b22001118387f531b693cb7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9574","title":"Always send ThreadInfo cursors","createdAt":"2023-12-07T18:29:18Z"}
{"state":"Merged","mergedAt":"2023-12-07T20:07:08Z","number":9575,"mergeCommitSha":"fd58069c404287571201f6b63133ed7b65a65277","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9575","title":"Fix repo_id for topic mappings","createdAt":"2023-12-07T20:03:35Z"}
{"state":"Merged","mergedAt":"2023-12-07T22:15:00Z","number":9576,"mergeCommitSha":"7d4c87788ab72a8e4bf7b37a0600a905c27df90c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9576","title":"Cluster topic mapping","createdAt":"2023-12-07T21:43:01Z"}
{"state":"Merged","mergedAt":"2023-12-07T22:28:14Z","number":9577,"mergeCommitSha":"c3387aa01bc9fd11441d15717dada2fd94fcee61","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9577","title":"Fix swift 5 compat issue","createdAt":"2023-12-07T21:57:46Z"}
{"state":"Merged","mergedAt":"2023-12-07T23:55:19Z","number":9578,"body":"https://www.notion.so/nextchaptersoftware/How-User-Events-Map-to-Activity-fe162974de5845b4b3c2e0d1deac3c07","mergeCommitSha":"be4c3c02b2b018f28bf4990d4658285555d1f116","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9578","title":"ContentViewed metric no longer tracks activity on the \"Recently Deleted\" or \"Components\" pages","createdAt":"2023-12-07T22:33:14Z"}
{"state":"Merged","mergedAt":"2023-12-07T23:56:21Z","number":9579,"body":"https://www.notion.so/nextchaptersoftware/How-User-Events-Map-to-Activity-fe162974de5845b4b3c2e0d1deac3c07","mergeCommitSha":"e30e0de9b19af3c8ab564e11cfcb68e419e91e30","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9579","title":"Search and TutorialViewed events are no longer considered activity","createdAt":"2023-12-07T22:38:46Z"}
{"state":"Merged","mergedAt":"2022-04-20T22:04:11Z","number":958,"body":"Replacing the Codeswell icons with Unblocked. We will most likely tweak the shape and colours but this at least gets us closer to our ultimate goal. \r\n\r\nSidebar\r\n![CleanShot 2022-04-20 at 14 53 53](https://user-images.githubusercontent.com/13353189/164330394-2d26e998-3c7a-45be-a0a8-cc07fc7239f6.gif)\r\n\r\n\r\nExtension Icon\r\n<img width=\"403\" alt=\"CleanShot 2022-04-20 at 14 54 20@2x\" src=\"https://user-images.githubusercontent.com/13353189/164330500-7b12b025-82ad-42d9-8243-d7cbd717bbde.png\">\r\n\r\n<img width=\"471\" alt=\"CleanShot 2022-04-20 at 14 54 38@2x\" src=\"https://user-images.githubusercontent.com/13353189/164330564-8e4e5400-1d97-4246-838b-470f4c1a46d3.png\">\r\n","mergeCommitSha":"6dd696211b67b73057dde6183667c5aed7720f69","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/958","title":"Updating assets that represent the Unblocked extension in VSCode","createdAt":"2022-04-20T21:55:11Z"}
{"state":"Merged","mergedAt":"2023-12-08T15:26:22Z","number":9580,"mergeCommitSha":"56f8499b4329409b108951735d46481d33c5837c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9580","title":"Add new repo focus extractor template","createdAt":"2023-12-07T23:20:59Z"}
{"state":"Merged","mergedAt":"2023-12-08T00:15:40Z","number":9581,"body":"- Add retries for pinecone\r\n- Update\r\n","mergeCommitSha":"857ffdca77a2171ce55367b72d594ac800de7f5f","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9581","title":"ADd pinecone backoffs","createdAt":"2023-12-08T00:00:18Z"}
{"state":"Merged","mergedAt":"2023-12-08T19:03:14Z","number":9582,"mergeCommitSha":"0ab588bad1d55923f40ea2940575623ddc5e1631","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9582","title":"Dashboard All/Unblocked/Scm thread listings","createdAt":"2023-12-08T00:49:08Z"}
{"state":"Merged","mergedAt":"2023-12-08T16:15:06Z","number":9583,"body":"Repo extractor service\n\nAdd tests","mergeCommitSha":"7fa69df91ee460d0c0d8c0d45a37d6b18c74bf6a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9583","title":"Repo extractor service","createdAt":"2023-12-08T01:26:03Z"}
{"state":"Merged","mergedAt":"2023-12-08T02:43:11Z","number":9584,"mergeCommitSha":"e259d22a66c1a731b9dc6b6b1dc085e8fa03e041","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9584","title":"Fix Lness off by one bug","createdAt":"2023-12-08T01:31:58Z"}
{"state":"Merged","mergedAt":"2023-12-08T02:38:46Z","number":9585,"mergeCommitSha":"58635f6d4c77be68e4ac86ee0ef11359229ce809","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9585","title":"Topic mapping does not handle empty strings well","createdAt":"2023-12-08T02:35:50Z"}
{"state":"Merged","mergedAt":"2023-12-08T04:22:55Z","number":9586,"mergeCommitSha":"66773b76140ba58f1c827f891533c43141def6bd","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9586","title":"Fix topic ingestion","createdAt":"2023-12-08T04:16:56Z"}
{"state":"Merged","mergedAt":"2023-12-08T08:19:36Z","number":9587,"mergeCommitSha":"a9e3dd4117bd89e07a7ddb529c5df3be5222b5c1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9587","title":"Fix timezone bug","createdAt":"2023-12-08T04:44:19Z"}
{"state":"Merged","mergedAt":"2023-12-08T05:06:28Z","number":9588,"mergeCommitSha":"27b2d9c247fa22380fa7f3a7e221e69c871c56df","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9588","title":"Add more logging","createdAt":"2023-12-08T05:06:05Z"}
{"state":"Merged","mergedAt":"2023-12-08T06:46:35Z","number":9589,"mergeCommitSha":"f832884fca6c6ec658269a8dc6653e4605301679","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9589","title":"Make topic mapping settings","createdAt":"2023-12-08T06:23:48Z"}
{"state":"Merged","mergedAt":"2022-04-20T23:37:10Z","number":959,"body":"<img width=\"340\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/164341071-c7ce689f-78b8-4e53-8529-b4432d8f1b83.png\">\r\n<img width=\"427\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/164341134-e3819587-8698-45a7-baf4-51bcbc8b970c.png\">\r\n<img width=\"712\" alt=\"image\" src=\"https://user-images.githubusercontent.com/13431372/164341125-6c17a061-20d1-481f-9411-ce5d9a1e47ea.png\">\r\n","mergeCommitSha":"55b5bfb8902405fcb6af52d06723df788f5a79c9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/959","title":"Update assets for web dashboard and extension","createdAt":"2022-04-20T23:31:07Z"}
{"state":"Merged","mergedAt":"2023-12-08T07:43:38Z","number":9590,"mergeCommitSha":"8bb254ba59156d399b1f82bcc817c3856cc9bbb9","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9590","title":"Fix GitLab 500 for getTeamStatus API","createdAt":"2023-12-08T07:29:53Z"}
{"state":"Merged","mergedAt":"2023-12-08T07:53:01Z","number":9591,"mergeCommitSha":"0e37f101ec87ddc38574dd1465e44ac440213867","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9591","title":"Batch upserts","createdAt":"2023-12-08T07:50:23Z"}
{"state":"Merged","mergedAt":"2023-12-08T21:56:36Z","number":9592,"mergeCommitSha":"7a4df4970f21e2de1776b3e35373a6aca3752f82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9592","title":"Remove RepoIngestionModel, which is dead code","createdAt":"2023-12-08T08:30:08Z"}
{"state":"Merged","mergedAt":"2023-12-08T16:57:07Z","number":9593,"mergeCommitSha":"aa55fce265378e7d41b44009a2cf6ce66a6a6e08","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9593","title":"Bring repo extractor online in dev","createdAt":"2023-12-08T16:15:02Z"}
{"state":"Merged","mergedAt":"2023-12-08T18:17:56Z","number":9594,"mergeCommitSha":"ba6f788d683dcc1f6a4388c3e0669325bfcaf8e4","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9594","title":"Fix xcode 14.2 compat","createdAt":"2023-12-08T18:08:08Z"}
{"state":"Merged","mergedAt":"2023-12-08T18:23:22Z","number":9595,"mergeCommitSha":"5b9fbe37b431739700cc1bbf3651368d55cc6a82","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9595","title":"Allow for topics deletion","createdAt":"2023-12-08T18:22:11Z"}
{"state":"Merged","mergedAt":"2023-12-12T18:59:22Z","number":9596,"body":"* The client falsely assumed that all markable threads will have Unread objects on them. This is untrue for newly ingested PR threads. Threads without Unread objects should still be able to be marked as Read/Unread manually\r\n* Amend client logic to show this functionality, but only scope the functionality to threads that are shown under My Discussions \r\n* NOTE: This PR is blocked by service end changes to backfill the Unread object. Do not merge until that work is done.","mergeCommitSha":"a7e9755ffe8db4854158f6e2a1c9c93ebb75b7fb","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9596","title":"Fix marking threads as unread","createdAt":"2023-12-08T18:34:38Z"}
{"state":"Merged","mergedAt":"2023-12-08T18:42:36Z","number":9597,"mergeCommitSha":"3a220c5c732ea149f682514f6e198fb9a9b56b6c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9597","title":"Remove dead field","createdAt":"2023-12-08T18:42:20Z"}
{"state":"Merged","mergedAt":"2023-12-08T19:31:19Z","number":9598,"mergeCommitSha":"7b43353c837deaff33c14e1aff9773ca5ddf7e38","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9598","title":"Backfill missing ThreadUnread when setting a thread as unread","createdAt":"2023-12-08T19:03:06Z"}
{"state":"Merged","mergedAt":"2023-12-11T23:07:40Z","number":9599,"body":"When opening a reference in the IDE, attempt to open in IDE. If fails, launch in scm provider.\r\n\r\nThis will launch the *local* version of the file. It may not match what was used to generate the QA. \r\n\r\n\r\n\r\n~This currently only works for references listed at the bottom of a message (aka part of the references API object). This does not work for inline references as those are only passed as URLs to the clients atm. Next piece of work is to update backend to send necessary metadata to launch within IDEs.~\r\n\r\nInline references to source files will now open in IDE if there is a corresponding references in the overall list. This should always be the case based on conversation in [Slack](https://chapter2global.slack.com/archives/C02HEVCCJA3/p1702316778976899)\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/ab76182c-2e90-47a6-b6ba-d7b17b8377a7\r\n\r\n\r\nhttps://github.com/NextChapterSoftware/unblocked/assets/1553313/91b858ec-47aa-4ee7-8985-7fca8ab0ba98\r\n\r\n","mergeCommitSha":"07b53f213be0706c585ded6beefbdaaddfea3130","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9599","title":"Open source references locally in IDE if possible","createdAt":"2023-12-08T19:54:41Z"}
{"state":"Merged","mergedAt":"2022-01-24T19:23:33Z","number":96,"body":"## Problem\r\nThe previous auth flow design had 3 major shortcomings:\r\n\r\n1. The web-app wasn't taken into account. A response token body via a redirect to the api-service would stop the browser dead in its tracks\r\n2. The native client experience relied on a custom uri scheme handler to pop the user back to the client to finish the token exchange\r\n3. The browser wasn't treated like a first class citizen during the native auth flow\r\n\r\n## Proposal\r\nWith these changes we can create an experience where the browser has a consistent authentication flow that ends up on the dashboard, possibly adorned with some extras if the entry point was a native client.\r\n\r\nThe native client can also authenticate in the background seamlessly without any additional user interaction after the browser auth flow is complete.\r\n\r\nWe're also taking some security measure that should prevent token sidejacking (like setting an httpOnly strict cookie), and storing a relationship between the secret and nonce for token retrieval \r\n\r\n![image](https://user-images.githubusercontent.com/858772/*********-d7056125-da9c-471a-bfa3-18ccf9c93559.png)\r\n\r\n","mergeCommitSha":"232d402e9873075b6614109bce93e198fa9ce77d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/96","title":"Redesign user auth flow sequence for optimal UX","createdAt":"2022-01-21T06:58:40Z"}
{"state":"Merged","mergedAt":"2022-04-27T20:04:53Z","number":960,"body":"**Moved code block into the left side panel**\r\n![CleanShot 2022-04-26 at 17 21 24@2x](https://user-images.githubusercontent.com/13353189/165413877-9c004771-722d-4084-a216-009336dc5c0f.png)\r\n\r\n**Discussion titles have been moved into the first post.**\r\n![CleanShot 2022-04-26 at 17 14 20@2x](https://user-images.githubusercontent.com/13353189/165413312-b0d85563-0326-4d6e-b0db-f25cb40bad6d.png)\r\n\r\n**Tune responsive layout (thanks @matthewjamesadam)**\r\nWhen the discussion window hits the narrow breakpoint, the message content tucks underneath the avatar, name, and date. Beyond this breakpoint, the discussion thread takes up more horizontal space, where the avatars all align visually in one column\r\nhttps://user-images.githubusercontent.com/13353189/165412911-e356d6ca-8df8-4db0-9d28-aef67b900b68.mp4\r\n\r\n**Margins, Padding, line heights**\r\nCleaned up the spacing between elements in each message, as well as the surrounding container. I also adjusted the line heights.\r\n![CleanShot 2022-04-26 at 17 15 30@2x](https://user-images.githubusercontent.com/13353189/165413435-50a12759-d7cf-41f7-8a83-557829113a89.png)\r\n\r\n**Participants side panel**\r\nRemoved the title divider as it was clashing with the horizontal lines in the main discussion panel. Also cleaned up the spacing between participants.\r\n![CleanShot 2022-04-26 at 17 17 05@2x](https://user-images.githubusercontent.com/13353189/165413527-819a9a8a-7ff2-4699-a523-e33014c9d6ab.png)\r\n\r\n**Message editor controls and styling**\r\nSwapped out the text for font awesome icons, added a min height, and styled the various states, including the generic code block. \r\n<img width=\"569\" alt=\"CleanShot 2022-04-26 at 17 40 22@2x\" src=\"https://user-images.githubusercontent.com/13353189/165415564-71fd425d-cc63-4878-a4a3-7727186a2e59.png\">\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n","mergeCommitSha":"e28c368cdb58c0b915f0fa33c39e651fa7fe4b9c","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/960","title":"Cleaning up thread layout","createdAt":"2022-04-21T00:19:13Z"}
{"state":"Merged","mergedAt":"2023-12-08T21:00:11Z","number":9600,"mergeCommitSha":"7b7651a88ebc9b11f7e8f3a17677be23b491d3b1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9600","title":"Use correct SCM label","createdAt":"2023-12-08T20:50:33Z"}
{"state":"Merged","mergedAt":"2023-12-08T21:54:21Z","number":9601,"mergeCommitSha":"f180668934bd62f0204645ba7323f3bd0e88f138","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9601","title":"Fix delete by index","createdAt":"2023-12-08T21:29:45Z"}
{"state":"Merged","mergedAt":"2023-12-08T22:21:10Z","number":9602,"body":"* Default to showing the 'short' form for provider names \r\n* Show long form names in login UIs","mergeCommitSha":"af1ec997ce2ae07a966ff796fab6e27ad47608ff","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9602","title":"Update provider display name helper","createdAt":"2023-12-08T22:01:53Z"}
{"state":"Merged","mergedAt":"2023-12-08T22:19:42Z","number":9603,"mergeCommitSha":"d879eee71ee3fff74fa9d289f244f6c6bbd792e8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9603","title":"Fix duplicate message bug","createdAt":"2023-12-08T22:08:35Z"}
{"state":"Merged","mergedAt":"2023-12-08T23:17:40Z","number":9604,"body":"## Motivation\n- paging in any direction (before or after) SHOULD ideally return exclusive items.\n- however, polling with `modifiedSince` in either direction (before or after) MUST be inclusive in order to be correctly notified of changes to the boundary elements.\n\n## Option A\n- introduce 4 directions and let the client decide which one to use based on the circumstance:\n   * beforeExclusive\n   * beforeInclusive\n   * afterExclusive\n   * afterInclusive\n- the main risk with this approach is that without careful thought it is easy to introduce a change that inadvertently chooses the wrong direction. given that thre is no integration testing in the TS clients this would be prone to regression.\n\n## Option B (this change)\n- before and after are consistently inclusive\n- the downside is that the paging use case will return duplicate items that need to be deduplicated.\n- as long as the client has uniquification logic to deduplicate the boundary items, this is the safer approach.","mergeCommitSha":"62debc2a0b862c5a265b827010b24ec0ea2cc230","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9604","title":"Using a cursor with `after` parameter is now inclusive","createdAt":"2023-12-08T22:28:45Z"}
{"state":"Merged","mergedAt":"2023-12-08T22:35:45Z","number":9605,"mergeCommitSha":"56b99e438452f68fa4ae98f711e8840b52325905","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9605","title":"Fix vector deletion","createdAt":"2023-12-08T22:35:34Z"}
{"state":"Merged","mergedAt":"2023-12-08T23:14:16Z","number":9606,"mergeCommitSha":"c58b34316cfa1282bbe836511073c8cf02836f7d","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9606","title":"Move to standardized pinecone client","createdAt":"2023-12-08T22:37:37Z"}
{"state":"Merged","mergedAt":"2023-12-12T02:28:54Z","number":9607,"mergeCommitSha":"61a3dcb69b3ca24554c3325d6a381c72555389f7","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9607","title":"Use filepath rather than filename + prompt update","createdAt":"2023-12-08T22:41:55Z"}
{"state":"Merged","mergedAt":"2023-12-11T20:02:31Z","number":9608,"mergeCommitSha":"50f9afc43b9b9afc48e9d5d6c65d1983f0ea88f1","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9608","title":"Handle GitLab repo url sub-groups","createdAt":"2023-12-08T23:28:13Z"}
{"state":"Closed","mergedAt":null,"number":9609,"body":"Inline references within a QA do not contain enough information to load the file within an IDE.\r\n\r\nAdd `FileReference` to the Links block which contains enough metadata to load the file locally.\r\n\r\n![CleanShot 2023-12-08 at 15 31 15@2x](https://github.com/NextChapterSoftware/unblocked/assets/1553313/6082828c-bce4-40bc-88c5-a3afecf02dfc)\r\n\r\nWill implement  if spec accepted.","mergeCommitSha":"aeb830aa0338eb2fd38b5b4e50562323b9456527","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9609","title":"[RFC] Message File Reference Proto","createdAt":"2023-12-08T23:34:33Z"}
{"state":"Merged","mergedAt":"2022-04-21T05:30:46Z","number":961,"body":"- Now correctly takes into account uncommitted changes, triggered on save.\r\n- Also now hides deleted and significantly modified points from editor.\r\n\r\nhttps://user-images.githubusercontent.com/1798345/*********-06665a61-9143-46bd-a06b-1ae0e491bed3.mov\r\n","mergeCommitSha":"265198b41169e2b99934df79888bd34b0d6f84fa","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/961","title":"Fix recalc for uncommitted changes","createdAt":"2022-04-21T01:06:00Z"}
{"state":"Merged","mergedAt":"2023-12-09T00:32:31Z","number":9610,"mergeCommitSha":"009512bec8aaf1a43ad97918f139b77af2fca49a","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9610","title":"[SKIP TESTS] Fix adminweb route typo","createdAt":"2023-12-08T23:36:31Z"}
{"state":"Merged","mergedAt":"2023-12-08T23:53:03Z","number":9611,"body":"Fix some deprecation warnings","mergeCommitSha":"5c2543c75dc97aa60ded69433c49ce5d82fe9e71","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9611","title":"Support new JB IDEs","createdAt":"2023-12-08T23:39:24Z"}
{"state":"Merged","mergedAt":"2023-12-09T00:03:10Z","number":9612,"body":"- Set min-width on badges so that short numbers appear round\r\n- Change item ordering to match dashboard\r\n- Don't show badges in All button","mergeCommitSha":"03f03984f380627b7d252a1c2cf0cf46bc297e96","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9612","title":"Hub filter bar tweaks","createdAt":"2023-12-08T23:53:37Z"}
{"state":"Merged","mergedAt":"2023-12-09T00:42:53Z","number":9613,"mergeCommitSha":"0cee7077fd936bad07cd5f12011e0ebaec0b2af8","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9613","title":"Add WebIngestionJob","createdAt":"2023-12-09T00:00:45Z"}
{"state":"Merged","mergedAt":"2023-12-09T01:16:27Z","number":9614,"mergeCommitSha":"2515cb7fd4befbde0b4d1dd67619773a255aadca","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9614","title":"Enable repo extraction in prod","createdAt":"2023-12-09T00:53:09Z"}
{"state":"Merged","mergedAt":"2023-12-11T22:12:53Z","number":9615,"body":"Refactor how some of our settings/onboarding UIs work:\r\n\r\n* Add a reusable footer to the OnboardingOutlet -- this is available on any `/new/team` or `/connect` routes, and is triggered by the `setSettingsFooter` context method.  This means settings and onboarding can now have consistent header/footer content and saving logic.\r\n* Remove `GithubSettings` -- `ConfigureGithub` now works in any UI context\r\n* Rename `SettingsIntegrationFooter` to `SettingsFooter`, and tweak it so that it's usable in both settings and onboarding\r\n* Remove some of the elements from the GH settings UI that are no longer needed\r\n\r\nSome of the work here was for an earlier GitHub UI that had dynamic footer content, so some of this isn't needed today, but we will likely be using it soon.","mergeCommitSha":"f7f533996b3144e37a737046a10bccbc0f4eab86","htmlUrl":"https://github.com/NextChapterSoftware/unblocked/pull/9615","title":"Add settings footer support","createdAt":"2023-12-10T23:10:13Z"}