[tool.poetry]
name = "evaluation-processor"
version = "0.1.0"
description = ""
authors = ["cancelself <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10.12,<3.14"
deepeval = "^2.4.4" 
fastapi = "^0.115.8"
uvicorn = "^0.34.0"
unblocked-aws-utils = "^0"

[[tool.poetry.packages]]
from = 'src'
include = 'evaluation_processor'

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = 'jfrog-server'
url = 'https://getunblocked.jfrog.io/artifactory/api/pypi/unblocked-pypi/simple'
priority = 'supplemental'
