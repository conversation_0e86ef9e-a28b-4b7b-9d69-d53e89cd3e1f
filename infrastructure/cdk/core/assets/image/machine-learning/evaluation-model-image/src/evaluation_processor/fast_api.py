import os

import uvicorn
from fastapi import FastAPI, Request
from deepeval import evaluate
from deepeval.metrics import (
    AnswerRelevancyMetric,
    ContextualRelevancyMetric,
    FaithfulnessMetric,
    HallucinationMetric,
    PromptAlignmentMetric,
)
from deepeval.test_case import LLMTestCase
from typing import List, Dict, Any
import re

from deepeval.metrics import GEval
from deepeval.test_case import LLMTestCaseParams

from evaluation_processor.openai_constants import OPENAI_API_KEY
from evaluation_processor.eval_api_types import EvaluationRequest

app = FastAPI()


@app.get("/ping")
async def ping() -> dict[str, str]:
    return {"message": "ok"}


def parse_markdown_sections(markdown_text: str) -> List[Dict[str, List[str]]]:
    sections = []
    current_section = None

    lines = markdown_text.splitlines()

    for line in lines:
        h1_match = re.match(r"^#\s+(.*)", line)
        h2_match = re.match(r"^##\s+(.*)", line)

        if h1_match:
            section_title = h1_match.group(1).strip()
            current_section = {"title": section_title, "subsections": []}
            sections.append(current_section)
        elif h2_match and current_section is not None:
            subsection_title = h2_match.group(1).strip()
            current_section["subsections"].append(subsection_title)

    return sections


@app.post("/")
@app.post("/invocations")
async def eval(request: EvaluationRequest) -> Any:
    prompt_instruction_sections = parse_markdown_sections(request.prompt_instructions)
    all_instructions = []
    for section in prompt_instruction_sections:
        all_instructions.append(section["title"])
        all_instructions.extend(section["subsections"])

    metrics = [
        AnswerRelevancyMetric(threshold=0.7, verbose_mode=False),
        FaithfulnessMetric(threshold=0.7, verbose_mode=False),
        ContextualRelevancyMetric(threshold=0.7, verbose_mode=False),
        HallucinationMetric(threshold=0.7, verbose_mode=False),
        PromptAlignmentMetric(all_instructions, threshold=0.7, verbose_mode=False),
        GEval(
            name="Explicitness",
            criteria="Assess how explicitly the response presents information. An explicit response leaves little room for ambiguity or interpretation. Score high if the response is straightforward and leaves no ambiguity. Score low if the response is vague or requires interpretation.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Helpfulness",
            criteria="Evaluate the utility of the response in addressing the user's needs or solving their problem. Score high if the response is likely to be very useful to the user. Score low if it doesn't contribute to solving the user's query or issue.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Directness",
            criteria="Measure how directly the response addresses the question, without unnecessary detours or information. Score high if the response directly addresses the main point of the question. Score low if the response meanders or includes irrelevant information.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Relevance",
            criteria="Assess whether the response is pertinent to the question asked. Score high if every part of the response is related to the question. Score low if there are irrelevant sections.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Accuracy",
            criteria="Judge the factual correctness and precision of the response. Score high if the response is entirely correct and precise. Score low if there are factual errors or imprecise details.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Referential",
            criteria="Assess the response's use of external references or sources to support claims or provide additional information. Score high if relevant references are used effectively. Score low if references are missing, irrelevant, or poorly integrated.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Code",
            criteria="Evaluate the inclusion and quality of code examples in technical responses. Score high if relevant, correct, and clear code examples are provided. Score low if code examples are absent, incorrect, irrelevant, or unclear.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Conciseness",
            criteria="Measure the brevity of the response, ensuring it is as short as possible while still being complete. Score high if the response is brief and to the point but still complete. Score low if the response is overly wordy or contains unnecessary information.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
        GEval(
            name="Clarity",
            criteria="Assess the clarity and comprehensibility of the response. Score high if the response is easy to understand and well-structured. Score low if the response is confusing, poorly structured, or uses complex language unnecessarily.",
            evaluation_params=[
                LLMTestCaseParams.INPUT,
                LLMTestCaseParams.ACTUAL_OUTPUT,
            ],
            threshold=0.7,
            verbose_mode=False,
        ),
    ]

    test_case = LLMTestCase(
        input=request.input,
        actual_output=request.output,
        retrieval_context=[doc["content"] for doc in request.context],
        context=[doc["content"] for doc in request.context],
    )

    evaluation_result = evaluate(
        [test_case], metrics, verbose_mode=False, print_results=False
    )

    return evaluation_result


if __name__ == "__main__":
    # Annoyingly required to set via environment variable
    os.environ["OPENAI_API_KEY"] = OPENAI_API_KEY

    current_dir = os.path.dirname(os.path.abspath(__file__))
    log_config_path = f"{current_dir}/log_conf.yaml"
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        loop="asyncio",
        reload=False,
        log_config=log_config_path,
    )
