from topic_mapping.inputs_processor import InputsProcessor
from topic_mapping.topic_mapping_processor import TopicMappingProcessor

from topic_mapping.topic_mapping_api_types import TopicMappingRequest


class TopicMappingInference:
    __inputs_processor = InputsProcessor()

    def get_topic_mappings(self, topic_mapping_request: TopicMappingRequest) -> list[list[dict]]:
        documents = self.__inputs_processor.process_inputs(inputs=topic_mapping_request.inputs)
        topic_mapping_processor = TopicMappingProcessor(
            repo_id=topic_mapping_request.configuration.repo_id,
            pinecone_namespace=topic_mapping_request.configuration.pinecone_namespace,
            pinecone_hybrid_index=topic_mapping_request.configuration.pinecone_hybrid_index,
        )

        all_topic_mappings: list[list[dict]] = []
        for document in documents:
            topic_mappings = topic_mapping_processor.get_topic_mappings(
                document=document,
                alpha=topic_mapping_request.parameters.alpha,
                top_k=topic_mapping_request.parameters.top_k,
            )
            all_topic_mappings.append(topic_mappings)

        return all_topic_mappings
