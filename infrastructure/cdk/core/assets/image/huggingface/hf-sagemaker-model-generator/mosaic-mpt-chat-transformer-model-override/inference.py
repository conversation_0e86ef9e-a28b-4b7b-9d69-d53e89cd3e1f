# Documentation:
# https://www.philschmid.de/deploy-flan-t5-sagemaker
# https://github.com/lm-sys/FastChat/blob/fc24a77acade88a55cdbc89cfeb5f7bfe10b25eb/fastchat/serve/huggingface_api.py#L14
# https://github.com/lm-sys/FastChat/blob/fc24a77acade88a55cdbc89cfeb5f7bfe10b25eb/fastchat/model/model_adapter.py#L344
# https://huggingface.co/spaces/mosaicml/mpt-7b-chat/blob/main/app.py
# https://colab.research.google.com/drive/16D9tjggLukD38Un0hC-Gss3mrehPXng_?usp=sharing#scrollTo=ba4iip63h_pm
# https://github.com/puneet-jain159/Diy-QA-LLM-Bot/blob/fb851b40ddda2f7a1d2f5b73c1de91f163a40224/temp_code.py#L56
import os
import json
import torch
from transformers import AutoConfig, AutoModelForCausalLM, AutoTokenizer, TextStreamer, pipeline
from accelerate import init_empty_weights, load_checkpoint_and_dispatch, infer_auto_device_map


def model_fn(model_dir):
    print(model_dir)
    os.system("ls -Arl {}".format(model_dir))

    config = AutoConfig.from_pretrained(
        pretrained_model_name_or_path=model_dir,
        trust_remote_code=True,
    )
    config.update({
        # "max_seq_len": 8192,
        # "attn_impl": 'triton',
        # "init_device": 'cuda:0'
    })

    with init_empty_weights():
        model = AutoModelForCausalLM.from_pretrained(
            pretrained_model_name_or_path=model_dir,
            config=config,
            trust_remote_code=True,
            torch_dtype=torch.bfloat16,
        )

    model.tie_weights()

    max_memory = {"cpu": "60GiB"}
    for i in range(8):
        max_memory[i] = "5GiB" if i <= 2 else "75GiB"

    print(max_memory)

    device_map = infer_auto_device_map(
        model,
        no_split_module_classes=["MPTBlock"],
        dtype=torch.float16,
        max_memory=max_memory,
    )

    model = load_checkpoint_and_dispatch(
        model,
        model_dir,
        device_map=device_map,
        no_split_module_classes=["MPTBlock"],
        offload_folder="offload",
        offload_state_dict=True,
    )

    tokenizer = AutoTokenizer.from_pretrained(
        pretrained_model_name_or_path=model_dir,
        trust_remote_code=True,
        padding='left',
        dtype=torch.bfloat16,
        use_fast=True,
    )

    print("model.device", model.device)

    streamer = TextStreamer(tokenizer, skip_prompt=True)
    text_generation_pipeline = pipeline("text-generation",
                                        model=model,
                                        config=config,
                                        tokenizer=tokenizer,
                                        streamer=streamer,
                                        torch_dtype=torch.bfloat16,
                                        device_map=device_map,
                                        )

    return model, tokenizer, text_generation_pipeline


def input_fn(input_data, content_type):
    print("input_data", input_data)
    print("content_type", content_type)

    return json.loads(input_data)


def get_predictions(text_generation_pipeline, tokenizer, prompt, **kwargs):
    if "max_new_tokens" not in kwargs:
        kwargs["max_new_tokens"] = 512

    kwargs.update(
        {
            "do_sample": True,
            "use_cache": True,
            "pad_token_id": tokenizer.eos_token_id,
            "eos_token_id": tokenizer.eos_token_id,
        }
    )

    if "return_full_text" not in kwargs:
        kwargs["return_full_text"] = False

    # Example template
    # template = "<|im_start|>system\n{systemPrompt}<|im_end|>\n<|im_start|>user\n{userPrompt}<|im_end|><|im_start|>assistant\n"
    generated_texts = text_generation_pipeline(prompt, **kwargs)
    predictions = [e[0]["generated_text"] for e in generated_texts]
    return predictions


def predict_fn(data, model_and_tokenizer_and_pipeline):
    # unpack model and tokenizer
    model, tokenizer, text_generation_pipeline = model_and_tokenizer_and_pipeline

    # process input
    inputs = data.pop("inputs", data)
    parameters = data.pop("parameters", {})

    predictions = get_predictions(
        text_generation_pipeline=text_generation_pipeline,
        tokenizer=tokenizer,
        prompt=inputs,
        **parameters,
    )

    print("predictions", predictions)

    return [{"predictions": predictions}]
