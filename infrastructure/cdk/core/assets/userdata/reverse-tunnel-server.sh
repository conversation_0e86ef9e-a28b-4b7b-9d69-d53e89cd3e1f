#!/bin/bash

# Install Chissl
sudo curl -L -o /tmp/chisel.gz "https://static-infra-binaries-public.s3.us-west-2.amazonaws.com/chissl_1.0_linux_amd64.gz"
sudo gunzip /tmp/chisel.gz
sudo chmod +x /tmp/chisel
sudo mv /tmp/chisel /bin/

# Setup the service
cat <<EOT > /etc/systemd/system/chisel.service
[Unit]
Description=Chisel Tunnel Server
After=network.target

[Service]
ExecStart=/bin/chisel server --port 443 --tls-domain tunnel.secops.getunblocked.com --auth machine:AUTH_TOKEN
Restart=always
User=root

[Install]
WantedBy=multi-user.target
EOT

# Launch the service
sudo systemctl daemon-reload
sudo systemctl enable chisel
sudo systemctl start chisel
sudo systemctl status chisel

