{"compilerOptions": {"target": "ES2018", "module": "commonjs", "lib": ["es2018"], "declaration": true, "resolveJsonModule": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "inlineSourceMap": true, "inlineSources": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictPropertyInitialization": false, "typeRoots": ["./node_modules/@types"]}, "exclude": ["node_modules", "cdk.out"]}