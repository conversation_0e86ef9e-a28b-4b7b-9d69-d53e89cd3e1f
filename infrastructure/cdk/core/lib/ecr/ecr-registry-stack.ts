import { Stack, StackProps, RemovalPolicy, Tags } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { SecOpsBuildConfig } from '../build-config';
import { Repository, TagMutability, CfnReplicationConfiguration } from 'aws-cdk-lib/aws-ecr';
import * as iam from 'aws-cdk-lib/aws-iam';

export interface EcrRegistryStackProps extends StackProps {
    secOpsbuildConfig: SecOpsBuildConfig;
}

export class EcrRegistryStack extends Stack {
    constructor(scope: Construct, id: string, props: EcrRegistryStackProps) {
        super(scope, id, props);

        const ecrConfig = props.secOpsbuildConfig.ecr;
        if (ecrConfig.sourceAccount && ecrConfig.ecrReplicationTargets.length > 0) {
            throw new TypeError(`Cannot specify ecrSourceAccount and ecrReplicationTargets together for this stack`);
        }

        new CfnReplicationConfiguration(this, 'CrossAccountECRReplication', {
            replicationConfiguration: {
                rules: [
                    {
                        destinations: ecrConfig.ecrReplicationTargets,
                    },
                ],
            },
        });

        for (const repo of ecrConfig.ecrRepos) {
            const newRepo = new Repository(this, `${repo.name}-ecr-repo`, {
                repositoryName: repo.name,
                removalPolicy: RemovalPolicy.RETAIN,
                imageTagMutability: repo.mutableTags ? TagMutability.MUTABLE : TagMutability.IMMUTABLE,
                lifecycleRules: [{ maxImageCount: repo.maxImageCount }],
            });
            Tags.of(newRepo).add('managed-by', 'cdk');
        }

        // Create deployer role for pushing images
        new iam.Role(this, 'EcrDeployerRole', {
            roleName: 'EcrDeployerRole',
            assumedBy: new iam.AccountPrincipal(props.secOpsbuildConfig.awsEnvAccount.awsOrgRootAccountID),
            description: 'Role used by CI/CD pipeline for pushing images to ECR under sec-ops account',
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonEC2ContainerRegistryPowerUser')],
        });
    }
}
