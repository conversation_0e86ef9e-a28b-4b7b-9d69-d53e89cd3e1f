import { array, JSONObject, optional, required } from 'ts-json-object';

export class TransitGatewayAttachment extends JSON<PERSON>bject {
    @required
    name: string;
    @required
    attachmentId: string;
    @optional([])
    targetAttachmentNames: string[];
}

export class CustomerGateway extends JSONObject {
    @required
    externalIpAddress: string;
    @required
    externalBgpAsn: number;
    @required
    deviceName: string;
}

export class TransitGateway extends J<PERSON>NObject {
    @array(TransitGatewayAttachment)
    transitGatewayAttachments: Array<TransitGatewayAttachment>;
    @array(CustomerGateway)
    customerGateways: Array<CustomerGateway>;
}
