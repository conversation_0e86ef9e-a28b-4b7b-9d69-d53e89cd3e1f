import { JSONObject, optional, required } from 'ts-json-object';
import { EnvironmentSecret } from '../env/config';

export class DockerEcrRepository extends JSONObject {
    @required
    repositoryName: string;
    @required
    tag: string;
}

export class DockerImageAsset extends JSON<PERSON>bject {
    @required
    name: string;
    @optional(undefined)
    image?: string;
    @optional(undefined)
    file?: string;
    @optional(undefined)
    directory?: string;
    @optional('LINUX_AMD64')
    platform: string;
    @optional(undefined)
    buildArgs?: Record<string, string>;
    @optional(undefined)
    secretBuildArgs?: Array<EnvironmentSecret>;
}
