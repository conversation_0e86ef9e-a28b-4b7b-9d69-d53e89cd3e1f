import { Removal<PERSON><PERSON>y, Stack, StackProps, Tags } from 'aws-cdk-lib';
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { BlockDeviceVolume } from "aws-cdk-lib/aws-ec2";
import * as efs from "aws-cdk-lib/aws-efs";
import { ThroughputMode } from "aws-cdk-lib/aws-efs";
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { NetworkTargetGroup, Protocol, SslPolicy, TargetType } from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { InstanceIdTarget } from 'aws-cdk-lib/aws-elasticloadbalancingv2-targets'
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53Targets from 'aws-cdk-lib/aws-route53-targets';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { readFileSync } from "fs";
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { Dns } from '../common/dns/config';

interface AtlassianTestServersStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
}

export interface AtlassianTestServersStackProps extends StackProps {
    buildConfig: AtlassianTestServersStackConfig;
}

export class AtlassianTestServersStack extends Stack {
    readonly host: ec2.Instance;
    readonly snsTopic: sns.Topic;

    constructor(scope: Construct, id: string, props: AtlassianTestServersStackProps) {
        super(scope, id, props);

        // Lookup core VPC
        const vpcId = ssm.StringParameter.valueFromLookup(this, '/network-stack/core-vpc-id');
        const vpc = ec2.Vpc.fromLookup(this, `ImportCoreVPC`, {
            isDefault: false,
            vpcId: vpcId,
        });

        // Look up hosted zone for current environment
        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'Route53Zone', {
            zoneName: props.buildConfig.dns.route53HostedZoneName,
            hostedZoneId: props.buildConfig.dns.route53HostedZoneID,
        });

        // Create Keypair
        const cfnKeyPair = new ec2.CfnKeyPair(this, 'AtlassianTestServersKeypair', {
            keyName: 'AtlassianTestServersKeypair',
        });
        const keypair = ec2.KeyPair.fromKeyPairName(this, 'AtlassianTestServersKeypairObject', cfnKeyPair.keyName);
        keypair.node.addDependency(cfnKeyPair);

        // Instance security group
        const ec2SecurityGroup = new ec2.SecurityGroup(this, 'AtlassianTestServersSG', {
            vpc: vpc,
            allowAllOutbound: true,
            securityGroupName: 'AtlassianTestServersSG',
        });

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(7990),
            'Allows Bitbucket UI port access from nlb'
        );

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(7999),
            'Allows Bitbucket UI port access from nlb'
        );

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(8080),
            'Allows Jira UI port access from nlb'
        );

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(8001),
            'Allows Jira UI port access from nlb'
        );

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(8090),
            'Allows Confluence UI port access from nlb'
        );

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.anyIpv4(),
            ec2.Port.tcp(8091),
            'Allows Confluence UI port access from nlb'
        );

        ec2SecurityGroup.addIngressRule(
            ec2.Peer.ipv4("***********/16"),
            ec2.Port.tcp(22),
            'Allows SSH from VPN subnet'
        );

        // ---------- EFS Setup ----------
        const efsSecurityGroup = new ec2.SecurityGroup(this, 'EfsSecurityGroupAtlassian', {
            vpc: vpc,
            allowAllOutbound: true, // Allow outbound traffic on all ports
            securityGroupName: 'EfsSecurityGroupAtlassian',
        });

        // Add an inbound rule to allow connections on port 2049
        efsSecurityGroup.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(2049), 'Allow NFS Connections');

        const fileSystem = new efs.FileSystem(this, 'AtlassianTestServerVolume', {
            fileSystemName: "AtlassianTestServerVolume",
            vpc: vpc,
            removalPolicy: RemovalPolicy.RETAIN,
            performanceMode: efs.PerformanceMode.GENERAL_PURPOSE, // default
            replicationOverwriteProtection: efs.ReplicationOverwriteProtection.ENABLED, // Set to `DISABLED` if you want to create a read-only file system for use as a replication destination
            throughputMode: ThroughputMode.BURSTING,
            securityGroup: efsSecurityGroup
        });

        // Create mount targets for the file system
        fileSystem.addAccessPoint('MyAccessPoint', {
            createAcl: {
                ownerGid: '1000',
                ownerUid: '1000',
                permissions: '755',
            },
            path: '/', // Example mount point path
            posixUser: {
                gid: '1000',
                uid: '1000',
            }
        });

        this.host = new ec2.Instance(this, 'AtlassianTestServersNode', {
            vpc: vpc,
            associatePublicIpAddress: true,
            securityGroup: ec2SecurityGroup,
            instanceName: 'atlassian-test-server',
            instanceType: ec2.InstanceType.of(ec2.InstanceClass.M5A, ec2.InstanceSize.XLARGE),
            vpcSubnets: {
                subnetType: ec2.SubnetType.PUBLIC,
            },
            machineImage: new ec2.AmazonLinuxImage({
                generation: ec2.AmazonLinuxGeneration.AMAZON_LINUX_2,
            }),
            blockDevices: [
                {
                    deviceName: '/dev/xvda',
                    volume: BlockDeviceVolume.ebs(55, {
                        encrypted: false,
                    }),
                },
            ],
        });
        Tags.of(this.host).add('DrataExclude', 'Internal Dev system with no data or system access');
        fileSystem.connections.allowDefaultPortFrom(this.host)

        // load and add user data script
        const userDataScript = readFileSync('./assets/userdata/atlassian-test-server.sh', 'utf8');
        const finalUserData = userDataScript.replace("EFS_FILE_SYSTEM_ID", fileSystem.fileSystemId);
        this.host.addUserData(finalUserData);

        // add ssh key that we created manually
        this.host.instance.addPropertyOverride('KeyName', cfnKeyPair.keyName);


        const cert = new acm.Certificate(this, `cert-atlassian-test-server`, {
            domainName: `atlassian-test.${props.buildConfig.dns.route53HostedZoneName}`,
            validation: acm.CertificateValidation.fromEmail(),
            subjectAlternativeNames: Array.from([`atlassian-test.${props.buildConfig.dns.route53HostedZoneName}`]),
        });

        const elbSecurityGroup = new ec2.SecurityGroup(this, 'AtlassianTestServersElbSG', {
            vpc: vpc,
            allowAllOutbound: true,
            securityGroupName: 'AtlassianTestServersElbSG',
        });

        const allowedIPs = ['************/32', '************/32'];
        const allowedPorts = [7990, 8080, 8001, 8090, 8091];

        allowedIPs.forEach((ip) => {
            allowedPorts.forEach((port) => {
                elbSecurityGroup.addIngressRule(
                    ec2.Peer.ipv4(ip),
                    ec2.Port.tcp(port),
                    `Allows port ${port} access from ${ip}`
                );
            });
        });


        // Create and configure loadbalancer
        const nlb = new elbv2.NetworkLoadBalancer(this, 'NLB', {
            vpc,
            internetFacing: true,
            securityGroups: [elbSecurityGroup],
            vpcSubnets: { subnetType: ec2.SubnetType.PUBLIC },

        });

        nlb.addListener('Listener', {
            port: 7990,
            protocol: Protocol.TLS,
            certificates: [cert],
            sslPolicy: SslPolicy.RECOMMENDED_TLS,
            defaultTargetGroups: [
                new NetworkTargetGroup(this, "BitbucketUITarget", {
                    port: 7990,
                    protocol: Protocol.TCP,
                    targetType: TargetType.INSTANCE,
                    vpc: vpc,
                    targets: [new InstanceIdTarget(this.host.instanceId, 7990)]
                })
            ]
        });

        nlb.addListener('Jira-1', {
            port: 8080,
            protocol: Protocol.TLS,
            certificates: [cert],
            sslPolicy: SslPolicy.RECOMMENDED_TLS,
            defaultTargetGroups: [
                new NetworkTargetGroup(this, "JiraUITarget-1", {
                    port: 8080,
                    protocol: Protocol.TCP,
                    targetType: TargetType.INSTANCE,
                    vpc: vpc,
                    targets: [new InstanceIdTarget(this.host.instanceId, 8080)]
                })
            ]
        });

        nlb.addListener('ConfluenceListener-1', {
            port: 8090,
            protocol: Protocol.TLS,
            certificates: [cert],
            sslPolicy: SslPolicy.RECOMMENDED_TLS,
            defaultTargetGroups: [
                new NetworkTargetGroup(this, "ConfluenceUITarget-1", {
                    port: 8090,
                    protocol: Protocol.TCP,
                    targetType: TargetType.INSTANCE,
                    vpc: vpc,
                    targets: [new InstanceIdTarget(this.host.instanceId, 8090)]
                })
            ]
        });


        // Create DNS record
        new route53.ARecord(this, 'AtlassianTestServersARecord', {
            recordName: `atlassian-test.${props.buildConfig.dns.route53HostedZoneName}`,
            zone: hostedZone,
            target: route53.RecordTarget.fromAlias(new route53Targets.LoadBalancerTarget(nlb)),
        });
    }
}
