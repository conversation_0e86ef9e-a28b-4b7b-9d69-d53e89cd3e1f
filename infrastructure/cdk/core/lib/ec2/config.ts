import { JSONObject, optional, required } from 'ts-json-object';
import { AutoScalingGroup } from '../common/autoscaling/config';

export class BastionHost extends J<PERSON><PERSON>Object {
    @required
    sshKeyName: string;
    @optional([])
    allowedCIDRs: string[];
}

export class <PERSON>radleBuildCacheNode extends JSONObject {
    @required
    sshKeyName: string;
    @optional([])
    allowedCIDRs: string[];
    @optional(80)
    blockDeviceVolumeSizeGB: number;
}

export class HttpProxy extends J<PERSON>NObject {
    @optional(true)
    enable: boolean;
    @required
    autoScalingGroup: AutoScalingGroup;
    // This is a special case for Dev us-west-2
    // 172.17.x.x clashes with docker internal addresses
    // This helps force the load balancer to a subnet that does not clash!
    @optional([])
    elbCidrRanges: string[];
}
