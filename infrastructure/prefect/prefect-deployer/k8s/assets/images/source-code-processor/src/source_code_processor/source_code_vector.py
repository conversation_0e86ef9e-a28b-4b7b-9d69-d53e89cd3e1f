import base64

from embedding_utils.embedding_generator import Embedding<PERSON><PERSON>ator
from crypto_utils.aes_encryption import AESEncryption
from embedding_utils.embedding_models import Embeddings, EmbeddingType, SafeDenseVector, SafeSparseVector
from logging_utils.unblocked_logger import Unblocked<PERSON>ogger
from partition_utils.code_partition import CodePartition
from vector_store_utils.vector_interface import VectorInterface, VectorDict
from text_utils.compression import Compression

from source_code_processor.embedding_secrets import EMBEDDING_CONTENT_ENCRYPTION_KEY

logger = UnblockedLogger(name=__name__)


class SourceCodeVector(VectorInterface):
    document_id: str
    file_content: str
    file_path: str
    file_sha: str
    start_line: int
    end_line: int
    dense_vector: SafeDenseVector = None
    sparse_vector: SafeSparseVector = None
    content_with_metadata: str

    document_source: str
    document_type: str
    insight_type: str
    installation_id: str
    repo_id: str

    compressor: Compression

    def __init__(
        self,
        rel_filepath: str,
        repo_full_name: str,
        file_metadata: dict[str, str],
        partition: CodePartition,
        document_id: str,
        document_source: str,
        document_type: str,
        insight_type: str,
        installation_id: str,
        repo_id: str,
        embedder: EmbeddingGenerator,
        compressor: Compression = Compression(),
    ):
        # Check the partition is valid
        if not partition.space_compressed_code:
            logger.error(
                "Partition is empty or blank\n"
                + f"Repo: {repo_full_name}\n"
                + f"File path: {rel_filepath}\n"
                + f"Line range: {partition.start_line} to {partition.end_line}\n"
            )
            raise AssertionError("Partition is empty or blank")

        # Add Metadata
        self.document_id = document_id
        self.file_content = f"Repo: {repo_full_name}\n" + f"File path: {rel_filepath}\n\n" + partition.code
        self.file_path = rel_filepath
        self.file_sha = file_metadata.get("hash")
        self.start_line = partition.start_line
        self.end_line = partition.end_line

        # Assemble content for embedding
        self.content_with_metadata = (
            f"Repo: {repo_full_name}\n" + f"File path: {rel_filepath}\n\n" + partition.space_compressed_code
        )

        self.document_source = document_source
        self.document_type = document_type
        self.insight_type = insight_type
        self.installation_id = installation_id
        self.repo_id = repo_id

        self.compressor = compressor

        embeddings = embedder.get_embeddings(
            embedding_type=EmbeddingType.DOCUMENT,
            docs=[self.content_with_metadata],
        )

        self.dense_vector = embeddings.dense_vectors[0]
        self.sparse_vector = embeddings.sparse_vectors[0]

    def get_dense_vector(self) -> SafeDenseVector:
        return self.dense_vector

    def get_sparse_vector(self) -> SafeSparseVector:
        return self.sparse_vector

    def to_dict(self) -> VectorDict:
        data: VectorDict = {
            "id": self.document_id,
            "values": self.dense_vector,
            "metadata": {
                "source": self.document_source,
                "type": self.document_type,
                "insightType": self.insight_type,
                "installation": self.installation_id,
                "group": self.repo_id,
                "contentEnc": self.compress_and_encrypt(self.file_content),
                "filePathEnc": self.compress_and_encrypt(self.file_path),
            },
        }

        sparse_vector = self.sparse_vector

        # Conditionally adding sparse_values to the dictionary
        if sparse_vector is not None:
            data["sparse_values"] = sparse_vector
        else:
            logger.warning(f"Skipping sparse vector upload.")

        return data

    def compress_and_encrypt(self, plaintext: str) -> str:
        compressed_bytes = self.compressor.compress_to_bytes(plaintext)
        encrypted_bytes = AESEncryption.aes_encrypt(
            plaintext=compressed_bytes, secret_key=base64.b64decode(EMBEDDING_CONTENT_ENCRYPTION_KEY)
        )
        return base64.b64encode(encrypted_bytes).decode("utf-8")
