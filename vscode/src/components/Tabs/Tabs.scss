@use 'layout' as *;
@use 'misc' as *;

.tabs {
    &.tabs__primary {
        .tabs__headers {
            .tab_header {
                color: var(--vscode-tab-inactiveForeground);
                background-color: var(--vscode-tab-inactiveBackground);
                text-align: center;
                border-bottom: $border-width $border-style var(--vscode-terminal-border);

                &.tab_header__selected {
                    color: var(--vscode-tab-activeForeground);
                    background-color: var(--vscode-tab-activeBackground);
                    border-top: $border-width $border-style var(--vscode-tab-activeBorderTop);
                    border-bottom: $border-width $border-style transparent;
                    border-left: $border-width $border-style var(--vscode-terminal-border);
                    border-right: $border-width $border-style var(--vscode-terminal-border);

                    &:focus-visible {
                        outline: none;
                    }

                    &:first-of-type {
                        border-left: 0;
                    }

                    &:last-of-type {
                        border-right: 0;
                    }
                }
            }

            &.tabs__headers__regular {
                .tab_header {
                    padding: $spacer-8 0 $spacer-10;
                }
            }

            &.tabs__headers__small {
                .tab_header {
                    padding: $spacer-4 0 $spacer-6;
                }
            }
        }
    }

    &.tabs__settings {
        .tabs__headers {
            border-color: var(--vscode-tab-inactiveBackground);
            background: var(--vscode-tab-inactiveBackground);
        }

        .tab_header {
            color: var(--vscode-tab-inactiveForeground);
            &:not(:last-of-type) {
                &::after {
                    background: var(--vscode-tab-inactiveBackground);
                }
            }

            &.tab_header__selected {
                color: var(--vscode-tab-activeForeground);
                &::before {
                    background: var(--vscode-tab-activeBackground);
                    border: $border-width $border-style var(--vscode-tab-activeBorder);
                }
            }
        }
    }
}
