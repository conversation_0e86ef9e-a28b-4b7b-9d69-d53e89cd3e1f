import { TextEditor, window, workspace } from 'vscode';

import { environment } from '@config';
import { ExtensionContextVariableNames, setContextVariable } from '@extension';
import { RepoStore } from '@repo';
import { createValueStream, ValueStream } from '@shared/stores/ValueStream';
import { Debouncer, SetUtils } from '@shared-web-utils';
import { LocalStorage } from '@utils';

import { TextEditorSourceMarks } from './TextEditorSourceMarks';

/**
 * Synchronizes sourcemarks between the source mark engine and text editors.
 */
export class TextEditorSourceMarkManager {
    private static instance_: TextEditorSourceMarkManager | undefined;

    // Singleton instance
    static get instance(): TextEditorSourceMarkManager {
        if (!this.instance_) {
            this.instance_ = new TextEditorSourceMarkManager();
        }

        return this.instance_;
    }

    // Maps every text document with a visible viewer to its set of source marks
    private editorSourceMarks = new Map<string, TextEditorSourceMarks>();

    // The current set of visible and active files
    private activeFiles = new Set<string>();

    // The current set of text editors
    private textEditors = new Set<TextEditor>();

    // Debounce updates to the active/visible editors, so we update state consistently
    private textEditorDebouncer = new Debouncer<void>(10, () => this.updateTextEditors());

    private static HIDDEN_SOURCE_MARK_KEY = `${environment.keyPrefix}_UB_HIDDEN_SOURCE_MARKS`;
    private sourceMarkVisibilityStream: ValueStream<boolean> = createValueStream();

    constructor() {
        window.onDidChangeVisibleTextEditors(() => this.textEditorDebouncer.trigger());

        workspace.onDidChangeTextDocument((event) => {
            const editorSourceMarks = this.editorSourceMarks.get(event.document.fileName);
            editorSourceMarks?.onFileEdited(event);
        });

        this.updateTextEditors();
        const isVisible = !LocalStorage.getGlobalValue<boolean>(TextEditorSourceMarkManager.HIDDEN_SOURCE_MARK_KEY);
        this.updateSourceMarkVisibility(isVisible);

        // TBD: Determine when to unsubscribe, unclear if we need to (as this is workspace-duration)
        RepoStore.instance.stream.subscribe({
            next: () => {
                this.onReposChanged();
            },
        });
    }

    async startup(): Promise<void> {
        // Disabled for now
        // await this.monitor.startup();
    }

    private onReposChanged() {
        // This is very heavyweight, but should really only happen once after login:
        // After we log in and have resolved repos, all our existing sourcemarks are going
        // to potentially be invalid, so we will destroy and recreate everything.
        // This could be made more efficient by only recreating the documents that are
        // within the changed repos.
        this.textEditors.clear();
        this.activeFiles.clear();

        for (const [, editorSourceMark] of this.editorSourceMarks) {
            editorSourceMark.dispose();
        }
        this.editorSourceMarks.clear();

        this.updateTextEditors();
    }

    private updateTextEditors() {
        const visibleTextEditors = window.visibleTextEditors.filter((editor) => editor.document.uri.scheme === 'file');

        const currentFiles = new Set(visibleTextEditors.map((editor) => editor.document.fileName));

        const addedFiles = SetUtils.difference(currentFiles, this.activeFiles);
        const removedFiles = SetUtils.difference(this.activeFiles, currentFiles);
        this.activeFiles = currentFiles;

        // Create and remove TextEditorSourceMarks for all active/inactive files
        for (const filePath of addedFiles) {
            this.editorSourceMarks.set(
                filePath,
                new TextEditorSourceMarks(filePath, this.sourceMarkVisibilityStream.stream)
            );
        }

        for (const filePath of removedFiles) {
            this.editorSourceMarks.get(filePath)?.dispose();
            this.editorSourceMarks.delete(filePath);
        }

        // Calculate whichever editors are added and removed
        const currentEditors = new Set(visibleTextEditors);

        const addedEditor = SetUtils.difference(currentEditors, this.textEditors);
        const removedEditor = SetUtils.difference(this.textEditors, currentEditors);
        this.textEditors = currentEditors;

        for (const editor of addedEditor) {
            this.editorSourceMarks.get(editor.document.fileName)?.addEditor(editor);
        }

        for (const editor of removedEditor) {
            this.editorSourceMarks.get(editor.document.fileName)?.removeEditor(editor);
        }
    }

    updateSourceMarkVisibility(visible: boolean) {
        this.sourceMarkVisibilityStream.updateValue(visible);
        LocalStorage.setGlobalValue(TextEditorSourceMarkManager.HIDDEN_SOURCE_MARK_KEY, !visible);
        setContextVariable(ExtensionContextVariableNames.HiddenSourceMarks, !visible);
    }
}
