#!/bin/sh
set -e

echo "$MACOS_CERTIFICATE" | base64 --decode > developerId.p12
echo "$ASC_AUTHKEY" | base64 --decode > AuthKey.p8

security create-keychain -p github-actions build.keychain
security set-keychain-settings -lut 21600 build.keychain # Set keychain lock timeout to 6 hours
security unlock-keychain -p github-actions build.keychain
security import developerId.p12 -k build.keychain -f pkcs12 -A -T /usr/bin/codesign -T /usr/bin/security -P "$MACOS_CERTIFICATE_PWD"
security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k github-actions build.keychain
security list-keychains -d user -s build.keychain login.keychain
security default-keychain -s build.keychain
security find-identity -v

UNBLOCKED_PKG_ARM=Unblocked\ arm.pkg

pkgbuild --scripts ./desktop/scripts --ownership preserve --install-location /Applications --component ./desktop/arm/Unblocked.app/ ./desktop.pkg

productbuild --distribution ./DesktopDistribution.xml --resources ./Resources --sign "Developer ID Installer: Next Chapter Software Inc (2FNXZ6K9M4)" --package-path . "./$UNBLOCKED_PKG_ARM"
# TODO need to edit Distribution.xml to update the com.nextchaptersoftware.UnblockedHub version

xcrun notarytool submit "./$UNBLOCKED_PKG_ARM" --key ./AuthKey.p8 --key-id $ASC_KEY_ID --issuer $ASC_KEY_ISSUER --wait
xcrun stapler staple "./$UNBLOCKED_PKG_ARM"

# Delete the keychain and the provisioning profile
security delete-keychain build.keychain
rm -f AuthKey.p8
rm -f developerId.p12
