/* ktlint-disable no-wildcard-imports */
import com.google.protobuf.gradle.*

plugins {
    kotlin("jvm")
    id("com.google.protobuf")
}

dependencies {
    api(libs.bundles.protobuf)
}

sourceSets {
    main {
        proto {
            srcDir("src/main/resources/protos")
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protobufVersion.get()}"
    }
    plugins {
        id("ts_proto") {
            path = "$projectDir/../node_modules/ts-proto/protoc-gen-ts_proto"
        }
    }

    generateProtoTasks {
        all().forEach { task ->
            task.dependsOn(":installNodeDependencies")
            task.plugins {
                id("ts_proto") {
                    option("esModuleInterop=true")
                    option("useOptionals=messages")
                    option("oneof=unions")
                    option("outputServices=grpc-js")
                }
                task.builtins {
                    id("kotlin")
                }
            }
        }
    }
}
