type: object
properties:
  id:
    $ref: "./ApiResourceId.yml"
  messageId:
    $ref: "./ApiResourceId.yml"
  threadId:
    $ref: "./ApiResourceId.yml"
  messageReference:
    $ref: "./MessageReference.yml"
  pullRequestData:
    $ref: "./PullRequestReferenceData.yml"
  archivedAt:
    type: string
    format: date-time
  archivedBy:
    $ref: "./ApiResourceId.yml"
  archiveReason:
    $ref: "./ArchiveReasonType.yml"
  archiveComment:
    type: string
  cursor:
    $ref: "./Cursor.yml"
  capabilities:
    $ref: "./ArchivedReferenceCapabilities.yml"
required:
  - id
  - archivedAt
  - capabilities
