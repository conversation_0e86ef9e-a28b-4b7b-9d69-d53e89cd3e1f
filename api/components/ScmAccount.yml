type: object
description: |
  Represents a Source Control Management (SCM) Account.
  This is a user or organization account in a SCM system.
properties:
  allReposAccessible:
    description: |
      Unblocked has access to all repos in the SCM Account.
    type: boolean
  avatarUrl:
    type: string
    description: |
      URL to the avatar image or a data URL.
  displayName:
    type: string
    description: |
      Human readable name of the SCM Account.
    example: Eng
  enterpriseProviderId:
    $ref: "./ApiResourceId.yml"
  externalId:
    type: string
    description: |
      External SCM account ID.
  externalInstallationId:
    type: string
    description: |
      External SCM installation ID.
  fullPath:
    type: string
    description: |
      Fully qualified path.
      Typically the slug or login of the installation.
      In some cases this includes sub-group hierarchy.
    example: stripe/eng
  htmlUrl:
    type: string
  isPersonalAccount:
    type: boolean
  provider:
    $ref: "./Provider.yml"
  needsCiScopes: # TODO remove
    description: |
      True if the SCM Account needs to accept new permissions for CI.
      When true, the user must visit the `appPermissionsUrl` to accept new permissions.
    type: boolean
  appPermissionsUrl:
    description: |
      URL to the page where the user can accept new permissions.
    type: string
  isCiEligible: # TODO remove
    deprecated: true
    description: Deprecated. Do not use.
    type: boolean
required:
  - allReposAccessible
  - avatarUrl
  - displayName
  - externalId
  - externalInstallationId
  - fullPath
  - htmlUrl
  - isCiEligible # TODO remove
  - isPersonalAccount
  - needsCiScopes # TODO remove
  - provider
