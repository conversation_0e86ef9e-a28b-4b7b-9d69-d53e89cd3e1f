type: object
properties:
  thread:
    $ref: "./Thread.yml"
  repoId:
    $ref: "./ApiResourceId.yml"
  messages:
    type: array
    items:
      $ref: "./Message.yml"
    description: |
      All messages in the thread ordered descending by `createdAt`.
  mentions:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
    description: |
      List of TeamMember IDs that have been mentioned in the thread.
  participants:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
    description: |
      List of TeamMember IDs that are participating in the thread.
  modifiedAt:
    type: integer
    format: int64
    description: |
      Timestamp in microseconds of the last time that components of the thread info was modified.
  mark:
    $ref: "./Mark.yml"
  pullRequest:
    $ref: "./PullRequest.yml"
  pullRequests:
    type: array
    items:
      $ref: "./PullRequest.yml"
    description: |
      List of pull requests that are associated with the thread.
  # Unread state for the current user.
  # This is optional; when null, implies that the thread is read by the current user.
  unread:
    $ref: "./Unread.yml"
  slack:
    $ref: "./ThreadSlackInfo.yml"
  cursor:
    $ref: "./Cursor.yml"
  topics:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
    description: |
      List of topic IDs that are associated with the thread.
  experts:
    type: array
    items:
      $ref: "./ApiResourceId.yml"
    description: |
      List of TeamMember IDs that are experts in the thread.
  capabilities:
    $ref: "./ThreadCapabilities.yml"
  sensitiveDataSources:
    type: array
    items:
      $ref: ./Provider.yml
    description: >
      Sources of sensitive data which updates the thread privacy state
required:
  - thread
  - messages
  - participants
  - modifiedAt
  - capabilities
