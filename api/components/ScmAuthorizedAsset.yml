type: object
properties:
  assetUrl:
    description: Original URL of the asset.
    type: string
  authorizedAssetUrl:
    description: |
      URL of the asset that is authorized to be downloaded.
    type: string
  expiresAt:
    description: |
      Timestamp in seconds since the Unix epoch indicating when the `authorizedAssetUrl` authorization expires.
    type: integer
required:
  - assetUrl
  - authorizedAssetUrl
  - expiresAt
