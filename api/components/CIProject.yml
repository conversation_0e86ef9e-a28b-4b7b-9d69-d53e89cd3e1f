type: object
properties:
  projectSlug:
    description: >
      The project slug.
    type: string
  projectName:
    type: string
    description: >
      The project display name.
  projectUrl:
    type: string
    description: >
      The project web URL in the external CI system provider.
  isSelected:
    type: boolean
    description: >
      True if the project has been selected for CI in Unblocked.
required:
  - projectSlug
  - projectName
  - projectUrl
  - isSelected
