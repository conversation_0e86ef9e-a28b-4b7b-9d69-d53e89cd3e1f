type: object
description: >
  Represents a connection between an Identity and a Provider.
  This connection is used to make Provider specific resource requests on behalf of the Identity.

  The `enterpriseId` is only present if the Provider is an Enterprise Provider.
properties:
  connectingIdentityId:
    $ref: "./ApiResourceId.yml"
  identityDisplayName:
    type: string
  identityAvatarUrl:
    type: string
  identityHtmlUrl:
    type: string
  provider:
    $ref: "./Provider.yml"
  enterpriseId:
    $ref: "./ApiResourceId.yml"
required:
  - identityId
  - identityDisplayName
  - identityAvatarUrl
  - identityHtmlUrl
  - provider
