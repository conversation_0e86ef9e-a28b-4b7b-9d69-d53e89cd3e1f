package com.nextchaptersoftware.jetbrains.util

import com.intellij.codeInsight.highlighting.TooltipLinkHandler
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.nextchaptersoftware.common.ideagent.agentCommandOpenThread
import com.nextchaptersoftware.common.ideagent.agentCommandRequest
import com.nextchaptersoftware.jetbrains.services.ProjectService

@Suppress("MagicNumber")
class UnblockedTooltipLinkHandler : TooltipLinkHandler() {
    override fun handleLink(link: String, editor: Editor): Boolean {
        val project = editor.project ?: return false

        val linkItems = link.split("/")
        return when (linkItems.first()) {
            "thread" -> this.handleThread(project, linkItems)
            else -> false
        }
    }

    private fun handleThread(project: Project, items: List<String>): Boolean {
        if (items.size < 4) {
            return false
        }

        val command = agentCommandRequest {
            openThread = agentCommandOpenThread {
                teamId = items[1]
                repoId = items[2]
                threadId = items[3]
                shouldScroll = false
            }
        }

        project.serviceOrThrow<ProjectService>().launchWithIDEAgent { agent ->
            agent.runAgentCommand(command)
        }
        return true
    }
}
