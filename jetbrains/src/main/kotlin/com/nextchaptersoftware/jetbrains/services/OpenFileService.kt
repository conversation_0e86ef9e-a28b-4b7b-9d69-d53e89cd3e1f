package com.nextchaptersoftware.jetbrains.services

import com.intellij.openapi.Disposable
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.diagnostic.logger
import com.intellij.openapi.editor.LogicalPosition
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.editor.colors.EditorColors
import com.intellij.openapi.editor.markup.EffectType
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.editor.markup.HighlighterTargetArea
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.OpenFileDescriptor
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.LocalFileSystem
import com.intellij.openapi.vfs.VirtualFile
import com.nextchaptersoftware.common.ideagent.FocusRange
import com.nextchaptersoftware.common.ideagent.openFileResponse
import com.nextchaptersoftware.jetbrains.util.ProjectCoroutineScope

private val LOG = logger<OpenFileService>()

@Service(Service.Level.PROJECT)
class OpenFileService(override val project: Project) : DumbAware, Disposable, ProjectCoroutineScope {
    init {
        LOG.info("OpenFileService initialized")
    }

    private val ideAgentJob = launchWithUpdatingIDEAgent {
        it.openFile(openFileResponse { }).collect { openFileRequest ->
            openFile(openFileRequest.filePath, openFileRequest.range)
        }
    }

    private fun openFile(filePath: String, focusRange: FocusRange?, readOnly: Boolean = false) {
        when (val file = LocalFileSystem.getInstance().findFileByPath(filePath)) {
            null -> println("Could not open file")
            else -> openFile(file, focusRange, readOnly)
        }
    }

    fun isFileOpen(file: VirtualFile): Boolean {
        return FileEditorManager.getInstance(project).isFileOpen(file)
    }

    fun openFile(file: VirtualFile, focusRange: FocusRange?, readOnly: Boolean = false) {
        ApplicationManager.getApplication().invokeLater {
            val fileDescriptor = OpenFileDescriptor(project, file)
            val editor =
                FileEditorManager.getInstance(project).openTextEditor(fileDescriptor, true)
            if (editor == null) {
                println("Failed to open ${file.path}")
            } else {
                println("success open ${file.path}")
                editor.document.setReadOnly(readOnly)
                focusRange?.let {
                    if (it.shouldScroll) {
                        editor.caretModel.moveToLogicalPosition(LogicalPosition(it.startLine, 0))
                        editor.scrollingModel.scrollToCaret(ScrollType.CENTER)
                    }

                    if (it.shouldHighlight) {
                        val document = editor.document
                        val lineStartOffset = document.getLineStartOffset(it.startLine)
                        val lineEndOffset = document.getLineEndOffset(it.endLine)

                        editor.markupModel.addRangeHighlighter(
                            lineStartOffset,
                            lineEndOffset,
                            HighlighterLayer.ADDITIONAL_SYNTAX,
                            TextAttributes(
                                null,
                                null,
                                editor.colorsScheme.getColor(EditorColors.SELECTION_BACKGROUND_COLOR),
                                EffectType.SEARCH_MATCH,
                                0,
                            ),
                            HighlighterTargetArea.LINES_IN_RANGE,
                        )
                    }
                }
            }
        }
    }

    override fun dispose() {
        LOG.info("OpenFileService disposed")
        ideAgentJob?.cancel()
    }
}
