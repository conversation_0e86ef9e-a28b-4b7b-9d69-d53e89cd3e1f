package com.nextchaptersoftware.jetbrains.agent

import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.nextchaptersoftware.common.client.ClientType
import com.nextchaptersoftware.common.ideagent.HeartbeatRequest
import com.nextchaptersoftware.common.ideagent.HeartbeatState
import com.nextchaptersoftware.common.ideagent.IDEAgentServiceGrpcKt
import com.nextchaptersoftware.common.ideagent.ThemeMode
import com.nextchaptersoftware.common.ideagent.heartbeatRequest
import com.nextchaptersoftware.common.ideagent.initializeRequest
import com.nextchaptersoftware.jetbrains.util.NodeRunner
import com.nextchaptersoftware.jetbrains.webview.WebViewTheme
import io.grpc.ManagedChannelBuilder
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout

const val ANDROID_STUDIO_PREFIX = "AndroidStudio"

private val LOG = Logger.getInstance(IDEAgentProcess::class.java)

@Suppress("TooGenericExceptionCaught", "MagicNumber")
class IDEAgentProcess(
    val process: Process,
    val port: Int,
    val stub: IDEAgentServiceGrpcKt.IDEAgentServiceCoroutineStub,
    val coroutineScope: CoroutineScope,
    private val heartbeatFlow: MutableSharedFlow<HeartbeatRequest>,
    private val heartbeatJob: Job,
) {
    companion object {

        suspend fun create(project: Project): IDEAgentProcess {
            val debugAgent = System.getenv("unblockedDebugAgent")?.toBoolean() ?: false

            val runner = NodeRunner("extension.js", debugProcess = debugAgent)
            val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

            val agentStdOut = runner.stdout
                .bufferedReader()
                .lineSequence()
                .asFlow()
                .shareIn(coroutineScope, SharingStarted.Eagerly, 1)

            // Start the agent running
            runner.run()

            // Read in first line from stdout (port)
            val port = withTimeout(10000) {
                agentStdOut
                    .first { it.startsWith("Agent at port ") }
                    .removePrefix("Agent at port ")
                    .toInt()
            }
            val channel = ManagedChannelBuilder.forAddress("localhost", port).usePlaintext().build()
            val stub = IDEAgentServiceGrpcKt.IDEAgentServiceCoroutineStub(channel)

            // Launch a sub-task to heartbeat
            val heartbeatFlow = MutableSharedFlow<HeartbeatRequest>()
            val heartbeatJob = coroutineScope.launch {
                while (true) {
                    heartbeatFlow.emit(heartbeatRequest { state = HeartbeatState.Running })
                    delay(500)
                }
            }

            coroutineScope.launch {
                // Cause heartbeats to flow both ways
                stub.heartbeat(heartbeatFlow).collect { }
            }

            // Initialize the agent
            withTimeout(10000) {
                LOG.info("Sending initialization request")
                stub.initialize(
                    initializeRequest {
                        clientType = ClientType.INTELLIJ
                        themeMode = if (WebViewTheme.isDarkMode()) ThemeMode.Dark else ThemeMode.Light
                        name = project.name
                    },
                )
            }

            return IDEAgentProcess(runner.process, port, stub, coroutineScope, heartbeatFlow, heartbeatJob)
        }
    }

    suspend fun delayUntilClosed() = suspendCoroutine<Unit> { continuation ->
        LOG.info("Setting up delay until closed")
        this.process.onExit().thenAccept {
            val exitValue = process.exitValue()
            LOG.info("Cancelling coroutine scope with exit value $exitValue")
            val errorMessage = process.errorStream.bufferedReader().readText()
            if (errorMessage.isNotEmpty()) {
                LOG.error("Agent Process exited with error: $errorMessage")
            }

            LOG.info("Cancelling corouting scope")
            this.coroutineScope.cancel()
            LOG.info("continuing from delay")
            continuation.resume(Unit)
        }
    }

    // Shuts down the agent
    suspend fun shutdown() {
        this.heartbeatJob.cancel()
        heartbeatFlow.emit(heartbeatRequest { state = HeartbeatState.Shutdown })
        this.process.destroy()
    }
}
