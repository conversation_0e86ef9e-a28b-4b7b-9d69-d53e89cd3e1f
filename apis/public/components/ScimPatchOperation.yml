type: object

description: |
  A SCIM Patch Operation as defined in [RFC 7644](https://tools.ietf.org/html/rfc7644#section-3.5.2).
  This object is used to represent a single patch operation in a SCIM patch request.

  ### `op`
  The "op" attribute is a String containing the operation to be performed. The following operations are defined:
  - "add": The "add" operation is used to add a new attribute value to an existing resource.
  - "remove": The "remove" operation is used to remove a value from a multi-valued attribute.
  - "replace": The "replace" operation is used to replace the value of a pre-existing attribute with a new value.

  ### `path`
  The "path" attribute value is a String containing an attribute path describing the target of the operation.

  ### `value`
  The "value" attribute contains the attribute value to be used in the operation.
  The attribute value is OPTIONAL for "remove" operations.
  The attribute value is REQUIRED for "add" and "replace" operations.
additionalProperties: true
