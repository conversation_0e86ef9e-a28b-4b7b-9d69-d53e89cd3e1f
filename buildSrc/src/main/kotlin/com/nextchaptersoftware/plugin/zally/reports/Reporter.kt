package com.nextchaptersoftware.plugin.zally.reports

import com.diogonunes.jcolor.Ansi.colorize
import com.diogonunes.jcolor.Attribute.BOLD
import com.diogonunes.jcolor.Attribute.BRIGHT_RED_TEXT
import com.diogonunes.jcolor.Attribute.RED_TEXT
import com.nextchaptersoftware.plugin.zally.internal.ZallyViolationRules
import java.nio.file.Files
import java.nio.file.Path
import org.zalando.zally.core.Result

interface Reporter {
    val rules: ZallyViolationRules
    val config: ZallyReportConfig

    @Suppress("TooGenericExceptionCaught")
    fun write(violations: List<Result>, filePath: Path) {
        try {
            val reportData = render(violations)
            if (reportData != null) {
                filePath.parent?.let { Files.createDirectories(it) }
                Files.write(filePath, reportData.toByteArray())
                println(
                    colorize(
                        "The violation report can be found at ${filePath.toAbsolutePath()}",
                        RED_TEXT(),
                        BOLD()
                    )
                )
            }
        } catch (e: Exception) {
            println(
                colorize(
                    """
                    "could write violation report ${filePath.fileName} 
                    at ${filePath.toAbsolutePath()}
                    ,error=${e.message}"    
                    """.trimIndent(),
                    BOLD(), BRIGHT_RED_TEXT()
                )
            )
        }
    }

    fun render(violations: List<Result>): String?
}
