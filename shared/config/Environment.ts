import fs from 'fs';
import os from 'os';
import path from 'path';
import { z } from 'zod';

import { HomeAppConfigFolder, SystemAppConfigFolder } from './AppConfigPaths';
import { UnblockedEnvProperty } from './UnblockedEnvProperty';

declare global {
    // eslint-disable-next-line no-var
    var unblockedEnv: string | undefined;
}

/**
 * Runtime schema that mirrors the compile-time **Environment** shape,
 * including in-line comments for quick documentation / IDE hover help.
 *
 * If you prefer to derive the TS type from the schema instead of keeping
 * a separate interface, just do:
 *
 *   export type Environment = z.infer<typeof EnvironmentSchema>;
 */
export const EnvironmentSchema = z
    .object({
        // Env name
        name: z.string(),

        // CloudFront asset URL
        cfAssetsBaseUrl: z.string().optional(),

        // Base URL for API requests
        apiServiceBaseUrl: z.string(),

        // Base URL for dashboard web UI
        webDashboardBaseUrl: z.string(),

        // Base URL for landing page UI
        landingPageBaseUrl: z.string(),

        // Router base name
        dashboardBaseName: z.string(),

        // Environment-specific prefix used for key values (tokens, settings, etc.)
        keyPrefix: z.string(),

        // FIXME: REMOVE AND USE INTEGRATION URL INSTEAD
        githubInstallUrl: z.string(),

        vscodePluginId: z.string(),

        jetbrainsPluginId: z.string(),

        jetbrainsPluginName: z.string(),

        // Login redirect URL override
        loginRedirectUrl: z.string().optional(),

        // Login Manifest redirect URL override
        loginManifestRedirectUrl: z.string().optional(),

        // Intercom app id
        intercomAppId: z.string().optional(),

        // Google tracking info
        googleTrackingInfo: z
            .object({
                // Google Analytics tag ID
                gtagId: z.string(),
                // Google Tag Manager container ID
                googleTagManagerId: z.string(),
            })
            .optional(),

        // Umami website ID / script info
        umamiScriptInfo: z
            .object({
                scriptUrl: z.string(),
                websiteId: z.string(),
            })
            .optional(),

        // Stripe publishable key
        stripePublishableKey: z.string().optional(),

        // Includes Get Started
        includeGetStarted: z.boolean().optional(),
    })
    .strict();

export type Environment = z.infer<typeof EnvironmentSchema>;

const PartialEnvironmentSchema = EnvironmentSchema.partial();
type PartialEnvironment = z.infer<typeof PartialEnvironmentSchema>;

const BaseProdEnvironment: Environment = {
    name: 'prod',
    cfAssetsBaseUrl: 'https://getunblocked.com/assets',
    apiServiceBaseUrl: 'https://getunblocked.com/api',
    webDashboardBaseUrl: 'https://getunblocked.com/dashboard',
    landingPageBaseUrl: 'https://getunblocked.com',
    dashboardBaseName: '/dashboard',
    keyPrefix: '',
    githubInstallUrl: 'https://github.com/apps/un-blocked/installations/new',
    vscodePluginId: 'nextchaptersoftware.unblocked-vscode',
    jetbrainsPluginId: 'com.nextchaptersoftware.unblocked-jetbrains',
    jetbrainsPluginName: 'unblocked-jetbrains',
};

const ProdEnvironment: Environment = {
    ...BaseProdEnvironment,
    intercomAppId: 'crhakcyc',
    googleTrackingInfo: {
        gtagId: 'G-90ZWG7J7C3',
        googleTagManagerId: 'GTM-KRHSC7M9',
    },
    umamiScriptInfo: {
        scriptUrl: 'https://cloud.umami.is/script.js',
        websiteId: 'eff3efd2-12f4-458b-8cf0-5fe9d19309d2',
    },
    stripePublishableKey:
        'pk_live_51PsWxW04IOT1VvLMySffeaAiGbCrFK3MHwPk2k3j9vGlY7aD4N5QM2J5gyAa6s0OP697zjb9ZEGZjpJ8xFIDz4Rt00YNB9aBqn',
    includeGetStarted: true,
};

const DevEnvironment: Environment = {
    name: 'dev',
    cfAssetsBaseUrl: 'https://dev.getunblocked.com/assets',
    apiServiceBaseUrl: 'https://dev.getunblocked.com/api',
    webDashboardBaseUrl: 'https://dev.getunblocked.com/dashboard',
    landingPageBaseUrl: 'https://dev.getunblocked.com',
    dashboardBaseName: '/dashboard',
    keyPrefix: 'dev-',
    intercomAppId: 'crhakcyc',
    umamiScriptInfo: {
        scriptUrl: 'https://cloud.umami.is/script.js',
        websiteId: '679e970c-3472-4110-ae44-e3ca34532a90',
    },
    stripePublishableKey:
        'pk_test_51PsWxW04IOT1VvLM0kKD9d9oy77dDO62REi9ze3uXFQoCdwD8UqKt6yZkfEADGTJiJPUJMExpW4U7Uwgx4vKZeqO00tS8fQoHk',
    githubInstallUrl: 'https://github.com/apps/dev-un-blocked/installations/new',
    vscodePluginId: 'nextchaptersoftware.unblocked-vscode',
    jetbrainsPluginId: 'com.nextchaptersoftware.unblocked-jetbrains',
    jetbrainsPluginName: 'unblocked-jetbrains',
    includeGetStarted: true,
};

const LocalEnvironment: Environment = {
    name: 'local',
    apiServiceBaseUrl: 'http://localhost.proxyman.io:8080/api',
    webDashboardBaseUrl: 'http://localhost.proxyman.io:9000',
    landingPageBaseUrl: 'http://localhost.proxyman.io:9001',
    dashboardBaseName: '/',
    keyPrefix: 'local-',
    intercomAppId: 'j9x36zr1',
    stripePublishableKey:
        'pk_test_51PsWxW04IOT1VvLM0kKD9d9oy77dDO62REi9ze3uXFQoCdwD8UqKt6yZkfEADGTJiJPUJMExpW4U7Uwgx4vKZeqO00tS8fQoHk',
    githubInstallUrl: 'https://github.com/apps/local-un-blocked/installations/new',
    vscodePluginId: 'nextchaptersoftware.unblocked-vscode',
    jetbrainsPluginId: 'com.nextchaptersoftware.unblocked-jetbrains',
    jetbrainsPluginName: 'unblocked-jetbrains',
    includeGetStarted: true,
};

const OnPremEnvironment: Environment = {
    ...BaseProdEnvironment,
    name: 'onprem',
    vscodePluginId: 'nextchaptersoftware.unblocked-vscode-onprem',
    jetbrainsPluginId: 'com.nextchaptersoftware.unblocked-jetbrains-onprem',
    jetbrainsPluginName: 'unblocked-jetbrains-onprem',
};

// Regex to match dashboard path root -- either '/dashboard' or '/dashboard/*'
const DashboardRootRegex = /^\/dashboard(\/.*)?$/;

const allEnvironments = [ProdEnvironment, DevEnvironment, LocalEnvironment];

function getEnvironmentFromString(env?: string): Environment | undefined {
    switch (env) {
        case 'dev':
            return DevEnvironment;

        case 'local':
            return LocalEnvironment;

        case 'prod':
            return ProdEnvironment;

        case 'onprem':
            return OnPremEnvironment;
    }

    return undefined;
}

/**
 * Attempts to read and parse an environment configuration from a specified path
 */
function readEnvironmentConfig(configPath: string): PartialEnvironment | undefined {
    try {
        const configStr = fs.readFileSync(configPath, 'utf-8');
        const jsonContent = JSON.parse(configStr);
        return PartialEnvironmentSchema.parse(jsonContent);
    } catch (error) {
        console.debug(`Error reading environment config from ${configPath}`, error);
        return undefined;
    }
}

/**
 * Gets partial environment configuration from available config files
 * Prioritizes home directory config over system config
 */
function getPartialEnvironmentFromConfig(): PartialEnvironment | undefined {
    // Check configurations in priority order
    return (
        readEnvironmentConfig(path.join(HomeAppConfigFolder(), 'config')) ||
        readEnvironmentConfig(path.join(SystemAppConfigFolder(), 'config'))
    );
}

function hasValidFs(): boolean {
    return (
        typeof fs !== 'undefined' &&
        typeof fs.readFileSync !== 'undefined' &&
        typeof os !== 'undefined' &&
        typeof os.homedir !== 'undefined' &&
        typeof path !== 'undefined' &&
        typeof path.join !== 'undefined'
    );
}

let env: Environment | undefined;

// #1: Get a pre-baked global javascript variable
// This is generally used by IDE webviews, the parent IDE app provides the environment directly though
// this global variable.  This is the highest priority because webviews should always use the same env
// as their parent extension.
if (globalThis[UnblockedEnvProperty]) {
    env = getEnvironmentFromString(globalThis[UnblockedEnvProperty]);
    if (env) {
        console.debug(`Using environment ${env.name} from unblockedEnv`);
    }
}

// #2: Get environment from the webpack build script -- this is set for local and dev builds
// This is how the IDE "Run on <env>" tasks can produce builds for a particular environment
if (!env && typeof ENVIRONMENT !== 'undefined') {
    env = getEnvironmentFromString(ENVIRONMENT);
    if (env) {
        console.debug(`Using environment ${env.name} from build scripts`);
    }
}

// #3: Get environment from the JOB_CONTROLLER_ENV environment variable
// This is used when running code in a cloud environment (source code agent)
if (!env && typeof process !== 'undefined') {
    env = getEnvironmentFromString(process?.env?.JOB_CONTROLLER_ENV);
    if (env) {
        console.debug(`Using environment ${env.name} from JOB_CONTROLLER_ENV environment variable`);
    }
}

// #4: Check the hub environment file
// This is how developers change their local env for testing, but is only valid for node environments
if (!env && hasValidFs()) {
    try {
        const envStr = fs.readFileSync(path.join(HomeAppConfigFolder(), 'env'), 'utf-8').trim();
        env = getEnvironmentFromString(envStr);
        if (env) {
            console.debug(`Using environment ${env.name} from environment file`);
        }
    } catch {}
}

// #4: Check the hub config file
// This adds support for onprem environment overrides on top of the prod environment
if (env && env.name === 'onprem' && hasValidFs()) {
    try {
        const partialConfig = getPartialEnvironmentFromConfig();

        if (partialConfig) {
            env = { ...env, ...partialConfig };
            console.debug(`Applying onprem environment config`, partialConfig.name ?? env.name);
        }
    } catch {
        console.debug(`Error reading environment config`, env);
    }
}

// #5: If we are running in a browser, match the environment against the web address
// This allows the web dashboard to automatically run on the dev environment for prod builds
if (!env && typeof window !== 'undefined' && window && window.location && window.location.href) {
    env = allEnvironments.find(
        (e) =>
            window.location.href.startsWith(e.webDashboardBaseUrl) ||
            window.location.href.startsWith(e.landingPageBaseUrl)
    );

    if (env) {
        console.debug(`Using environment ${env.name} from browser URL`);
    }

    // Check if the base dashboard URL is https://somedomain/dashboard -- if it is, adopt
    // the root domain and assume a prod environment.  This allows the dashboard in on-prem
    // installations to find the right endpoints
    else if (DashboardRootRegex.test(window.location.pathname)) {
        const baseUrl = new URL('/', window.location.href);
        env = {
            ...OnPremEnvironment,
            apiServiceBaseUrl: new URL('/api', baseUrl).toString(),
            webDashboardBaseUrl: new URL('/dashboard', baseUrl).toString(),
        };

        console.debug(`Using environment ${env.name} from browser URL with base URL ${baseUrl}`);
    }
}

// #6: Use prod by default
if (!env) {
    console.debug(`Using environment ${ProdEnvironment.name}`);
}
let environment: Environment = env ?? ProdEnvironment;

// Apply overrides
if (typeof ENVIRONMENT_OVERRIDES !== 'undefined') {
    environment = { ...environment, ...ENVIRONMENT_OVERRIDES };
}

const isBillingEnabled = !!environment.stripePublishableKey;
const isPlansEnabled = !!environment.stripePublishableKey;

export { environment, isBillingEnabled, isPlansEnabled };
