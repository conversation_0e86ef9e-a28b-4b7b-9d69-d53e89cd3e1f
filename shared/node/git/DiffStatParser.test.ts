import { DiffStatParser } from './DiffStatParser';

describe('DiffStatParser', () => {
    test('parses diff stat', () => {
        const line = '5\t20\tvscode/src/repo/VSCodeThreadListStore.ts';
        const parser = new DiffStatParser([line]);
        const diffStat = parser.values;
        expect(diffStat).toEqual([{ insertions: 5, deletions: 20, file: 'vscode/src/repo/VSCodeThreadListStore.ts' }]);
    });

    test('corrupt input values', () => {
        const value1 = new DiffStatParser(['q\t20\tvscode/src/repo/VSCodeThreadListStore.ts']).values;
        expect(value1).toEqual([]);
        const value2 = new DiffStatParser(['q\t20\t']).values;
        expect(value2).toEqual([]);
        const value3 = new DiffStatParser(['']).values;
        expect(value3).toEqual([]);
    });

    test('parse cases where the file has no diff (eg binary diff)', () => {
        const line = '-\t-\tapps/native-component-list/assets/icons/api/AdMob.png';
        const parser = new DiffStatParser([line]);
        const diffStat = parser.values;
        expect(diffStat).toEqual([]);
    });

    test('handles files with spaces, tab, newlines, quotes', () => {
        const lines = [
            // 2 tabs
            `19\t0\t"60\\t0\\tandro"`,
            // normal
            `18\t1\tfgdf`,
            // tab
            `17\t2\t"file\\ttab"`,
            // newline
            `16\t3\t"file\\nline"`,
            // double quote
            `15\t4\t"file\\"g"`,
            // single quote
            `14\t5\tfile'f`,
            // spaces and trailing spaces
            `0\t6\tsdfgsdf sdg `,
            // normal file, but no changes, so filter out
            `0\t0\ta.c`,
        ];
        const parser = new DiffStatParser(lines);
        const diffStat = parser.values;
        expect(diffStat).toEqual([
            {
                insertions: 19,
                deletions: 0,
                file: `60\\t0\\tandro`,
            },
            {
                insertions: 18,
                deletions: 1,
                file: `fgdf`,
            },
            {
                insertions: 17,
                deletions: 2,
                file: `file\\ttab`,
            },
            {
                insertions: 16,
                deletions: 3,
                file: `file\\nline`,
            },
            {
                insertions: 15,
                deletions: 4,
                file: `file\\"g`,
            },
            {
                insertions: 14,
                deletions: 5,
                file: `file'f`,
            },
            {
                insertions: 0,
                deletions: 6,
                file: `sdfgsdf sdg `,
            },
        ]);
    });
});
