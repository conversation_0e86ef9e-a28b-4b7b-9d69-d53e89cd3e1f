diff --git api/private.yml |||api/private.yml
index e88fd03776988b21bfcf2f37f5ff659fcdb8bb29..cc9834e0d69dafe7f42dff230d0a59088c6f2089 100644
--- api/private.yml
+++ |||api/private.yml
@@ -78,0 +79,3 @@ x-tagGroups:
+  - name: Management
+    tags:
+      - Versions
@@ -97 +100 @@ x-tagGroups:
-  - name: ClientManagement
+  - name: Webhooks
@@ -99 +102 @@ x-tagGroups:
-      - Versions
+      - Hooks
@@ -101,0 +105,4 @@ tags:
+  - name: Assets
+    description: |
+      Assets such as videos, images, and binaries.
+
@@ -104 +111 @@ tags:
-      Methods related to signing up, signing in, and signing out.
+      Signing up, signing in, and signing out.
@@ -108 +115 @@ tags:
-      Methods related to sending emails for various notification events.
+      Sending emails for various notification events.
@@ -112 +119 @@ tags:
-      Methods related to capturing user and product events
+      Capturing user and product events.
@@ -116 +123 @@ tags:
-      Methods related to invite events.
+      Invite events.
@@ -120 +127,5 @@ tags:
-      Methods related to validating the health of the service.
+      Validating the health of the service.
+
+  - name: Hooks
+    description: |
+      Inbound events from other services.
@@ -124 +135 @@ tags:
-      Methods related to SCM app installation
+      SCM app installation.
@@ -128 +139 @@ tags:
-      Methods related to outputting diagnostic logs
+      Outputting diagnostic logs.
@@ -146 +157 @@ tags:
-      Methods related to push channel subscription and modified since queries
+      Push channel subscription and modified since queries.
@@ -160 +171 @@ tags:
-      Methods related to SourceMarks
+      SourceMarks.
@@ -187 +198 @@ tags:
-  - name: VideoChannel
+  - name: Versions
@@ -189 +200 @@ tags:
-      Methods related to video channel management
+      Management of client upgrades and obsolescence.
@@ -191 +202 @@ tags:
-  - name: VideoRecording
+  - name: VideoChannel
@@ -193 +204 @@ tags:
-      Methods related to cloud video recording
+      Video channel management.
@@ -195 +206 @@ tags:
-  - name: Versions
+  - name: VideoRecording
@@ -197 +208 @@ tags:
-      Methods related to management of client upgrades and obsolescence
+      Cloud video recording.
@@ -199,3 +209,0 @@ tags:
-  - name: Assets
-    description: |
-      Methods related to Assets such as videos/images/binary
@@ -1707,0 +1716,86 @@ paths:
+
+  /hooks/github:
+    post:
+      tags:
+        - Hooks
+      description: |
+        Event from GitHub.
+
+        See delivery headers:
+        https://docs.github.com/en/developers/webhooks-and-events/webhooks/webhook-events-and-payloads#delivery-headers
+      operationId: githubEvent
+      parameters:
+        - name: X-GitHub-Delivery
+          description: |
+            A GUID to identify the delivery.
+          in: header
+          required: true
+          schema:
+            type: string
+            minLength: 1
+            maxLength: 256
+        - name: X-GitHub-Event
+          description: |
+            Name of the event that triggered the delivery.
+          in: header
+          required: true
+          schema:
+            type: string
+            minLength: 1
+            maxLength: 256
+        - name: X-GitHub-Hook-ID
+          description: |
+            External ID of the hook configuration resource in GitHub.
+            Always the same ID for a given GitHub App.
+            https://docs.github.com/en/rest/apps/webhooks
+          in: header
+          required: true
+          schema:
+            type: string
+            minLength: 1
+            maxLength: 256
+        - name: X-GitHub-Hook-Installation-Target-ID
+          description: |
+            GitHub App ID.
+          in: header
+          required: true
+          schema:
+            type: string
+            minLength: 1
+            maxLength: 256
+        - name: X-GitHub-Hook-Installation-Target-Type
+          description: |
+            Always `integration` for GitHub Apps.
+          in: header
+          required: true
+          schema:
+            type: string
+            minLength: 1
+            maxLength: 256
+        - name: X-Hub-Signature-256
+          description: |
+            This header is sent if the webhook is configured with a secret.
+            This is the HMAC hex digest of the request body,
+            and is generated using the SHA-256 hash function and the secret as the HMAC key.
+          in: header
+          required: true
+          schema:
+            type: string
+            minLength: 1
+            maxLength: 256
+      requestBody:
+        required: true
+        content:
+          application/json:
+            schema:
+              type: string
+      responses:
+        '202':
+          description: OK
+          content:
+            application/json:
+              schema:
+                type: string
+        default:
+          $ref: '#/components/responses/Default'
+
