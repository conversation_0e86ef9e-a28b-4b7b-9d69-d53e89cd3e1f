// noinspection ES6PreferShortImport

import { LRUCache } from 'lru-cache';
import { Stream } from 'xstream';

import { ThreadInfoAggregate } from '@shared/api/generatedExtraApi';

import { ApiDataStreamState, createApiDataStream } from '@shared/stores/ApiDataStream';
import { CreateInitialAsyncFetchFn, CreatePagedApiFetchFn } from '@shared/stores/ApiDataStreamTraits';
import { SLOW_POLL_MS } from '@shared/stores/ChannelPoller';
import { ObjectMap } from '@shared/stores/ObjectMap';
import { filterReady } from '@shared/stores/StreamOperators';
import { TeamThreadsType } from '@shared/stores/ThreadListStoreTypes';
import { createValueStream } from '@shared/stores/ValueStream';

import { API } from '../../../api';
import { FilePoint, Mark, NewSourcePoints, Range, SourcePoint } from '../../../api/models';
import { ArrayUtils, FilePath, logger, StreamAsyncFetcher, UUID } from '../../../webUtils';
import { GlobbedRunningPromise } from '../../../webUtils/GlobbedRunningPromise';

const { isEmpty, isNotEmpty } = ArrayUtils;
const log = logger('SourceMarkStore');

const getSourceMarkStoreTraits = (teamId: UUID, repoId: UUID) => ({
    channelIds: `/repos/${repoId.value}/sourceMarks`,
    teamId: teamId.value,
    cacheKey: `sourcemarks.${teamId.value}.${repoId.value}`,
    pollFrequencyMS: SLOW_POLL_MS,
    fetchFn: CreateInitialAsyncFetchFn(
        CreatePagedApiFetchFn(async (lastModified) => {
            log.debug('Fetching SourceMarks', { teamId: teamId.value, repoId: repoId.value });
            const response = await API.sourceMarks.getRepoSourceMarksRaw({
                teamId: teamId.value,
                repoId: repoId.value,
                xUnblockedIfModifiedSince: lastModified,
            });

            const value = await response.value();
            return {
                value: value,
                ifModifiedSince: response.raw.headers.get('X-Unblocked-Last-Modified') ?? undefined,
            };
        }),
        []
    ),
});

export type ThreadStreams = ObjectMap<TeamThreadsType, Stream<ApiDataStreamState<ThreadInfoAggregate[]>>>;

const SourceMarkStoreDescription = (state: ApiDataStreamState<Mark[]>, metadata?: string) => {
    switch (state.$case) {
        case 'loading':
            return `Loading ${metadata}`;
        case 'ready':
            return `Loaded ${metadata}: ${state.value.length} SourceMarks`;
    }
};

// noinspection RedundantIfStatementJS
export class SourceMarkStore {
    constructor(
        private readonly teamId: UUID,
        private readonly repoId: UUID,
        private readonly hybridSourcemarkEngine: boolean
    ) {}

    private sourceMarks: Map<string, Mark> = new Map();

    /**
     * Stream of changed sourcemarks for this repo.
     * This is not a stream of the entire sourcemark collection from the server.
     * This is not a memoized stream.
     */
    private transientMarkChanges = createValueStream<Mark[]>(undefined, { isPassthrough: true });
    getChangedMarksStream = () => this.transientMarkChanges.stream;

    /**
     * The entire sourcemark collection for this repo as a stream from the server.
     */
    private continuousApiStream = this.hybridSourcemarkEngine
        ? undefined
        : createApiDataStream(getSourceMarkStoreTraits(this.teamId, this.repoId), {
              logger: log,
              description: SourceMarkStoreDescription,
              metadata: `repoId: ${this.repoId.value}`,
          })
              .stream.compose(filterReady)
              .map((state) => state.value);

    private sourceMarkSubscription = this.continuousApiStream?.subscribe({
        next: (marks: Mark[]) => this.updateCachedSourceMarks(marks),
    });

    private mergeTo(into: Mark, from: Mark): boolean {
        let changed = false;

        if (!from.isFileMark && !into.isFileMark) {
            from.sourcePoints?.forEach((fromPoint) => {
                if (!into.sourcePoints?.some((point) => SourceMarkStore.areSameSourcePoint(point, fromPoint))) {
                    into.sourcePoints?.push(fromPoint);
                    changed = true;
                }
            });
        } else if (from.isFileMark && into.isFileMark) {
            from.filePoints?.forEach((fromPoint) => {
                if (!into.filePoints?.some((point) => SourceMarkStore.areSameFilePoint(point, fromPoint))) {
                    into.filePoints?.push(fromPoint);
                    changed = true;
                }
            });
        } else {
            log.error('Invalid merge of sourcepoints and filepoints', { from, into });
        }

        // update mutable mark properties
        if (from.isArchived !== into.isArchived) {
            into.isArchived = from.isArchived;
            changed = true;
        }
        if (from.isDeleted !== into.isDeleted) {
            into.isDeleted = from.isDeleted;
            changed = true;
        }

        return changed;
    }

    private static areSameSourcePoint(a: SourcePoint, b: SourcePoint): boolean {
        return (
            a.columnEnd === b.columnEnd &&
            a.columnStart === b.columnStart &&
            a.commitHash === b.commitHash &&
            a.fileHash === b.fileHash &&
            a.overrideFilePath === b.overrideFilePath &&
            a.isOriginal === b.isOriginal &&
            a.isUntrusted === b.isUntrusted &&
            a.stopPropagation === b.stopPropagation &&
            a.overrideSnippetEncoded === b.overrideSnippetEncoded &&
            SourceMarkStore.areSameRange(a.lines, b.lines)
        );
    }

    private static areSameFilePoint(a: FilePoint, b: FilePoint): boolean {
        return (
            a.commitHash === b.commitHash &&
            a.fileHash === b.fileHash &&
            a.overrideFilePath === b.overrideFilePath &&
            a.isOriginal === b.isOriginal &&
            a.stopPropagation === b.stopPropagation
        );
    }

    private static areSameRange(a: Range, b: Range): boolean {
        return a.end === b.end && a.start === b.start;
    }

    onSourceMarksInitialized: Promise<void> = this.continuousApiStream
        ? StreamAsyncFetcher(this.continuousApiStream, () => {
              return;
          })
        : Promise.resolve();

    destroy() {
        this.sourceMarkSubscription?.unsubscribe();
    }

    async getSourceMark(id: UUID): Promise<Mark | undefined> {
        const mark = this.sourceMarks.get(id.value);
        if (mark) {
            return mark;
        }

        if (this.hybridSourcemarkEngine) {
            return await this.findSourceMarkById(id);
        }
    }

    private async findSourceMarkById(markId: UUID): Promise<Mark | undefined> {
        const marks = await this.findSourceMarksByIds([markId]);
        return marks.find((m) => m.id === markId.value);
    }

    private async findSourceMarksByIds(markIds: UUID[]): Promise<Mark[]> {
        if (markIds.length === 0) {
            return [];
        }

        const marks = await API.sourceMarks.findRepoSourceMarksById({
            teamId: this.teamId.value,
            repoId: this.repoId.value,
            findSourceMarksByIdRequest: {
                markIds: markIds.map((id) => id.value),
            },
        });

        this.updateCachedSourceMarks(marks);

        return marks;
    }

    private fileFetchedCache = new LRUCache<string, boolean>({
        max: 1000,
        // Fetch marks for a file at most once per ttl milliseconds.
        ttl: 19_000,
    });

    async fetchSourceMarksByFiles(allFiles: FilePath[]): Promise<void> {
        const chunkSize = 100; // Chunk to avoid large requests.
        const maxFiles = 1000; // Clamp to avoid huge numbers of requests.

        const filesToFetch = allFiles.filter((f) => !this.fileFetchedCache.has(f.value));

        for (const files of ArrayUtils.chunk(filesToFetch.slice(0, maxFiles), chunkSize)) {
            const marks = await API.sourceMarks.findRepoSourceMarksByFile({
                teamId: this.teamId.value,
                repoId: this.repoId.value,
                findSourceMarksByFileRequest: {
                    filePaths: files.map((f) => f.value),
                },
            });

            this.updateCachedSourceMarks(marks);

            files.forEach((f) => this.fileFetchedCache.set(f.value, true));
        }
    }

    private updateCachedSourceMarks(marks: Mark[]) {
        let shouldUpdateDataStream = false;

        marks.forEach((mark) => {
            if (mark.isDeleted) {
                this.sourceMarks.delete(mark.id);
            } else {
                const existingMark = this.sourceMarks.get(mark.id);
                if (existingMark) {
                    const changed = this.mergeTo(existingMark, mark);
                    shouldUpdateDataStream ||= changed;
                } else {
                    this.sourceMarks.set(mark.id, mark);
                    shouldUpdateDataStream = true;
                }
            }
        });

        if (shouldUpdateDataStream) {
            this.transientMarkChanges.updateValue(marks);
        }
    }

    getSourceMarks(): Mark[] {
        return SourceMarkStore.sanityCheck(
            Array.from(this.sourceMarks.values()).filter((m) => m.isArchived !== true && m.isDeleted !== true)
        );
    }

    private static sanityCheck(marks: Mark[]): Mark[] {
        const corruptMarks = new Set(
            marks.filter((m) => (m.isFileMark ? this.corruptFileMark(m) : this.corruptSourceMark(m))).map((m) => m.id)
        );

        if (corruptMarks.size) {
            log.error('found corrupt mark', { corruptMarks: [...corruptMarks] });
            return marks.filter((m) => !corruptMarks.has(m.id));
        }

        return marks;
    }

    private static corruptSourceMark(sm: Mark): boolean {
        if (sm.filePoints) {
            return true;
        }
        if (!sm.sourcePoints) {
            return true;
        }
        if (!sm.sourcePoints?.some((p: SourcePoint) => p.isOriginal)) {
            return true;
        }
        return false;
    }

    private static corruptFileMark(sm: Mark): boolean {
        if (sm.sourcePoints) {
            return true;
        }
        if (!sm.filePoints) {
            return true;
        }
        if (!sm.filePoints.some((p: FilePoint) => p.isOriginal)) {
            return true;
        }
        return false;
    }

    putSourcePoint(markId: string, point: SourcePoint, onlyCacheLocally: boolean) {
        const mark = this.sourceMarks.get(markId);
        if (!mark || !mark.sourcePoints) {
            log.warn('Could not upstream point, because mark does not exist', { sourceMarkId: markId });
            return;
        }

        if (mark?.isFileMark === true) {
            throw Error('Expected SourceMark');
        }

        if (mark.sourcePoints.some((p: SourcePoint) => SourceMarkStore.areSameSourcePoint(p, point))) {
            return;
        }

        mark.sourcePoints.push(point);
        if (onlyCacheLocally) {
            return;
        }

        const newPoints = [...this.newSourcePointsToUpstream].reverse().find((i) => i.sourceMarkId === markId);
        // FIXME richie HACK, since server cannot accept null overrideFilePaths yet
        const uploadPoint = { ...point, overrideFilePath: point.overrideFilePath ?? mark.originalFilePath };
        if (newPoints) {
            newPoints.sourcePoints?.push(uploadPoint);
        } else {
            this.newSourcePointsToUpstream.push({ sourceMarkId: markId, sourcePoints: [uploadPoint] });
        }
    }

    putFilePoint(markId: string, point: FilePoint, onlyCacheLocally: boolean) {
        const mark = this.sourceMarks.get(markId);
        if (!mark || !mark.filePoints) {
            log.warn('Could not upstream point, because mark does not exist', { sourceMarkId: markId });
            return;
        }

        if (mark?.isFileMark === false) {
            throw Error('Expected FileMark');
        }

        if (mark.filePoints.some((p: FilePoint) => SourceMarkStore.areSameFilePoint(p, point))) {
            return;
        }

        mark.filePoints.push(point);
        if (onlyCacheLocally) {
            return;
        }

        const newPoints = [...this.newSourcePointsToUpstream].reverse().find((i) => i.sourceMarkId === markId);
        // FIXME richie HACK, since server cannot accept null overrideFilePaths yet
        const uploadPoint = { ...point, overrideFilePath: point.overrideFilePath ?? mark.originalFilePath };
        if (newPoints) {
            newPoints.filePoints?.push(uploadPoint);
        } else {
            this.newSourcePointsToUpstream.push({ sourceMarkId: markId, filePoints: [uploadPoint] });
        }
    }

    private newSourcePointsToUpstream: NewSourcePoints[] = [];
    private upstreamingSourcePointsPatiently = new GlobbedRunningPromise<void>();
    private upstreamingSourcePointsImmediately = new GlobbedRunningPromise<void>();

    /**
     * Upstream points in bulk, in batches.
     *
     * Points are buffered until there are enough of them to include in a full batch,
     * with the goal being to reduce network round trips.
     *
     * @param forceUpstream - upstream even the buffered points are smaller than the batch size.
     */
    async upstreamSourcePoints(forceUpstream = false): Promise<void> {
        if (isEmpty(this.newSourcePointsToUpstream)) {
            return;
        }

        if (forceUpstream) {
            await this.upstreamingSourcePointsImmediately.run(async () => {
                await this.doUpstreamSourcePoints(forceUpstream);
            });
        } else {
            await this.upstreamingSourcePointsPatiently.run(async () => {
                await this.doUpstreamSourcePoints(forceUpstream);
            });
        }
    }

    private async doUpstreamSourcePoints(forceUpstream: boolean): Promise<void> {
        const batchSize = 2000;
        let upstreamFailCount = 0;

        while (isNotEmpty(this.newSourcePointsToUpstream)) {
            // Grab the next batch
            let count = 0;
            let markCount = 0;

            // Batch to roughly 1000 points, but ensure we always batch at least one
            const batch = ArrayUtils.takeWhile(this.newSourcePointsToUpstream, (p) => {
                count += p.filePoints?.length ?? 0;
                count += p.sourcePoints?.length ?? 0;
                markCount += 1;
                return count <= 1000 || markCount <= 1;
            });

            // Put em back if batch is too small
            if (!forceUpstream && batch.length < batchSize) {
                this.newSourcePointsToUpstream.unshift(...batch);
                return;
            }

            // Attempt to upstream
            try {
                await API.sourceMarks.putRepoSourcePoints({
                    teamId: this.teamId.value,
                    repoId: this.repoId.value,
                    newSourcePoints: batch,
                });
                continue;
            } catch (e) {
                // Put em back if upstream failed or if the batch is too small
                this.newSourcePointsToUpstream.unshift(...batch);
                log.warn('Failed to upstream points', { teamId: this.teamId.value, e });
                upstreamFailCount++;
            }

            // Prevent infinite loop. Something wrong with service maybe?
            if (upstreamFailCount > 2) {
                log.error('Failed to upstream points repeatedly', { teamId: this.teamId.value });
                return;
            }
        }
    }

    async flushSourcePoints(): Promise<void> {
        return this.upstreamSourcePoints(true);
    }
}
