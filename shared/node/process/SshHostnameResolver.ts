import { LRUCache } from 'lru-cache';
import parseGitUrl from 'parse-github-url';

import { logger } from '@shared/webUtils';

import { SshRunner } from './SshRunner';
import { SshRunnerInterface } from './SshRunnerInterface';

const log = logger('SshHostnameResolver');

/**
 * Apply SSH config rules to resolve a hostname given a potential host nickname.
 */
export class SshHostnameResolver {
    constructor(
        private readonly sshRunner: SshRunnerInterface = new SshRunner(),
        private readonly cache: LRUCache<string, string> = new LRUCache({
            max: 100,
            ttl: 60_000,
        })
    ) {}

    async resolveUrlString(urlString: string): Promise<string> {
        const url = parseGitUrl(urlString);
        const hostname = url?.hostname || url?.host;
        if (!hostname) {
            return urlString;
        }

        const resolvedHostname = await this.resolveHostname(hostname);
        if (resolvedHostname === hostname) {
            return urlString;
        }

        return urlString.replace(new RegExp(`\\b${hostname}\\b`), resolvedHostname);
    }

    async resolveUrl(url: URL): Promise<URL> {
        const resolvedHostname = await this.resolveHostname(url.hostname);
        if (resolvedHostname === url.hostname) {
            return url;
        }

        const resolvedUrl = new URL(url.toString());
        resolvedUrl.hostname = resolvedHostname;
        return resolvedUrl;
    }

    async resolveHostname(hostname: string): Promise<string> {
        const cacheKey = hostname;
        const cachedResolvedHostname = this.cache.get(cacheKey);

        // cache hit
        if (cachedResolvedHostname !== undefined) {
            return cachedResolvedHostname;
        }

        // cache miss
        const resolvedHostname = (await this.resolveHostnameWithSshConfig(hostname)) || hostname;
        this.cache.set(cacheKey, resolvedHostname);

        return resolvedHostname;
    }

    private async resolveHostnameWithSshConfig(hostname: string): Promise<string> {
        try {
            const result = await this.sshRunner.run(['-G', hostname], false);
            const resolvedHostname = result
                .split('\n')
                .find((line) => line.startsWith('hostname '))
                ?.trim()
                ?.split(' ')[1]
                ?.trim();

            return resolvedHostname || hostname;
        } catch (e) {
            log.error('resolveHostnameWithSshConfig: failed', e, { hostname });
            return hostname;
        }
    }
}
