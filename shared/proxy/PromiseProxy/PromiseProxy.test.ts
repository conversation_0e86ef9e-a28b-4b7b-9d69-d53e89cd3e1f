import { PromiseProxyClient, PromiseProxyService, PromiseProxyTraits, PromiseProxyTransport } from './PromiseProxy';

const clientTransport: PromiseProxyTransport = {
    sendData: (data: string) => {
        backgroundProxyService.dataReceived(data);
    },
};
const backgroundTransport: PromiseProxyTransport = {
    sendData: (data: string) => {
        clientProxyService.dataReceived(data);
    },
};

type MockRequest = { input: string };
type MockResponse = { output: string };

const MockTraits: PromiseProxyTraits<MockRequest, MockResponse> = {
    key: 'mock',
};

const clientProxyService = new PromiseProxyClient(clientTransport);
const backgroundProxyService = new PromiseProxyService(backgroundTransport);

beforeEach(() => {
    backgroundProxyService.unregister(MockTraits);
});

test('PromiseProxy basic send', async () => {
    backgroundProxyService.register(MockTraits, async (req) => {
        return { output: req.input };
    });

    const input = 'Test';
    const response = await clientProxyService.register(MockTraits)({ input });
    expect(response.output).toEqual(input);
});

test('PromiseProxy handles error', async () => {
    const input = 'Test';
    const mockError = new Error(input);
    backgroundProxyService.register(MockTraits, async () => {
        throw mockError;
    });
    await expect(clientProxyService.register(MockTraits)({ input })).rejects.toThrow(mockError);
});
