import { Stream, Subscription } from 'xstream';

import { logger } from '@shared/webUtils/log/BaseLogger';

import {
    StoreProxyClientToServerMessage,
    StoreProxyMessageBase,
    StoreProxySubscribeMessage,
    StoreProxyTransport,
    StoreProxyUnsubscribeMessage,
} from './StoreProxyMessageTypes';
import { StoreProxyApiGenerateFn, StoreProxyRegistrationTarget, StoreProxyRegistry } from './StoreProxyRegistry';
import { StoreProxyActionTraits, StoreProxyKey, StoreProxyStreamTraits, StoreProxyTraits } from './StoreProxyTypes';

const log = logger('StoreProxyServer');

interface StoreProxyServerEntryBase {
    process: (message: StoreProxyMessageBase) => Promise<void>;
    dispose: () => void;
}

interface StoreProxyStreamEntry {
    stream: Stream<unknown>;
    subscription: Subscription;
}

class StoreProxyServerEntry<
    Traits extends StoreProxyTraits<Key, Actions, Streams>,
    Key extends StoreProxyKey,
    Actions extends StoreProxyActionTraits,
    Streams extends StoreProxyStreamTraits,
> implements StoreProxyServerEntryBase
{
    private streams = new Map<unknown, StoreProxyStreamEntry>();

    constructor(
        private transport: StoreProxyTransport,
        private generateFn: StoreProxyApiGenerateFn<Traits, Key, Actions, Streams>
    ) {}

    dispose() {
        this.streams.forEach((stream) => stream.subscription.unsubscribe());
        this.streams.clear();
    }

    async process(message: StoreProxyMessageBase) {
        const typedMessage = message as StoreProxyClientToServerMessage<Traits, Key, Actions, Streams>;

        const store = this.generateFn(typedMessage.key);

        switch (typedMessage.$case) {
            case 'call':
                try {
                    // Make the call to the actual store function being proxied.
                    // Note that we pass all arguments along, which is kind of odd, but does work, even if the target
                    // function takes fewer args.
                    const response = await store[typedMessage.fn](typedMessage.request[0], typedMessage.request[1]);

                    this.transport.sendData({
                        $case: 'resolve',
                        $category: typedMessage.$category,
                        key: typedMessage.key,
                        keyId: typedMessage.keyId,
                        id: typedMessage.id,
                        response,
                    });
                } catch (e) {
                    const message = e instanceof Error ? e.message : 'Promise Rejected';
                    this.transport.sendData({
                        $case: 'reject',
                        $category: typedMessage.$category,
                        key: typedMessage.key,
                        keyId: typedMessage.keyId,
                        id: typedMessage.id,
                        message,
                    });
                }
                break;

            case 'subscribe':
                this.subscribe(typedMessage);
                break;

            case 'unsubscribe':
                this.unsubscribe(typedMessage);
                break;
        }
    }

    private getStreamKey(streamName: string | number | symbol, keyId: string) {
        return String(streamName) + '.' + keyId;
    }

    private subscribe(message: StoreProxySubscribeMessage<Key, Streams, keyof Streams>) {
        const streamId = this.getStreamKey(message.stream, message.keyId);
        if (this.streams.has(streamId)) {
            /*
                This is probably a bug -- a given webview should not subscribe
                to a stream twice.

                To ensure this new stream works, unsubscribe the existing stream,
                and allow the newer stream to continue
            */
            log.warn('StoreProxy stream is being subscribed to twice', {
                storeKey: message.key,
                streamId,
            });
            this.streams.get(streamId)?.subscription.unsubscribe();
        }

        const store = this.generateFn(message.key);
        const stream = store[message.stream];
        const subscription = stream.subscribe({
            next: (data) => {
                try {
                    this.transport.sendData({
                        $case: 'next',
                        $category: message.$category,
                        key: message.key,
                        keyId: message.keyId,
                        stream: message.stream,
                        data,
                    });
                } catch (error) {
                    log.warn('Error sending next data to stream', error);
                }
            },
            complete: () => {
                try {
                    this.transport.sendData({
                        $case: 'complete',
                        $category: message.$category,
                        key: message.key,
                        keyId: message.keyId,
                        stream: message.stream,
                    });
                } catch (error) {
                    log.warn('Error sending complete data to stream', error);
                }
            },
            error: (error) => {
                try {
                    this.transport.sendData({
                        $case: 'error',
                        $category: message.$category,
                        key: message.key,
                        keyId: message.keyId,
                        stream: message.stream,
                        message: error instanceof Error ? error.message : 'Error',
                    });
                } catch (error) {
                    log.warn('Error sending error data to stream', error);
                }
            },
        });

        this.streams.set(streamId, { stream: stream as Stream<unknown>, subscription });
    }

    private unsubscribe(message: StoreProxyUnsubscribeMessage<Key, Streams, keyof Streams>) {
        const streamId = this.getStreamKey(message.stream, message.keyId);
        const entry = this.streams.get(streamId);
        if (!entry) {
            return;
        }

        entry.subscription.unsubscribe();
        this.streams.delete(streamId);
    }
}

export class StoreProxyServer implements StoreProxyRegistrationTarget {
    private entries = new Map<string, StoreProxyServerEntryBase>();

    constructor(
        private transport: StoreProxyTransport,
        private registry = StoreProxyRegistry.instance()
    ) {}

    dispose() {
        this.entries.forEach((entry) => entry.dispose());
        this.entries.clear();
    }

    register<
        Traits extends StoreProxyTraits<Key, Actions, Streams>,
        Key extends StoreProxyKey,
        Actions extends StoreProxyActionTraits,
        Streams extends StoreProxyStreamTraits,
    >(traits: Traits, generateFn: StoreProxyApiGenerateFn<Traits, Key, Actions, Streams>) {
        if (this.entries.has(traits.category)) {
            return;
        }

        this.entries.set(
            traits.category,
            new StoreProxyServerEntry<Traits, Key, Actions, Streams>(this.transport, generateFn)
        );
    }

    processMessage(data: string) {
        const message = JSON.parse(data) as StoreProxyMessageBase;
        const category = message.$category;
        let entry = this.entries.get(category);
        if (!entry) {
            this.registry.requestTargetRegistration(category, this);
            entry = this.entries.get(category);

            if (!entry) {
                return;
            }
        }
        entry.process(message);
    }
}
