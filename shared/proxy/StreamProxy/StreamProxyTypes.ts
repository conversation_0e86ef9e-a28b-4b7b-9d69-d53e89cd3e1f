import { Jsonifiable } from 'type-fest';
import { Stream } from 'xstream';

export interface StreamProxyKey {
    $case: string;
}

export interface StreamProxyCommand {
    $case: string;
}

export type StreamProxyData = Jsonifiable;

export interface StreamProxyTraits<
    Key extends StreamProxyKey,
    // eslint-disable-next-line unused-imports/no-unused-vars
    Data extends StreamProxyData,
    // eslint-disable-next-line unused-imports/no-unused-vars
    Commands extends StreamProxyCommand = never,
> {
    keyToString: (key: Key) => string;
}

export interface ControlledStream<Data extends StreamProxyData, Commands extends StreamProxyCommand> {
    stream: Stream<Data>;
    sendCommand: (command: Commands) => void;
}

export interface StreamProxyMessageBase {
    $case: string;
    key: StreamProxyKey;
    keyId: string;
}

export interface StreamProxyTransport {
    sendData: <T extends StreamProxyMessageBase>(data: T) => void;
}

interface StreamProxySubscribeMessage<Key extends StreamProxyKey> extends StreamProxyMessageBase {
    $case: 'subscribe';
    key: Key;
}

interface StreamProxyUnsubscribeMessage<Key extends StreamProxyKey> extends StreamProxyMessageBase {
    $case: 'unsubscribe';
    key: Key;
}

interface StreamProxyCommandMessage<Key extends StreamProxyKey, Command extends StreamProxyCommand>
    extends StreamProxyMessageBase {
    $case: 'command';
    key: Key;
    command: Command;
}

export type StreamProxyClientToServerMessage<Key extends StreamProxyKey, Command extends StreamProxyCommand> =
    | StreamProxySubscribeMessage<Key>
    | StreamProxyUnsubscribeMessage<Key>
    | StreamProxyCommandMessage<Key, Command>;

interface StreamProxyNextMessage<Key extends StreamProxyKey, Data extends StreamProxyData>
    extends StreamProxyMessageBase {
    $case: 'next';
    key: Key;
    value: Data;
}

interface StreamProxyErrorMessage<Key extends StreamProxyKey> extends StreamProxyMessageBase {
    $case: 'error';
    key: Key;
}

interface StreamProxyCompleteMessage<Key extends StreamProxyKey> extends StreamProxyMessageBase {
    $case: 'complete';
    key: Key;
}

export type StreamProxyServerToClientMessage<Key extends StreamProxyKey, Data extends StreamProxyData> =
    | StreamProxyNextMessage<Key, Data>
    | StreamProxyErrorMessage<Key>
    | StreamProxyCompleteMessage<Key>;

export type StreamCommandRegisterFn<Command extends StreamProxyCommand> = (handler: (command: Command) => void) => void;

export type StreamProxyStreamFn<
    Key extends StreamProxyKey,
    Data extends StreamProxyData,
    Command extends StreamProxyCommand,
> = (key: Key, regFn: StreamCommandRegisterFn<Command>) => Stream<Data>;

export interface StreamProxyRegistrationTarget {
    register: <Key extends StreamProxyKey, Data extends StreamProxyData, Command extends StreamProxyCommand>(
        key: Key['$case'],
        traits: StreamProxyTraits<Key, Data, Command>,
        streamFn: StreamProxyStreamFn<Key, Data, Command>
    ) => void;
}
