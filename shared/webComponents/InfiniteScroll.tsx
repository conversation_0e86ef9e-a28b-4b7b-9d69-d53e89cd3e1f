import { ReactNode } from 'react';

import { PaginationSource } from '@shared/stores/PaginationSource';
import { useInfiniteScroll } from '@shared/webComponents/ScrollContext';
import { Spinner } from '@shared/webComponents/Spinner/Spinner';

import './InfiniteScroll.scss';

export function InfiniteScrollImpl({ source, children }: { source: PaginationSource; children: ReactNode }) {
    const showLoader = useInfiniteScroll(source);

    return (
        <>
            {children}
            {showLoader && (
                <div className="infinite_scroll__show_more" key="threadListShowMore">
                    <Spinner size="small" />
                </div>
            )}
        </>
    );
}

/**
 * Provide infinite scrolling on the given pagination source
 */
export function InfiniteScroll({ source, children }: { source?: PaginationSource; children: ReactNode }) {
    return source ? <InfiniteScrollImpl source={source}>{children}</InfiniteScrollImpl> : children;
}
