import { AnswersTutorialSteps, AnswersTutorialStepsOrder } from './AnswersTutorial/AnswersTutorialSteps';

export type TutorialType = 'answers' | 'dsacAnswers';

export type TutorialStep = string;
export type TutorialSteps = Record<TutorialStep, TutorialStep>;
export type TutorialStepOrder = TutorialStep[];

export type TutorialState = { $case: 'inactive' } | { $case: 'active'; state: ActiveTutorialState };

export const AvailableTutorials = {
    answers: AnswersTutorialSteps,
    dsacAnswers: AnswersTutorialSteps,
} as const satisfies Record<TutorialType, TutorialSteps>;

export const TutorialStepOrders = {
    answers: AnswersTutorialStepsOrder,
    dsacAnswers: AnswersTutorialStepsOrder,
} as const satisfies Record<TutorialType, readonly string[]>;

export type TutorialMap = Record<PropertyKey, TutorialSteps>;
export type TutorialStepOrdersMap = typeof TutorialStepOrders;

export type ActiveTutorialState = {
    [K in TutorialType]: {
        tutorialType: K; // keeps the literal key
        currentIndex: number; // runtime
        steps: TutorialStepOrdersMap[K]; // readonly tuple for *this* tutorial
        stepCount: TutorialStepOrdersMap[K]['length'];
        nextLabel: string;
    };
}[TutorialType];
