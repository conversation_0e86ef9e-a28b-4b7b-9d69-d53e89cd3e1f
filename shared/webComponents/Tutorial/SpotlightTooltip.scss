@use 'layout' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'animations' as *;

.spotlight_tooltip__overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    mix-blend-mode: hard-light; /* Try also 'screen' or 'lighten' */
    pointer-events: auto;
    z-index: 999;
}

.spotlight_tooltip__highlight {
    position: fixed;
    top: var(--highlight-top);
    left: var(--highlight-left);
    width: var(--highlight-width);
    height: var(--highlight-height);
    border-radius: var(--highlight-radius, 12px);
    background-color: gray;
    pointer-events: none;
    z-index: 1000;
    transition:
        top 0.3s ease-in-out,
        left 0.3s ease-in-out,
        width 0.3s ease-in-out,
        height 0.3s ease-in-out;
}

.spotlight_tooltip__content {
    background-color: white;
    border-radius: $border-radius;
    @include shadow-border;
    z-index: 1001;
    position: absolute;
    opacity: 0;
    animation: fade-in 300ms ease-in-out;
    animation-fill-mode: forwards;

    &.spotlight_tooltip__content--animate {
        transition: transform 0.3s ease-in-out;
    }
}

.spotlight_transition {
    transition-property: opacity;
    transition-timing-function: ease-in-out;
    transition-duration: 200ms;
}

.spotlight_enter_from {
    opacity: 0;
}
.spotlight_enter_to {
    opacity: 1;
}
.spotlight_exit_from {
    opacity: 1;
}
.spotlight_exit_to {
    opacity: 0;
}
