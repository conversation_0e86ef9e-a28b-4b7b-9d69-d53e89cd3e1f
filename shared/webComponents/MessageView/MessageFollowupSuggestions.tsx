import { FollowOnSuggestion, MessageSuggestions } from '@shared/api/models';

import { faPaperPlaneTop } from '@fortawesome/pro-regular-svg-icons/faPaperPlaneTop';

import { ContextRow } from '../ContextRow/ContextRow';
import { Icon } from '../Icon/Icon';
import { RowsList } from '../Rows/RowsList';

import './MessageFollowupSuggestions.scss';

interface Props {
    suggestions?: MessageSuggestions;
    onAskSuggestion?: (question: FollowOnSuggestion) => void;
    isLastBotMessage?: boolean;
}

export const MessageFollowupSuggestions = ({ suggestions, onAskSuggestion, isLastBotMessage }: Props) => {
    const followupSuggestions = suggestions?.followOn.filter((followOn) => {
        switch (followOn.hasBeenAsked) {
            case true:
                return false;
            case false:
                return true;

            // Old threads don't have state stored -- display if this is the last bot message
            case undefined:
                return isLastBotMessage;
        }
    });

    if (!followupSuggestions?.length) {
        return null;
    }

    return (
        <div className="message_view__followup">
            <div className="message_view__followup__label">Suggested follow-up questions:</div>

            <RowsList className="message_view__followup__suggestions">
                {followupSuggestions.map((suggestion, idx) => (
                    <ContextRow
                        key={idx}
                        as="button"
                        className="message_view__followup__suggestion"
                        rightContent={
                            <Icon
                                className="message_view__followup__suggestion_icon"
                                icon={faPaperPlaneTop}
                                size="small"
                            />
                        }
                        onClick={() => onAskSuggestion?.(suggestion)}
                    >
                        {suggestion.question}
                    </ContextRow>
                ))}
            </RowsList>
        </div>
    );
};
