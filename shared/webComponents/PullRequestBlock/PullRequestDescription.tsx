import classNames from 'classnames';

import { PullRequestAndAuthor } from '@shared/api/generatedExtraApi';

import { MessageTransformer, MessageUtils } from '../../webUtils';
import { MessageView } from '../MessageView/MessageView';

export const PullRequestDescriptionBlock = ({
    pullRequest,
    onAuthorNavigate,
    withBlocks,
}: {
    pullRequest: PullRequestAndAuthor;
    onAuthorNavigate?: (teamMemberId: string) => void;
    withBlocks: boolean;
}) => {
    const { author, createdAt, descriptionMessage } = pullRequest;
    const descriptionContent = MessageTransformer.fromBytesToMessage(descriptionMessage.messageContent.content);

    const classes = classNames({
        pull_request_description: true,
        pull_request_description__no_blocks: !withBlocks,
    });

    if (!MessageUtils.hasContent(descriptionContent.blocks)) {
        return null;
    }

    return (
        <div className={classes}>
            <MessageView
                readOnly
                id={descriptionMessage.id}
                teamId={pullRequest.teamId}
                repoId={pullRequest.repoId}
                isAuthorCurrentPerson={pullRequest.isAuthorCurrentPerson}
                author={author}
                onNavigateAuthor={onAuthorNavigate}
                createdAt={createdAt}
                messageContent={descriptionMessage.messageContent}
                links={descriptionMessage.links}
                isAnchor={false}
                provider={pullRequest.provider}
            />
        </div>
    );
};
