@use '../styles/layout' as *;
@use '../styles/misc' as *;
@use '../styles/flex' as *;

.button {
    padding: $spacer-6 $spacer-12;
    @include stories_button_color_styles;
}

.button__as_link {
    color: cornflowerblue;
}

.button__as_icon {
    &:hover {
        color: cornflowerblue;
    }
}

.button__options_dropdown {
    padding: $spacer-6;
    @include stories_button_color_styles;

    .button__options {
        padding: 0 $spacer-4;

        .button__option {
            padding: $spacer-4 $spacer-24 $spacer-4 0;

            &:not(:last-of-type) {
                border-bottom: $border-width $border-style;
            }
        }
    }

    &.button__primary {
        .button__options {
            background-color: darkslateblue;

            .button__option {
                color: lightgrey;

                &:not(:last-of-type) {
                    border-color: lightgrey;
                }
            }
        }
    }
    &.button__secondary {
        .button__options {
            background-color: violet;

            .button__option {
                color: lightgrey;

                &:not(:last-of-type) {
                    border-color: lightgrey;
                }
            }
        }
    }
}
