import classNames from 'classnames';

import { IntegrationInstallationV3, Provider } from '@shared/api/models';

import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';
import { getProviderIconSrc } from '@shared/webUtils/ProviderUtils';

import { useViewThemeContext } from '../View/ViewThemeContext';
import { ViewTheme } from '../View/ViewThemeTypes';
import { Icon, Props as IconProps } from './Icon';

export type ProviderIconProps = Omit<IconProps, 'icon'> & {
    provider: Provider;
    color?: boolean;
    theme?: ViewTheme;
};

export const ProviderIcon = ({
    provider,
    size = 'medium',
    color = true,
    className,
    theme: fixedTheme,
    ...props
}: ProviderIconProps) => {
    const { theme } = useViewThemeContext();
    const iconSrc = getProviderIconSrc(provider, color, fixedTheme ?? theme);

    if (!iconSrc) {
        return null;
    }

    const classes = classNames({
        provider_icon: true,
        [`provider_icon__${provider}`]: provider,
        [`${className}`]: !!className,
    });

    return <Icon {...props} className={classes} icon={iconSrc} size={size} />;
};

export const InstallationProviderIcon = ({
    installation,
    size = 'medium',
    color = true,
    className,
    theme: fixedTheme,
    ...props
}: Omit<ProviderIconProps, 'provider'> & { installation: IntegrationInstallationV3 }) => {
    const { theme } = useViewThemeContext();
    const iconSrc = ProviderTraitsUtil.get(installation.provider).installationProviderIconUrl(
        installation,
        color,
        fixedTheme ?? theme
    );

    if (!iconSrc) {
        return null;
    }

    const classes = classNames({
        provider_icon: true,
        [`provider_icon__${installation.provider}`]: true,
        [`${className}`]: !!className,
    });

    return <Icon {...props} className={classes} icon={iconSrc} size={size} />;
};
