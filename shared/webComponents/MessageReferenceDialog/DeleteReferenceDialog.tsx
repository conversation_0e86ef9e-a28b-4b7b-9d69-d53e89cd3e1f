import { useState } from 'react';

import { ArchiveReasonType, Provider } from '@shared/api/models';

import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';

import { ModalContextApi, useDialogContext } from '../Modal/ModalContext';
import { ModalDialog } from '../Modal/ModalDialog';
import { TwoEqualButtons } from '../Modal/ModalDialogButtons/DialogButtons';
import { CheckboxRow, RadioRow } from '../Rows/Rows';
import { TextArea } from '../TextInput/TextArea';

import './DeleteReferenceDialog.scss';

interface Props {
    id: string;
    title: string;
    provider: Provider;
    onArchiveReference: (reason?: ArchiveReasonType, comment?: string) => void;
    description?: string;
    isPr?: boolean;
}

export const DeleteReferenceDialog = ({ provider, description, isPr, onArchiveReference }: Props) => {
    const [isReasonSelected, setIsReasonSelected] = useState<boolean>(false);
    const [reasonType, setReasonType] = useState<ArchiveReasonType>();
    const [reasonComment, setReasonComment] = useState<string>();

    const { closeModal } = useDialogContext();

    return (
        <ModalDialog
            title={`Are you sure you want to archive this ${isPr ? 'Pull Request' : ProviderTraitsUtil.get(provider).threadLabel() ?? 'reference'} from Unblocked?`}
            className="delete_reference_dialog"
            size="medium"
            buttons={
                <TwoEqualButtons
                    primary={{
                        children: 'Archive',
                        variant: 'primary',
                        onClick: () => {
                            onArchiveReference(reasonType, reasonComment);
                            closeModal();
                        },
                        disabled: isReasonSelected && reasonType === undefined && reasonComment === undefined,
                    }}
                    secondary={{
                        children: 'Cancel',
                        variant: 'secondary',
                        onClick: closeModal,
                    }}
                />
            }
        >
            <div>
                Unblocked won&apos;t use this{' '}
                {isPr ? 'pull request' : ProviderTraitsUtil.get(provider).threadLabel(false) ?? 'reference'} for any
                future responses or surface it as contextual knowledge in your IDE.
            </div>

            {description ? <div className="delete_reference_dialog__secondary_description">{description}</div> : null}

            <div className="delete_reference_dialog__reasons_container">
                <CheckboxRow
                    selected={isReasonSelected}
                    inputKey={isReasonSelected ? `reason-selected` : `reason-unselected`}
                    onClick={() => setIsReasonSelected((selected) => !selected)}
                >
                    <div className="delete_reference_dialog__reasons_checkbox_label">
                        Include why this reference is being archived
                    </div>
                </CheckboxRow>

                {isReasonSelected ? (
                    <div className="delete_reference_dialog__reasons">
                        <div className="delete_reference_dialog__reasons_rows">
                            <RadioRow
                                selected={reasonType === ArchiveReasonType.Incorrect}
                                onClick={() => setReasonType(ArchiveReasonType.Incorrect)}
                            >
                                It is inaccurate
                            </RadioRow>
                            <RadioRow
                                selected={reasonType === ArchiveReasonType.Outdated}
                                onClick={() => setReasonType(ArchiveReasonType.Outdated)}
                            >
                                It is outdated
                            </RadioRow>
                            <RadioRow
                                selected={reasonType === ArchiveReasonType.Custom}
                                onClick={() => setReasonType(ArchiveReasonType.Custom)}
                            >
                                Other
                            </RadioRow>
                        </div>

                        <TextArea
                            value={reasonComment}
                            onChange={(e) => setReasonComment(e.currentTarget.value)}
                            rows={2}
                            placeholder="Add a comment"
                            autoFocus={reasonType === ArchiveReasonType.Custom}
                        />
                    </div>
                ) : null}
            </div>
        </ModalDialog>
    );
};

export function ShowDeleteMessageReferenceDialog(modalContext: ModalContextApi, dialogProps: Props) {
    modalContext.openModal(<DeleteReferenceDialog {...dialogProps} />);
}
