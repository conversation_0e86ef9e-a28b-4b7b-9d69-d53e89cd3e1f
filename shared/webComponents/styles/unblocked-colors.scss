// white
$white-100: #fff;
$white-95: rgb(255 255 255 / 95%);
$white-90: rgb(255 255 255 / 90%);
$white-85: rgb(255 255 255 / 85%);
$white-80: rgb(255 255 255 / 80%);
$white-70: rgb(255 255 255 / 70%);
$white-60: rgb(255 255 255 / 60%);
$white-50: rgb(255 255 255 / 50%);
$white-40: rgb(255 255 255 / 40%);
$white-32: rgb(255 255 255 / 32%);
$white-30: rgb(255 255 255 / 30%);
$white-20: rgb(255 255 255 / 20%);
$white-16: rgb(255 255 255 / 16%);
$white-12: rgb(255 255 255 / 12%);
$white-10: rgb(255 255 255 / 10%);
$white-08: rgb(255 255 255 / 08%);
$white-06: rgb(255 255 255 / 06%);
$white-05: rgb(255 255 255 / 05%);
$white-04: rgb(255 255 255 / 04%);

// indigo
$indigo-100: #3e2dda;
$indigo-84a: rgb(62 45 218 / 84%);
$indigo-80a: rgb(62 45 218 / 80%);
$indigo-70a: rgb(62 45 218 / 70%);
$indigo-60: #8d81f0;
$indigo-60a: rgb(62 45 218 / 60%);
$indigo-40: #a9a1ee;
$indigo-40a: rgb(62 45 218 / 40%);
$indigo-32: #beb8ed;
$indigo-32a: rgba(62 45 218 / 32%);
$indigo-24a: rgb(62 45 218 / 24%);
$indigo-20a: rgb(62 45 218 / 20%);
$indigo-20: #d9d5fb;
$indigo-16: #dcd9f3;
$indigo-16a: rgb(62 45 218 / 16%);
$indigo-12a: rgb(62 45 218 / 12%);
$indigo-12: #e7e4f9;
$indigo-10a: rgb(62 45 218 / 10%);
$indigo-10: #eceafc;
$indigo-08a: rgb(62 45 218 / 08%);
$indigo-6: #efeef9;
$indigo-6a: rgb(62 45 218 / 6%);
$indigo-5a: rgb(62 45 218 / 5%);
$indigo-4a: rgb(62 45 218 / 4%);
$indigo-3: #f9f9fe;
$indigo-2a: rgb(62 45 218 / 2%);
$indigo-hover-100: #1100ac;

// pale indigo
$pale-indigo-100: #513eff;
$pale-indigo-80: rgba(81 62 255 / 80%);
$dark-indigo-black-100: #0b0916;
$indigo-gray-100: #353251;
$light-indigo-gray-100: #555368;

// dark gray
$dark-gray-100: #625f74;
$dark-gray-60: #a19fad;
$dark-gray-60a: rgba(98 95 116 / 60%);
$dark-gray-40: #c1c0c8;
$dark-gray-30a: rgba(98 95 116 / 30%);
$dark-gray-20a: rgba(98 95 116 / 20%);
$dark-gray-15a: rgba(98 95 116 / 15%);
$dark-gray-10a: rgba(98 95 116 / 10%);
$dark-gray-10: #e9e9f6;
$dark-gray-08a: rgba(98 95 116 / 8%);

// Medium dark gray
$medium-dark-gray-60a: rgba(60 60 67 / 60%);
$medium-dark-gray-16a: rgba(60 60 67 / 16%);
$medium-dark-gray-9a: rgba(60 60 67 / 9%);

// superdark-gray

$superdark-gray-100: rgba(40 40 40 / 100%);
$superdark-gray-60a: rgba(40 40 40 / 60%);
$superdark-gray-40a: rgba(40 40 40 / 60%);

// light gray
$light-gray-100: #e3e3f1;
$light-gray-50: #f2f2f3;
$light-gray-40: #f8f8f9;
$light-gray-30: #f7f7fb;

// superlight-gray
$superlight-gray-60a: rgba(235 235 245 / 60%);
$superlight-gray-9a: rgba(235 235 245 / 9%);

// silver
$silver-100: #d9d9d9;

// dark purple
$dark-purple-100: #1f1a38;
$dark-purple-84: #433f58;
$dark-purple-84a: rgba(31 26 56 / 84%);
$dark-purple-80a: rgba(31 26 56 / 80%);
$dark-purple-70: #625f75;
$dark-purple-70a: rgba(98 95 116 / 70%);
$dark-purple-56a: rgba(31 26 56 / 56%);
$dark-purple-50: #8f8c9c;
$dark-purple-40: #a5a3b0;
$dark-purple-40a: rgba(31 26 56 / 40%);
$dark-purple-30a: rgba(31 26 56 / 30%);
$dark-purple-30: #bcbac3;
$dark-purple-20a: rgba(31 26 56 / 20%);
$dark-purple-20: #d2d1d7;
$dark-purple-16: #d1c5f4;
$dark-purple-12a: rgba(31 26 56 / 12%);
$dark-purple-10: #e9e8e8;
$dark-purple-6a: rgba(31 26 56 / 6%);
$dark-purple-4: #f6f6f7;
$dark-purple-4a: rgba(31 26 56 / 4%);
$dark-purple-3: #f8f8f9;
$dark-purple-2a: rgba(31 26 56 / 2%);

// superdark-purple
$superdark-purple-100: rgba(71 69 82 / 100%);
$superdark-purple-60a: rgba(71 69 82 / 60%);

// plum
$plum-80a: rgba(51 16 78 / 80%);
$plum-40a: rgba(51 16 78 / 40%);
$plum-20a: rgba(51 16 78 / 20%);

// Dark grey-blue
$dark-grey-blue-8a: rgba(60 60 67 / 8%);

// Medium purple
$medium-purple: #834cde;

// light purple
$light-purple-100: #dbd6ff;
$light-purple-60a: rgba(219 214 255 / 60%);
$light-purple-50a: rgba(219 214 255 / 50%);
$light-purple-50: #e8e6fd;
$light-purple-40a: rgba(219 214 255 / 40%);
$light-purple-40: #f1efff;
$light-purple-30: #f4f3ff;
$light-purple-25a: rgba(219 214 255 / 25%);
$light-purple-20: #f8f7ff;
$light-purple-10: #eceafc;

// background purple
$background-purple-100: #f0effc;

// mauve
$mauve: #cb96e4;

// magenta
$magenta-100: #ad00ff;
$magenta-60a: rgba(173 0 255 / 60%);
$magenta-40a: rgba(173 0 255 / 40%);
$magenta-32a: rgba(173 0 255 / 32%);
$magenta-24a: rgba(173 0 255 / 24%);
$magenta-20a: rgba(173 0 255 / 20%);
$magenta-16a: rgba(173 0 255 / 16%);
$magenta-12a: rgba(173 0 255 / 12%);
$magenta-8a: rgba(173 0 255 / 8%);
$magenta-4: #fbf6ff;
$magenta-4a: rgba(173 0 255 / 4%);
$magenta-3a: rgba(173 0 255 / 3%);
$magenta-2: #fdfaff;
$magenta-2a: rgba(173 0 255 / 2%);
$magenta-0a: rgba(173 0 255 / 0%);

// Electric violet
$electric-violet: rgb(152 0 255);

// red
$red-100: #e70000;
$red-hover-100: #b60000;
$red-70: #dc5953;
$red-64a: rgba(231 0 0 / 64%);
$red-40: #ff9396;
$red-32a: rgba(231 0 0 / 32%);
$red-24a: rgba(231 0 0 / 24%);
$red-20a: rgba(231 0 0 / 20%);
$red-16a: rgba(231 0 0 / 16%);
$red-16: #4b2d2c;
$red-12a: rgba(231 0 0 / 12%);
$red-10a: rgba(231 0 0 / 10%);
$red-10: #ffe4e4;
$red-4a: rgba(231 0 0 / 4%);
$red-4: #fef5f5;

// light red
$salmon-light-100: #f38080;
$salmon-100: #f94144;
$salmon-32a: rgba(249 65 68 / 32%);
$salmon-24a: rgba(249 65 68 / 24%);
$salmon-16a: rgba(249 65 68 / 16%);
$salmon-12a: rgba(249 65 68 / 12%);
$salmon-8a: rgba(249 65 68 / 8%);
$salmon-hover-100: #c73436;

// yellow
$yellow-100: #a5a49d;
$light-yellow-100: #fffced;
$dark-yellow-100: #f2f2d0;
$mustard-100: #716713;
$mustard-64a: rgba(219 197 0 / 64%);
$mustard-32a: rgba(219 197 0 / 32%);

// gold
$gold-100: rgb(255 214 0);
$gold-64a: rgba(255 214 0 / 64%);
$gold-32a: rgb(255 214 0 / 32%);
$gold-24a: rgb(255 214 0 / 24%);
$gold-16a: rgb(255 214 0 / 16%);
$gold-8a: rgba(255 214 0 / 8%);
$gold-8: #312933;

// orange
$orange-dark-100: #bf6f00;
$orange-dark-64a: rgba(191 111 0 / 64%);
$orange-dark-32a: rgba(191 111 0 / 32%);
$orange-light-64a: rgba(252 163 38 / 64%);
$orange-light-32a: rgba(252 163 38 / 32%);
$orange-light-20a: rgba(252 163 38 / 20%);
$orange-medium: #f70;
$orange-medium-32a: rgba(255 119 0 / 32%);
$orange-medium-16a: rgba(255 119 0 / 16%);
$orange-100: #f77f00;
$orange-24a: rgba(247 127 0 / 24%);
$orange-16a: rgba(247 127 0 / 16%);

// dark green
$dark-green-100: #007b50;

// emerald green
$emerald-100: #06a77d;

// green
$green: #4ac398;
$green-100: $green;
$green-88a: rgba(74 195 152 / 88%);
$green-72a: rgba(74 195 152 / 72%);
$green-64a: rgba(74 195 152 / 64%);
$green-40a: rgba(74 195 152 / 40%);
$green-32a: rgba(74 195 152 / 32%);
$green-24a: rgba(74 195 152 / 24%);
$green-20a: rgba(74 195 152 / 20%);
$green-20: #e2f2eb;
$green-16a: rgba(74 195 152 / 16%);
$green-12a: rgba(74 195 152 / 12%);
$green-8a: rgba(74 195 152 / 8%);
$green-0a: rgba(74 195 152 / 0%);

// mint
$mint-100: #bdffe8;

// vivid-green
$vivid-green-32a: rgba(0 230 148 / 32%);
$vivid-green-16a: rgba(0 230 148 / 16%);

// tan
$tan-100: #ffddbd;

// orangeage
$orangeade-64a: rgb(252 163 38 / 64%);
$orangeade-40a: rgb(252 163 38 / 40%);
$orangeade-32a: rgb(252 163 38 / 32%);
$orangeade-20a: rgb(252 163 38 / 20%);

// violet
$violet-90a: rgb(111 45 218 / 90%);

// violet crayola
$violet-crayola-100: #766fb6;
$violet-crayola-80a: rgba(118 111 182 / 80%);
$violet-crayola-20a: rgba(118 111 182 / 20%);
$violet-crayola-16a: rgba(118 111 182 / 16%);

// amethyst
$amethyst-100: #685fd4;

// cinnamon red
$cinnamon-red-100: #ffbdbd;

// pale violet
$pale-violet: rgb(155 148 220);

// pale violet alternate
$pale-violet-alternate: #8a7ef9;
$periwinkle-100: rgba(138 126 249 / 100%);
$periwinkle-8: #1e1a36;
$periwinkle-light: #ada3ff;
$pale-violet-alternate-80a: rgba(138 126 249 / 80%);
$pale-violet-alternate-60a: rgba(138 126 249 / 60%);
$pale-violet-alternate-60: #b9b2fb;
$pale-violet-alternate-50a: rgba(138 126 249 / 50%);
$pale-violet-alternate-40a: rgba(138 126 249 / 40%);
$pale-violet-alternate-40: #d0cbfd;
$pale-violet-alternate-32a: rgba(138 126 249 / 32%);
$pale-violet-alternate-32: #dad6fd;
$pale-violet-alternate-24a: rgba(138 126 249 / 24%);
$pale-violet-alternate-16a: rgba(138 126 249 / 16%);
$pale-violet-alternate-24: #e3e0fe;
$pale-violet-alternate-12a: rgba(138 126 249 / 12%);
$pale-violet-alternate-12: #f1f0fe;
$pale-violet-alternate-8a: rgba(138 126 249 / 8%);
$pale-violet-alternate-5a: rgba(138 126 249 / 5%);
$pale-violet-alternate-5: #f1f0fe;

// pale violet dark alternate
$pale-violet-dark-alternate: #675bd5;

$soft-pink-100: #c67ef9;

// black
$black: #000;
$black-70a: rgba(0 0 0 / 70%);
$black-40a: rgba(0 0 0 / 40%);
$black-32a: rgba(0 0 0 / 32%);
$black-20a: rgba(0 0 0 / 20%);
$black-16a: rgba(0 0 0 / 16%);
$black-10a: rgba(0 0 0 / 10%);
$black-08a: rgba(0 0 0 / 08%);
$black-05a: rgba(0 0 0 / 05%);
$black-04a: rgba(0 0 0 / 04%);

// Ebony
$ebony: #110d21;
$ebony-90a: rgba(17 13 33 / 90%);
$ebony-70a: rgba(17 13 33 / 70%);
$light-ebony-100: #141126;

// Meteorite
$meteorite: rgb(68 35 109);
$meteorite-4a: rgba(68 35 109 / 4%);

// Ultramarine
$ultramarine: rgb(51 0 255);

$cobalt-100: #0045ce;

// misc
$aqua: #00a3ff;
$aqua-10a: rgba(0 163 255 / 10%);
$aqua-5a: rgba(0 163 255 / 5%);
$aquamarine: #89fcd5;
$turquoise: #89fcd5;
$turquoise-dark: #00ffa9;
$blue-crayola: #236df6;
$beau-blue: #c2ddf1;
$ghost-white: #f8fcff;
$sage: #5dd39e;
$cerise: #e25282;
$mikado: #ffc300;
$heliotrope: #c362ff;
$jacarrta: #3f246d;
$mineshaft: #3d3d3d;
$cornflower: #76a4ff;
$dixie: #e5a818;

// gradients
$ube: #8d75c1;
$scampi: #6168aa;
$astronaut: #495081;

// transparent (used for gradients)
$transparent: rgba(0 0 0 / 0%);

// iOS UI Kit System Colors
// https://developer.apple.com/design/human-interface-guidelines/foundations/color/
$system-orange-light: rgb(255 149 0);
$system-orange-light-100: rgb(255 149 0 / 100%);
$system-orange-light-16a: rgb(255 149 0 / 16%);

$system-orange-dark: rgb(255 159 10);
$system-orange-dark-100: rgb(255 159 10 / 100%);
$system-orange-dark-16a: rgb(255 159 10 / 16%);

$system-yellow-light: rgb(255 204 0);
$system-yellow-dark: rgb(255 214 10);
$system-dialog-grey: #f6f6f6;

@mixin light-gradient-background {
    background: radial-gradient(133.86% 100% at 50% 0%, $ube 0%, $scampi 100%);
}
