import { ReactElement } from 'react';
import { RenderElementProps } from 'slate-react';

import { ElementTraits } from '../../MessageEditorTypes';
import { ListItemElement } from './ListItemElement';

export function OrderedListRenderer(props: RenderElementProps) {
    return <ol className="ordered_list_element">{props.children}</ol>;
}

export const OrderedListElementTraits: ElementTraits = {
    type: 'ordered-list',
    isVoid: false,
    isMultiline: false,
    handleDeleteBackwards: true,
    canBeLastBlock: true,
    render: function (props: RenderElementProps): ReactElement | null | undefined {
        if (props.element.type === 'ordered-list') {
            return OrderedListRenderer(props);
        }
    },
};

export type OrderedListElement = { type: 'ordered-list'; children: ListItemElement[] };
