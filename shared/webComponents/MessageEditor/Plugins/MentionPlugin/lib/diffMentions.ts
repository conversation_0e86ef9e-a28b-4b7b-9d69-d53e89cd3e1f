import { EditorContent } from '../../../MessageEditor';
import { findMentions } from './findMentions';

export interface DiffMentionOptions {
    onMentionAdded?: (teamMemberId: string) => void;
    onMentionDeleted?: (teamMemberId: string) => void;
}

export const diffMentions = (
    editorContent: EditorContent,
    prevMentions: string[],
    { onMentionAdded, onMentionDeleted }: DiffMentionOptions
): string[] => {
    const newMentions = findMentions(editorContent);

    const oldMentionsSet = new Set<string>(prevMentions);
    const newMentionSet = new Set<string>(newMentions);

    if (onMentionDeleted) {
        for (const oldMention of oldMentionsSet) {
            if (!newMentionSet.has(oldMention)) {
                onMentionDeleted(oldMention);
            }
        }
    }

    if (onMentionAdded) {
        for (const newMention of newMentionSet) {
            if (!oldMentionsSet.has(newMention)) {
                onMentionAdded(newMention);
            }
        }
    }

    return Array.from(newMentionSet);
};
