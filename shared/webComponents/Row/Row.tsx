import classNames from 'classnames';
import { ReactNode, useMemo } from 'react';

import { Icon, IconSize, IconSrc } from '../Icon/Icon';

import './Row.scss';

type RowPadding = 'standard' | 'compact';

interface Props {
    icon?: IconSrc;
    iconTemplate?: ReactNode;
    iconSize?: IconSize;
    iconRounded?: boolean;
    header: ReactNode;
    description?: ReactNode;
    rightAction?: ReactNode;
    rightActionOnHover?: boolean;
    rightIcon?: IconSrc;
    onClick?: () => void;
    className?: string | undefined;
    tooltip?: string;
    padding?: RowPadding;
}

export function Row({
    icon,
    iconSize,
    iconTemplate,
    iconRounded,
    header,
    description,
    rightIcon,
    rightAction,
    rightActionOnHover,
    onClick,
    className,
    tooltip,
    padding = 'standard',
}: Props) {
    const classnames = classNames({
        row: true,
        [`row--${padding}`]: true,
        row__clickable: !!onClick,
        row__right_action_on_hover: !!rightActionOnHover,
        [`${className}`]: !!className,
    });

    const iconClassnames = classNames({
        row__icon: true,
        row__icon_rounded: iconRounded,
    });

    const iconSection = useMemo(() => {
        if (icon) {
            return <Icon icon={icon} size={iconSize ?? 'large'} />;
        }
        if (iconTemplate) {
            return iconTemplate;
        }
        return null;
    }, [icon, iconSize, iconTemplate]);

    return (
        <div className={classnames} onClick={onClick} title={tooltip}>
            {iconSection && <div className={iconClassnames}>{iconSection}</div>}
            <div className="row__title">{header}</div>
            {description && <div className="row__secondary">{description}</div>}
            {rightAction && <div className="row__right_action">{rightAction}</div>}
            {rightIcon && <Icon className="row__right_icon" icon={rightIcon} size="xSmall" />}
        </div>
    );
}
