import { ReactNode, useCallback, useMemo } from 'react';

import { PlanTemplateButtonAction } from '@shared/api/generatedApi';

import { UpsellPlanTemplateInfo } from '@shared/stores/UpsellTypes';
import { Button } from '@shared/webComponents/Button/Button';
import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';
import { DashboardUrls } from '@shared/webUtils';

import { faArrowUpRightFromSquare } from '@fortawesome/pro-solid-svg-icons/faArrowUpRightFromSquare';

import { BadgeIcon } from '../BadgeIcon/BadgeIcon';
import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { IconDialog } from '../Modal/IconDialog';
import { useDialogContext } from '../Modal/ModalContext';

import './UpsellDialog.scss';

interface Props {
    teamId: string;
    icon: IconSrc;
    children: ReactNode;
    template: UpsellPlanTemplateInfo;
}

export type UpsellSubDialogProps = Omit<Props, 'children' | 'icon'>;

export function UpsellDialog({ teamId, icon, children, template }: Props) {
    const { closeModal } = useDialogContext();

    const action = useMemo<{ title: string; action: () => void } | undefined>(() => {
        switch (template.buttonAction) {
            case 'contactSales':
            case 'downgradeContact':
                return {
                    title: 'Contact Sales',
                    action: () => ClientWorkspace.instance().handleAction({ $case: 'contactSupport' }),
                };

            case 'upgradePlan':
                return {
                    title: `Upgrade to the ${template.displayName} Plan`,
                    action: () =>
                        ClientWorkspace.instance().handleAction({
                            $case: 'openUrl',
                            url: DashboardUrls.orgPlans(teamId, template.id),
                            navigate: true,
                        }),
                };

            case 'downgradePlan':
            case 'cancelDowngrade':
            case PlanTemplateButtonAction.UnknownDefaultOpenApi:
                return undefined;
        }
    }, [template, teamId]);

    const viewPlans = useCallback(() => {
        closeModal();
        ClientWorkspace.instance().handleAction({
            $case: 'openUrl',
            url: DashboardUrls.orgPlans(teamId),
            navigate: true,
        });
    }, [teamId, closeModal]);

    return (
        <IconDialog
            bodyClassName="upsell_dialog"
            contentClassName="upsell_dialog__content"
            closeable
            variant="emphasis"
            icon={<BadgeIcon className="upsell_dialog__icon" icon={icon} />}
            title={children}
        >
            {action && (
                <Button
                    className="upsell_dialog__button"
                    onClick={() => {
                        closeModal();
                        action.action();
                    }}
                >
                    {action.title}
                    <Icon className="upsell_dialog__external_icon" icon={faArrowUpRightFromSquare} size="xxSmall" />
                </Button>
            )}

            <Button className="upsell_dialog__view_all_plans" as="link" onClick={viewPlans}>
                View all plans and features
            </Button>

            <Button className="upsell_dialog__close_link" as="link" onClick={closeModal}>
                Cancel
            </Button>

            <Button className="upsell_dialog__close_button" variant="secondary" onClick={closeModal}>
                Cancel
            </Button>
        </IconDialog>
    );
}
