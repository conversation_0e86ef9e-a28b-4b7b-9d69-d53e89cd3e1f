import { ProviderProcessingStatus, ProviderProcessingStatusStatusEnum, Team } from '@shared/api/generatedApi';

import { BrandIcons } from '@shared/webUtils/BrandIcons';
import { getProviderIconSrc } from '@shared/webUtils/ProviderUtils';

import PendingImg from '@clientAssets/process-pending.svg';

import { faClock } from '@fortawesome/pro-regular-svg-icons/faClock';
import { faArrowsRotate } from '@fortawesome/pro-solid-svg-icons/faArrowsRotate';
import { faCircleCheck } from '@fortawesome/pro-solid-svg-icons/faCircleCheck';

import { Icon } from '../Icon/Icon';
import { RowsList } from '../Rows/RowsList';

import './PendingProcessingState.scss';

const PendingProgressIcon = ({ status }: { status: ProviderProcessingStatusStatusEnum }) => {
    switch (status) {
        case 'complete':
            return <Icon icon={faCircleCheck} className="pending_progress_icon_complete" size="small" />;
        case 'notStarted':
            return <Icon icon={faClock} className="pending_progress_icon_notStarted" size="small" />;
        case 'running':
            return <Icon icon={faArrowsRotate} className="pending_progress_icon_running" size="small" />;
        case ProviderProcessingStatusStatusEnum.UnknownDefaultOpenApi:
            return null;
    }
};

const PendingProcessingProgressRow = ({ progress }: { progress: ProviderProcessingStatus }) => (
    <div className="pending_processing__progress_row">
        <Icon icon={getProviderIconSrc(progress.provider, true) ?? BrandIcons.unblocked} size="small" />
        <div>{progress.description}</div>
        <PendingProgressIcon status={progress.status} />
    </div>
);

export const PendingProcessingSection = ({ progressList }: { progressList: ProviderProcessingStatus[] }) => {
    return (
        <>
            {progressList.length > 0 && (
                <RowsList className="pending_processing__progress">
                    <div className="pending_processing__title">PROCESSING STATUS:</div>
                    {progressList.map((progress, idx) => (
                        <PendingProcessingProgressRow key={idx} progress={progress} />
                    ))}
                </RowsList>
            )}
        </>
    );
};
export const ReprocessingState = ({ progressList }: { progressList: ProviderProcessingStatus[] }) => {
    return (
        <div className="pending_processing">
            <div>
                <Icon className="pending_processing__icon" icon={PendingImg} size={130} />
                <h1>Welcome back!</h1>
                <p>
                    To protect your privacy, Unblocked archives data for accounts that have been inactive for a while.
                </p>
                <p>
                    We&apos;re currently reprocessing your connected systems and we&apos;ll notify you when it&apos;s
                    complete.
                </p>
                <PendingProcessingSection progressList={progressList} />
            </div>
        </div>
    );
};

export const InitialProcessingState = ({
    team,
    progressList,
}: {
    team: Team;
    progressList: ProviderProcessingStatus[];
}) => {
    return (
        <div className="pending_processing">
            <div>
                <Icon className="pending_processing__icon" icon={PendingImg} size={130} />
                <h1>Bootstrapping your information...</h1>
                <p>
                    Unblocked is processing the repositories and integrations you&apos;ve connected for &ldquo;
                    {team.displayName}.&rdquo;
                </p>
                <p>This may take longer if you&apos;re connecting large amounts of data.</p>
                <p>
                    <b>We&apos;ll notify you when processing is complete!</b>
                </p>
                <PendingProcessingSection progressList={progressList} />
            </div>
        </div>
    );
};
