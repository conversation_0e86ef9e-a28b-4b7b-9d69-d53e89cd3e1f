import { FilePoint, SourcePoint } from './generatedApi';

export * from './generatedApi/models';
export type {
    MessageAggregate,
    ThreadInfoAggregate,
    InstallationGroupAggregate,
    GetStorageRequest,
    SetStorageRequest,
    GetStorageResponse,
    SetStorageResponse,
} from './generatedExtraApi';

// TODO richie consolidate { shared/, common/, api/ }
//  then these weird cross module dependencies go away.
export * from '../../common/build/generated/source/proto/main/ts_proto/Message';
export {
    TranscriptionSegment,
    TranscriptionBody,
} from '../../common/build/generated/source/proto/main/ts_proto/Transcription';

export type Point = FilePoint | SourcePoint;
