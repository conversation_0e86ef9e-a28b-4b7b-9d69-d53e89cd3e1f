import sharedConfig from '../sharedConfigs/jest.config';
export default {
    ...sharedConfig,

    // A map from regular expressions to module names or to arrays of module names that allow to stub out resources with a single module
    moduleNameMapper: {
        '@clientAssets/.*': '<rootDir>../shared/mocks/MockClientAsset.txt',
        '\\.(scss)$': '<rootDir>../shared/mocks/MockStyle.ts',
        '@shared/(.*)$': '<rootDir>../shared/$1',
    },
};
