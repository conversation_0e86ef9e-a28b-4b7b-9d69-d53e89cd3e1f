import { CreateStoreProxyTraits, StoreProxyStream } from '@shared/proxy/StoreProxy/StoreProxyTypes';

import { ViewRouterViewState } from './ViewRouterTypes';

export type IDEViewRouterKey =
    | 'explorer' // VSCode explorer
    | 'main' // VSCode main
    | 'left' // JetBrains left
    | 'right' // Jetbrains right
    | 'editor';

export const IDEViewRouterStoreTraits = CreateStoreProxyTraits({
    category: 'ideViewRouter',
    keyToString: ({ key }: { key: IDEViewRouterKey }) => key,
    actions: {},
    streams: {
        stream: StoreProxyStream<ViewRouterViewState>(),
    },
});
