// variation of regex from MDN: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/email#basic_validation
const EmailValidator =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

export const EmailsHelper = {
    validate: (str: string): boolean => {
        return EmailValidator.test(str);
    },

    filterReceivable: (emails: string[]): string[] => {
        return emails.filter((email) => !(email.includes('noreply') || email.includes('no-reply')));
    },
};
