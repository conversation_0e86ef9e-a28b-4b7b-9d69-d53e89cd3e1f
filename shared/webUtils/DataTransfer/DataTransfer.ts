/**
 * Helper utility for handling clipboard and drag and drop events as they
 * share the same DataTransfer container.
 * It's a poorly structured data type that we want to simplify the extraction
 * of through this utility.
 * Down the line, we want to also be able to handle Slate fragments and nodes as well.
 */
import { ArrayUtils } from '../index';

export enum TransferMimeType {
    FRAGMENT = 'application/x-slate-fragment',
    HTML = 'text/html',
    NODE = 'application/x-slate-node',
    RICH = 'text/rtf',
    TEXT = 'text/plain',
}

export type TransferType = 'files' | 'node' | 'fragment' | 'html' | 'text' | undefined;

export interface EventDataTransfer {
    html: string | undefined;
    rich: string | undefined;
    text: string | undefined;
    files: File[] | undefined;
    type: TransferType;
}

export function getEventDataTransfer(transfer: DataTransfer): EventDataTransfer {
    const html = getDataByMimeType(transfer, TransferMimeType.HTML);
    const rich = getDataByMimeType(transfer, TransferMimeType.RICH);
    const text = getDataByMimeType(transfer, TransferMimeType.TEXT);
    let files;

    // Get and normalize files if they exist.
    if (transfer.items && transfer.items.length) {
        const fileItems = Array.from(transfer.items).map((item) =>
            item.kind === 'file' ? item.getAsFile() : undefined
        );
        files = ArrayUtils.compact(fileItems);
    } else if (transfer.files && transfer.files.length) {
        files = Array.from(transfer.files);
    }

    // Determine the type of the data.
    const data: EventDataTransfer = { files, html, rich, text, type: undefined };
    data.type = getTransferType(data);
    return data;
}

function getTransferType(data: EventDataTransfer): TransferType | undefined {
    if (data.rich && data.html) {
        return 'html';
    }
    if (data.rich && data.text) {
        return 'text';
    }

    if (data.files && data.files.length) {
        return 'files';
    }
    if (data.html) {
        return 'html';
    }
    if (data.text) {
        return 'text';
    }
    return undefined;
}

function getDataByMimeType(transfer: DataTransfer, type: TransferMimeType): string | undefined {
    const types = Array.from(transfer.types);
    return types.indexOf(type) !== -1 ? transfer.getData(type) || undefined : undefined;
}
