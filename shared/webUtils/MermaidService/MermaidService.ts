import { LazyValue } from '../LazyValue';

export type DiagramResponse = {
    svg: string;
    diagramType: string;
};

export type DiagramTheme = 'dark' | 'light';

export class MermaidService {
    static instance = LazyValue(() => new MermaidService());
    private hasInitialized = false;
    private currentTheme: DiagramTheme = 'light';

    async generate({
        id,
        content,
        theme,
    }: {
        id: string;
        content: string;
        theme: DiagramTheme;
    }): Promise<DiagramResponse> {
        await this.initializeWithTheme(theme);
        const m = await import('mermaid');
        const rr = await m.default.render(`${id}-svg`, content);
        return rr;
    }

    private async initializeWithTheme(theme: DiagramTheme) {
        if (theme === this.currentTheme && this.hasInitialized) {
            return;
        }
        this.hasInitialized = true;
        this.currentTheme = theme;
        const m = await import('mermaid');
        m.default.initialize({
            startOnLoad: false,
            logLevel: 5,
            theme: theme === 'dark' ? 'dark' : 'base',
            suppressErrorRendering: true,
            themeVariables:
                theme === 'light'
                    ? {
                          fontFamily: 'inherit',
                          textColor: '#1f1a38',
                          primaryTextColor: '#1f1a38',
                          primaryColor: '#eceafc',
                      }
                    : {
                          fontFamily: 'inherit',
                      },
        });
    }
}
