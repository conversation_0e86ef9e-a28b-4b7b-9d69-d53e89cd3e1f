import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { faGithub } from '@fortawesome/free-brands-svg-icons/faGithub';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class GithubProviderTraits extends ProviderTraits {
    readonly displayName: string = 'GitHub.com';
    readonly shortenedDisplayName: string = 'GitHub';
    readonly adminNode: string = 'Organization Owner';
    readonly determinateAdminNode: string = 'an Organization Owner';

    threadLabel(includeProvider: boolean = true): string {
        return includeProvider ? 'GitHub discussion' : 'discussion';
    }

    projectLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'GitHub repository', plural: 'GitHub repositories' }
            : { singular: 'repository', plural: 'repositories' };
    }

    workspaceLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'GitHub account', plural: 'GitHub accounts' }
            : { singular: 'account', plural: 'accounts' };
    }

    orgNode(short?: boolean): string {
        return short ? 'Account' : 'GitHub Account';
    }

    iconSrc(color?: boolean, viewTheme?: 'dark' | 'light'): IconSrc {
        return color ? (viewTheme === 'dark' ? BrandIcons.githubWhite : BrandIcons.github) : faGithub;
    }

    isScmProvider(): boolean {
        return true;
    }
}
