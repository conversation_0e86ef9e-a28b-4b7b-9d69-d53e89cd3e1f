import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { faGoogleDrive } from '@fortawesome/free-brands-svg-icons/faGoogleDrive';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class GoogleDriveProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Google Drive';
    readonly shortenedDisplayName: string = 'Google Drive';
    readonly isLongFormDocProvider = true;

    threadLabel(includeProvider: boolean = true): string {
        return includeProvider ? 'Google Drive file' : 'file';
    }

    projectLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Google Drive file', plural: 'Google Drive files' }
            : { singular: 'file', plural: 'files' };
    }

    workspaceLabelCounts(includeProvider: boolean = true): StringPluralCounts {
        return includeProvider
            ? { singular: 'Google Drive content', plural: 'Google Drive content' }
            : { singular: 'content', plural: 'content' };
    }

    iconSrc(color?: boolean): IconSrc {
        return color ? BrandIcons.googleDrive : faGoogleDrive;
    }
}
