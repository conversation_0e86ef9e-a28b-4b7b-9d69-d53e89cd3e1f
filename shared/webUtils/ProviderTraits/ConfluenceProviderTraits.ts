import { IconSrc } from '@shared/webComponents/Icon/Icon';

import { BrandIcons } from '../BrandIcons';
import { StringPluralCounts } from '../StringsHelper/StringsHelper';
import { ProviderTraits } from './ProviderTraits';

export class ConfluenceProviderTraits extends ProviderTraits {
    readonly displayName: string = 'Confluence';
    readonly shortenedDisplayName: string = 'Confluence';
    readonly isLongFormDocProvider = true;

    threadLabel(includeProvider: boolean): string {
        return includeProvider ? 'Confluence page' : 'page';
    }

    projectLabelCounts(includeProvider: boolean): StringPluralCounts {
        return includeProvider
            ? { singular: 'Confluence space', plural: 'Confluence spaces' }
            : { singular: 'space', plural: 'spaces' };
    }

    workspaceLabelCounts(includeProvider: boolean): StringPluralCounts {
        return includeProvider
            ? { singular: 'Confluence site', plural: 'Confluence sites' }
            : { singular: 'site', plural: 'sites' };
    }

    iconSrc(color?: boolean): IconSrc {
        return color ? BrandIcons.confluence : BrandIcons.confluenceDesaturated;
    }
}
