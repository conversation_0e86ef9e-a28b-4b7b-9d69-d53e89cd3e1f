import { Events } from '@shared/metrics';

export async function handleCampaign(searchParams: URLSearchParams) {
    const source = searchParams.get('utm_source') ?? undefined;
    const term = searchParams.get('utm_term') ?? undefined;
    const medium = searchParams.get('utm_medium') ?? undefined;
    const name = searchParams.get('utm_campaign') ?? undefined;

    if (!name) {
        return;
    }
    try {
        await Events.session.recordCampaign(window.location.href, {
            name,
            source,
            term,
            medium,
        });
    } catch {
        return;
    }
}
