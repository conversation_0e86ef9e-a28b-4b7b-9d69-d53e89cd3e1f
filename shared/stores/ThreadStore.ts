import { v4 as uuid } from 'uuid';

import { AskQuestionContent, ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { LocalContext } from '@shared/webComponents/ClientWorkspace/LocalContextProvider';
import { BlockUtils } from '@shared/webUtils/BlockUtils';

import { API } from '../api';
import {
    Block,
    Mark,
    Provider,
    Thread,
    ThreadContext,
    ThreadInfo,
    ThreadParticipant,
    ThreadType,
    UpdateThreadRequest,
} from '../api/models';
import { ArrayUtils, MessageTransformer } from '../webUtils';
import { ApiDataStreamState } from './ApiDataStream';
import { getCurrentTeamMember } from './PersonStore';
import { ProductFeedbackStore } from './ProductFeedbackStore';
import { OverlayInstance, withOverlay } from './StreamOverlay';
import { TeamMemberBotStore } from './TeamMemberBotStore';
import { ThreadInfoAggregate } from './ThreadInfoAggregate';
import { TeamThreadInfoStreams } from './ThreadListStore';
import { createValueStream } from './ValueStream';

// A stream that publishes events whenever the local user creates or archives a stream.
// This is used so that the FileSourceMarkStream can publish the new stream immediately.
const createOrArchiveThreadStreamBase = createValueStream<{ threadId: string; thread?: ThreadInfoAggregate }>();
export const createThreadStream = createOrArchiveThreadStreamBase.stream;

export interface CreateThreadArgs {
    threadId: string;
    title: string;
    blocks: Block[];
    repoId?: string;
    sourcemark?: Mark;
    threadParticipants: ThreadParticipant[];
    mentions?: string[];
    fileMarks?: Mark[];
    isPrivate?: boolean;
    context?: ThreadContext | undefined;
}

export async function createThread(teamId: string, createThreadRequest: CreateThreadArgs): Promise<Thread> {
    const { threadId, title, blocks, repoId, sourcemark, fileMarks, threadParticipants, mentions, isPrivate, context } =
        createThreadRequest;

    const version = '1';
    const encodedMessage = MessageTransformer.fromMessageToBytes({
        blocks,
        version,
    });

    const userIdentity = getCurrentTeamMember(teamId);

    const messageId = crypto.randomUUID();

    // Make a mock ThreadInfo to temporarily overlay in the UI
    const threadInfo: ThreadInfo = {
        thread: {
            id: threadId,
            teamId,
            title,
            threadType: ThreadType.UnknownDefaultOpenApi,
            provider: Provider.Unblocked,
            lastMessageCreatedAt: new Date(),
            links: {
                dashboardUrl: '',
                webExtensionUrl: '',
            },
        },
        repoId,
        messages: [
            {
                id: messageId,
                authorTeamMemberId: userIdentity?.id ?? crypto.randomUUID(),
                createdAt: new Date(),
                links: {},
                messageContent: {
                    content: encodedMessage,
                    version: '1',
                },
            },
        ],
        participants: ArrayUtils.compactMap(
            [...threadParticipants, { teamMemberId: userIdentity?.id }],
            (participant) => participant.teamMemberId
        ),
        mark: sourcemark,
        modifiedAt: new Date().getMilliseconds() * 1_000,
        capabilities: {
            canReply: true,
            canUpdatePrivacy: true,
        },
    };

    const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
        overlayMutationFn: (state) =>
            state.$case === 'ready' ? { ...state, value: [...state.value, threadInfo] } : state,
        shouldCancel: (state) =>
            state.$case === 'ready' && state.value.some((threadInfo) => threadInfo.thread.id === threadId),
    };

    const addedThread = await withOverlay(
        TeamThreadInfoStreams.getOverlays((key) => key.teamId === teamId && key.type === 'mine'),
        overlay,
        async () =>
            await API.threads.createThreadV3({
                threadId,
                teamId,
                createThreadRequestV3: {
                    title,
                    message: {
                        id: messageId,
                        repoId,
                        threadId,
                        sourcemark,
                        fileMarks,
                        messageContent: {
                            content: encodedMessage,
                            version,
                        },
                        mentions,
                    },
                    threadParticipants,
                    isPrivate,
                    context,
                },
            })
    );

    // Trigger updates for FileSourceMarkStream
    const threadInfoAggregate: ThreadInfoAggregate = {
        thread: addedThread,
        repoId: threadInfo.repoId,
        messages: [],
        participants: [],
        teamParticipants: [],
        mark: sourcemark ?? fileMarks?.[0],
        experts: [],
        capabilities: {
            canReply: true,
            canUpdatePrivacy: false,
        },
    };

    createOrArchiveThreadStreamBase.updateValue({ threadId, thread: threadInfoAggregate });

    return addedThread;
}

export async function updateThread(
    teamId: string,
    threadId: string,
    updateThreadRequest: UpdateThreadRequest
): Promise<Thread> {
    return API.threads.updateThread({
        threadId,
        teamId,
        updateThreadRequest,
    });
}

export async function updateThreadTitle(teamId: string, threadId: string, title: string): Promise<Thread> {
    const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
        overlayMutationFn: (state) => {
            if (state.$case === 'loading') {
                return state;
            }
            const updatedThreads = state.value.map((threadInfo) =>
                threadInfo.thread.id === threadId
                    ? { ...threadInfo, thread: { ...threadInfo.thread, title } }
                    : threadInfo
            );
            return { ...state, value: updatedThreads };
        },
        shouldCancel: (state) =>
            state.$case === 'ready' &&
            state.value.some((threadInfo) => threadInfo.thread.id === threadId && threadInfo.thread.title === title),
    };

    return await withOverlay(
        TeamThreadInfoStreams.getOverlays((key) => key.teamId === teamId),
        overlay,
        async () => updateThread(teamId, threadId, { title })
    );
}

export async function confirmArchiveThread(teamId: string, threadId: string, threadTitle: string) {
    const response = await ClientWorkspace.instance().dialog({
        title: 'Are you sure you want to archive this discussion?',
        description:
            'Archived discussions will be removed, and can be found in the Recently Archived section of the web dashboard.',
        okTitle: 'Archive',
        cancelTitle: 'Cancel',
    });

    if (response.response === 'ok') {
        {
            await archiveThread(teamId, threadId);
            ClientWorkspace.instance().notify({
                type: 'info',
                title: `'${threadTitle}' has been archived.`,
                content: '',
            });

            return true;
        }
    }
    return false;
}

export async function archiveThread(teamId: string, threadId: string): Promise<Thread> {
    const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
        overlayMutationFn: (state) => {
            if (state.$case === 'loading') {
                return state;
            }

            const threads = state.value.filter((thread) => thread.thread.id !== threadId);
            return { ...state, value: threads };
        },
        shouldCancel: (state) =>
            state.$case === 'ready' && state.value.every((threadInfo) => threadInfo.thread.id !== threadId),
    };

    const thread = await withOverlay(
        TeamThreadInfoStreams.getOverlays((key) => key.teamId === teamId),
        overlay,
        async () =>
            API.threads.archiveThread({
                threadId,
                teamId,
            })
    );

    // Trigger updates for FileSourceMarkStream
    createOrArchiveThreadStreamBase.updateValue({ threadId });

    return thread;
}

export async function confirmRestoreThread(teamId: string, threadId: string, threadTitle: string) {
    const response = await ClientWorkspace.instance().dialog({
        title: 'Are you sure you want to restore this discussion?',
        description: 'Restored discussions will re-appear in the source code.',
        okTitle: 'Restore',
        cancelTitle: 'Cancel',
    });

    if (response.response === 'ok') {
        {
            await restoreThread(teamId, threadId);
            ClientWorkspace.instance().notify({
                type: 'info',
                title: `'${threadTitle}' has been restored.`,
                content: '',
            });

            return true;
        }
    }
    return false;
}

export async function restoreThread(teamId: string, threadId: string): Promise<Thread> {
    const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
        overlayMutationFn: (state) => {
            if (state.$case === 'loading') {
                return state;
            }

            const threads = state.value.filter((thread) => thread.thread.id !== threadId);
            return { ...state, value: threads };
        },
        shouldCancel: (state) =>
            state.$case === 'ready' && state.value.every((threadInfo) => threadInfo.thread.id !== threadId),
    };

    TeamThreadInfoStreams.refresh((key) => key.teamId === teamId && key.type === 'archived');
    const thread = await withOverlay(
        TeamThreadInfoStreams.getOverlays((key) => key.teamId === teamId && key.type === 'archived'),
        overlay,
        async () =>
            await API.threads.restoreThread({
                threadId,
                teamId,
            })
    );

    // Trigger updates for FileSourceMarkStream
    const threadInfoAggregate: ThreadInfoAggregate = {
        thread,
        messages: [],
        participants: [],
        teamParticipants: [],
        experts: [],
        capabilities: {
            canReply: false,
            canUpdatePrivacy: false,
        },
    };

    createOrArchiveThreadStreamBase.updateValue({ threadId, thread: threadInfoAggregate });

    return thread;
}

export async function deleteThread(teamId: string, threadId: string): Promise<void> {
    const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
        overlayMutationFn: (state) => {
            if (state.$case === 'loading') {
                return state;
            }

            const threads = state.value.filter((threadInfo) => threadInfo.thread.id !== threadId);
            return { ...state, value: threads };
        },
        shouldCancel: (state) => {
            return state.$case === 'ready' && state.value.every((threadInfo) => threadInfo.thread.id !== threadId);
        },
    };

    TeamThreadInfoStreams.refresh((key) => key.teamId === teamId && key.type === 'archived');

    return await withOverlay(
        TeamThreadInfoStreams.getOverlays((key) => key.teamId === teamId && key.type === 'archived'),
        overlay,
        async () =>
            API.threads.deleteThread({
                threadId,
                teamId,
            })
    );
}

export async function createQaThread({
    teamId,
    threadId,
    query,
    isThreadPrivate,
    mentions = [],
    localContext,
}: {
    teamId: string;
    threadId?: string;
    query: AskQuestionContent;
    isThreadPrivate?: boolean | undefined;
    mentions?: string[];
    localContext?: LocalContext;
}): Promise<Thread> {
    const botTeamMemberId = await TeamMemberBotStore.instance().getBotTeamMemberId(teamId);
    const context =
        localContext ??
        (await ClientWorkspace.instance().localContextProvider?.getContext(teamId, threadId)) ??
        undefined;
    const title = query.$case === 'blocks' ? BlockUtils.getTitleText(query.content) : query.content;

    const blocks = query.$case === 'blocks' ? query.content : BlockUtils.createTextBlocks(query.content);

    const newThread = await createThread(teamId, {
        threadId: threadId ?? uuid(),
        title,
        blocks,
        threadParticipants: [],
        mentions: [botTeamMemberId, ...mentions],
        isPrivate: isThreadPrivate,
        context: context?.threadContext,
        sourcemark: context?.sourceMark,
        repoId: context?.repoId,
    });
    await ProductFeedbackStore.get(teamId).refresh();
    return newThread;
}
