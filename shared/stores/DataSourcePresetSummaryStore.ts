import { Stream } from 'xstream';

import { API } from '@shared/api';
import { DataSourcePresetSummary, IntegrationInstallationV3 } from '@shared/api/generatedApi';

import { ArrayUtils, getProviderDisplayName, Sorter } from '@shared/webUtils';
import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { isScmProvider } from '@shared/webUtils/ProviderUtils';
import { MS } from '@shared/webUtils/TimeUtils';

import { ResolvedPreset, ResolvedPresets } from './DataSourcePresetTypes';
import { IntegrationsStore } from './IntegrationsStore';
import { LoadableState } from './LoadableState';
import { PollingDataStream } from './PollingDataStream';

export class DataSourcePresetSummaryStore {
    static readonly get = LazyValueCache((teamId: string) => new DataSourcePresetSummaryStore(teamId));

    constructor(private teamId: string) {}

    private pollingStream = new PollingDataStream({
        teamId: this.teamId,
        period: MS.hours(1),
        pollFn: () => API.dataSourcePresets.getDataSourcePresets({ teamId: this.teamId }),
    });

    readonly stream = this.pollingStream
        .map<LoadableState<DataSourcePresetSummary[]>>((value) => ({ $case: 'ready', value }))
        .startWith({ $case: 'loading' });

    private joinPresetProviders = (
        preset: DataSourcePresetSummary,
        installationMap: Map<string, IntegrationInstallationV3>
    ): ResolvedPreset | undefined => {
        const installationsList = ArrayUtils.compactMap(preset.installationIds, (id) => installationMap.get(id));
        const installations = ArrayUtils.distinct(installationsList.sort(DataSourceProviderSorter));

        return {
            id: preset.id,
            avatarUrl: preset.avatarUrl,
            displayName: preset.displayName,
            installations,
        };
    };

    readonly resolvedPresetStream = Stream.combine(this.stream, IntegrationsStore.get(this.teamId).installations)
        .map<LoadableState<ResolvedPreset[]>>(([state, installationsState]) => {
            if (state.$case === 'loading' || installationsState.$case === 'loading') {
                return { $case: 'loading' };
            }
            const installationsMap = new Map(
                installationsState.value.installations.map((installation) => [installation.id, installation])
            );
            return {
                $case: 'ready',
                value: ArrayUtils.compactMap(state.value, (preset) =>
                    this.joinPresetProviders(preset, installationsMap)
                ),
            };
        })
        .remember();

    async refresh(): Promise<DataSourcePresetSummary[]> {
        return await this.pollingStream.trigger();
    }

    resolvedPresetsStream(selectedId?: string) {
        return this.resolvedPresetStream.map<LoadableState<ResolvedPresets>>((state) => {
            if (state.$case === 'loading') {
                return state;
            }
            const available = state.value;
            const selected = selectedId ? available.find((preset) => preset.id === selectedId) : undefined;
            return { $case: 'ready', value: { available, selected } };
        });
    }
}

export const DataSourceProviderSorter = Sorter.makeSortFn<IntegrationInstallationV3>([
    Sorter.byValue((installation) => {
        switch (true) {
            case isScmProvider(installation.provider):
                return -1;
            default:
                return 0;
        }
    }),
    Sorter.byValue((installation) => getProviderDisplayName(installation.provider)),
]);
