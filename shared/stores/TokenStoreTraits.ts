import { CreateApiKeyResponse } from '@shared/api/generatedApi';

import {
    CreateStoreProxyTraits,
    StoreProxyAction1Args,
    StoreProxyStream,
} from '@shared/proxy/StoreProxy/StoreProxyTypes';

import { ClientApiKeyMetadata, ScimTokenStoreState } from './ApiTokenStoreTypes';
import { LoadableState } from './LoadableState';

export const TokenStoreTraits = CreateStoreProxyTraits({
    category: 'apiToken',
    keyToString: (key: { teamId: string }) => key.teamId,
    actions: {
        addToken: StoreProxyAction1Args<
            {
                tokenName: string;
            },
            CreateApiKeyResponse
        >(),
        deleteToken: StoreProxyAction1Args<
            {
                apiKeyId: string;
            },
            void
        >(),
    },
    streams: {
        stream: StoreProxyStream<LoadableState<ClientApiKeyMetadata[]>>(),
    },
});

export const ScimTokenStoreTraits = CreateStoreProxyTraits({
    category: 'ScimToken',
    keyToString: (key: { teamId: string }) => key.teamId,
    actions: {
        addToken: StoreProxyAction1Args<
            {
                tokenName: string;
            },
            CreateApiKeyResponse
        >(),
        deleteToken: StoreProxyAction1Args<
            {
                apiKeyId: string;
            },
            void
        >(),
    },
    streams: {
        stream: StoreProxyStream<LoadableState<ScimTokenStoreState>>(),
        tokenNameStream: StoreProxyStream<LoadableState<string>>(),
    },
});
