import { Person } from '@shared/api/generatedApi';

import { CreateStoreProxyTraits, StoreProxyStream } from '@shared/proxy/StoreProxy/StoreProxyTypes';

export type PersonState =
    | { $case: 'loading' }
    | {
          $case: 'unauthenticated';
      }
    | { $case: 'loaded'; person: Person }
    | { $case: 'error' };

export const PersonStoreTraits = CreateStoreProxyTraits({
    category: 'person',
    keyToString: ({}) => '',
    actions: {},
    streams: {
        stream: StoreProxyStream<PersonState>(),
        person: StoreProxyStream<Person | undefined>(),
    },
});
