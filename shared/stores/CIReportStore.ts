import { API } from '@shared/api';
import { CIReport } from '@shared/api/generatedApi';

import { CIProvider } from '@shared/webUtils/CIProviderTraits/CIProviderTraitsUtil';
import { logger } from '@shared/webUtils/log';

import { LoadableState } from './LoadableState';
import { ValueStream } from './NewValueStream';
export type CIReportItem = CIReport & {
    id: string;
};

type CIReportState = {
    items: CIReportItem[];
};
const log = logger('CIReportStore');
export class CIReportStore {
    private valueStream = new ValueStream<LoadableState<CIReportState>>({ $case: 'loading' });
    readonly stream = this.valueStream;

    constructor(
        private provider: CIProvider,
        public teamId: string,
        private installationId: string
    ) {
        setTimeout(() => this.refresh());
    }

    private async refresh() {
        let reports: CIReport[] = [];
        try {
            reports = await API.ci.listCIReports({
                teamId: this.teamId,
                installationId: this.installationId,
                limit: 5,
            });
        } catch (e) {
            log.error('Failed to fetch reports', e);
            reports = [];
        }
        this.valueStream.value = {
            $case: 'ready',
            value: {
                items: reports.map(
                    (report): CIReportItem => ({
                        ...report,
                        id: report.externalHtmlUrl,
                    })
                ),
            },
        };
    }
}
