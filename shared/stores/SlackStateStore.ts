import { Stream } from 'xstream';

import { API } from '@shared/api';
import { Plan, Provider, SlackState, SlackTeam } from '@shared/api/generatedApi';

import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { MS } from '@shared/webUtils/TimeUtils';

import { FeatureSettingsStore } from './FeatureSettingsStore';
import { IntegrationsStore } from './IntegrationsStore';
import { PlanStore } from './PlanStore';
import { PollingDataStream } from './PollingDataStream';
import { UpsellStore } from './UpsellStore';

export type FetchSlackStreamState = { $case: 'loading' } | { $case: 'ready'; slackState: SlackState | undefined };

export type SlackStateStreamState =
    | { $case: 'loading' }
    | { $case: 'loadedWithoutData' }
    | {
          $case: 'ready';
          slackState: SlackState;
          installUrl: string;
          plan: Plan;
          upsellForPlan: boolean;
          userCanReinstall: boolean;
      };

// Class dependencies
interface Deps {
    getSlackTeamsFn: (teamId: string) => Promise<SlackTeam[]>;

    getSlackStateFn: (teamId: string, slackId: string) => Promise<SlackState>;
}

// Default dependencies
const DefaultDeps = (): Deps => ({
    getSlackTeamsFn: (teamId: string) => API.slack.getSlackTeams({ teamId }),
    getSlackStateFn: (teamId: string, slackId: string) => API.slack.getSlackState({ teamId, slackId }),
});

export class SlackStateStore {
    // Map of teams to stores
    static get = LazyValueCache((teamId: string) => new SlackStateStore(teamId));

    readonly stream: Stream<SlackStateStreamState>;

    private pollingSignalStream: PollingDataStream<FetchSlackStreamState>;
    private integrationsStore: IntegrationsStore;
    constructor(
        private teamId: string,
        private deps = DefaultDeps()
    ) {
        this.pollingSignalStream = new PollingDataStream<FetchSlackStreamState>({
            period: MS.minutes(60),
            teamId,
            pollFn: async () => {
                try {
                    const [slackTeam] = await deps.getSlackTeamsFn(teamId);
                    const slackState = await deps.getSlackStateFn(teamId, slackTeam.id);
                    return {
                        $case: 'ready',
                        slackState,
                    };
                } catch {
                    return { $case: 'ready', slackState: undefined };
                }
            },
        });
        this.integrationsStore = IntegrationsStore.get(teamId);

        this.stream = Stream.combine(
            this.pollingSignalStream,
            PlanStore.get(teamId).stream,
            FeatureSettingsStore.get(teamId).stream,
            UpsellStore.get(teamId).stream,
            this.integrationsStore.installations
        )
            .mapAsync<SlackStateStreamState>(
                async ([slackStateStreamState, planState, featureSettingsState, upsellState, installationsState]) => {
                    if (
                        slackStateStreamState.$case === 'loading' ||
                        planState.$case === 'loading' ||
                        featureSettingsState.$case === 'loading' ||
                        upsellState.$case === 'loading' ||
                        installationsState.$case === 'loading'
                    ) {
                        return { $case: 'loading' };
                    }

                    const slackState = slackStateStreamState.slackState;
                    const { enableRoleBasedAccessControl } = featureSettingsState.settings;
                    const userCanReinstall = enableRoleBasedAccessControl
                        ? featureSettingsState.isAllowedToUpdate
                        : true;

                    const slackIsInstalled = installationsState.value.installations.some(
                        ({ provider }) => provider === Provider.Slack
                    );

                    const installUrl = await this.integrationsStore.getInstallUrl({ provider: Provider.Slack });

                    if (!slackState || !installUrl || !slackIsInstalled) {
                        return { $case: 'loadedWithoutData' };
                    }

                    return {
                        $case: 'ready',
                        slackState,
                        installUrl,
                        plan: planState.plan,
                        upsellForPlan: !!upsellState.answerPreferences,
                        userCanReinstall,
                    };
                }
            )
            .startWith({ $case: 'loading' })
            .remember();

        // Keep these streams alive forever -- we want to cache all output indefinitely and
        // allow instant replay.
        // FIXME: find a better way to do this kind of thing
        this.stream.subscribe({});
    }

    refresh() {
        this.pollingSignalStream.trigger();
    }

    async sendReinstallRequest(memberIds: string[]) {
        await API.slack.sendReinstallAppRequest({ teamId: this.teamId, slackReinstallAppRequestBody: { memberIds } });
    }
}
