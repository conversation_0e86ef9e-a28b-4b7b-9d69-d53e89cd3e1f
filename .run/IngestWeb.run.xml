<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="IngestWeb" type="JetRunConfigurationType" folderName="internal batch">
    <envs>
      <env name="SERVICE_PORT" value="8115" />
      <env name="SERVICE_TARGET_PORT" value="8115" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.nextchaptersoftware.ingest.web.ApplicationKt" />
    <module name="unblocked.projects.services.ingest-web.main" />
    <shortenClasspath name="NONE" />
    <extension name="net.ashald.envfile">
      <option name="IS_ENABLED" value="false" />
      <option name="IS_SUBST" value="false" />
      <option name="IS_PATH_MACRO_SUPPORTED" value="false" />
      <option name="IS_IGNORE_MISSING_FILES" value="false" />
      <option name="IS_ENABLE_EXPERIMENTAL_INTEGRATIONS" value="false" />
      <ENTRIES>
        <ENTRY IS_ENABLED="true" PARSER="runconfig" IS_EXECUTABLE="false" />
      </ENTRIES>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>