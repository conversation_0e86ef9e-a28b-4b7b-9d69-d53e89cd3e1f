package com.nextchaptersoftware.ktlint.utils

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class PackageValidatorTest {
    @Test
    fun testValidPackage() {
        val packageValidator = PackageValidator(inclusionPatterns = listOf(Regex("com.nextchaptersoftware.db")))
        val validPackageName = "com.nextchaptersoftware.db.richie.is.awesome"
        assertThat(packageValidator.validate(validPackageName)).isTrue
    }

    @Test
    fun testInvalidPackage() {
        val packageValidator = PackageValidator(inclusionPatterns = listOf(Regex("com.nextchaptersoftware.db")))
        val invalidPackageName = "com.nextchaptersoftware.richie.is.awesome"
        assertThat(packageValidator.validate(invalidPackageName)).isFalse
    }
}
