@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'fonts' as *;
@use 'theme' as *;
@use 'flex' as *;

.unconnected_docs {
    @include flex-column-center;
    gap: $spacer-18;
    width: 100%;
    margin-top: $spacer-26;
    color: themed($text);

    h5 {
        font-size: $font-size-16;
        font-weight: $font-weight-bold;
        line-height: $line-height-22;
    }
    .unconnected_docs__selector {
        width: 100%;
        .dropdown {
            width: 100%;
            margin-top: $spacer-16;
        }
    }
    .button__primary {
        width: 100%;
        padding: $spacer-14 $spacer-40;
    }

    @include breakpoint(sm) {
        @include flex-column-start;
    }
}

.integration_dropdown_row {
    .icon {
        margin-right: $spacer-8;
    }
}
