import { API } from '@shared/api';
import { ListInstallationsRequest, Provider, ScmInstance, TeamStatusStatusEnum } from '@shared/api/generatedApi';

import { PollingDataStream } from '@shared/stores/PollingDataStream';
import { ArrayUtils } from '@shared/webUtils';
import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { isScmProvider } from '@shared/webUtils/ProviderUtils';
import { MS } from '@shared/webUtils/TimeUtils';
import { SkipSelectRepoCheck } from '@web/auth/Connect/SkipSelectRepoCheck';
import { RepoToPartial, ScmRepoToPartial } from '@web/settings/Scm/ScmSettingsTypes';

export type ScmOnboardingState = {
    $case: 'scm';
    scmInstance: ScmInstance;
    newInstallUrl?: string;
    modifyInstallUrl?: string;
    teamStatus: TeamStatusStatusEnum;
    hasMultipleInstallations: boolean;
    allReposAccessible: boolean;
    provider: Provider;
};

export class TeamOnboardingScmStore {
    static get = LazyValueCache((teamId: string) => new TeamOnboardingScmStore(teamId));
    readonly stream: PollingDataStream<ScmOnboardingState>;

    constructor(private readonly teamId: string) {
        this.stream = new PollingDataStream({
            teamId,
            pollFn: async () => {
                return this.getScmState();
            },
            period: MS.minutes(10),
        });
    }

    public async refresh() {
        await this.stream.trigger();
    }

    private async getScmState(): Promise<ScmOnboardingState> {
        const status = await API.teams.getTeamStatus({ teamId: this.teamId });
        const { installations } = await API.installations.listIntegrationInstallationsV4({ teamId: this.teamId });

        // HACK 1: Find the first SCM installation -- there should always be one
        const installation = installations.find((installation) => isScmProvider(installation.provider));
        if (!installation) {
            throw new Error('SCM IntegrationInstallation not found');
        }
        const scmInstance = await API.scmInstall.getOrgInstallationScm({
            teamId: this.teamId,
            installationId: installation.id,
        });

        // HACK 2: This is a hack IMO. We should be trying to get the installation for the scm prrovider of the current identity from team member at hack 1. Need to funnel person in.
        const listInstallationsRequest: ListInstallationsRequest = scmInstance.scmAccount.enterpriseProviderId
            ? {
                  provider: installation.provider,
                  enterpriseProviderId: scmInstance.scmAccount.enterpriseProviderId,
              }
            : {};

        const [scmInstallation, scmRepos] = await Promise.all([
            await API.scmInstall.listInstallations(listInstallationsRequest),
            await API.scmInstall.getOrgInstallationScmRepos({
                teamId: this.teamId,
                installationId: installation.id,
            }),
        ]);

        const repos = [
            ...scmRepos.reposSelected.map((repo) => RepoToPartial(repo, true)),
            ...ArrayUtils.compactMap(scmRepos.reposNotSelected, (repo) => ScmRepoToPartial(repo, false)),
        ];
        // Find the installation URL for the provider for the SCM type we've already installed
        const { installUrl: newInstallUrl } = scmInstallation;
        const modifyInstallUrl = scmInstance.installUrl;
        const hasMultipleInstallations = scmInstallation.installations.length > 1;
        const allReposAccessible = scmInstance.scmAccount.allReposAccessible;
        try {
            const { reposToSelect, shouldShowRepoSelection } = SkipSelectRepoCheck(scmInstance.scmAccount, repos);

            // If the team already has selected repos, we should *not* auto select and respect the currently selected repos
            // This should only occur for the nth user.
            const alreadySelectedRepos = repos.filter((repo) => repo.isSelected);
            if (shouldShowRepoSelection || !reposToSelect.length || alreadySelectedRepos.length > 0) {
                return {
                    $case: 'scm',
                    scmInstance,
                    newInstallUrl,
                    modifyInstallUrl,
                    teamStatus: status.status,
                    provider: installation.provider,
                    hasMultipleInstallations,
                    allReposAccessible,
                };
            }
            const installRepositoriesIds = reposToSelect.map((repo) => repo.id);

            await API.scmInstall.patchOrgInstallationScmRepos({
                teamId: this.teamId,
                installationId: scmInstance.installationId,
                scmRepoChanges: {
                    repoIdsAdded: installRepositoriesIds,
                    repoIdsRemoved: [],
                },
            });
            const updatedStatus = await API.teams.getTeamStatus({ teamId: this.teamId });
            return {
                $case: 'scm',
                scmInstance,
                newInstallUrl,
                modifyInstallUrl,
                teamStatus: updatedStatus.status,
                provider: installation.provider,
                hasMultipleInstallations,
                allReposAccessible,
            };
        } catch (e) {
            return {
                $case: 'scm',
                scmInstance,
                newInstallUrl,
                modifyInstallUrl,
                teamStatus: status.status,
                provider: installation.provider,
                hasMultipleInstallations,
                allReposAccessible,
            };
        }
    }
}
