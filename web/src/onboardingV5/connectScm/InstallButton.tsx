import { MouseE<PERSON>, ReactNode, useCallback } from 'react';

import { Button, Props as ButtonProps } from '@shared/webComponents/Button/Button';
import { IconSrc } from '@shared/webComponents/Icon/Icon';
import { InstallStorage } from '@web/auth/Install/InstallStorage';

import './InstallButton.scss';

interface Props extends ButtonProps {
    url?: string;
    icon?: IconSrc;
}

export const InstallButton = ({ url, onClick, children, ...props }: Props) => {
    const clickHandler = useCallback(
        (event: MouseEvent<HTMLButtonElement>) => {
            onClick?.(event);

            if (url) {
                InstallStorage.setInstallLocation('Web');
                window.location.href = url;
            }
        },
        [url, onClick]
    );

    return (
        <Button className="install_button" {...props} iconLocation="end" onClick={clickHandler}>
            <span>{children}</span>
        </Button>
    );
};

export const InstallButtonGroup = ({ children }: { children: ReactNode }) => (
    <div className="install_button_group">{children}</div>
);
