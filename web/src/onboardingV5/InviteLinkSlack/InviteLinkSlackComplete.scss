@use 'misc' as *;
@use 'layout' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'theme' as *;

.invite_link_slack_complete {
    height: 100%;
    color: themed($text-secondary);
    display: grid;
    place-items: center;

    .invite_link_slack_complete__content {
        @include flex-column-center;
        text-align: center;
        width: 100%;
        max-width: 560px;

        h1 {
            font-size: $font-size-36;
            line-height: $line-height-44;
            margin-bottom: $spacer-8;
        }

        p {
            color: themed($text-secondary);
            font-size: $font-size-18;
            line-height: $line-height-24;
            margin: 0 0 $spacer-24;
        }
    }

    .invite_link_slack_complete__button {
        @include flex-center-center;
        padding: $spacer-10 $spacer-60;
        margin-bottom: $spacer-12;
    }

    .invite_link_slack_complete__backup {
        font-size: $font-size-14;
        color: themed($text-secondary);
        line-height: $spacer-20;
    }
}
