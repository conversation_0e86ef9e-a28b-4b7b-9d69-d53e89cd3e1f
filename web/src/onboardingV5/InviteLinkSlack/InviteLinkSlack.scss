@use 'misc' as *;
@use 'layout' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'theme' as *;

$invite-slack-max-width: 600px;
.invite_link_slack {
    padding: $spacer-48 0;
    max-width: $invite-slack-max-width;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
    text-align: center;
    @include flex-center-center;
    .title-highlighted {
        color: themed($headline-highlighted);
    }

    .invite_link_slack__title {
        font-size: $font-size-36;
        line-height: $line-height-44;
        margin-bottom: $spacer-8;
    }

    .invite_link_slack__desc {
        color: themed($text-secondary);
        font-size: $font-size-18;
        line-height: $line-height-24;
        margin: 0 0 $spacer-22;
        max-width: 400px;
        b {
            font-weight: $font-weight-bold;
            color: themed($text);
        }
    }

    .invite_link_slack__desc,
    .button {
        z-index: 1;
    }

    .invite_link_slack__button {
        padding: $spacer-16 $spacer-40;
        margin-top: $spacer-12;
        font-size: $font-size-16;
    }
}
