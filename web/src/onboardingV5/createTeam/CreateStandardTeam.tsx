import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { InstallationV2, Provider } from '@shared/api/generatedApi';

import { ButtonVariant } from '@shared/webComponents/Button/Button';
import { ExternalLink } from '@shared/webComponents/ExternalLink/ExternalLink';
import { IntegrationImg } from '@shared/webComponents/IntegrationImg/IntegrationImg';
import { DashboardUrls, Sorter, StringsHelper } from '@shared/webUtils';
import { getProviderAdminNode, getProviderOrgNode } from '@shared/webUtils/ProviderUtils';
import { Button } from '@web/components';
import { InstallationIcon } from '@web/components/InstallationIcon/InstallationIcon';
import { AdminPermissionDialogLink } from '@web/settings/Scm/AdminPermissionDialog';

import './CreateStandardTeam.scss';

interface Props {
    provider: Provider;
    installations: InstallationV2[];
}

export const CreateStandardTeam = ({ provider, installations }: Props) => {
    const navigate = useNavigate();
    const onConnect = useCallback(
        (installation: InstallationV2) => {
            // Existing teams: go to the team root for this team
            if (installation.teamId) {
                navigate(DashboardUrls.team(installation.teamId));
            }
            // New teams: go to the connection UI for this installation
            else {
                navigate(DashboardUrls.connectRepo(installation.installationId));
            }
        },
        [navigate]
    );
    if (!installations.length) {
        return <EmptyInstallations provider={provider} />;
    }
    return (
        <div className="create_standard_team">
            <p>
                Listed below are the&nbsp;{StringsHelper.pluralize({ singular: getProviderOrgNode(provider, false) })}
                &nbsp;available for you to connect.
            </p>
            <ConnectRowList provider={provider} installations={installations} onConnect={onConnect} />
        </div>
    );
};

const EmptyInstallations = ({ provider }: { provider: Provider }) => {
    return (
        <div className="create_team__empty_content">
            <IntegrationImg provider={provider} isConnected={false} />
            <h1>
                Ask a {getProviderAdminNode(provider)} to connect your {getProviderOrgNode(provider)}
            </h1>
            <p>
                To connect repositories to Unblocked, you need to be an&nbsp;{getProviderAdminNode(provider)}&nbsp;for
                the&nbsp;
                {getProviderOrgNode(provider)}.
            </p>
            <p>Share Unblocked with a {getProviderAdminNode(provider)} and ask them to connect it for you.</p>
        </div>
    );
};

const ConnectRowList = ({
    provider,
    installations,
    onConnect,
    modifyButtonLabel,
    createButtonLabel,
}: {
    provider: Provider;
    installations: InstallationV2[];
    onConnect: (installation: InstallationV2) => void;
    modifyButtonLabel?: string;
    createButtonLabel?: string;
}) => {
    const sortedInstallations = Sorter.sortByProperty(installations, (t) => t.displayName.toLocaleLowerCase());

    return (
        <>
            <div className="create_team__list">
                {sortedInstallations.map((installation) => (
                    <ConnectRow
                        key={installation.installationId}
                        installation={installation}
                        connect={onConnect}
                        modifyButtonLabel={modifyButtonLabel}
                        createButtonLabel={createButtonLabel}
                    />
                ))}

                <AdminPermissionDialogLink provider={provider} />
            </div>
        </>
    );
};

const ConnectRow = ({
    installation,
    connect,
    modifyButtonLabel,
    createButtonLabel,
}: {
    installation: InstallationV2;
    connect: (installation: InstallationV2) => void;
    modifyButtonLabel?: string;
    createButtonLabel?: string;
}) => {
    const label = installation.isInstalled ? modifyButtonLabel ?? 'Manage' : createButtonLabel ?? 'Select';
    const variant: ButtonVariant = installation.isInstalled ? 'primary' : 'outline';

    return (
        <div className="create_team__row">
            <div>
                <InstallationIcon installation={installation} />
                <div className="connected_teams__description">
                    <span className="connected_teams__name">{installation.displayName}</span>
                    <ExternalLink href={installation.htmlUrl} withIcon={false} variant="secondary">
                        {installation.fullPath}
                    </ExternalLink>
                </div>
            </div>

            <Button onClick={() => connect(installation)} variant={variant}>
                {label}
            </Button>
        </div>
    );
};
