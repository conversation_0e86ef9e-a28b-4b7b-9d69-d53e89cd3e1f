@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'misc' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'theme' as *;
@use 'colors' as *;
@use 'media' as *;

$view-max-width: 760px;

.download_installer {
    // Override basic onboarding layout, use full-width layout.
    // This is super ugly, it'd be better to figure out some way to opt-out of the
    // basic onboarding layout
    position: absolute;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    display: grid;

    &.download_installer__open_desktop {
        overflow-y: hidden;
    }
}

.download_installer_body {
    display: grid;
    place-items: center;
    align-content: start;

    padding: $spacer-60 $spacer-20 $spacer-40 $spacer-20;
    text-align: center;
    background: #f6f0fe;

    &:not(:last-child) {
        border-bottom: $border-style $border-width #dee5ed;
    }

    &.download_installer__gradient {
        background: linear-gradient(to bottom right, #f7f6ff 36%, #f0dfff 100%);
    }

    .download_installer__ide_image {
        width: 80%;
        height: auto;
        max-width: 435px;
    }

    .download_installer__downloading_image {
        margin-bottom: $spacer-24;
    }

    & > h1 {
        @include heading;
        color: themed($text);
        // margin-bottom: 0;

        &.download_installer__install_plugin_header {
            margin-bottom: $spacer-16;
        }
    }

    & > h2 {
        font-size: $font-size-24;
        margin-bottom: $spacer-8;
    }

    & > h3 {
        font-size: $font-size-20;
        margin-bottom: $spacer-12;
    }

    .download_installer__download_buttons {
        display: grid;
        grid: auto / auto-flow 1fr;
        gap: $spacer-8;
    }

    .download_installer__open_desktop_button {
        margin: $spacer-24 0;
    }

    .download_installer__open_desktop_image {
        min-width: 0;
        width: 754px;
        max-width: 100%;
        margin: $spacer-24;
        box-shadow: 0 16px 100px rgba(173 0 255 / 24%);
        border-radius: 17px; // match image border radius and box shadow
    }

    .download_installer__download_button {
        .icon {
            margin-right: $spacer-6;
        }
    }

    & > p {
        color: themed($text-secondary);
        font-size: $font-size-18;
        line-height: $size-26;
        margin-top: $spacer-8;
        margin-bottom: $spacer-24;
        max-width: $view-max-width;
    }

    .download_installer__ide_links {
        display: flex;
        flex-direction: column;
        gap: $spacer-16;
        margin: $spacer-12 0 $spacer-56;

        @include breakpoint(md) {
            flex-direction: row;
        }
    }

    .download_installer__tiles {
        @include flex-column-center;
        justify-content: center;
        gap: $spacer-16;
        margin: $spacer-12 0 $spacer-56;
        @include fill-available;

        .download_tile {
            align-self: stretch;
        }

        @media (500px <= width < $tablet) {
            .download_tile {
                display: grid;
                grid-template:
                    'icon title button' auto
                    'icon description button' auto / 60px 1fr 120px;
                gap: $spacer-4 $spacer-16;
                text-align: left;
                padding: $spacer-16;

                .download_tile__image {
                    grid-area: icon;

                    .icon {
                        height: $size-60;
                        width: $size-60;
                    }
                }
                .download_tile__title {
                    grid-area: title;
                    align-self: end;
                }
                .download_tile__description {
                    grid-area: description;
                    align-self: start;
                }

                .download_tile__button,
                .download_installer__download_button {
                    grid-area: button;
                    align-self: center;
                }
            }
        }

        @include breakpoint(md) {
            flex-direction: row;
        }

        @include breakpoint(lg) {
            .download_tile {
                width: 280px;
            }
        }

        .mac_app_download_tile {
            display: none;
        }

        .download_installer__tiles__ides {
            @include flex-column;

            gap: $spacer-16;
            align-self: stretch;
            border-radius: $border-radius-8;

            @include breakpoint(md) {
                flex-direction: row;
                gap: $spacer-16;
                border-radius: $border-radius-8;
                @include container-shadow(16%);

                .download_tile {
                    box-shadow: none;
                }
            }
        }

        &.download_installer__tiles__mac_platform {
            @include breakpoint(md) {
                .download_tile {
                    width: 200px;
                }
            }

            .mac_app_download_tile {
                display: grid;

                @include breakpoint(lg) {
                    display: flex;
                }
            }

            .download_installer__tiles__ides {
                gap: $spacer-1;

                .download_tile {
                    &:first-of-type {
                        border-radius: $border-radius-8 $border-radius-8 0 0;
                    }

                    &:last-of-type {
                        border-radius: 0 0 $border-radius-8 $border-radius-8;
                    }

                    @include breakpoint(md) {
                        &:first-of-type {
                            border-radius: $border-radius-8 0 0 $border-radius-8;
                        }

                        &:last-of-type {
                            border-radius: 0 $border-radius-8 $border-radius-8 0;
                        }
                    }
                }
            }
        }
    }

    .download_installer__skip_link {
        font-size: $font-size-18;
    }

    &.download_installer_body__beta {
        align-content: center;

        .download_installer__ide_image {
            width: 100%;
            max-width: 1020px;
            background: linear-gradient(238.8deg, #6f2dda 0%, #cb95e4 50.05%);
            box-shadow: 0 16px 100px rgba(173 0 255 / 24%);
            border-radius: 20px;
            margin: 48px 0;
        }

        .download_installer__icon {
            width: 120px;
            height: 120px;
            margin-bottom: 16px;
        }

        .download_installer__download_buttons {
            // margin-top: $spacer-24;
            max-width: 300px;
            width: 100%;
            gap: $spacer-16;
        }

        .download_installer__download_button {
            width: 100%;
        }
        p {
            line-height: $line-height-24;
        }
    }
}

.download_tile {
    padding: $spacer-24 $spacer-16 $spacer-16;
    gap: $spacer-12;
    border-radius: $border-radius-8;
    background-color: themed($integration-tile-bg);
    font-size: $font-size-18;
    text-decoration: none;
    @include flex-column-center;
    @include container-shadow(16%);

    .download_tile__title {
        color: themed($text);
        font-weight: $font-weight-semibold;
    }

    .download_tile__description {
        @include detail;

        color: themed($text-secondary);
    }

    .download_tile__image {
        @include flex-center-center;

        .icon {
            height: $size-100;
            width: $size-100;
        }
    }

    .download_tile__button,
    .download_installer__download_button {
        align-self: stretch;
        font-size: $font-size-14;
    }

    .download_installer_jb_image {
        margin-left: -$spacer-12;
        margin-right: -$spacer-12;
        z-index: 2;
    }
}
