@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.qa_input_view {
    @include flex-column;

    width: 100%;
    margin-top: $spacer-16;

    .qa_input_view__header {
        margin-bottom: $spacer-8;
        position: relative;
        @include flex-column-center;

        .qa_input_view__loading_dots {
            position: absolute;
            left: 0;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.3;

            @include flex-column;
            gap: $spacer-6;

            .animated_dots__content {
                background-image: radial-gradient(
                    ellipse at center,
                    themed($emphasis) 0%,
                    themed($emphasis) 30%,
                    transparent 30%
                );
            }
        }

        .qa_input_view__icon {
            z-index: 1;
        }
    }

    h1 {
        font-size: $font-size-20;
        text-align: center;
        margin-bottom: $spacer-4;

        @include breakpoint(md) {
            @include heading($size-28);
        }
    }

    .qa_input_view__suggestions {
        margin-bottom: $spacer-20;
        align-self: stretch;

        @include flex-column-center;
        gap: $spacer-12;

        h3 {
            font-size: $font-size-18;
            text-align: center;
        }

        .suggestion_view {
            margin-bottom: $spacer-6;
        }
    }

    .qa_input_view__subtitle {
        font-size: $font-size-18;
        text-align: center;
        align-self: center;
        color: themed($text-secondary);
        margin-bottom: $spacer-32;
    }

    .qa_input_view__integrations_description {
        margin-bottom: $spacer-40;
        color: themed($text-secondary);

        a {
            color: themed($link-secondary);
            text-decoration: none;
            &:hover {
                text-decoration: underline;
            }
        }
    }

    .integrations_description__loading {
        color: themed($icon-processing);
    }

    .qa_input_view__input {
        align-self: stretch;
        max-height: 40vh;
    }
}
