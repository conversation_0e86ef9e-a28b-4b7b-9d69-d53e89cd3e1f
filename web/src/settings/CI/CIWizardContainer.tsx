import { useEffect } from 'react';

import { Provider } from '@shared/api/generatedApi';

import { OptionsButton } from '@shared/webComponents/Button/OptionsButton';
import { useViewThemeContext } from '@shared/webComponents/View/ViewThemeContext';
import { CIWizardStep, CIWizardStepLabels } from '@shared/webUtils/CIProviderTraits/CIProviderWizardSteps';
import { ProviderTraitsUtil } from '@shared/webUtils/ProviderTraits/ProviderTraitsUtil';
import { getProviderIconSrc } from '@shared/webUtils/ProviderUtils';
import { Dropdown, DropdownItem } from '@web/components';

import { SettingsIntegrationHeader } from '../IntegrationSettingsUtils';
import { SettingsFooter } from '../SettingsFooter';
import { useSettingsOutletContext } from '../SettingsOutletContext';
import { CIWizardProgressSteps } from './CIWizardProgressSteps';

import './CIWizardContainer.scss';

interface Props {
    description: string;
    provider: Provider;
    steps: CIWizardStep[];
    currentStep: number;
    onSave: () => Promise<void>;
    disableSave?: boolean;
    children: React.ReactNode;
    onUninstall?: () => void;
}

export const CIWizardContainer = ({
    description,
    provider,
    steps,
    currentStep,
    onSave,
    disableSave,
    children,
    onUninstall,
}: Props) => {
    const { setSettingsHeader, setSettingsFooter } = useSettingsOutletContext();
    const { theme } = useViewThemeContext();
    const traits = ProviderTraitsUtil.get(provider);
    useEffect(() => {
        setSettingsHeader(
            <SettingsIntegrationHeader
                provider={provider}
                icon={getProviderIconSrc(provider, undefined, theme)}
                subLabel={description}
                actionButton={
                    onUninstall && (
                        <Dropdown header={<OptionsButton iconSize="large" />} withCaret={false}>
                            <DropdownItem textStyle="danger" onClick={onUninstall}>
                                Delete {traits.displayName}
                            </DropdownItem>
                        </Dropdown>
                    )
                }
            />
        );

        return () => {
            setSettingsHeader(null);
        };
    }, [setSettingsHeader, description, provider, theme, onUninstall, traits.displayName]);

    useEffect(() => {
        const saveLabel = currentStep <= steps.length - 1 ? 'Continue' : 'Save Settings';
        setSettingsFooter(
            <SettingsFooter onSavePromise={() => onSave()} saveLabel={saveLabel} disableSave={disableSave} />
        );
        return () => {
            setSettingsFooter(null);
        };
    }, [setSettingsFooter, currentStep, steps, onSave, disableSave]);

    const stepLabels = steps.map((step) => CIWizardStepLabels[step]);

    return (
        <div className="ci_wizard_container">
            <CIWizardProgressSteps steps={stepLabels} currentStep={currentStep} />
            <div className="ci_wizard_container__content">{children}</div>
        </div>
    );
};
