import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { Provider } from '@shared/api/generatedApi';

import { CISettingsStore } from '@shared/stores/CISettingsStore';
import { DashboardUrls } from '@shared/webUtils';
import { useTeamContext } from '@web/components';
import { useMakeToast } from '@web/components/Toast/useMakeToast';
import { useDocumentTitle } from '@web/hooks/useDocumentTitle';
import { ConnectIntegrationSetting } from '@web/settings/ConfigureIntegration/ConnectIntegrationSetting';
import { SettingsIntegrationHeader } from '@web/settings/IntegrationSettingsUtils';
import { useSettingsOutletContext } from '@web/settings/SettingsOutletContext';

import './ConnectGithubActions.scss';

export const ConnectGitHubActions = () => {
    const { currentTeamId } = useTeamContext();
    const { setSettingsHeader } = useSettingsOutletContext();
    const { makeToast } = useMakeToast();
    const navigate = useNavigate();

    useDocumentTitle(() => 'Connect GitHub Actions', []);

    useEffect(() => {
        setSettingsHeader(<SettingsIntegrationHeader provider={Provider.GithubActions} />);

        return () => setSettingsHeader(null);
    }, [setSettingsHeader]);

    const onConnect = useCallback(async () => {
        try {
            const newInstallation = await CISettingsStore.createCI(currentTeamId, Provider.GithubActions);
            navigate(
                DashboardUrls.providerInstallation(currentTeamId, Provider.GithubActions, newInstallation.id, {
                    initialConnection: true,
                })
            );
        } catch {
            makeToast({
                type: 'alert',
                message: `There was an issue connecting setting up GitHub Actions. Please try again or contact support.`,
            });
        }
    }, [currentTeamId, navigate, makeToast]);

    return (
        <div className="connect_github_actions">
            <ConnectIntegrationSetting
                provider={Provider.GithubActions}
                showButton
                header="Configure GitHub Actions"
                buttonText={
                    <div className="connect_github_actions__button_content">
                        <span>Configure GitHub Actions</span>
                    </div>
                }
                onClick={onConnect}
            >
                <div className="connect_github_actions__content">
                    <div>
                        Unblocked can assess and triage CI failures using insights from your source code and connected
                        data sources.
                    </div>
                </div>
            </ConnectIntegrationSetting>
        </div>
    );
};
