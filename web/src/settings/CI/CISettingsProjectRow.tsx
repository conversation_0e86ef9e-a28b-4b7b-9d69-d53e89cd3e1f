import { CIProject } from '@shared/api/generatedApi';

import { BadgeIcon } from '@shared/webComponents/BadgeIcon/BadgeIcon';
import { DeleteButton } from '@shared/webComponents/Button/DeleteButton';
import { Row } from '@shared/webComponents/Row/Row';

import { faFolderBookmark } from '@fortawesome/pro-duotone-svg-icons/faFolderBookmark';

import './CISettingsProjectRow.scss';

interface Props {
    item: CIProject;
    onDelete?: () => void;
}

export const CISettingsProjectRow = ({ item, onDelete }: Props) => {
    return (
        <Row
            className="ci_settings_project_row"
            header={item.projectName}
            iconTemplate={<BadgeIcon icon={faFolderBookmark} size="medium" />}
            description={
                <a href={item.projectUrl} target="_blank" rel="noreferrer">
                    {item.projectSlug}
                </a>
            }
            rightActionOnHover
            rightAction={
                onDelete ? (
                    <DeleteButton
                        className="ci_settings_project_row__delete"
                        iconSize="medium"
                        onClick={() => onDelete()}
                    />
                ) : null
            }
        />
    );
};
