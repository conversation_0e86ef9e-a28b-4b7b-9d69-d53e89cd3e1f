@use 'flex' as *;
@use 'layout' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'fonts' as *;

.ci_public_opt_out {
    @include flex-column-center;
    padding-top: $spacer-40;

    &__content {
        max-width: 472px;
        @include flex-column;

        &__header {
            @include flex-column-center;
            gap: $spacer-2;
            margin-bottom: $spacer-16;
        }

        .icon__initials {
            font-size: $font-size-24;
        }

        h2 {
            font-size: $size-24;
            line-height: $line-height-32;
            font-weight: $font-weight-bold;
            color: themed($text);
            margin: 0;
            text-align: center;
        }

        &__description {
            margin-bottom: $spacer-16;
            p {
                font-size: $font-size-14;
                line-height: $line-height-20;
                color: themed($text-secondary);
                margin: 0 0 $spacer-12 0;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }

        h4 {
            margin-bottom: $spacer-8;
            font-size: $size-14;
            line-height: $line-height-20;
            font-weight: $font-weight-bold;
        }

        .row {
            background-color: themed($bg-primary);
        }
    }
}
