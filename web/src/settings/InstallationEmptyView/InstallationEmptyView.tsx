import './InstallationEmptyView.scss';

interface Props {
    title: React.ReactNode;
    children: React.ReactNode;
    action?: React.ReactNode;
}

export const InstallationEmptyView = ({ title, children, action }: Props) => {
    return (
        <div className="installation_empty_view">
            <div className="installation_empty_view__title">{title}</div>
            <div className="installation_empty_view__content">{children}</div>

            {action ? <div className="installation_empty_view__action">{action}</div> : null}
        </div>
    );
};
