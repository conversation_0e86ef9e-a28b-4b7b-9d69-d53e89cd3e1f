import { useEffect, useMemo, useState } from 'react';

import { Team, TeamMember } from '@shared/api/generatedApi';

import { useStream } from '@shared/stores/DataCacheStream';
import { TeamMemberListStore } from '@shared/stores/TeamMemberListStore';
import { TeamMembersSort } from '@shared/stores/TeamMembersListStoreTypes';
import { InfiniteScroll } from '@shared/webComponents/InfiniteScroll';
import { SortableHeaderCell } from '@shared/webComponents/Table/SortableHeaderCell';
import { ColumnProps, ColumnVariant, Table } from '@shared/webComponents/Table/Table';
import { TeamMemberRow } from '@shared/webComponents/TeamMemberRow/TeamMemberRow';
import { HoverTooltip } from '@shared/webComponents/Tooltip/HoverTooltip';
import { DateTime, StringsHelper } from '@shared/webUtils';

import './MembersTable.scss';

interface MembersTableState {
    sortBy: TeamMembersSort;
    setSortBy: (sort: TeamMembersSort) => void;
}

interface MembersTableProps {
    team: Team;
    /** Array of column definitions that determine what columns are shown and how they behave */
    columns: ColumnProps<TeamMember, MembersTableState, TeamMembersSort>[];
    /** Optional store instance - if not provided, one will be created internally */
    membersStore?: TeamMemberListStore;
    /** Filter to show only members with or without licenses */
    hasLicense?: boolean;
    /** Placeholder text for the search input */
    searchPlaceholder?: string;
    /** Default sort order when the table loads */
    defaultSort?: TeamMembersSort;
    /** Whether to show a border around the table */
    borderless?: boolean;
    /** Whether to show a placeholder when the table is empty */
    emptyView?: React.ReactNode;
}

const DEFAULT_SORT = TeamMembersSort.nameAsc;

/**
 * A generic, configurable table for displaying team members.
 * Allows callers to specify which columns to display and how they should behave.
 */
export const MembersTable = ({
    team,
    columns,
    membersStore,
    hasLicense,
    searchPlaceholder = 'Find a team member',
    defaultSort = DEFAULT_SORT,
    borderless,
    emptyView,
}: MembersTableProps) => {
    const [sortBy, setSortBy] = useState(defaultSort);
    const [search, setSearch] = useState('');

    const membersStoreInstance = useMemo(
        () =>
            membersStore ||
            new TeamMemberListStore(team.id, {
                sortOrder: defaultSort,
                hasLicense: hasLicense,
            }),
        [team.id, defaultSort, hasLicense, membersStore]
    );

    useEffect(() => {
        membersStoreInstance.update({
            sort: sortBy,
            search,
        });
    }, [sortBy, search, membersStoreInstance]);

    const currentMembers = useStream(() => membersStoreInstance.stream, [membersStoreInstance], []);

    const tableState: MembersTableState = useMemo(
        () => ({
            sortBy,
            setSortBy,
        }),
        [sortBy, setSortBy]
    );

    return (
        <InfiniteScroll source={membersStoreInstance}>
            <Table
                onSearch={setSearch}
                searchPlaceholder={searchPlaceholder}
                items={currentMembers}
                columns={columns}
                sort={sortBy}
                state={tableState}
                borderless={borderless}
                rowDividers={false}
                className="members_table"
                emptyView={emptyView}
            />
        </InfiniteScroll>
    );
};

/**
 * Creates a sortable column with ascending/descending sort capabilities
 */
export const createSortableColumn = (
    title: string,
    ascendingSort: TeamMembersSort,
    descendingSort: TeamMembersSort,
    cellRenderer: (member: TeamMember) => React.ReactNode,
    width?: string,
    defaultOrder: 'ascending' | 'descending' = 'descending',
    variant?: ColumnVariant,
    display?: (args: { width: number }) => boolean
): ColumnProps<TeamMember, MembersTableState, TeamMembersSort> => ({
    header: (state) => (
        <SortableHeaderCell<TeamMembersSort>
            ascendingValue={ascendingSort}
            descendingValue={descendingSort}
            value={state.sortBy}
            onValueChanged={state.setSortBy}
            defaultOrder={defaultOrder}
        >
            {title}
        </SortableHeaderCell>
    ),
    cell: (member) => cellRenderer(member),
    width,
    variant,
    display,
});

/**
 * Creates a simple column with a static header (no sorting)
 */
export const createSimpleColumn = (
    title: string,
    cellRenderer: (member: TeamMember) => React.ReactNode,
    width?: string,
    variant?: ColumnVariant,
    display?: (args: { width: number }) => boolean
): ColumnProps<TeamMember, MembersTableState, TeamMembersSort> => ({
    header: () => title,
    cell: (member) => cellRenderer(member),
    width,
    variant,
    display,
});

/**
 * Creates an action column (no header, usually for buttons/dropdowns)
 */
export const createActionColumn = (
    cellRenderer: (member: TeamMember) => React.ReactNode,
    width?: string,
    variant?: ColumnVariant,
    visibleOnHover?: boolean,
    display?: (args: { width: number }) => boolean
): ColumnProps<TeamMember, MembersTableState, TeamMembersSort> => ({
    cell: (member) => cellRenderer(member),
    width,
    variant,
    visibleOnHover,
    display,
});

// Pre-built column implementations that callers can use directly

/**
 * Standard name column with sorting - shows member displayName
 */
export const NameColumn = createSortableColumn(
    'Members',
    TeamMembersSort.nameAsc,
    TeamMembersSort.nameDesc,
    (member) => (
        <div className="members_table__name">
            <TeamMemberRow teamMember={member} iconSize="large" />
        </div>
    ),
    '1fr'
);

/**
 * Questions asked column with sorting - shows count or dash if zero
 */
export const QuestionsAskedColumn = createSortableColumn(
    'Questions Asked',
    TeamMembersSort.questionsAsc,
    TeamMembersSort.questionsDesc,
    (member) => (member.questionsAsked > 0 ? member.questionsAsked : '-'),
    '0.7fr',
    undefined,
    'secondary',
    ({ width }) => width > 400
);

/**
 * Report column with sorting - shows count or dash if zero
 */
export const CITriageReportsColumn = createSimpleColumn(
    'CI Reports',
    (member) => ((member.ciReportsReceived ?? 0) > 0 ? member.ciReportsReceived : '-'),
    '0.7fr',
    'secondary',
    ({ width }) => width > 400
);

/**
 * Activity column with sorting - shows count or dash if zero
 */
export const LastActivityColumn = createSimpleColumn(
    'Last Active',
    (member) => {
        const latestDate =
            member.lastQuestionAskedAt && member.lastCIReportReceivedAt
                ? DateTime.latest(member.lastQuestionAskedAt, member.lastCIReportReceivedAt)
                : member.lastQuestionAskedAt ?? member.lastCIReportReceivedAt;

        return (
            <HoverTooltip
                header={({ ref }) => (
                    <div ref={ref}>
                        {latestDate ? StringsHelper.capitalize(DateTime.recentRelative(latestDate)) : '-'}
                    </div>
                )}
            >
                {member.lastQuestionAskedAt ? (
                    <div>Last question asked {DateTime.shortPretty(member.lastQuestionAskedAt, true)}</div>
                ) : null}
                {member.lastCIReportReceivedAt ? (
                    <div>Last CI report received {DateTime.shortPretty(member.lastCIReportReceivedAt, true)}</div>
                ) : null}
            </HoverTooltip>
        );
    },
    '0.7fr',
    'secondary'
);
