import { useEffect } from 'react';

import { ConfigureScm, Props as ConfigureScmProps } from './ConfigureScm';
import { getEnterpriseConnectScmRepoStep, getEnterpriseProgressSteps } from './EnterpriseProgressSteps';
import { ProgressStepContainerProvider, useProgressStepContainerContext } from './ProgressStepsContainerContext';
type Props = ConfigureScmProps & { initialConnection?: boolean };

export const EnterpriseConfigureScm = (props: Props) => {
    return (
        <ProgressStepContainerProvider>
            <EnterpriseConfigureScmBody {...props} />
        </ProgressStepContainerProvider>
    );
};

const EnterpriseConfigureScmBody = (props: Props) => {
    const { provider, initialConnection } = props;
    const { setSteps, setCurrentStep, resetProgressSteps } = useProgressStepContainerContext();

    useEffect(() => {
        const steps = getEnterpriseProgressSteps(provider);
        const targetStep = getEnterpriseConnectScmRepoStep(provider);
        if (initialConnection && steps.length && targetStep > 0) {
            setSteps(steps);
            setCurrentStep(targetStep);
        } else {
            resetProgressSteps();
        }
    }, [initialConnection, provider, setSteps, setCurrentStep, resetProgressSteps]);

    return <ConfigureScm {...props} />;
};
