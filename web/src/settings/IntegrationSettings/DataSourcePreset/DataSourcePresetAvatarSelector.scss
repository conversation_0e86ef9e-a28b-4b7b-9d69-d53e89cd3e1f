@use 'layout' as *;
@use 'misc' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'theme' as *;

.data_source_preset_avatar_selector {
    position: relative;
    .icon {
        border-radius: $border-radius-circle;
    }

    .tooltip_container {
        position: absolute;
        left: 0;
        right: 0;
        bottom: -$spacer-10;
        @include flex-center-center;

        .data_source_preset_avatar_selector__button {
            @include shadow-border;
        }
    }
}

.data_source_preset_avatar_selector__tooltip {
    width: 250px;
    padding: $spacer-8 $spacer-16;

    label,
    span {
        font-size: $font-size-12;
        line-height: $line-height-16;
        color: themed($tooltip-fg-tertiary);
    }

    .data_source_preset_avatar_selector__options {
        margin-top: $spacer-8;
        border-top: $border-width $border-style themed($tooltip-divider);
        padding: $spacer-10 0 $spacer-6;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: $spacer-16 $spacer-10;
        place-items: center;

        .data_source_preset_avatar_selector__avatar {
            border-radius: $border-radius-circle;
            box-shadow: 0 0 0 $size-1 themed($data-source-preset-avatar-border);

            transition: box-shadow 0.2s ease-in-out;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-sizing: border-box;

            &:hover {
                box-shadow: 0 0 0 $size-2 themed($data-source-preset-avatar-border);
            }

            &.data_source_preset_avatar_selector__avatar--selected {
                box-shadow: 0 0 0 $size-5 themed($data-source-preset-avatar-selected-border);
            }
        }
    }
}
