import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { Team } from '@shared/api/generatedApi';

import { StateIcon } from '@shared/webComponents/StateIcon/StateIcon';
import { DashboardUrls } from '@shared/webUtils';
import { DocsUrl } from '@shared/webUtils/DocsUrl';
import { Button } from '@web/components';
import { useSettingsOutletContext } from '@web/settings/SettingsOutletContext';

import { DataSourcePresetErrorState } from './DataSourcePresetErrorState';

export const DataSourcePresetMissing = ({ team }: { team: Team }) => {
    const navigate = useNavigate();
    const { setSettingsHeader } = useSettingsOutletContext();

    useEffect(() => {
        setSettingsHeader(
            <>
                <h2>Shared Presets</h2>
            </>
        );
        return () => {
            setSettingsHeader(null);
        };
    }, [setSettingsHeader]);

    return (
        <DataSourcePresetErrorState
            icon={<StateIcon state="error" size={100} />}
            title="The preset no longer exists"
            className="data_source_reset_missing"
        >
            <p>
                Data Presets allow you to choose and save a scoped set of data sources from your connected systems.
                Unblocked will only reference the selected data sources of your preferred preset when answering
                questions.&nbsp;
                <a href={DocsUrl.dataSourcePresets()} target="_blank" rel="noreferrer">
                    Learn more
                </a>
            </p>

            <Button variant="outline" onClick={() => navigate(DashboardUrls.newDataSourcePreset(team.id))}>
                Create Another Preset
            </Button>
        </DataSourcePresetErrorState>
    );
};
