import classNames from 'classnames';
import { ReactNode } from 'react';

import './DataSourcePresetErrorState.scss';

interface Props {
    icon: ReactNode;
    title: ReactNode;
    children: ReactNode;
    className?: string;
}

export const DataSourcePresetErrorState = ({ icon, title, children, className }: Props) => {
    const classes = classNames({
        data_source_preset_error: true,
        [`${className}`]: !!className,
    });
    return (
        <div className={classes}>
            <div className="data_source_preset_error__content">
                <div className="data_source_preset_error__header">{icon}</div>
                <h1>{title}</h1>
                {children}
            </div>
        </div>
    );
};
