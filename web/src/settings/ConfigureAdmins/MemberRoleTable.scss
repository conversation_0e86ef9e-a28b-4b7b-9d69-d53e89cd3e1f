@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.member_role_table {
    border: $border-width $border-style themed($border);
    border-radius: $border-radius-8;

    .member_role_table__header,
    .member_role_table__row {
        display: grid;
        grid-template-columns: 1fr $spacer-100 $spacer-100;
        place-items: center;

        & > div {
            @include flex-center-center;
        }
    }

    .member_role_table__header {
        font-weight: $font-weight-bold;
        padding: $spacer-6 $spacer-12;
    }

    .member_role_table__row {
        padding: $spacer-8 $spacer-12;
        &:not(:last-of-type) {
            border-bottom: $border-width $border-style themed($border);
        }

        .member_role_table__row__capability {
            text-align: left;
            justify-self: start;
        }

        .member_role_table__row__icon_capable {
            color: themed($success);
        }

        .member_role_table__row__icon_incapable {
            color: themed($text-tertiary);
        }
    }
}
