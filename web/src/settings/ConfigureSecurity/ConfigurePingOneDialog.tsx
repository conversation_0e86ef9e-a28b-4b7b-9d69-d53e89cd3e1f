import { Provider, SamlIdentityProviderData, SamlServiceProviderData, Team } from '@shared/api/generatedApi';

import { OrderedStep, OrderedStepsList } from '@shared/webComponents/OrderedStepsList/OrderedStepsList';
import { CopyInput } from '@shared/webComponents/TextInput/CopyInput';
import { TextArea } from '@shared/webComponents/TextInput/TextArea';
import { DocsUrl } from '@shared/webUtils/DocsUrl';
import { TextInput } from '@web/components';

import { BaseConfigureSamlDialog } from './BaseConfigureSamlDialog';

interface Props {
    serviceProviderData: SamlServiceProviderData;
    identityProviderData?: SamlIdentityProviderData;
    team: Team;
    provider?: Provider;
    signInCapableProviders: string[];
    onSave: (data: SamlIdentityProviderData) => Promise<void>;
    onDelete: () => void;
    isEdit: boolean;
}

export const ConfigurePingOneDialog = ({
    serviceProviderData,
    identityProviderData,
    team,
    provider,
    signInCapableProviders,
    onSave,
    isEdit,
    onDelete,
}: Props) => {
    return (
        <BaseConfigureSamlDialog
            title="Configure PingOne"
            docsUrl={DocsUrl.ssoPingOne()}
            serviceProviderData={serviceProviderData}
            identityProviderData={identityProviderData}
            team={team}
            provider={provider}
            signInCapableProviders={signInCapableProviders}
            onSave={onSave}
            isEdit={isEdit}
            onDelete={onDelete}
        >
            {({ serviceProviderData, entityId, setEntityId, testUrl, setTestUrl, cert, setCert }) => (
                <OrderedStepsList variant="secondary">
                    <OrderedStep index={1} title="Create a SAML Application">
                        <ul>
                            <li>
                                From your PingOne console, navigate to the <b>Applications</b> tab.
                            </li>
                            <li>
                                Click the plus (+) button to <b>App Application</b>.
                            </li>
                            <li>Enter &ldquo;Unblocked&rdquo; as the application name.</li>
                            <li>
                                Select <b>SAML Application</b> as the application type.
                            </li>
                            <li>
                                Click <b>Configure</b>.
                            </li>
                        </ul>
                    </OrderedStep>
                    <OrderedStep index={2} title="SAML Configuration">
                        <p>
                            Copy the following values from your Unblocked web app into your PingOne SAML configuration:
                        </p>
                        <div className="configure_saml_dialog__action_section">
                            <div className="section_title">ACS URL:</div>
                            <CopyInput fullWidth value={serviceProviderData.assertionConsumerServiceUrl} />
                            <div className="section_title">Entity ID:</div>
                            <CopyInput fullWidth value={serviceProviderData.serviceProviderEntityId} />
                        </div>
                    </OrderedStep>
                    <OrderedStep index={3} title="Copy SAML metadata into Unblocked">
                        <p>
                            From the <b>Overview</b> tab of your SAML application, copy the following three values from
                            your new PingOne application into the fields below:
                        </p>
                        <div className="configure_saml_dialog__action_section">
                            <div className="section_title">Issuer ID:</div>
                            <TextInput
                                fullWidth
                                placeholder="Issuer ID"
                                value={entityId}
                                onValueChange={(v) => setEntityId(v)}
                                readOnly={isEdit}
                            />
                            <div className="section_title">Initiate Single Sign-On URL:</div>
                            <TextInput
                                fullWidth
                                placeholder="Single Sign-On URL"
                                value={testUrl}
                                onValueChange={(v) => setTestUrl(v)}
                            />
                            <div className="section_title">Signing Certificate (X509 PEM):</div>
                            <TextArea
                                placeholder="X.509 Certificate"
                                value={cert}
                                rows={5}
                                onChange={(e) => setCert(e.currentTarget.value)}
                            />
                        </div>
                    </OrderedStep>
                    <OrderedStep index={4} title="Configure Attribute Mappings">
                        <p>
                            From the <b>Attribute Mappings</b> tab of your SAML application, ensure that the following
                            three attribute mappings are configured:
                        </p>
                        <table className="configure_saml_dialog__attributes">
                            <thead>
                                <tr>
                                    <th>
                                        <span>Attributes</span>
                                    </th>
                                    <th>
                                        <span>PingOne Mappings</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <CopyInput fullWidth value={'saml_subject'} />
                                    </td>
                                    <td>
                                        <TextInput value="Email Address" readOnly />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <CopyInput fullWidth value={'firstName'} />
                                    </td>
                                    <td>
                                        <TextInput value="Given Name" readOnly />
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <CopyInput fullWidth value={'lastName'} />
                                    </td>
                                    <td>
                                        <TextInput value="Family Name" readOnly />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </OrderedStep>
                    <OrderedStep index={5} title="Click Save">
                        <p>
                            Click <b>Next</b> to proceed to the next step, then click the toggle to enable the
                            application.
                        </p>
                    </OrderedStep>
                </OrderedStepsList>
            )}
        </BaseConfigureSamlDialog>
    );
};
