import { useEffect, useMemo, useRef, useState } from 'react';

import { Provider } from '@shared/api/models';

import { useDocumentTitle } from '@web/hooks/useDocumentTitle';

import { InstallationSettingProps } from '../IntegrationSettingContainer/InstallationSettingRedirect';
import { SettingsIntegrationHeader } from '../IntegrationSettingsUtils';
import { useSettingsContext } from '../SettingsContext';
import { SettingsFooter } from '../SettingsFooter';
import { useSettingsOutletContext } from '../SettingsOutletContext';
import { ConfigureSettingForwardedRef } from '../SettingsTypes';
import { ConfigureGoogleDriveWorkspace } from './ConfigureGoogleDriveWorkspace';

export const GoogleDriveWorkspaceSettings = ({ installationId }: InstallationSettingProps) => {
    const ref = useRef<ConfigureSettingForwardedRef>(null);

    const { setSettingsHeader, setSettingsFooter } = useSettingsOutletContext();
    const { teamIntegrations, refetchInstallations } = useSettingsContext();
    const { installations: installationsMap } = teamIntegrations;

    const [disableSave, setDisableSave] = useState(true);

    const installation = useMemo(
        () => installationsMap.get(Provider.GoogleDriveWorkspace)?.find(({ id }) => id === installationId),
        [installationId, installationsMap]
    );

    useDocumentTitle(() => {
        if (installation) {
            return `Google Drive on ${installation.displayName}`;
        }

        return 'Google Drive';
    }, [installation]);

    useEffect(() => {
        setSettingsHeader(
            <SettingsIntegrationHeader
                provider={Provider.GoogleDriveWorkspace}
                connection={
                    installation
                        ? {
                              displayName: installation.displayName,
                              iconUrl: installation.avatarUrl,
                          }
                        : undefined
                }
                loading={!installation}
                menu={{
                    onDelete: () => ref.current?.onDeleteInstallation(),
                }}
            />
        );

        return () => setSettingsHeader(null);
    }, [setSettingsHeader, installation]);

    useEffect(() => {
        setSettingsFooter(
            <SettingsFooter onSave={() => ref.current?.onSave()} disableSave={!ref.current || disableSave} />
        );

        return () => setSettingsFooter(null);
    }, [setSettingsFooter, disableSave]);

    return (
        <ConfigureGoogleDriveWorkspace
            ref={ref}
            installationId={installationId}
            onDelete={refetchInstallations}
            refreshInstallations={refetchInstallations}
            onSettingsChanged={({ disabled }) => {
                setDisableSave(!!disabled);
            }}
        />
    );
};
