@use 'layout' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'theme' as *;
@use 'misc' as *;
@use 'layout-mixin' as *;

$selected-tab-line-size: 0.5px;

.tabs {
    &.tabs__primary {
        .tabs__headers {
            position: relative;

            &::after {
                content: '';
                border-bottom: $border-width $border-style themed($border);
                width: 100%;
                height: $size-40;
                position: absolute;
                z-index: 0;
                margin-bottom: -$spacer-1;
            }

            .tab_header {
                flex: none;
                font-size: $h4-size;
                line-height: $line-height-22;
                font-weight: $font-weight-bold;
                text-align: center;
                color: themed($text-tertiary);
                background: themed($bg-primary);
                border: $border-width $border-style themed($border);
                border-top-right-radius: $border-radius-2;
                border-top-left-radius: $border-radius-2;
                position: relative;
                margin-left: -$spacer-1;
                padding: $spacer-8 $spacer-18 $spacer-10;
                z-index: 1;

                @include breakpoint(sm) {
                    padding: $spacer-8 $spacer-48 $spacer-10;
                    font-size: $h4-size;
                }

                &:first-of-type {
                    margin-left: 0;
                }

                &.tab_header__selected {
                    color: themed($text);
                    border-bottom-color: themed($bg-primary);
                    z-index: 2;

                    &::after {
                        content: '';
                        position: absolute;
                        top: -$selected-tab-line-size;
                        left: -$spacer-1;
                        border-top: $border-width-2 $border-style themed($link);
                        z-index: 2;
                        width: calc(100% + $spacer-2);
                        height: $spacer-1;
                        border-top-right-radius: $border-radius-2;
                        border-top-left-radius: $border-radius-2;
                    }

                    &:focus-visible {
                        outline: none;
                    }
                }
            }
        }
    }

    &.tabs__secondary,
    &.tabs__settings {
        .tabs__headers {
            border-color: themed($tab-secondary-border);
            background: themed($tab-secondary-bg);
        }

        .tab_header {
            color: themed($tab-secondary-fg);
            &:not(:last-of-type) {
                &::after {
                    background: themed($tab-secondary-bg);
                }
            }

            &.tab_header__selected {
                color: themed($tab-secondary-selected-fg);
                &::before {
                    background: themed($tab-secondary-selected-bg);
                    @include container-shadow(8%, 0, 1px, 2px);
                }
            }
        }
    }
}
