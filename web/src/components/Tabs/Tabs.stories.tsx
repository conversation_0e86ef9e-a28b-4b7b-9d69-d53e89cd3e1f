import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Tab, Tabs } from './Tabs';

import './Tabs.scss';

export default {
    title: 'Web/Tabs',
    component: Tabs,
    argTypes: {
        backgroundColor: { control: 'color' },
    },
} as Meta<typeof Tabs>;

type Story = StoryObj<typeof Tabs>;

export const Primary: Story = {
    args: {
        variant: 'primary',
        children: [
            <Tab key="1" header="Tab 1">
                Tab 1 content
            </Tab>,
            <Tab key="2" header="Tab 2">
                Tab 2 content
            </Tab>,
        ],
    },
};

export const Secondary: Story = {
    args: {
        variant: 'secondary',
        children: [
            <Tab key="1" header="Tab 1">
                Tab 1 content
            </Tab>,
            <Tab key="2" header="Tab 2">
                Tab 2 content
            </Tab>,
            <Tab key="3" header="Tab 3">
                Tab 3 content
            </Tab>,
            <Tab key="4" header="Tab 4">
                Tab 4 content
            </Tab>,
        ],
    },
};
