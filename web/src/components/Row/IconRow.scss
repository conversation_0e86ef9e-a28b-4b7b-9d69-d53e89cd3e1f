@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.icon_row {
    @include flex-center($spacer-8);

    overflow: hidden;

    .icon_row__icon {
        flex: none;
        color: themed($context-row-icon-fg);
    }

    .icon_row__content {
        flex: 1;
        color: themed($text);
        @include ellipsis;
    }
}
