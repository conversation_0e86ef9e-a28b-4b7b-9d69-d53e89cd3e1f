import './PullRequestSummary.scss';

interface PullRequestSummaryData {
    title: string;
    number: number;
}

export const PullRequestSummary = ({ pullRequest }: { pullRequest: PullRequestSummaryData }) => {
    return (
        <div className="pull_request_summary" title={`${pullRequest.title} #${pullRequest.number}`}>
            <span className="pull_request_summary__title">{pullRequest.title}</span>
            <span className="pull_request_summary__number">#{pullRequest.number}</span>
        </div>
    );
};
