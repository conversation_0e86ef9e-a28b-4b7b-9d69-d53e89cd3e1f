@use 'flex' as *;
@use 'theme' as *;
@use 'layout' as *;
@use 'misc' as *;
@use 'animations' as *;
@use 'fonts' as *;

.emphasis_text {
    background: themed($emphasis-text);
    background-clip: text;
    /* stylelint-disable-next-line property-no-vendor-prefix */
    -webkit-background-clip: text;
    color: transparent;
    font-weight: $font-weight-bold;

    &::selection {
        color: themed($text);
    }
}
