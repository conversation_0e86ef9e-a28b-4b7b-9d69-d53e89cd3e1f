import type { Meta, StoryObj } from '@storybook/react';
import { userEvent, within } from '@storybook/test';

import csImg from '@assets/unblocked.svg';

import { faBars } from '@fortawesome/pro-regular-svg-icons/faBars';
import { faCrown } from '@fortawesome/pro-regular-svg-icons/faCrown';
import { faFlag } from '@fortawesome/pro-regular-svg-icons/faFlag';
import { faHandPaper } from '@fortawesome/pro-regular-svg-icons/faHandPaper';
import { faMapPin } from '@fortawesome/pro-regular-svg-icons/faMapPin';
import { faSparkles } from '@fortawesome/pro-regular-svg-icons/faSparkles';
import { faUnlink } from '@fortawesome/pro-regular-svg-icons/faUnlink';
import { faUser } from '@fortawesome/pro-regular-svg-icons/faUser';
import { faWaterRise } from '@fortawesome/pro-regular-svg-icons/faWaterRise';
import { faBars as faBarsSolid } from '@fortawesome/pro-solid-svg-icons/faBars';
import { faCrown as faCrownSolid } from '@fortawesome/pro-solid-svg-icons/faCrown';
import { faFlag as faFlagSolid } from '@fortawesome/pro-solid-svg-icons/faFlag';
import { faHandPaper as faHandPaperSolid } from '@fortawesome/pro-solid-svg-icons/faHandPaper';
import { faMapPin as faMapPinSolid } from '@fortawesome/pro-solid-svg-icons/faMapPin';
import { faSparkles as faSparklesSolid } from '@fortawesome/pro-solid-svg-icons/faSparkles';
import { faUnlink as faUnlinkSolid } from '@fortawesome/pro-solid-svg-icons/faUnlink';
import { faUser as faUserSolid } from '@fortawesome/pro-solid-svg-icons/faUser';
import { faWaterRise as faWaterRiseSolid } from '@fortawesome/pro-solid-svg-icons/faWaterRise';

import { NavigationSectionItem } from './NavigationSection';
import { Navigator } from './Navigator';

// More on default export: https://storybook.js.org/docs/react/writing-stories/introduction#default-export
export default {
    title: 'Web/Navigation',
    component: Navigator,
    parameters: {
        layout: 'fullscreen',
        chromatic: { disableSnapshot: false },
    },
} as Meta<typeof Navigator>;

type Story = StoryObj<typeof Navigator>;

const sections: NavigationSectionItem[] = [
    {
        title: 'My Conversations',
        items: [
            {
                kind: 'link',
                name: 'All',
                href: '/mine/all',
                icon: {
                    base: csImg,
                    active: csImg,
                },
                count: 15,
            },
            {
                kind: 'link',
                name: "Questions I've asked",
                href: '/mine/asked',
                icon: {
                    base: faHandPaper,
                    active: faHandPaperSolid,
                },
            },
            {
                kind: 'link',
                name: 'Questions for me',
                href: '/mine/me',
                icon: {
                    base: faCrown,
                    active: faCrownSolid,
                },
                count: 2,
            },
            {
                kind: 'link',
                name: 'Bookmarked',
                href: '/mine/bookmarked',
                icon: {
                    base: faFlag,
                    active: faFlagSolid,
                },
            },
        ],
    },
    {
        title: 'Discover',
        items: [
            {
                kind: 'link',
                name: 'All',
                href: '/discover/all',
                icon: {
                    base: faBars,
                    active: faBarsSolid,
                },
            },
            {
                kind: 'link',
                name: "Near areas you're coding",
                href: '/discover/near',
                icon: {
                    base: faMapPin,
                    active: faMapPinSolid,
                },
            },
            {
                kind: 'link',
                name: 'New conversations',
                href: '/discover/new',
                icon: {
                    base: faSparkles,
                    active: faSparklesSolid,
                },
            },
            {
                kind: 'link',
                name: 'Starting to swell',
                href: '/discover/swell',
                icon: {
                    base: faWaterRise,
                    active: faWaterRiseSolid,
                },
            },
            {
                kind: 'link',
                name: 'Group conversations',
                href: '/discover/group',
                icon: {
                    base: faUser,
                    active: faUserSolid,
                },
            },
            {
                kind: 'click',
                name: 'Detached',
                onClick: () => {
                    /* */
                },
                icon: {
                    base: faUnlink,
                    active: faUnlinkSolid,
                },
            },
        ],
    },
];

export const NavigatorStory: Story = {
    args: {
        sections,
        children: <div>Children</div>,
    },
};

export const MobileNavigatorStory: Story = {
    args: {
        sections,
        children: <div>Mobile Children</div>,
    },
    parameters: {
        viewport: {
            defaultViewport: 'iphone12',
        },
        // Set the viewports in Chromatic at a story level.
        chromatic: { viewports: [390] },
    },
    play: async ({ canvasElement }) => {
        // Need to wait for viewport to change to mobile
        const canvas = within(canvasElement);
        const button = await canvas.findByRole('button', { name: 'menu' });
        await userEvent.click(button);
    },
};
