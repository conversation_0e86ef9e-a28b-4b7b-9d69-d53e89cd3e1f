import { useMemo, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { useAsyncOperation } from '@shared/hooks/UseAsyncOperation';
import { useQuery } from '@shared/hooks/useQuery';
import { Events } from '@shared/metrics';
import { AuthStore } from '@shared/stores/AuthStore';
import { useStream } from '@shared/stores/DataCacheStream';
import { PersonStore } from '@shared/stores/PersonStore';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { logger } from '@shared/webUtils';
import { DashboardUrls } from '@shared/webUtils/DashboardUrls';
import unblocked from '@web/assets/unblocked-light.svg';

import { Button } from '../Button/Button';
import { UserIcon } from '../UserIcon/UserIcon';

import './NotFound.scss';

interface Props {
    children: React.ReactNode;
}

interface NotFoundProps {
    url?: string;
}

const log = logger('NotFound');

const NotFoundTemplate = ({ children }: Props) => {
    return (
        <div className="not_found">
            <div className="not_found__content">
                <div>
                    <h1>Page Not Found</h1>
                    <p>
                        We&rsquo;re sorry, the page you&rsquo;re looking for may not exist, may have been deleted, or
                        might be private.
                    </p>
                </div>

                {children}
            </div>
            <div className="not_found__icon_container">
                <Icon className="not_found__icon" icon={unblocked} />
            </div>
        </div>
    );
};

export const NotFound = ({ url }: NotFoundProps) => {
    const navigate = useNavigate();
    const authState = useStream(() => AuthStore.get().stream, []);

    if (authState?.$case === 'authenticated') {
        return <AuthedNotFound url={url} />;
    }

    return (
        <NotFoundTemplate>
            <Button size="large" onClick={() => navigate(url || DashboardUrls.login())}>
                Log In or Get Started
            </Button>
        </NotFoundTemplate>
    );
};

const connectSlackAccountNotFound = 'connectSlackAccountNotFound';

export const AuthedNotFound = ({ url }: { url?: string }) => {
    const navigate = useNavigate();
    const query = useQuery();
    const isConnectSlackAccount = !!query.get('connectSlackAccount');
    const location = useLocation();
    const person = useStream(() => PersonStore.person, []);

    const initialized = useRef(false);
    useAsyncOperation(async () => {
        try {
            if (isConnectSlackAccount && !initialized.current) {
                initialized.current = true;
                await Events.session.triggerAction(window.location.href, connectSlackAccountNotFound);
            }
        } catch (error) {
            log.error('Failed to trigger action:', error);
        }
    }, []);

    const { displayName, avatarUrl } = useMemo(() => {
        const currentTeamMemberInfo = person?.memberships.find(({ teamMember }) => teamMember.isPrimary);

        let displayName = person?.displayName;
        let avatarUrl = person?.avatarUrl;

        if (currentTeamMemberInfo) {
            const { teamMember: currentTeamMember } = currentTeamMemberInfo;
            displayName = currentTeamMember.identity.displayName;
            avatarUrl = currentTeamMember.identity.avatarUrl;
        }

        return { displayName, avatarUrl };
    }, [person]);

    return (
        <NotFoundTemplate>
            {person && displayName && (
                <div className="not_found__person_info">
                    <span className="not_found__person_info__person_prefix">You&rsquo;re currently logged in as:</span>
                    <span className="not_found__person_info__person">
                        <UserIcon user={{ name: displayName, iconUrl: avatarUrl }} size="xLarge" /> {displayName}{' '}
                        &mdash; Is this the correct account?
                    </span>
                </div>
            )}

            <div className="not_found__buttons">
                <Button size="large" onClick={() => navigate(url || DashboardUrls.index())}>
                    Go to Dashboard
                </Button>
                <Button
                    size="large"
                    variant="tertiary"
                    onClick={async () => {
                        AuthStore.get().logout('manual');
                        navigate(DashboardUrls.login(), { state: { loginRedirect: location.pathname } });
                    }}
                >
                    Log in with a different account
                </Button>
            </div>
        </NotFoundTemplate>
    );
};
