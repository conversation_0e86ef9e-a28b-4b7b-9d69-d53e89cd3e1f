import { ExternalLink } from '@shared/webComponents/ExternalLink/ExternalLink';
import { Icon } from '@shared/webComponents/Icon/Icon';

import { faHashtag } from '@fortawesome/pro-regular-svg-icons/faHashtag';

interface Props {
    channelName: string;
    channelUrl?: string;
}

import './SlackChannelLink.scss';

export const SlackChannelLink = ({ channelName, channelUrl }: Props) => {
    return (
        <span className="slack_channel_link">
            <Icon icon={faHashtag} size={13} />
            <ExternalLink withIcon={false} href={channelUrl} onClick={(e) => e.stopPropagation()}>
                {channelName}
            </ExternalLink>
        </span>
    );
};
