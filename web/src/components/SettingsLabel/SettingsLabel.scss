@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'fonts-mixin' as *;
@use 'theme' as *;
@use 'flex' as *;

.settings_label {
    .settings_label__title {
        @include flex-center;
        gap: $spacer-12;
    }

    .settings_label__description {
        @include detail;

        color: themed($text-secondary);
    }

    &.settings_label__inline {
        .settings_label__description {
            @include paragraph;

            color: themed($text-secondary);
        }
    }
}
