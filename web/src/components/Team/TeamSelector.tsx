import classNames from 'classnames';
import { useCallback } from 'react';

import { Team } from '@models';

import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { PopoverEvent } from '@shared/webComponents/Popover/Popover';
import { TeamRow } from '@shared/webComponents/TeamRow/TeamRow';
import { DashboardUrls } from '@shared/webUtils/DashboardUrls';

import { faCirclePlus } from '@fortawesome/pro-solid-svg-icons/faCirclePlus';

import { NewTeamDialog } from './NewTeamDialog';

import './TeamSelector.scss';

interface Props {
    allTeams: Team[];
    currentTeam?: Team;
    onSelectTeam?: (team: Team) => void;
    triggerEvent?: PopoverEvent;
    className?: string;
}

export const TeamsSelectorList = ({ onSelectTeam, className, currentTeam, allTeams }: Omit<Props, 'triggerEvent'>) => {
    const currentTeamId = currentTeam?.id;
    const { openModal } = useModalContext();

    const navigateTo = useCallback((url: string) => {
        ClientWorkspace.instance().handleAction({
            $case: 'openUrl',
            url,
            navigate: true,
        });
    }, []);

    return (
        <div className={classNames('teams_selector_list', className)}>
            <div className="teams_selector_list__container">
                {allTeams.map((team) => {
                    const isSelected = currentTeamId === team.id;
                    return (
                        <div
                            key={team.id}
                            className={classNames('teams_selector_row', isSelected && 'teams_selector_row__selected')}
                            onClick={() => {
                                if (isSelected) {
                                    return;
                                }
                                navigateTo(DashboardUrls.team(team.id));
                                onSelectTeam?.(team);
                            }}
                        >
                            <TeamRow team={team} isSelected={isSelected} />
                        </div>
                    );
                })}
                {currentTeam && (
                    <>
                        <hr />
                        <div
                            className="team_selector__add"
                            onClick={() =>
                                openModal(
                                    <NewTeamDialog
                                        currentTeam={currentTeam}
                                        addDataSource={() =>
                                            navigateTo(DashboardUrls.connectedIntegrations(currentTeam.id))
                                        }
                                        createNewTeam={() => {
                                            navigateTo(DashboardUrls.onboardingCreateLogin());
                                        }}
                                    />
                                )
                            }
                        >
                            <Icon icon={faCirclePlus} size="small" />
                            <span>Add another Team</span>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
};
