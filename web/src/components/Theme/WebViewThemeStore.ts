import { Stream } from 'xstream';

import { ValueStream } from '@shared/stores/NewValueStream';
import { ViewThemeStore } from '@shared/webComponents/View/ViewThemeStore';
import { LazyValue } from '@shared/webUtils';

import { stringToWebTheme, WebTheme, webThemeToViewTheme } from './WebViewThemeTypes';
const THEME_KEY = 'USER_PREFERENCES_THEME';
const defaultTheme: WebTheme = 'system';
export class WebViewThemeStore extends ViewThemeStore {
    static get = LazyValue(() => new WebViewThemeStore());
    private _webThemeStream: ValueStream<WebTheme>;
    public webThemeStream: Stream<WebTheme>;
    constructor() {
        const storedValue = localStorage.getItem(THEME_KEY);
        const theme: WebTheme = storedValue ? stringToWebTheme(storedValue) : defaultTheme;
        super(webThemeToViewTheme(theme));
        this._webThemeStream = new ValueStream<WebTheme>(theme);
        this.webThemeStream = this._webThemeStream;

        this.webThemeStream.subscribe({
            next: (v) => {
                this.setTheme(webThemeToViewTheme(v));
            },
        });
    }

    public setWebTheme(theme: WebTheme) {
        localStorage.setItem(THEME_KEY, theme.toString());
        this._webThemeStream.value = theme;
    }
}
