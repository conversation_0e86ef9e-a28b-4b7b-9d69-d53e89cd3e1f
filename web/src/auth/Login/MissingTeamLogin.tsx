import { Navigate, useNavigate, useParams } from 'react-router-dom';

import { useQuery } from '@shared/hooks/useQuery';
import { useStream } from '@shared/stores/DataCacheStream';
import { useStore } from '@shared/stores/useStore';
import { BadgeIcon } from '@shared/webComponents/BadgeIcon/BadgeIcon';
import { LoginStoreTraits } from '@shared/webComponents/Login/LoginStoreTraits';
import { StateIcon } from '@shared/webComponents/StateIcon/StateIcon';
import { useViewThemeContext } from '@shared/webComponents/View/ViewThemeContext';
import { DashboardUrls } from '@shared/webUtils';
import { getProviderDisplayName, getProviderFromAppRoute, getProviderIconSrc } from '@shared/webUtils/ProviderUtils';
import { Button, Loading } from '@web/components';

import { faUserGroup } from '@fortawesome/pro-duotone-svg-icons/faUserGroup';

import './MissingTeamLogin.scss';

export const MissingTeamLogin = () => {
    const { integration } = useParams();
    const query = useQuery();
    const workspaceName = query.get('workspace');
    const navigate = useNavigate();
    const provider = integration ? getProviderFromAppRoute(integration) : undefined;
    const { theme } = useViewThemeContext();
    const store = useStore(LoginStoreTraits, {});
    const state = useStream(() => store.state, [store]);
    // Strange to be missign provider at this point. Redirect back to login
    if (!provider) {
        return <Navigate to={DashboardUrls.login()} replace />;
    }

    if (!state || state?.$case === 'loading') {
        return <Loading centerAlign />;
    }
    const allProviders = state.$case === 'login' ? [...state.options, ...state.bottomOptions] : [];
    const targetLoginOption = allProviders.find((p) => p.provider === provider);
    const providerIcon = getProviderIconSrc(provider, true, theme);

    return (
        <div className="missing_team_login">
            <div>
                <StateIcon state="error" size={100} />
                <h1>No existing Unblocked team</h1>
                <p>
                    The {getProviderDisplayName(provider)} workspace
                    {workspaceName && (
                        <span className="missing_team_login__workspace">
                            , &ldquo;<b>{workspaceName}</b>&rdquo;
                        </span>
                    )}
                    &nbsp;is not connected to an existing Unblocked team.
                </p>
                <div className="missing_team_login__content">
                    {targetLoginOption && (
                        <div className="missing_team_login__section">
                            {providerIcon && <BadgeIcon icon={providerIcon} size="large" />}
                            <h1>Join an existing team</h1>
                            <p>Did you log in to the wrong {getProviderDisplayName(provider)} Workspace?</p>
                            <Button
                                variant="tertiary"
                                onClick={() => {
                                    store.triggerLogin({ loginOption: targetLoginOption.loginOption, metadata: {} });
                                }}
                            >
                                Select a different Workspace
                            </Button>
                        </div>
                    )}
                    <div className="missing_team_login__section">
                        <BadgeIcon icon={faUserGroup} size="large" />

                        <h1>Create a new Unblocked team</h1>
                        <p>Create an entirely new team with its own members and data sources.</p>
                        <Button
                            variant="tertiary"
                            onClick={() => {
                                navigate(DashboardUrls.getStarted());
                            }}
                        >
                            Create a new team
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};
