@use 'layout' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'theme' as *;
@use 'colors' as *;

.enterprise_scm__manifest {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;

    .provider_header {
        margin-bottom: $spacer-20;
    }

    h1 {
        font-size: $font-size-36;
        font-weight: $font-weight-light;
        line-height: $line-height-44;
        margin-bottom: $spacer-6;
    }

    .enterprise_scm__manifest__header {
        margin-top: $spacer-40;
        margin-bottom: $spacer-20;
    }

    .enterprise_scm__manifest__body {
        max-width: 700px;
        width: 100%;
        margin-bottom: $spacer-40;
    }

    li,
    p {
        font-size: $font-size-18;
        line-height: $line-height-22;
        color: themed($text-secondary);
    }

    ol.enterprise_scm__list {
        counter-reset: item;

        & > li {
            counter-increment: item;
            display: flex;
            gap: $spacer-16;
            align-items: self-start;
            margin-bottom: $spacer-24;

            & > div {
                width: 100%;
            }
        }

        & > li::before {
            margin-top: $spacer-2;
            content: counter(item);
            background: themed($text-secondary);
            border-radius: 100%;
            color: themed($bg);
            font-size: $font-size-14;
            line-height: $line-height-18;
            width: $size-18;
            text-align: center;
            display: inline-block;
        }

        ul {
            margin-left: $spacer-18;

            & > li {
                padding-left: $spacer-12;

                &:not(:last-of-type) {
                    margin-bottom: $spacer-8;
                }

                & > ul {
                    list-style: circle;
                    list-style-position: inside;
                }
            }
        }

        li::marker {
            text-indent: 5px;
        }

        p {
            margin-bottom: $spacer-10;
        }

        p.enterprise_scm__manifest--error {
            @include description-responsive;
            margin: $spacer-8 0 0;
            color: themed($danger);
        }
    }

    .enterprise_scm__manifest__submit {
        width: 100%;
    }
}
