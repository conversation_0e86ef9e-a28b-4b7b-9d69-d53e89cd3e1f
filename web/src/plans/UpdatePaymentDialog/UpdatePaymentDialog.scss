@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.update_payment_dialog {
    .update_payment_dialog__content {
        display: grid;
        grid-template:
            'card card' auto
            'expiry cvc' auto
            'error error' auto / 1fr 1fr;
        padding: $spacer-16 0 0;
        gap: $spacer-24 $spacer-16;

        .update_payment_dialog__card {
            grid-area: card;
        }

        .update_payment_dialog__expiry {
            grid-area: expiry;
        }

        .update_payment_dialog__cvc {
            grid-area: cvc;
        }

        .update_payment_dialog__error {
            grid-area: error;
            @include detail;

            color: themed($danger);
        }
    }
}
