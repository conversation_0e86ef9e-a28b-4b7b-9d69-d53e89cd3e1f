import { CardCvcElement, CardExpiryElement, CardNumberElement, useElements, useStripe } from '@stripe/react-stripe-js';
import { SetupIntentResult } from '@stripe/stripe-js';
import classNames from 'classnames';
import { useCallback, useMemo, useState } from 'react';

import { EmailInput } from '@shared/webComponents/TextInput/EmailInput';
import { useViewThemeContext } from '@shared/webComponents/View/ViewThemeContext';
import { LandingUrls } from '@shared/webUtils/LandingUrls';
import { Button } from '@web/components/Button/Button';
import { Loading } from '@web/components/Loading/Loading';
import { StripeInputDarkStyles, StripeInputStyles } from '@web/components/Stripe/StripeStyles';

import './PaymentInputForm.scss';

interface Props {
    planName: string;
    onSetupPayment: () => Promise<SetupIntentResult | undefined>;
    onPaymentSuccess: () => Promise<void>;
    contactEmail: string;
    setContactEmail: (email: string) => void;
    className?: string;
}

export const PaymentInputForm = ({
    planName,
    onSetupPayment,
    onPaymentSuccess,
    contactEmail,
    setContactEmail,
    className,
}: Props) => {
    const [paymentLoading, setPaymentLoading] = useState<boolean>(false);
    const [paymentError, setPaymentError] = useState<string>();
    const [isValidEmail, setIsValidEmail] = useState<boolean>(false);
    const { theme } = useViewThemeContext();

    const styles = theme === 'dark' ? StripeInputDarkStyles : StripeInputStyles;

    const stripe = useStripe();
    const elements = useElements();

    const onCreatePayment = useCallback(async () => {
        setPaymentLoading(true);
        setPaymentError(undefined);
        const result = await onSetupPayment();

        if (!result || result.error) {
            setPaymentLoading(false);

            if (result) {
                setPaymentError(result.error.message);
            } else {
                setPaymentError('There was an issue processing your payment.');
            }
        } else {
            try {
                await onPaymentSuccess();
            } catch {
                setPaymentError('There was an issue processing your payment.');
            }
        }
    }, [onSetupPayment, onPaymentSuccess]);

    const footer = useMemo(() => {
        if (paymentError) {
            return <div className="payment_input_form__error">{paymentError}</div>;
        }

        if (paymentLoading) {
            return <div className="payment_input_form__footer">Unblocked is processing your payment...</div>;
        }

        return (
            <div className="payment_input_form__footer">
                Your team will be upgraded to the {planName} plan immediately.
            </div>
        );
    }, [paymentError, paymentLoading, planName]);

    if (!elements) {
        return <Loading />;
    }

    const classes = classNames({
        payment_input_form: true,
        [`${className}`]: !!className,
    });

    return (
        <div className={classes}>
            <h2>Billing contact</h2>
            <EmailInput
                className="payment_input_form__contact_entry"
                value={contactEmail}
                onValueChange={setContactEmail}
                onValidChanged={setIsValidEmail}
                fullWidth
                placeholder="<EMAIL>"
            />

            <h2>Payment information</h2>
            <div className="payment_input_form__card_entry">
                <CardNumberElement
                    className="payment_input_form__card"
                    options={{
                        showIcon: true,
                        classes: {
                            base: 'stripe_input',
                            focus: 'stripe_input__focus',
                        },
                        style: styles,
                    }}
                />
                <CardExpiryElement
                    className="payment_input_form__expiry"
                    options={{
                        classes: {
                            base: 'stripe_input',
                            focus: 'stripe_input__focus',
                        },
                        style: styles,
                    }}
                />
                <CardCvcElement
                    className="payment_input_form__cvc"
                    options={{
                        placeholder: 'Security Code',
                        classes: {
                            base: 'stripe_input',
                            focus: 'stripe_input__focus',
                        },
                        style: styles,
                    }}
                />
                <Button
                    className="payment_input_form__submit"
                    size="large"
                    disabled={!stripe || !contactEmail || !isValidEmail}
                    onClickPromise={onCreatePayment}
                >
                    Pay Now
                </Button>
                {footer}
                <div className="payment_input_form__disclaimer">
                    <span>
                        By selecting a plan, you&rsquo;re agreeing to the Unblocked <br />
                        <a href={LandingUrls.tos()} target="_blank" rel="noreferrer">
                            Terms of Service
                        </a>{' '}
                        and{' '}
                        <a href={LandingUrls.privacy()} target="_blank" rel="noreferrer">
                            Privacy Policy
                        </a>
                        .
                    </span>
                </div>
            </div>
        </div>
    );
};
