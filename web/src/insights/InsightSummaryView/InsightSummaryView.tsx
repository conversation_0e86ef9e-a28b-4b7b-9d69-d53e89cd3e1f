import classNames from 'classnames';
import { Link } from 'react-router-dom';

import { TeamMember } from '@models';

import { ThreadInfoAggregate } from '@shared/stores/ThreadInfoAggregate';
import { useGetRouteState } from '@web/hooks/usePrevRoutes';
import { UnreadIndicator } from '@web/threads/UnreadIndicator/UnreadIndicator';

import './InsightSummaryView.scss';

interface Props {
    title: React.ReactNode;
    subtitle?: React.ReactNode;
    titleAction?: React.ReactNode;
    displayLongTitle?: boolean;
    route: string;
    routeState?: { threadInfo: ThreadInfoAggregate };
    icon: React.ReactNode;
    author: TeamMember;
    messageContent?: React.ReactNode;
    metadataContent?: React.ReactNode;
    menu?: React.ReactNode;
    participants: TeamMember[];
    count: number;
    latestTimestamp: Date;
    isMentioned?: boolean;
    isUnread?: boolean;
    archivedAt?: Date;
    archivedBy?: TeamMember;
}

export const InsightSummaryView = ({
    title,
    subtitle,
    route,
    routeState,
    icon,
    messageContent,
    isMentioned,
    isUnread,
    menu,
    archivedAt,
}: Props) => {
    const prevRoutesState = useGetRouteState();

    const iconClasses = classNames({
        chat_summary_view__insight_icon: true,
        chat_summary_view__insight_icon__archived: !!archivedAt,
    });

    return (
        <Link className="insight_summary_view" to={route} state={{ ...routeState, ...prevRoutesState }}>
            <div className="insight_summary_view__body">
                <div className="insight_summary_view__content">
                    <div className="chat_summary_view__insight_icon_container">
                        <UnreadIndicator isMentioned={isMentioned} isUnread={isUnread} />
                        <div className={iconClasses}>{icon}</div>
                    </div>
                    <div className="chat_summary_view__header">
                        <h3>{title}</h3>
                        <div className="chat_summary_view__header__menu">{menu}</div>
                        {subtitle && <div className="chat_summary_view__header__subtitle">{subtitle}</div>}
                    </div>

                    {messageContent ? (
                        <div className="chat_summary_view__grid_container chat_summary_view__content_container">
                            <div className="chat_summary_view__content">{messageContent}</div>
                        </div>
                    ) : null}
                </div>
            </div>
        </Link>
    );
};
