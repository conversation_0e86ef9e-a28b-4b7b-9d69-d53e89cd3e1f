const path = require('path');
const webpack = require('webpack');

module.exports = {
    stories: ['../src/**/*.stories.mdx', '../src/**/*.stories.@(js|jsx|ts|tsx)'],
    framework: '@storybook/react',
    addons: ['@storybook/addon-links', '@storybook/addon-essentials', '@storybook/addon-interactions'],
    core: {
        builder: 'webpack5',
    },
    webpackFinal: (config) => {
        config.resolve.alias = {
            ...config.resolve.alias,
            '@config': path.resolve(__dirname, '../../shared/config'),
            '@api': path.resolve(__dirname, '../src/api'),
            '@models': path.resolve(__dirname, '../src/api/models'),
            '@assets': path.resolve(__dirname, '../src/assets'),
            '@app': path.resolve(__dirname, '../src/app'),
            '@components': path.resolve(__dirname, '../src/components'),
            '@utils': path.resolve(__dirname, '../src/utils'),
            '@threads': path.resolve(__dirname, '../src/threads'),
            '@home': path.resolve(__dirname, '../src/home'),
            '@settings': path.resolve(__dirname, '../src/settings'),
            '@insights': path.resolve(__dirname, '../src/insights'),
            '@teamMembers': path.resolve(__dirname, '../src/teamMembers'),
            '@shared-web-utils': path.resolve(__dirname, '../../shared/webUtils'),
            '@shared-mocks': path.resolve(__dirname, '../../shared/mocks'),
            '@shared-styles': path.resolve(__dirname, '../../shared/webComponents/styles'),
            '@shared-api': path.resolve(__dirname, '../../shared/api'),
            '@shared-proxy': path.resolve(__dirname, '../../shared/proxy'),
            '@shared': path.resolve(__dirname, '../../shared'),
            '@clientAssets': path.resolve(__dirname, '../../shared/clientAssets'),
            '@web': path.resolve(__dirname, '../src'),
        };

        config.resolve.fallback = {
            ...config.resolve.fallback,
            os: false,
            zlib: false,
            http: false,
            https: false,
            fs: false,
            path: false,
        };

        config.module.rules.push({
            test: /\.scss$/,
            use: [
                'style-loader',
                'css-loader',
                {
                    loader: 'sass-loader',
                    options: {
                        sassOptions: {
                            includePaths: ['./src/styles'], // resolve paths
                        },
                    },
                },
            ],
        });

        config.module.rules.push({
            test: /\.tsx?$/,
            loader: 'esbuild-loader',
            options: {
                jsx: 'automatic',
            },
        });

        config.plugins.push(
            new webpack.ProvidePlugin({
                process: 'process/browser',
                Buffer: ['buffer', 'Buffer'],
                setImmediate: ['timers-browserify', 'setImmediate'],
            })
        );

        config.plugins.push(
            new webpack.DefinePlugin({
                'nodeUrl.URL': 'nodeUrl.Url',
            })
        );

        return config;
    },
};
