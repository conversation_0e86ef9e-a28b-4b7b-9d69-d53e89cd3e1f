# GitGuardian
.cache_ggshield

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release
.kotlin

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# include subchart dependencies for helm
!**/charts/*.tgz

# Yarn Integrity file
.yarn-integrity

# Exclude all .env files
**/*.env
**/*.env.test

# Allow .env files under the assets folder
!**/assets/**/*.env

# Allow .env files in the docker directory
!docker/**/*.env

# Allow .env files under the .run folder
!**/.run/*.env

# parcel-bundler cache (https://parceljs.org/)
.cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and *not* Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Mac OS Junk
.DS_Store

# Packaged files
*.vsix

# Unzipped App
Unblocked.app

storybook-static/
.storybook-dist/

# Gradle
.gradle/
build/

# IntelliJ Files
*.iws

# Ignore generated api
**/api/generated*
**/sourcemark/generated

# Lint output for openspec API
zally/*.json

# Openspec Api Cleanup Task Output
build/specs

# Web extension distribution
extension.zip

# Ignore bin directories
/projects/**/bin/
custom-ktlint-rules/bin/
common/bin/

# Ignore index of source code
**/sourceindexer/**/resources/index/**

# Gradle build cache
.build-cache/

# Temp files
*~
*.pyc
.build
dataScienceEc2Node.pem

# ---------------------------------------------------------------------------------------------
# IDE
#
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Created by https://www.toptal.com/developers/gitignore/api/intellij
# Reference: https://intellij-support.jetbrains.com/hc/en-us/articles/206544839

# Allow list hack
**/.idea/**
!**/.idea/
!**/.idea/prettier.xml
!**/.idea/kotlinc.xml
!**/.idea/codeStyles/**
!**/.idea/jsLinters/**
!**/.idea/runConfigurations/**
!**/.idea/ktlint.xml
!**/.idea/detekt.xml

*.iml
*.ipr

# CMake
cmake-build-*/

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# End of IDE
# ---------------------------------------------------------------------------------------------

# Some extra intellij stuff
.intellijPlatform/*
