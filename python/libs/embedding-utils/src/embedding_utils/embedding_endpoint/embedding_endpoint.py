import logging as sys_logger
from typing import <PERSON><PERSON>, Callable, Any

import requests
from tenacity import stop_after_attempt, wait_exponential, retry_if_exception, before_sleep_log, retry

from embedding_utils.embedding_endpoint.embedding_endpoint_models import (
    EmbeddingInputsRequest,
    EmbeddingOutputResponse,
)
from embedding_utils.embedding_generator import Embedding<PERSON>enerator
from embedding_utils.embedding_models import Embeddings, DenseVector, SparseVector, EmbeddingType
from logging_utils.unblocked_logger import UnblockedLogger

logger = UnblockedLogger(name=__name__)


class EmbeddingEndpointGenerator(EmbeddingGenerator):
    _embedding_endpoint: str
    _version = "2"
    _timeout: Tuple[float, float]

    def __init__(
        self,
        embedding_endpoint: str,
        timeout: Tuple[float, float] = (1, 5),
    ):
        self._embedding_endpoint = embedding_endpoint
        self._timeout = timeout

    @staticmethod
    def _create_retry_decorator() -> Callable[[Any], Any]:
        min_seconds = 1
        max_seconds = 3
        max_retries = 2

        # Wait 2^x * 1 second between each retry starting with
        # 4 seconds, then up to 10 seconds, then 10 seconds afterwards
        return retry(
            reraise=True,
            stop=stop_after_attempt(max_retries),
            wait=wait_exponential(multiplier=1, min=min_seconds, max=max_seconds),
            retry=retry_if_exception(
                lambda e: isinstance(e, requests.HTTPError)
                and (e.response.status_code == 429 or e.response.status_code >= 500)
            ),
            before_sleep=before_sleep_log(logger, sys_logger.ERROR),
        )

    def get_embeddings(self, embedding_type: EmbeddingType, docs: list[str]) -> Embeddings:
        return self.__embed(embedding_type=embedding_type, inputs=docs)

    def get_dense_embeddings(self, embedding_type: EmbeddingType, docs: list[str]) -> list[DenseVector]:
        return self.__embed(embedding_type=embedding_type, inputs=docs).dense_vectors

    def get_sparse_embeddings(self, embedding_type: EmbeddingType, docs: list[str]) -> list[SparseVector]:
        return self.__embed(embedding_type=embedding_type, inputs=docs).sparse_vectors

    @_create_retry_decorator()
    def __embed(
        self,
        embedding_type: EmbeddingType,
        inputs: list[str],
    ) -> EmbeddingOutputResponse:
        try:
            request_body = EmbeddingInputsRequest(
                inputs=inputs,
                version=self._version,
                embedding_type=embedding_type,
            )
            request_body_json = request_body.model_dump()
            response = requests.post(url=self._embedding_endpoint, json=request_body_json, timeout=self._timeout)

            # Check if the response status code is OK (200)
            if response.status_code == 200:
                return EmbeddingOutputResponse.model_validate(response.json())
            else:
                # If the response status code is not OK, log the error
                logger.error("Received non-OK response: %s. Response message: %s", response.status_code, response.text)
                response.raise_for_status()
        except requests.RequestException as e:
            # Handle network or request-related errors
            logger.error("Error occurred during the request: %s", e)
            # Raise the original exception again
            raise
        except Exception as e:
            # Handle other unexpected errors
            logger.error("An unexpected error occurred: %s", e)
            # Raise the original exception again
            raise
