import unittest

from text_utils.compression import Compression


class TestCompression(unittest.TestCase):
    def setUp(self):
        self.compression = Compression()

    def test_compress_base64_empty_text(self):
        original = ""
        compressed = self.compression.compress_to_base64(original)
        decompressed = self.compression.decompress_from_base64(compressed)
        self.assertEqual(original, decompressed)

    def test_compress_base64_text(self):
        original = "Hello World!"
        compressed = self.compression.compress_to_base64(original)
        decompressed = self.compression.decompress_from_base64(compressed)
        self.assertEqual(original, decompressed)

    def test_compress_bytes_empty_text(self):
        original = ""
        compressed = self.compression.compress_to_bytes(original)
        decompressed = self.compression.decompress_from_bytes(compressed)
        self.assertEqual(original, decompressed.decode("utf-8"))

    def test_compress_bytes_text(self):
        original = "Hello World!"
        compressed = self.compression.compress_to_bytes(original)
        decompressed = self.compression.decompress_from_bytes(compressed)
        self.assertEqual(original, decompressed.decode("utf-8"))


if __name__ == "__main__":
    unittest.main()
