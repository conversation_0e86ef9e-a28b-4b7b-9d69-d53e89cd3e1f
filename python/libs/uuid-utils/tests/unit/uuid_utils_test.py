import unittest

import uuid_utils.uuid_utils as uuid_utils


class TestUuidUtils(unittest.TestCase):
    def test_empty_string(self):
        expected_uuid = "d41d8cd9-8f00-3204-a980-0998ecf8427e"
        result_uuid = uuid_utils.UuidUtils.get_uuid_from_string("").__str__()
        self.assertEqual(result_uuid, expected_uuid)

    def test_valid_string(self):
        expected_uuid = "acbd18db-4cc2-385c-adef-654fccc4a4d8"
        result_uuid = uuid_utils.UuidUtils.get_uuid_from_string("foo").__str__()
        self.assertEqual(result_uuid, expected_uuid)

    def test_get_uuid_from_file(self):
        expected_uuid = "d28d78b5-5ca2-351c-a518-ee8b163ad5df"
        repo_id = "4b49f903-4bb6-4695-9fb0-b016f7d8e2ab"
        file_path = "projects/libs/File.kt"
        result_uuid = uuid_utils.UuidUtils.get_uuid_from_file(repo_id, file_path).__str__()
        self.assertEqual(result_uuid, expected_uuid)


if __name__ == "__main__":
    unittest.main()
