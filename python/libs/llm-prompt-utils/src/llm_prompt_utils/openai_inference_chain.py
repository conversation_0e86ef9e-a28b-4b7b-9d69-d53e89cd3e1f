from typing import Optional, Any

from langchain.chains.base import Chain
from langchain.chains.llm import LL<PERSON>hain
from langchain_community.chat_models.openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate

from llm_prompt_utils.llm_inference_chain import LLMIn<PERSON><PERSON>hain
from llm_prompt_utils.llm_prompt import LLMPrompt


class OpenAIInferenceChain(LLMInferenceChain):
    _llm: ChatOpenAI
    _llm_chain: Chain
    _llm_human_prompt: LLMPrompt
    _llm_system_prompt: Optional[LLMPrompt]
    _llm_chat_prompt: ChatPromptTemplate

    def __init__(
        self,
        openai_api_key: str,
        model_name: str,
        llm_human_prompt: LLMPrompt,
        llm_system_prompt: Optional[LLMPrompt] = None,
        max_tokens: int = 512,
        top_p: float = 0.7,
        temperature: float = 0.01,
        presence_penalty: float = 1.1,
        model_kwargs: Optional[dict] = None,
    ):
        model_kwargs = model_kwargs or {}
        model_kwargs = {
            "max_tokens": max_tokens,
            "top_p": top_p,
            "temperature": temperature,
            "presence_penalty": presence_penalty,
            **model_kwargs,
        }

        self._llm_human_prompt = llm_human_prompt
        self._llm_system_prompt = llm_system_prompt

        self._llm = ChatOpenAI(
            openai_api_key=openai_api_key,
            model_name=model_name,
            **model_kwargs,
        )
        chat_messages = [self._llm_human_prompt.get_prompt_template()]
        if self._llm_system_prompt:
            chat_messages.append(self._llm_system_prompt.get_prompt_template())

        self._llm_chat_prompt = ChatPromptTemplate.from_messages(chat_messages)

        self._llm_chain = LLMChain(
            llm=self._llm,
            prompt=self._llm_chat_prompt,
            verbose=True,
        )

    def predict(self, inputs: dict, **kwargs: Any):
        return self._llm_chain.invoke(input=inputs, **kwargs)
