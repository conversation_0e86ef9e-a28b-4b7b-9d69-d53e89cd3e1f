AWSTemplateFormatVersion: '2010-09-09'
Description: Simple AWS CloudFormation Sample Template

Parameters:
  InstanceType:
    Description: EC2 instance type
    Type: String
    Default: t2.micro
    AllowedValues:
      - t2.micro
      - t1.micro
      - t2.small
    ConstraintDescription: must be a valid EC2 instance type.

Resources:
  MyEC2Instance:
    Type: 'AWS::EC2::Instance'
    Properties:
      ImageId: ami-0abcdef1234567890  # Replace with a valid AMI ID for your region
      InstanceType: !Ref InstanceType
      KeyName: my-key-pair            # Replace with your key pair name
      SecurityGroups:
        - !Ref MySecurityGroup

  MySecurityGroup:
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: Allow SSH and HTTP
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: '22'
          ToPort: '22'
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: '80'
          ToPort: '80'
          CidrIp: 0.0.0.0/0

Outputs:
  InstanceId:
    Description: The Instance ID
    Value: !Ref MyEC2Instance
  PublicIP:
    Description: The Public IP address of the newly created EC2 instance
    Value: !GetAtt MyEC2Instance.PublicIp
