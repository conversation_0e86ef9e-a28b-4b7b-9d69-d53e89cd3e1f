{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": [":automergeDisabled", ":autodetectPinVersions", ":ignoreUnstable", "config:base", "group:babel<PERSON><PERSON>repo", "group:definitelyTyped", "group:fortawesome", "group:postcss", "group:storybook<PERSON>onorepo", "group:test"], "rebaseWhen": "never", "prConcurrentLimit": 10, "prHourlyLimit": 0, "packageRules": [{"groupName": "slate monorepo", "matchPackagePatterns": ["slate"], "matchUpdateTypes": ["digest", "patch", "minor", "major"], "enabled": true}, {"groupName": "stylelint monorepo", "extends": "packages:stylelint", "enabled": true}, {"matchPackagePatterns": ["kotlinx-serialization"], "enabled": false}, {"matchPackagePatterns": ["ktor"], "enabled": false}, {"matchPackagePatterns": ["postgres"], "enabled": false}, {"matchPackagePatterns": ["ktlint"], "enabled": false}], "ignorePaths": ["**/video-app/**", "**/assets/image/**", "**/generated/**", "**/fixtures/**"], "dependencyDashboard": true, "semanticCommits": true, "vulnerabilityAlerts": {"enabled": true}}